package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.factory.RemoteUserFallbackFactory;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.api.model.SysUserWx;
import com.ruoyi.system.api.param.DeleteSysUserWxBySysUserIdParam;
import com.ruoyi.system.api.param.UserQueryIdParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteUserService {
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source   请求来源
     * @return 结果
     */
    @GetMapping("/user/info/{username}")
    public R<LoginUser> getUserInfo(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取所有用户
     */
    @GetMapping("/user/allList")
    public List<SysUser> getAll();

    /**
     * 新增用户
     */
    @PostMapping("/user")
    public AjaxResult add(@RequestBody SysUser sysUser);

    /**
     * 获取用户拥有平台权限信息
     *
     * @return 用户拥有平台权限信息
     */
    @GetMapping("/user/getPlatformsByUserId/{userId}")
    public Set<String> getPlatformsByUserId(@PathVariable("userId") Long userId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 统一用户新增接口
     *
     * @param sysUser
     * @return
     */
    @PostMapping("/user/addUser")
    public R<SysUser> addUser(@RequestBody SysUser sysUser);

    /**
     * 根据用户id获取用户信息
     *
     * @param userId 用户id
     * @return 用户信息
     */
    @PostMapping("/user/getInfoByUserId")
    public R<SysUser> getInfoByUserId(@RequestBody Long userId);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @param source  请求来源
     * @return 结果
     */
    @PostMapping("/user/register")
    public R<Boolean> registerUserInfo(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册用户信息并返回用户信息
     *
     * @param sysUser 用户信息
     * @param source  请求来源
     * @return 结果
     */
    @PostMapping("/user/register2")
    public R<SysUser> registerUserInfo2(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 通过用户id查询用户信息
     *
     * @param userId 用户名
     * @param source 请求来源 SecurityConstants.INNER
     * @return 结果
     */
    @GetMapping("/user/getById/{userId}")
    public SysUser getUserInfoById(@PathVariable("userId") Long userId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 批量通过用户ids查询用户信息
     *
     * @param userIds 用户名
     * @param source  请求来源 SecurityConstants.INNER
     * @return 结果
     */
    @GetMapping("/user/getByIds/{userIds}")
    public List<SysUser> getUserListByIds(@PathVariable("userIds") Long[] userIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据岗位id，获取用户列表
     *
     * @param param  查询参数
     * @param source 请求来源 SecurityConstants.INNER
     * @return 结果
     */
    @PostMapping("/user/getByQuery")
    public List<SysUser> getUserListByQuery(@RequestBody UserQueryIdParam param, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取微信小程序和用户绑定列表
     *
     * @param sysUserWx
     * @return
     */
    @PostMapping("/sysUserWx/selectList")
    public List<SysUserWx> selectList(@Validated SysUserWx sysUserWx);


    @DeleteMapping("/sysUserWx/deleteSysUserWxByOpenId")
    int deleteSysUserWxByOpenId(String openId);

    @DeleteMapping("/sysUserWx/deleteSysUserWxByUnionid")
    int deleteSysUserWxByUnionid(String openId);

    @DeleteMapping("/sysUserWx/deleteSysUserWxBySysUserId")
    int deleteSysUserWxBySysUserId(DeleteSysUserWxBySysUserIdParam param);

    @PostMapping("/sysUserWx/insert")
    int insertSysUserWx(@Validated @RequestBody SysUserWx sysUserWx);

    /**
     * 根据据用户id获取岗位的用户权限编码
     */
    @GetMapping("/user/getPostPermissionCodeByUserId/{userId}")
    public Set<String> getPostPermissionCodeByUserId(@PathVariable("userId") Long userId);


    /**
     * 新增用户信息
     *
     * @param sysUser 用户信息
     * @return 结果
     */
    @PostMapping("/user/addCareWorker")
    public R<Boolean> addCareWorker(@RequestBody SysUser sysUser);

    /**
     * 修改用户
     *
     * @param sysUser
     * @return
     */
    @PostMapping("/user/updateCareWorker")
    public AjaxResult updateCareWorker(@RequestBody SysUser sysUser);

    /**
     * 删除用户
     *
     * @param userId
     * @return
     */
    @DeleteMapping("/user/deleteByeCareWorkerUserId/{userId}")
    public AjaxResult deleteByeCareWorkerUserId(@PathVariable("userId") Long userId);

    /**
     * 用户状态修改
     *
     * @param
     * @return
     */
    @PutMapping("/user/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user);

    /**
     * 服务商修改工作人员用户状态修改
     *
     * @param
     * @return
     */
    @PutMapping("/user/changeWorkStatus")
    public AjaxResult changeWorkStatus(@RequestBody SysUser user);

    /**
     * 修改用户信息
     *
     * @param user
     * @return
     */
    @PutMapping("/user/editUser")
    public AjaxResult editUser(@Validated @RequestBody SysUser user);

    /**
     * 查询手机号是否唯一
     *
     * @param phone
     * @return
     */
    @PostMapping("/user/checkAppPhoneUnique")
    public AjaxResult checkAppPhoneUnique(@RequestBody String phone);

    /**
     * 批量根据ids查询用户信息，返回Map,key:id,value:SysUser
     */
    @GetMapping("/user/getUserMapByIds/{ids}/")
    public Map<Long, SysUser> getUserMapByIds(@PathVariable("ids") Long[] ids);

    /**
     * 修改用户头像
     */
    @PutMapping("/user/profile/remote/avatar")
    public AjaxResult updateAvatar(@RequestParam("username") String username, @RequestParam("url") String url);

    /**
     * 通过用户名查询用户
     */
    @GetMapping("/user/info/{userName}")
    public R<LoginUser> selectUserByUserName(@PathVariable("userName") String userName, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
