package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.api.model.SysUserWx;
import com.ruoyi.system.api.param.DeleteSysUserWxBySysUserIdParam;
import com.ruoyi.system.api.param.UserQueryIdParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);
    private Object Boolean;

    @Override
    public RemoteUserService create(Throwable throwable) {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteUserService() {
            @Override
            public R<LoginUser> getUserInfo(String username, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<SysUser> getInfoByUserId(Long userId) {
                return R.fail("通过用户id获取用户信息接口失败:" + throwable.getMessage());
            }

            @Override
            public R<SysUser> addUser(SysUser sysUser) {
                return R.fail("统一用户新增接口失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> addCareWorker(SysUser sysUser) {
                return R.fail("失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult updateCareWorker(SysUser sysUser) {
                return AjaxResult.error("失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult deleteByeCareWorkerUserId(Long userId) {
                return AjaxResult.error("失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult changeStatus(SysUser sysUser) {
                return AjaxResult.error("失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult changeWorkStatus(SysUser sysUser) {
                return AjaxResult.error("失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult editUser(SysUser sysUser) {
                return AjaxResult.error("失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult checkAppPhoneUnique(String phone) {
                return AjaxResult.error("失败:" + throwable.getMessage());
            }

            @Override
            public Map<Long, SysUser> getUserMapByIds(Long[] ids) {
                return Collections.emptyMap();
            }

            @Override
            public AjaxResult updateAvatar(String username, String url) {
                return AjaxResult.error("失败:" + throwable.getMessage());
            }

            @Override
            public R<LoginUser> selectUserByUserName(String userName, String source) {
                return null;
            }

            @Override
            public List<SysUser> getAll() {
                return null;
            }

            @Override
            public AjaxResult add(SysUser sysUser) {
                return null;
            }

            @Override
            public R<Boolean> registerUserInfo(SysUser sysUser, String source) {
                return R.fail("注册用户失败:" + throwable.getMessage());
            }

            @Override
            public R<SysUser> registerUserInfo2(SysUser sysUser, String source) {
                return R.fail("注册用户失败:" + throwable.getMessage());
            }

            @Override
            public SysUser getUserInfoById(Long userId, String source) {
                return null;
            }

            @Override
            public List<SysUser> getUserListByIds(Long[] userIds, String source) {
                return null;
            }

            @Override
            public Set<String> getPlatformsByUserId(Long userId, String source) {
                return null;
            }

            @Override
            public List<SysUser> getUserListByQuery(UserQueryIdParam param, String source) {
                return null;
            }

            @Override
            public List<SysUserWx> selectList(SysUserWx sysUserWx) {
                return null;
            }

            @Override
            public int deleteSysUserWxByOpenId(String openId) {
                return 1;
            }

            @Override
            public int deleteSysUserWxByUnionid(String openId) {
                return 0;
            }

            @Override
            public int deleteSysUserWxBySysUserId(DeleteSysUserWxBySysUserIdParam param) {
                return 1;
            }

            @Override
            public int insertSysUserWx(SysUserWx sysUserWx) {
                return 1;
            }

            @Override
            public Set<String> getPostPermissionCodeByUserId(Long userId) {
                return null;
            }


        };
    }
}
