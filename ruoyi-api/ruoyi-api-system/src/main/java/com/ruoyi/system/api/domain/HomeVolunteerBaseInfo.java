package com.ruoyi.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 志愿者信息对象 t_home_volunteer_base_info
 *
 * <AUTHOR>
 * @date 2022-06-17
 */
@Data
@ApiModel("志愿者信息")
public class HomeVolunteerBaseInfo extends BaseEntity {
    private static final long serialVersionUID = 91384857328897533L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * indexId
     */
    @ApiModelProperty("报名管理表的indexId")
    private String indexId;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    @ApiModelProperty("身份证号")
    private String idCardNum;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @ApiModelProperty("联系电话")
    @NotNull(message = "手机号必填！")
    private String phone;

    /**
     * 民族
     */
    @Excel(name = "民族")
    @ApiModelProperty("民族")
    private String nation;
    /**
     * 民族Label
     */
    @Excel(name = "民族Label")
    @ApiModelProperty("民族Label")
    private String nationLabel;

    /**
     * 年龄
     */
    @Excel(name = "年龄")
    @ApiModelProperty("年龄")
    private String age;

    /**
     * 性别
     */
    @Excel(name = "性别")
    @ApiModelProperty("性别")
    private String sex;

    /**
     * 性别Label
     */
    @Excel(name = "性别Label")
    @ApiModelProperty("性别Label")
    private String sexLabel;

    /**
     * 职业
     */
    @Excel(name = "职业")
    @ApiModelProperty("职业")
    private String occupation;

    /**
     * 职业Label
     */
    @Excel(name = "职业Label")
    @ApiModelProperty("职业Label")
    private String occupationLabel;

    /**
     * 登录密码
     */
    @Excel(name = "登录密码")
    @ApiModelProperty("登录密码")
    private String password;

    /**
     * 联系地址
     */
    @Excel(name = "联系地址")
    @ApiModelProperty("联系地址")
    private String address;

    /**
     * 服务社区
     */
    @Excel(name = "服务社区")
    @ApiModelProperty("服务社区")
    private String serviceArea;

    /**
     * 服务社区Label
     */
    @Excel(name = "服务社区Label")
    @ApiModelProperty("服务社区Label")
    private String serviceAreaLabel;

    /**
     * 服务技能
     */
    @Excel(name = "服务技能")
    @ApiModelProperty("服务技能")
    private String serviceSkills;

    /**
     * 服务技能Label
     */
    @Excel(name = "服务技能Label")
    @ApiModelProperty("服务技能Label")
    private String serviceSkillsLabel;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("出生日期")
    private Date dateBirth;

    /**
     * 头像
     */
    @Excel(name = "头像")
    @ApiModelProperty("头像")
    private String img;

    /**
     * 累计服务单数
     */
    @Excel(name = "累计服务单数")
    @ApiModelProperty("累计服务单数")
    private Long cumulativeServicesNumber;

    /**
     * 累计服务时长
     */
    @Excel(name = "累计服务时长")
    @ApiModelProperty("累计服务时长")
    private Long cumulativeServicesHours;

    /**
     * 累计时间币
     */
    @Excel(name = "累计时间币")
    @ApiModelProperty("累计时间币")
    private Long cumulativeTimeCoin;

    /**
     * 当前时间币
     */
    @Excel(name = "当前时间币")
    @ApiModelProperty("当前时间币")
    private Long currentTimeCoin;

    /**
     * 关联的系统用户id
     */
    @Excel(name = "关联的系统用户id")
    @ApiModelProperty("关联的系统用户id")
    private Long sysUserId;

    /**
     * 是否属于单一志愿者 1是，2不是 有其他的角色
     */
    @Excel(name = "是否属于单一志愿者 1是，2不是 有其他的角色")
    @ApiModelProperty("是否属于单一志愿者 1是，2不是 有其他的角色")
    private String type;

    /**
     * 好评率
     */
    @Excel(name = "好评率")
    @ApiModelProperty("好评率")
    private String favorableRating;

    /**
     * 文化水平
     */
    @Excel(name = "文化水平")
    @ApiModelProperty(value = "文化水平")
    private String educationLevel;

    /**
     * 是否报名
     */
    @ApiModelProperty("是否报名")
    private String flag;

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private String activityId;

    /**
     * 政治面貌
     */
    @ApiModelProperty("政治面貌")
    private String politicalStatus;


    /**
     * 政治面貌Label
     */
    @ApiModelProperty("政治面貌Label")
    private String politicalStatusLabel;

    /**
     * 服务时间
     */
    @ApiModelProperty("服务时间")
    private String serviceTime;
    /**
     * 审核状态：1未审核，2审核驳回，3审核通过
     */
    @ApiModelProperty("审核状态：1未审核，2审核驳回，3审核通过")
    private String status;


    /**
     * 服务时间Array
     */
    @ApiModelProperty("服务时间Array")
    private List<String> serviceTimeArray;
    /**
     * 服务时间ArrayLabel
     */
    @ApiModelProperty("服务时间ArrayLabel")
    private String serviceTimeArrayLabel;

    /**
     * 所属社区
     */
    @ApiModelProperty("所属社区")
    private Long street;

    /**
     * 所属社区Label
     */
    @ApiModelProperty("所属社区Label")
    private String streetLabel;


}
