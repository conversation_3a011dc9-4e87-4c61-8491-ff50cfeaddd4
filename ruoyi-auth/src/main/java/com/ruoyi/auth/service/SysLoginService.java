package com.ruoyi.auth.service;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.auth.form.VisitorRegisteredBody;
import com.ruoyi.common.core.constant.*;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.UserStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.ip.IpUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.*;
import com.ruoyi.system.api.domain.SysLogininfor;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.SysUserRole;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.api.model.SysUserWx;
import com.ruoyi.system.api.param.DeleteSysUserWxBySysUserIdParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.ruoyi.common.core.constant.CacheConstants.LOGIN_TOKEN_KEY;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {

    @Autowired
    private ComboHomecareService comboHomecareService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysRecordLogService recordLogService;

    @Autowired
    protected RemoteLogService remoteLogService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RemoteMenuService remoteMenuService;

    @Autowired
    private RemoteRoleService remoteRoleService;

    @Autowired
    private RemoteIbmsDangjianService remoteIbmsDangjianService;

    /**
     * 登录
     */
    public LoginUser login(String username, String password, String platform) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写", platform);
            throw new ServiceException("用户/密码必须填写");
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围", platform);
            throw new ServiceException("用户密码不在指定范围");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围", platform);
            throw new ServiceException("用户名不在指定范围");
        }
        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);

        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "登录用户不存在", platform);
            throw new ServiceException("登录用户：" + username + " 不存在");
        }
        if (R.FAIL == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }

        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除", platform);
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员", platform);
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }
        // 验证密码
        passwordService.validate(user, password, platform);

        // 小程序企业用户和访客用户不能登录后台管理系统
        if (user.getRoles().stream().filter(role -> {
            return StringUtils.isNotBlank(role.getRoleKey()) && ("enterprise_user ".equals(role.getRoleKey()) || "registered_visitor".equals(role.getRoleKey()));
        }).findFirst().orElse(null) != null) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "小程序企业用户和访客用户不能登录后台管理系统！", platform);
            throw new ServiceException("小程序企业用户和访客用户不能登录后台管理系统");
        }

        // 如果platform不是1（中控），就要判断用户是否有该平台的权限
        AtomicReference<Boolean> hasPlatform = new AtomicReference<>(false);
        if (!"1".equals(platform)) {
            user.getRoles().stream().filter(role -> StrUtil.isNotBlank(role.getPlatforms())).forEach(role -> {
                String[] platforms = role.getPlatforms().split(",");
                Arrays.stream(platforms).forEach(p -> {
                    if (p.equals(platform)) {
                        hasPlatform.set(true);
                    }
                });

            });

            if (!hasPlatform.get()) {
                recordLogininfor(username, Constants.LOGIN_FAIL, "当前用户没有本平台权限！", platform);
                throw new ServiceException("当前用户没有本平台权限");
            }

            // 如果platform是党建，校验账户是否绑定党员信息且有对应平台权限，否则不允许登录
            if (!SecurityUtils.isAdmin(user.getUserId()) && Objects.equals(platform, "8")) {
                if (!remoteIbmsDangjianService.checkBind(user.getUserId())) {
                    recordLogininfor(username, Constants.LOGIN_FAIL, "当前用户没有绑定党员信息！", platform);
                    throw new ServiceException("当前用户没有绑定党员信息");
                }
                if (user.getRoles().stream().noneMatch(a -> "dangjian_all".equals(a.getRoleKey()))) {
                    recordLogininfor(username, Constants.LOGIN_FAIL, "当前党员用户没有权限！", platform);
                    throw new ServiceException("当前党员用户没有权限");
                }
            }
        }

        recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功", platform);

        // 如果角色platforms包含“党建”， 触发党建登录获取积分接口
        dangjianLoginTrigger(user);
        return userInfo;
    }

    public void logout(String loginName, String platform) {
        recordLogService.recordLogininfor(loginName, Constants.LOGOUT, "退出成功", platform);
    }

    /**
     * 党建专用，触发登录获取积分接口
     *
     * @param user
     */
    private void dangjianLoginTrigger(SysUser user) {
        // 如果登录的是党建用户账号,调用登录获取积分接口
        user.getRoles().forEach(role -> {
            String[] platforms = role.getPlatforms() != null ? role.getPlatforms().split(",") : new String[]{};
            if (Arrays.stream(platforms).filter(p -> "8".equals(p)).count() > 0) {
                remoteIbmsDangjianService.loginTrigger(user.getUserId());
                return;
            }
        });
    }

    /**
     * 注册
     */
    public void register(String username, String password, String platform) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            throw new ServiceException("用户/密码必须填写");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new ServiceException("账户长度必须在2到20个字符之间");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new ServiceException("密码长度必须在5到20个字符之间");
        }

        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        R<?> registerResult = remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER);

        if (R.FAIL == registerResult.getCode()) {
            throw new ServiceException(registerResult.getMsg());
        }
        recordLogService.recordLogininfor(username, Constants.REGISTER, "注册成功", platform);
    }

    public R getTokenByOpenId(String openId, String unionid, String platform) {
        // 获取用户信息
        SysUserWx sysUserWx = getUserInfoByUnionid(unionid);
        if (sysUserWx == null) {
            return R.ok(null, "暂无绑定人员，请先绑定");
        }

        // 根据用户ID获取详细用户信息
        SysUser user = remoteUserService.getUserInfoById(sysUserWx.getSysUserId(), SecurityConstants.INNER);
        if (user == null) {
            throw new ServiceException("用户异常，请联系管理员！");
        }

        // 根据平台类型进行权限验证
        boolean hasPermission = verifyPlatformPermissions(sysUserWx.getSysUserId(), platform);
        if (!hasPermission) {
            return R.ok(null, "此账号当前平台没有权限！");
        }

        // 特殊平台额外检查
        if (!handleSpecialPlatformChecks(sysUserWx, user, platform)) {
            return R.ok(null, "当前用户没有绑定党员信息或权限不足");
        }

        // 更新或创建对应平台的记录
        updateOrCreatePlatformRecord(sysUserWx, openId, unionid, platform);

        // 模拟登录并刷新token
        Map<String, Object> token = simulateLoginAndRefreshToken(sysUserWx, user);

        // 记录登录信息
        recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, "移动端登录成功", platform);

        // 触发党建登录事件
        dangjianLoginTrigger(user);

        return R.ok(token);
    }


    public int setOpenIdToUser(String openId, String unionid, String platform) {
        LoginUser loginUser = SecurityUtils.getLoginUser();

        // 清楚微信小程序关联信息
        SysUserWx sysUserWxParam = new SysUserWx();
        sysUserWxParam.setSysUserId(loginUser.getUserid());
        List<SysUserWx> userWxList1 = remoteUserService.selectList(sysUserWxParam);// 此系统用户关联的所有平台
        SysUserWx sysUserWxParam2 = new SysUserWx();
        sysUserWxParam2.setUnionid(unionid);
        List<SysUserWx> userWxList2 = remoteUserService.selectList(sysUserWxParam2);// 此微信小程序统一标识关联的所有平台
        userWxList1.addAll(userWxList2);

        // 清空该用户下的redis的token信息
        for (SysUserWx homeSysUserWx : userWxList1) {
            boolean b = redisService.deleteObject(homeSysUserWx.getLoginTokens());
            recordLogininfor(loginUser.getUsername(), Constants.LOGOUT, "移动端被挤掉退出", platform);
        }

        // 删除该unionid下的关联
        remoteUserService.deleteSysUserWxByUnionid(unionid);
        // 删除该系统用户下的关联
        DeleteSysUserWxBySysUserIdParam param = new DeleteSysUserWxBySysUserIdParam();
        param.setUserId(loginUser.getUserid());
        remoteUserService.deleteSysUserWxBySysUserId(param);

        SysUserWx homeSysUserWx = new SysUserWx();
        homeSysUserWx.setSysUserId(loginUser.getUserid());
        homeSysUserWx.setOpenId(openId);
        homeSysUserWx.setPlatform(platform);
        homeSysUserWx.setUnionid(unionid);
        String token = loginUser.getToken();
        homeSysUserWx.setLoginTokens(LOGIN_TOKEN_KEY + token);
        return remoteUserService.insertSysUserWx(homeSysUserWx);
    }

    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param status   状态
     * @param message  消息内容
     * @param platform 平台类型
     * @return
     */
    public void recordLogininfor(String username, String status, String message, String platform) {
        SysLogininfor logininfor = new SysLogininfor();
        logininfor.setUserName(username);
        logininfor.setIpaddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
        logininfor.setMsg(message);
        logininfor.setPlatform(platform);
        // 日志状态
        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            logininfor.setStatus(Constants.LOGIN_SUCCESS_STATUS);
        } else if (Constants.LOGIN_FAIL.equals(status)) {
            logininfor.setStatus(Constants.LOGIN_FAIL_STATUS);
        }
        remoteLogService.saveLogininfor(logininfor, SecurityConstants.INNER);
    }

    public LoginUser appLogin(String username, String password, String platform, String flag) {
        // 校验用户名和密码
        validateCredentials(username, password, platform);

        // 获取用户信息
        R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);
        handleUserResult(userResult, username, platform);

        LoginUser userInfo = userResult.getData();
        SysUser user = userInfo.getSysUser();

        // 校验用户状态
        validateUserStatus(user, username, platform);

        // 校验密码
        validatePassword(password, user.getPassword(), username, platform);

        // 校验平台权限(包括platform&roleKy)
        validatePlatformPermission(userInfo, user, platform);

        // 记录登录信息并触发党建登录
        recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功", platform);
        dangjianLoginTrigger(user);

        return userInfo;
    }

    private void validateCredentials(String username, String password, String platform) {
        if (StringUtils.isAnyBlank(username, password)) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写", platform);
            throw new ServiceException("用户/密码必须填写");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围", platform);
            throw new ServiceException("用户密码不在指定范围");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围", platform);
            throw new ServiceException("用户名不在指定范围");
        }
    }

    private void handleUserResult(R<LoginUser> userResult, String username, String platform) {
        if (R.FAIL == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }
        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "登录用户不存在", platform);
            throw new ServiceException("登录用户：" + username + " 不存在");
        }
    }

    private void validateUserStatus(SysUser user, String username, String platform) {
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除", platform);
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员", platform);
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }
    }

    private void validatePassword(String inputPassword, String storedPassword, String username, String platform) {
        if (!SecurityUtils.matchesPassword(inputPassword, storedPassword)) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码错误", platform);
            throw new ServiceException("用户不存在/密码错误");
        }
    }

    private void validatePlatformPermission(LoginUser userInfo, SysUser user, String platform) {
        if (isValidPlatform(platform)) {
            Set<String> userPlatforms = remoteUserService.getPlatformsByUserId(userInfo.getSysUser().getUserId(), SecurityConstants.INNER);
            if (!userPlatforms.contains(platform)) {
                recordLogininfor(userInfo.getUsername(), Constants.LOGIN_FAIL, "用户没有当前平台权限", platform);
                throw new ServiceException("用户没有当前平台权限");
            }
            handleSpecificPlatformPermissions(userInfo, user, platform, userPlatforms);
        } else {
            userInfo.setPlatforms(remoteUserService.getPlatformsByUserId(user.getUserId(), SecurityConstants.INNER));
        }
    }

    private boolean isValidPlatform(String platform) {
        return AppRoleKeyConstants.ALL_LIST.contains(platform);
    }

    private void handleSpecificPlatformPermissions(LoginUser userInfo, SysUser user, String platform, Set<String> platforms) {
        switch (platform) {
            case "inner":
                if (platforms.isEmpty()) {
                    recordLogininfor(userInfo.getUsername(), Constants.LOGIN_FAIL, "用户没有当前平台权限", platform);
                    throw new ServiceException("用户没有当前平台权限");
                }
                userInfo.setPlatforms(platforms);
                break;
            case "outer":
                handleOuterPlatformPermission(userInfo, user, platform);
                break;
            default:
                handleSimplePlatform(userInfo, user, platform);
                break;
        }
    }

    private void handleOuterPlatformPermission(LoginUser userInfo, SysUser user, String platform) {
        SysRole enterpriseRole = user.getRoles().stream().filter(a -> "enterprise_user".equals(a.getRoleKey())).findFirst().orElse(null);
        SysRole visitorRole = user.getRoles().stream().filter(a -> "registered_visitor".equals(a.getRoleKey())).findFirst().orElse(null);

        if (enterpriseRole != null) {
            userInfo.setOtherPlatforms("enterprise_user");
        } else if (visitorRole != null) {
            userInfo.setOtherPlatforms("registered_visitor");
        } else {
            recordLogininfor(userInfo.getUsername(), Constants.LOGIN_FAIL, "用户没有当前平台权限", platform);
            throw new ServiceException("用户没有当前平台权限");
        }
    }

    private void handleSimplePlatform(LoginUser userInfo, SysUser user, String platform) {
        SysRole role = user.getRoles().stream().filter(a -> AppRoleKeyConstants.SIMPLE_LIST.contains(a.getRoleKey())).findFirst().orElse(null);
        if (role != null) {
            userInfo.setOtherPlatforms(role.getRoleKey());
        } else {
            recordLogininfor(userInfo.getUsername(), Constants.LOGIN_FAIL, "用户没有当前平台权限", platform);
            throw new ServiceException("用户没有当前平台权限");
        }
    }

    /**
     * 删除unionid、userid关联的所有信息，并清理缓存
     *
     * @param openId
     * @param unionid
     * @return
     */
    public int deleteOpenIdToUser(String openId, String unionid) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        Long userId = loginUser.getSysUser().getUserId();

        // 系统用户关联数据
        SysUserWx sysUserWxParam = new SysUserWx();
        sysUserWxParam.setSysUserId(userId);
        List<SysUserWx> wxList1 = remoteUserService.selectList(sysUserWxParam);
        // unionid关联数据
        SysUserWx sysUserWxParam2 = new SysUserWx();
        sysUserWxParam2.setUnionid(unionid);
        List<SysUserWx> wxList = remoteUserService.selectList(sysUserWxParam2);
        wxList.addAll(wxList1);
        if (wxList.size() > 0) {
            for (SysUserWx homeSysUserWx : wxList) {
                String loginTokens = homeSysUserWx.getLoginTokens();
                redisService.deleteObject(loginTokens);
            }
        }

        remoteUserService.deleteSysUserWxByUnionid(unionid);
        DeleteSysUserWxBySysUserIdParam deleteSysUserWxBySysUserIdParam = new DeleteSysUserWxBySysUserIdParam();
        deleteSysUserWxBySysUserIdParam.setUserId(userId);
        return remoteUserService.deleteSysUserWxBySysUserId(deleteSysUserWxBySysUserIdParam);
    }

    public void registerByRegisteredVisitor(VisitorRegisteredBody visitorRegisteredBody) {
        String nickname = visitorRegisteredBody.getNickname();
        String phone = visitorRegisteredBody.getPhone();
        String password = visitorRegisteredBody.getPassword();
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(phone, password)) {
            throw new ServiceException("手机号/密码必须填写");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new ServiceException("密码长度必须在5到20个字符之间");
        }

        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(phone);
        sysUser.setNickName(nickname);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        R<SysUser> registerResult = remoteUserService.registerUserInfo2(sysUser, SecurityConstants.INNER);
        if (R.FAIL == registerResult.getCode()) {
            throw new ServiceException(registerResult.getMsg());
        }

        // 挂到用户角色，固定的
        List<SysRole> sysRoles = remoteRoleService.list2(new SysRole());
        SysRole sysRole = sysRoles.stream().filter(a -> "registered_visitor".equals(a.getRoleKey())).findFirst().orElse(null);
        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setRoleId(sysRole.getRoleId());
        sysUserRole.setUserId(registerResult.getData().getUserId());
        List<SysUserRole> sysUserRoles = new ArrayList<>();
        sysUserRoles.add(sysUserRole);
        AjaxResult ajaxResult = remoteRoleService.addUserRole(sysUserRoles);
        if (ajaxResult.isError()) {
            throw new ServiceException("注册失败！");
        }
        recordLogService.recordLogininfor(phone, Constants.REGISTER, "注册成功", "registered_visitor");
    }

    /**
     * 商家PC端登录
     *
     * @param username
     * @param password
     * @return
     */
    public LoginUser businessLogin(String username, String password, String platform) {
        boolean roleFlag = false;
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写", platform);
            throw new ServiceException("用户/密码必须填写");
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围", platform);
            throw new ServiceException("用户密码不在指定范围");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围", platform);
            throw new ServiceException("用户名不在指定范围");
        }
        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);

        if (R.FAIL == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }

        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "登录用户不存在", platform);
            throw new ServiceException("登录用户：" + username + " 不存在");
        }
        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除", platform);
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员", platform);
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }
        if (!SecurityUtils.matchesPassword(password, user.getPassword())) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码错误", platform);
            throw new ServiceException("用户不存在/密码错误");
        }
        List<SysRole> roleIds = user.getRoles();
        List<String> arrayList = new ArrayList();
        for (SysRole role : roleIds) {
            String roleKey = role.getRoleKey();
            arrayList.add(roleKey);
        }
        // 根据标识判断当前用户角色是否有权限
        if (arrayList.contains("spfws") || arrayList.contains("cpfws") || arrayList.contains("fwfws") || arrayList.contains("fwswsh")) {
            roleFlag = true;
        }
        if (!roleFlag) {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户不存在/密码错误", platform);
            throw new ServiceException("用户不存在/密码错误");
        }
        recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功", platform);
        return userInfo;
    }

    private SysUserWx getUserInfoByUnionid(String unionid) {
        SysUserWx sysUserWx = new SysUserWx();
        sysUserWx.setUnionid(unionid);
        List<SysUserWx> sysUserWxList = remoteUserService.selectList(sysUserWx);
        return sysUserWxList.isEmpty() ? null : sysUserWxList.get(0);
    }

    private boolean verifyPlatformPermissions(Long userId, String platform) {
        Set<String> platforms = remoteUserService.getPlatformsByUserId(userId, SecurityConstants.INNER);
        return platforms != null && !platforms.isEmpty() && platforms.contains(platform);
    }

    private boolean handleSpecialPlatformChecks(SysUserWx sysUserWx, SysUser user, String platform) {
        if ("8".equals(platform) && !SecurityUtils.isAdmin(sysUserWx.getSysUserId())) {
            return remoteIbmsDangjianService.checkBind(sysUserWx.getSysUserId())
                    && user.getRoles().stream().anyMatch(role -> "dangjian_all".equals(role.getRoleKey()) || "dangjian_mobile".equals(role.getRoleKey()));
        }
        return true;
    }

    private void updateOrCreatePlatformRecord(SysUserWx sysUserWx, String openId, String unionid, String platform) {
        if (sysUserWx == null || !"inner".equals(platform) && !"outer".equals(platform) && !"applet_lr".equals(platform) && !"app_hg".equals(platform)) {
            sysUserWx = new SysUserWx();
            sysUserWx.setPlatform(platform);
            sysUserWx.setOpenId(openId);
            sysUserWx.setUnionid(unionid);
            sysUserWx.setCreateDate(new Date());
            remoteUserService.insertSysUserWx(sysUserWx);
        }
    }

    private Map<String, Object> simulateLoginAndRefreshToken(SysUserWx sysUserWx, SysUser user) {
        // 查询用户信息 模拟登录
        R<LoginUser> userResult = remoteUserService.getUserInfo(user.getUserName(), SecurityConstants.INNER);
        checkUserStatus(userResult, user.getUserName());

        // 刷新token过期时间，保存用户信息到redis
        LoginUser loginUser = userResult.getData();
        refreshToken(loginUser, sysUserWx);

        // 构建Jwt存储信息,方向寻找token
        Map<String, Object> claimsMap = createClaimsMap(sysUserWx, user);
        return createResponseToken(claimsMap);
    }

    private void checkUserStatus(R<LoginUser> userResult, String userName) {
        if (userResult.getCode() != 200 || "1".equals(userResult.getData().getSysUser().getStatus())) {
            throw new ServiceException("对不起，您的账号：" + userName + " 已停用");
        }
    }

    private void refreshToken(LoginUser loginUser, SysUserWx sysUserWx) {
        loginUser.setToken(sysUserWx.getLoginTokens().replace("login_tokens:", ""));
        loginUser.setUserid(loginUser.getSysUser().getUserId());
        loginUser.setUsername(loginUser.getSysUser().getUserName());
        loginUser.setIpaddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
        tokenService.refreshToken(loginUser);
    }

    private Map<String, Object> createClaimsMap(SysUserWx sysUserWx, SysUser user) {
        Map<String, Object> claimsMap = new HashMap<>();
        claimsMap.put(SecurityConstants.USER_KEY, sysUserWx.getLoginTokens().replace("login_tokens:", ""));
        claimsMap.put(SecurityConstants.DETAILS_USER_ID, user.getUserId());
        claimsMap.put(SecurityConstants.DETAILS_USERNAME, user.getUserName());
        return claimsMap;
    }

    private Map<String, Object> createResponseToken(Map<String, Object> claimsMap) {
        Map<String, Object> rspMap = new HashMap<>();
        rspMap.put("access_token", JwtUtils.createToken(claimsMap));
        rspMap.put("expires_in", CacheConstants.EXPIRATION);
        return rspMap;
    }
}
