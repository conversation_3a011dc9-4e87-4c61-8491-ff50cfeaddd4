package com.ruoyi.auth.form;

/**
 * 用户登录对象
 *
 * <AUTHOR>
 */
public class LoginBody {
    /**
     * 用户名
     */
    private String username;
    /**
     * 用户密码
     */
    private String password;
    /**
     * 业务系统类型,小程序系统标识，inner：内部，outer：外部，applet_lr：机构养老-老人微端，app_hg：机构养老-护工微端
     */
    private String platform;
    /**
     * 应用端（验证码在用）
     */
    private String application;
    /**
     * 登录标识(志愿者专用？)
     */
    private String flag;

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }
}
