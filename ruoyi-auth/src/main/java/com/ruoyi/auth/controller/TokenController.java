package com.ruoyi.auth.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import com.ruoyi.auth.form.LoginBody;
import com.ruoyi.auth.form.RegisterBody;
import com.ruoyi.auth.form.VisitorRegisteredBody;
import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.common.error.WxErrorException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@Api(value = "权限Controller", tags = {"权限Controller"})
@RestController
public class TokenController {
    private static final Logger log = LoggerFactory.getLogger(TokenController.class);
    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private WxMaService wxMaService;

    @PostMapping("login")
    public R<?> login(@RequestBody LoginBody form) {
        // 用户登录
        LoginUser userInfo = sysLoginService.login(form.getUsername(), form.getPassword(), form.getPlatform());
        // 获取登录token
        R<Map<String, Object>> ok = R.ok(tokenService.createToken(userInfo));
        return ok;
    }

    @ApiIgnore
    @PostMapping("createToken")
    public R<?> createToken(@RequestBody LoginUser userInfo) {
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo));
    }

    @DeleteMapping("logout")
    public R<?> logout(HttpServletRequest request, String platform) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            sysLoginService.logout(username, platform);
        }
        return R.ok();
    }

    @PostMapping("refresh")
    public R<?> refresh(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return R.ok();
        }
        return R.ok();
    }

    @PostMapping("register")
    public R<?> register(@RequestBody RegisterBody registerBody) {
        // 用户注册
        sysLoginService.register(registerBody.getUsername(), registerBody.getPassword(), registerBody.getPlatform());
        return R.ok();
    }

    /**
     * 商家PC端登录
     *
     * @param form
     * @return
     */
    @PostMapping("businessLogin")
    @ApiOperation(value = "商家PC端登录")
    public R<?> businessLogin(@RequestBody LoginBody form) {
        // 用户登录
        LoginUser userInfo = sysLoginService.businessLogin(form.getUsername(), form.getPassword(), form.getPlatform());
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo));
    }


    // 上是PC端登录相关，下是移动端登录相关
    // ------------------------------------------------------ 分界线 ------------------------------------------------------


    /**
     * 企业端小程序访客注册
     */
    @PostMapping("registerByRegisteredVisitor")
    public R<?> registerByRegisteredVisitor(@RequestBody VisitorRegisteredBody visitorRegisteredBody) {
        // 企业端小程序访客注册
        sysLoginService.registerByRegisteredVisitor(visitorRegisteredBody);
        return R.ok();
    }

    /**
     * 移动端登录获取token
     *
     * @param form
     * @return
     */
    @PostMapping("appLogin")
    @ApiOperation(value = "移动端登录获取token")
    public R<?> appLogin(@RequestBody LoginBody form) {
        // 用户登录
        LoginUser userInfo = sysLoginService.appLogin(form.getUsername(), form.getPassword(), form.getPlatform(), form.getFlag());
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo));
    }

    /**
     * @description: 根据code和appId获取openId
     * @author: lyc
     * @date: 2023/2/13 11:20:31
     * @param: [code]
     * @return: java.lang.String
     **/
    /**
     * 登陆接口
     */
    @ApiOperation(value = "根据code和appId获取openId&unionid")
    @GetMapping("getOpenIdByCode")
    public ResponseEntity getOpenIdByCode(@RequestParam(name = "code", required = true) String code, @RequestParam(name = "appId", required = true) String appId) {
        if (!wxMaService.switchover(appId)) {
            throw new IllegalArgumentException(String.format("未找到对应appId=[%s]的配置，请核实！", appId));
        }
        try {
            WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(code);
            Map<String, String> data = new HashMap();
            data.put("openId", session.getOpenid());
            data.put("unionid", session.getUnionid());
            // TODO 可以增加自己的逻辑，关联业务相关数据
            return ResponseEntity.ok(data);
        } catch (WxErrorException e) {
            log.error(e.getMessage());
            throw new ServiceException(e.getError().getJson());
        } finally {
            WxMaConfigHolder.remove();// 清理ThreadLocal
        }
    }

    @ApiOperation(value = "根据openId&unionid获取用户token")
    @GetMapping("getTokenByOpenId")
    public R getTokenByOpenId(@RequestParam(name = "openId") String openId, @RequestParam(name = "unionid") String unionid, @RequestParam(name = "platform") String platform) {
        R tokenByOpenId = sysLoginService.getTokenByOpenId(openId, unionid, platform);
        return tokenByOpenId;
    }

    /**
     * 账密登录后调用
     *
     * @param openId
     * @param unionid
     * @param platform
     * @return
     */
    @ApiOperation(value = "根据openId绑定用户信息")
    @GetMapping("setOpenIdToUser")
    @Transactional
    public AjaxResult setOpenIdToUser(@RequestParam(name = "openId", required = true) String openId, @RequestParam(name = "unionid") String unionid, @RequestParam(name = "platform", required = true) String platform) {
        int i = sysLoginService.setOpenIdToUser(openId, unionid, platform);
        return new AjaxResult();

    }

    /**
     * 注销当前账户后调用
     *
     * @param openId
     * @param unionid
     * @return
     */
    @ApiOperation(value = "根据openId解绑用户信息")
    @GetMapping("deleteOpenIdToUser")
    public AjaxResult deleteOpenIdToUser(@RequestParam(name = "openId", required = true) String openId, @RequestParam(name = "unionid") String unionid) {
        return AjaxResult.success(sysLoginService.deleteOpenIdToUser(openId, unionid));
    }

    @ApiOperation(value = "移动端通过token获取用户信息")
    @GetMapping("getAppUserDataInfo")
    public AjaxResult getAppUserDataInfo() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return AjaxResult.success().put("data", loginUser);
    }

}
