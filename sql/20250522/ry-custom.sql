ALTER TABLE `ry-custom`.`t_payment_change_record`
    ADD COLUMN `create_time` datetime NULL COMMENT '创建时间' AFTER `details`;

-- ----------------------------
-- 1. 保障金账户信息表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `t_security_balance_info` (
                                                         `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                                         `elderly_id` varchar(64) DEFAULT NULL COMMENT '老人id，关联表：t_elderly_people_info',
                                                         `last_amount` decimal(10,4) DEFAULT NULL COMMENT '上次余额',
                                                         `amount` decimal(10,4) DEFAULT NULL COMMENT '余额',
                                                         `create_time` datetime DEFAULT NULL COMMENT '创建日期',
                                                         `create_by` varchar(64) DEFAULT NULL COMMENT '创建人员',
                                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                         `update_by` varchar(64) DEFAULT NULL COMMENT '修改人员',
                                                         `del_flag` varchar(64) DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
                                                         `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
                                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB COMMENT='保障金账户信息表';

-- ----------------------------
-- 2. 保障金账户记录表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `t_security_balance_records` (
`id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                                            `elderly_id` varchar(64) DEFAULT NULL COMMENT '老人id，关联表：t_elderly_people_info',
                                                            `last_amount` decimal(10,4) DEFAULT NULL COMMENT '上次余额',
                                                            `changed_amount` decimal(10,4) DEFAULT NULL COMMENT '变动金额',
                                                            `amount` decimal(10,4) DEFAULT NULL COMMENT '余额',
                                                            `changed_type` varchar(2) DEFAULT NULL COMMENT '变动类型，字典：custom_security_balance_changed_type；1：充值，2：扣款，3：退款',
                                                            `create_time` datetime DEFAULT NULL COMMENT '创建日期',
                                                            `create_by` varchar(64) DEFAULT NULL COMMENT '创建人员',
                                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                            `update_by` varchar(64) DEFAULT NULL COMMENT '修改人员',
                                                            `del_flag` varchar(64) DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏',
                                                            `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
                                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB COMMENT='保障金账户记录表';

ALTER TABLE `ry-custom`.`t_consume_account_detail`
    MODIFY COLUMN `type` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型，字典：custom_consume_account_type；1:离院退费，2:变更退费，3:变更支出，4:溢缴收入，5:缴费支出，6:账户充值' AFTER `elderly_phone`;
