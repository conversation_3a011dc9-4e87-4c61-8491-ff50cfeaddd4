-- ----------------------------
-- 添加保障金变动类型字典
-- ----------------------------
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES ('机构养老-保障金-变动类型', 'custom_security_balance_changed_type', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1, '充值', '1', 'custom_security_balance_changed_type', NULL, 'default', 'N', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (2, '扣款', '2', 'custom_security_balance_changed_type', NULL, 'default', 'N', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (3, '退款', '3', 'custom_security_balance_changed_type', NULL, 'default', 'N', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3978, '编辑咨询表单', 3840, 1, 'searchRecordEditForm', 'marketingManagement/searchRecord/editForm', NULL, 1, 0, 'C', '1', '0', '', 'download', NULL, 'admin', '2025-05-26 08:55:42', 'admin', '2025-05-26 08:56:23', '', '100', NULL);
INSERT INTO `ry-cloud`.`sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `applet_icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `platform`, `applet_path`) VALUES (3977, '首页', -100, 1, 'index', '/index', NULL, 1, 0, 'C', '0', '0', '', 'rate', NULL, 'admin', '2025-05-23 15:56:03', 'admin', '2025-05-23 15:58:54', '', '100', NULL);

UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '入住长者信息', `parent_id` = 2000, `order_num` = 2, `path` = 'elderlyInfo', `component` = 'elderlyManage/elderlyInfo', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '1', `status` = '1', `perms` = '', `icon` = 'user', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-04-08 22:03:00', `update_by` = 'admin', `update_time` = '2025-06-05 10:36:40', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2027;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '费用统计', `parent_id` = 2012, `order_num` = 9, `path` = 'feeStatistics', `component` = 'costManage/feeStatistics/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'cascader', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-04-25 13:58:06', `update_by` = 'admin', `update_time` = '2025-06-03 10:59:39', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3961;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '医疗保障金支出记录', `parent_id` = 2012, `order_num` = 8, `path` = 'medicalInsuranceFundExpenseRecord', `component` = 'costManage/medicalInsuranceFundExpenseRecord/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'documentation', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-04-25 11:13:32', `update_by` = 'admin', `update_time` = '2025-06-03 10:59:34', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3960;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '消费账户明细', `parent_id` = 2012, `order_num` = 7, `path` = 'consumptionAccountDetails', `component` = 'costManage/consumptionAccountDetails/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'client', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-04-25 10:43:42', `update_by` = 'admin', `update_time` = '2025-06-03 10:58:15', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3959;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '缴费记录', `parent_id` = 2012, `order_num` = 6, `path` = 'paymentRecords', `component` = 'costManage/paymentRecords/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'druid', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-04-13 11:50:38', `update_by` = 'admin', `update_time` = '2025-06-03 10:58:08', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3953;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '消费账户充值', `parent_id` = 2012, `order_num` = 4, `path` = 'costPayment', `component` = 'costManage/costPayment/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'component', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-29 16:24:26', `update_by` = 'admin', `update_time` = '2025-06-03 10:57:46', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2015;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '套餐费用配置', `parent_id` = 2012, `order_num` = 3, `path` = 'monthlyCostConfig', `component` = 'costManage/monthlyCostConfig/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'dict', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-30 10:40:11', `update_by` = 'admin', `update_time` = '2025-06-03 10:57:39', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2018;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '缴费提醒', `parent_id` = 2012, `order_num` = 2, `path` = 'feesRemind', `component` = 'costManage/feesRemind/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'email', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-04-25 14:14:28', `update_by` = 'admin', `update_time` = '2025-06-03 10:57:32', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3963;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '合同管理', `parent_id` = 2012, `order_num` = 1, `path` = 'contractManagementList', `component` = 'marketingManagement/contractManagement/list', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'documentation', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2024-11-13 16:52:31', `update_by` = 'admin', `update_time` = '2025-06-03 10:57:25', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3851;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '护理报表', `parent_id` = 2008, `order_num` = 12, `path` = 'nursingReport', `component` = 'NursingManagement/nursingReport/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'chart', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2024-11-22 15:34:28', `update_by` = 'admin', `update_time` = '2025-06-03 10:56:40', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3859;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '护工查房记录', `parent_id` = 2008, `order_num` = 11, `path` = 'caregiverRoundingRecord', `component` = 'NursingManagement/caregiverRoundingRecord/index', `query` = '', `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'druid', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-05-07 11:27:10', `update_by` = 'admin', `update_time` = '2025-06-03 10:56:37', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3969;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '活动记录', `parent_id` = 2008, `order_num` = 10, `path` = 'activityRecord', `component` = 'NursingManagement/activityRecord/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'color', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-05-07 10:59:48', `update_by` = 'admin', `update_time` = '2025-06-03 10:56:34', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3968;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '护理记录', `parent_id` = 2008, `order_num` = 9, `path` = 'NursingRecords', `component` = 'NursingManagement/NursingRecords/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'care:careRecordInfo:list', `icon` = 'documentation', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-29 15:12:56', `update_by` = 'admin', `update_time` = '2025-06-03 10:56:30', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2013;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '服务工单', `parent_id` = 2008, `order_num` = 8, `path` = 'serviceTicketList', `component` = 'NursingManagement/serviceTicket/list', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'list', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2024-11-25 15:28:03', `update_by` = 'admin', `update_time` = '2025-06-03 10:56:27', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3865;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '护理计划', `parent_id` = 2008, `order_num` = 7, `path` = 'nursingCarePlanList', `component` = 'NursingManagement/nursingCarePlan/list', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'date', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2024-11-22 14:20:56', `update_by` = 'admin', `update_time` = '2025-06-03 10:56:24', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3858;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '护理类型', `parent_id` = 2008, `order_num` = 3, `path` = 'typeOfService', `component` = 'NursingManagement/typeOfService/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'cascader', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-04-28 20:55:33', `update_by` = 'admin', `update_time` = '2025-06-03 10:55:23', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3966;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '增值服务', `parent_id` = 2008, `order_num` = 6, `path` = 'valueAddedServices', `component` = 'NursingManagement/valueAddedServices/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'link', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-04-28 20:06:26', `update_by` = 'admin', `update_time` = '2025-06-03 10:51:02', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3965;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '用户选购记录', `parent_id` = 2008, `order_num` = 5, `path` = 'userChooseRecord', `component` = 'NursingManagement/userChooseRecord/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'documentation', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-04-28 19:30:55', `update_by` = 'admin', `update_time` = '2025-06-03 10:50:54', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3964;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '选购服务', `parent_id` = 2008, `order_num` = 4, `path` = 'purchaseOfServicesList', `component` = 'NursingManagement/purchaseOfServices/list', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'edit', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2024-11-25 15:16:25', `update_by` = 'admin', `update_time` = '2025-06-03 10:50:45', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3864;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '编辑咨询表单', `parent_id` = 3840, `order_num` = 1, `path` = 'searchRecordEditForm', `component` = 'marketingManagement/searchRecord/editForm', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '1', `status` = '0', `perms` = '', `icon` = 'download', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-05-26 08:55:42', `update_by` = 'admin', `update_time` = '2025-05-26 08:56:23', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3978;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '新增咨询表单', `parent_id` = 3840, `order_num` = 1, `path` = 'searchRecordForm', `component` = 'marketingManagement/searchRecord/form', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '1', `status` = '0', `perms` = '', `icon` = '#', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2024-11-11 17:20:26', `update_by` = 'admin', `update_time` = '2025-05-26 08:52:21', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3842;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '首页', `parent_id` = -100, `order_num` = 1, `path` = 'index', `component` = '/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'rate', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-05-23 15:56:03', `update_by` = 'admin', `update_time` = '2025-05-23 15:58:54', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3977;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '版本管理', `parent_id` = -100, `order_num` = 16, `path` = 'customInfo', `component` = 'custom/info/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'custom:info:list', `icon` = 'documentation', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-05-17 09:58:57', `update_by` = 'admin', `update_time` = '2025-05-23 15:58:28', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3972;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '护理设备管理', `parent_id` = -100, `order_num` = 15, `path` = 'deviceManage', `component` = NULL, `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'client', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-03-28 18:20:17', `update_by` = 'admin', `update_time` = '2025-05-23 15:58:22', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3943;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '模板管理', `parent_id` = -100, `order_num` = 14, `path` = 'templateAdministration', `component` = NULL, `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'tree-table', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-03-11 13:50:23', `update_by` = 'admin', `update_time` = '2025-05-23 15:58:15', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3919;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '评估管理', `parent_id` = -100, `order_num` = 13, `path` = '/assessAdministration', `component` = NULL, `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'link', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-03-10 13:58:10', `update_by` = 'admin', `update_time` = '2025-05-23 15:58:09', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3914;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '员工管理', `parent_id` = -100, `order_num` = 12, `path` = 'personnelManage', `component` = NULL, `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'user', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2024-11-25 16:46:35', `update_by` = 'admin', `update_time` = '2025-05-23 15:57:58', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3866;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '膳食管理', `parent_id` = -100, `order_num` = 11, `path` = 'dietManage', `component` = NULL, `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'daohanglantubiao_shanshi', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-04-17 21:54:45', `update_by` = 'admin', `update_time` = '2025-05-23 15:57:52', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2036;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '医护管理', `parent_id` = -100, `order_num` = 10, `path` = 'medicalCareManage', `component` = NULL, `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'documentation', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2025-01-15 17:17:02', `update_by` = 'admin', `update_time` = '2025-05-23 15:57:43', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3884;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '护工管理', `parent_id` = -100, `order_num` = 9, `path` = 'carerAdministration', `component` = NULL, `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'yuangongguanli', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-30 09:50:42', `update_by` = 'admin', `update_time` = '2025-05-23 15:57:34', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2016;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '费用管理', `parent_id` = -100, `order_num` = 8, `path` = 'costManage', `component` = NULL, `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'qitafeiyong', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-29 15:08:35', `update_by` = 'admin', `update_time` = '2025-05-23 15:57:27', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2012;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '护理管理', `parent_id` = -100, `order_num` = 7, `path` = 'NursingManagement', `component` = NULL, `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = '呵护', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-29 10:28:32', `update_by` = 'admin', `update_time` = '2025-05-23 15:57:19', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2008;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '健康管理', `parent_id` = -100, `order_num` = 6, `path` = 'healthManage', `component` = NULL, `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'ziliaoku', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-04-01 09:25:20', `update_by` = 'admin', `update_time` = '2025-05-23 15:57:05', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2025;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '床位信息', `parent_id` = -100, `order_num` = 5, `path` = 'bedInfo', `component` = NULL, `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'chuangwei', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-25 09:40:58', `update_by` = 'admin', `update_time` = '2025-05-23 15:56:58', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2001;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '居住管理', `parent_id` = -100, `order_num` = 4, `path` = 'liveManage', `component` = NULL, `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'chanpinguanli', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-25 09:47:27', `update_by` = 'admin', `update_time` = '2025-05-23 15:56:52', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2003;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '长者管理', `parent_id` = -100, `order_num` = 3, `path` = 'elderlyManage', `component` = '', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'renshiguanli-zhaopinguanli', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2022-03-25 09:32:51', `update_by` = 'admin', `update_time` = '2025-05-23 15:56:17', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 2000;
UPDATE `ry-cloud`.`sys_menu` SET `menu_name` = '接待管理', `parent_id` = -100, `order_num` = 2, `path` = '/marketingManagement', `component` = NULL, `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'client', `applet_icon` = NULL, `create_by` = 'admin', `create_time` = '2024-11-11 16:44:54', `update_by` = 'admin', `update_time` = '2025-05-23 15:56:13', `remark` = '', `platform` = '100', `applet_path` = NULL WHERE `menu_id` = 3840;

INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1623, 6, '账户充值', '6', 'custom_consume_account_type', NULL, 'default', 'N', '0', 'admin', '2025-06-05 18:11:49', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1622, 5, '缴费支出', '5', 'custom_consume_account_type', NULL, 'default', 'N', '0', 'admin', '2025-06-05 18:11:42', '', NULL, NULL);
INSERT INTO `ry-cloud`.`sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1621, 4, '溢缴收入', '4', 'custom_consume_account_type', NULL, 'default', 'N', '0', 'admin', '2025-06-05 18:11:34', '', NULL, NULL);

UPDATE `ry-cloud`.sys_dict_data SET dict_label = '离院退费' WHERE dict_type = 'custom_consume_account_type' AND dict_value = '1';
UPDATE `ry-cloud`.sys_dict_data SET dict_label = '变更退费' WHERE dict_type = 'custom_consume_account_type' AND dict_value = '2';
UPDATE `ry-cloud`.sys_dict_data SET dict_label = '变更支出' WHERE dict_type = 'custom_consume_account_type' AND dict_value = '3';
