ALTER TABLE `ry-custom`.`t_payment_record`
    DROP COLUMN `paid_cost`,
    DROP COLUMN `payment_method`,
    ADD COLUMN `paid_details` json NULL COMMENT '实缴详情（List<PaidDetail> JSON）' AFTER `total_cost`;

ALTER TABLE `ry-custom`.`t_payment_change_record` COMMENT = '缴费变更单';

ALTER TABLE `ry-custom`.`t_version_info`
    MODIFY COLUMN `version_number` varchar(255) NULL DEFAULT NULL COMMENT '版本号' AFTER `id`;

ALTER TABLE `ry-custom`.`t_payment_change_record`
    DROP COLUMN `change_date`;
