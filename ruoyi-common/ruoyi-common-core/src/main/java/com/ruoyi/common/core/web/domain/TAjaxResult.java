package com.ruoyi.common.core.web.domain;

import com.ruoyi.common.core.constant.HttpStatus;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: XaResult.java
 * @Package com.web.hhrz.base.kuadi100
 * @Description: Service返回结果统一对象
 * @date 2014年8月13日 下午7:46:23
 * @see
 * @see
 */
@SuppressWarnings("unchecked")
public class TAjaxResult<T> implements Serializable {

    @ApiModelProperty(value = "code : 返回代码，200表示OK，其它的都有对应问题")
    private int code = HttpStatus.SUCCESS;

    @ApiModelProperty(value = "msg : 如果code!=200,错误信息")
    private String msg = "";

/*    @ApiModelProperty(value = "如果code!=200,message的补充信息")
    private Object exception;*/

    @ApiModelProperty(value = "code为200时返回结果集")
    private T data = (T) new Object();

    public TAjaxResult(String errorMsg) {
        this.msg = errorMsg;
        this.code = 500;
        this.data = (T) new Object();
    }

    public TAjaxResult(String errorMsg, int code) {
        this.msg = errorMsg;
        this.code = code;
        this.data = (T) new Object();
    }

    public TAjaxResult(T object) {
        this.data = object;
    }

    public TAjaxResult() {
        this.data = (T) new Object();
    }

/*
    public Object getException() {
        return exception;
    }

    public void setException(Object exception) {
        this.exception = exception;
    }
*/

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public TAjaxResult<T> setData(T data) {
        this.data = data;
        return this;
    }

    public TAjaxResult<T> success(T data) {
        this.code = 200;
        this.msg = "sucess";
        this.data = data;
        return this;
    }

    public TAjaxResult<T> error() {
        this.code = 500;
        this.msg = "未知异常，请联系管理员";
        this.data = (T) new Object();
        return this;
    }

    public TAjaxResult<T> error(String message) {
        this.code = 500;
        this.msg = message;
        this.data = null;
        return this;
    }

    public TAjaxResult<T> error(String message, T t) {
        this.code = 500;
        this.msg = message;
        this.data = t;
        return this;
    }

    public TAjaxResult<T> error(String message, Boolean isArray) {
        this.code = 500;
        this.msg = message;
        this.data = isArray ? (T) new ArrayList<>() : (T) new Object();
        return this;
    }
}
