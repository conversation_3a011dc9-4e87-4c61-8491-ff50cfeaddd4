package com.ruoyi.common.core.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class Page {

    @ApiModelProperty(value = "页码", name = "pageNum", example = "1")
    private String pageNum;
    @ApiModelProperty(value = "条数", name = "pageSize", example = "10")
    private String pageSize;
    @ApiModelProperty(value = "排序", name = "orderBy", example = "")
    private String orderBy;

}
