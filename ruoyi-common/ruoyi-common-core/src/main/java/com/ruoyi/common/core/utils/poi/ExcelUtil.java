package com.ruoyi.common.core.utils.poi;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.annotation.Excel.ColumnType;
import com.ruoyi.common.core.annotation.Excel.Type;
import com.ruoyi.common.core.annotation.Excels;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.file.FileTypeUtils;
import com.ruoyi.common.core.utils.file.ImageUtils;
import com.ruoyi.common.core.utils.reflect.ReflectUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel相关处理
 *
 * <AUTHOR>
 */
public class ExcelUtil<T> {
    public static final String[] FORMULA_STR = {"=", "-", "+", "@"};
    /**
     * Excel sheet最大行数，默认65536
     */
    public static final int sheetSize = 65536;
    private static final Logger log = LoggerFactory.getLogger(ExcelUtil.class);
    /**
     * 数字格式
     */
    private static final DecimalFormat DOUBLE_FORMAT = new DecimalFormat("######0.00");
    /**
     * 实体对象
     */
    public Class<T> clazz;
    /**
     * 工作表名称
     */
    private String sheetName;
    /**
     * 导出类型（EXPORT:导出数据；IMPORT：导入模板）
     */
    private Type type;
    /**
     * 工作薄对象
     */
    private Workbook wb;
    /**
     * 工作表对象
     */
    private Sheet sheet;
    /**
     * 样式列表
     */
    private Map<String, CellStyle> styles;
    /**
     * 导入导出数据列表
     */
    private List<T> list;
    /**
     * 注解列表
     */
    private List<Object[]> fields;
    /**
     * 当前行号
     */
    private int rownum;
    /**
     * 标题
     */
    private String title;
    /**
     * 最大高度
     */
    private short maxHeight;
    /**
     * 统计列表
     */
    private Map<Integer, Double> statistics = new HashMap<Integer, Double>();

    public ExcelUtil(Class<T> clazz) {
        this.clazz = clazz;
    }

    /**
     * 获取画布
     */
    public static Drawing<?> getDrawingPatriarch(Sheet sheet) {
        if (sheet.getDrawingPatriarch() == null) {
            sheet.createDrawingPatriarch();
        }
        return sheet.getDrawingPatriarch();
    }

    /**
     * 解析导出值 0=男,1=女,2=未知
     *
     * @param propertyValue 参数值
     * @param converterExp  翻译注解
     * @param separator     分隔符
     * @return 解析后值
     */
    public static String convertByExp(String propertyValue, String converterExp, String separator) {
        StringBuilder propertyString = new StringBuilder();
        String[] convertSource = converterExp.split(",");
        for (String item : convertSource) {
            String[] itemArray = item.split("=");
            if (StringUtils.containsAny(separator, propertyValue)) {
                for (String value : propertyValue.split(separator)) {
                    if (itemArray[0].equals(value)) {
                        propertyString.append(itemArray[1] + separator);
                        break;
                    }
                }
            } else {
                if (itemArray[0].equals(propertyValue)) {
                    return itemArray[1];
                }
            }
        }
        return StringUtils.stripEnd(propertyString.toString(), separator);
    }

    /**
     * 反向解析值 男=0,女=1,未知=2
     *
     * @param propertyValue 参数值
     * @param converterExp  翻译注解
     * @param separator     分隔符
     * @return 解析后值
     */
    public static String reverseByExp(String propertyValue, String converterExp, String separator) {
        StringBuilder propertyString = new StringBuilder();
        String[] convertSource = converterExp.split(",");
        for (String item : convertSource) {
            String[] itemArray = item.split("=");
            if (StringUtils.containsAny(separator, propertyValue)) {
                for (String value : propertyValue.split(separator)) {
                    if (itemArray[1].equals(value)) {
                        propertyString.append(itemArray[0] + separator);
                        break;
                    }
                }
            } else {
                if (itemArray[1].equals(propertyValue)) {
                    return itemArray[0];
                }
            }
        }
        return StringUtils.stripEnd(propertyString.toString(), separator);
    }

    public void init(List<T> list, String sheetName, String title, Type type) {
        if (list == null) {
            list = new ArrayList<T>();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.type = type;
        this.title = title;
        createExcelField();
        createWorkbook();
        createTitle();
    }

    /**
     * 创建excel第一行标题
     */
    public void createTitle() {
        if (StringUtils.isNotEmpty(title)) {
            Row titleRow = sheet.createRow(rownum == 0 ? rownum++ : 0);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellStyle(styles.get("title"));
            titleCell.setCellValue(title);
            sheet.addMergedRegion(new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), titleRow.getRowNum(),
                    this.fields.size() - 1));
        }
    }

    /**
     * 对excel表单默认第一个索引名转换成list
     *
     * @param is 输入流
     * @return 转换后集合
     */
    public List<T> importExcel(InputStream is) throws Exception {
        return importExcel(is, 0);
    }

    /**
     * 对excel表单默认第一个索引名转换成list
     *
     * @param is       输入流
     * @param titleNum 标题占用行数
     * @return 转换后集合
     */
    public List<T> importExcel(InputStream is, int titleNum) throws Exception {
        return importExcel(StringUtils.EMPTY, is, titleNum);
    }

    /**
     * 对excel表单指定表格索引名转换成list
     *
     * @param sheetName 表格索引名
     * @param titleNum  标题占用行数
     * @param is        输入流
     * @return 转换后集合
     */
    public List<T> importExcel(String sheetName, InputStream is, int titleNum) throws Exception {
        this.type = Type.IMPORT;
        this.wb = WorkbookFactory.create(is);
        List<T> list = new ArrayList<T>();
        // 如果指定sheet名,则取指定sheet中的内容 否则默认指向第1个sheet
        Sheet sheet = StringUtils.isNotEmpty(sheetName) ? wb.getSheet(sheetName) : wb.getSheetAt(0);
        if (sheet == null) {
            throw new IOException("文件sheet不存在");
        }

        // 获取最后一个非空行的行下标，比如总行数为n，则返回的为n-1
        int rows = sheet.getLastRowNum();

        if (rows > 0) {
            // 定义一个map用于存放excel列的序号和field.
            Map<String, Integer> cellMap = new HashMap<String, Integer>();
            // 获取表头
            Row heard = sheet.getRow(titleNum);
            // 校验表头不能为空
            if(heard == null){
                throw new IOException("Excel模板错误，表头行不存在，请检查标题行数设置是否正确！");
            }
            for (int i = 0; i < heard.getPhysicalNumberOfCells(); i++) {
                Cell cell = heard.getCell(i);
                if (StringUtils.isNotNull(cell)) {
                    String value = this.getCellValue(heard, i).toString().trim(); // trim 表头
                    if(StringUtils.isNotEmpty(value)){ // 避免空的表头单元格干扰
                        cellMap.put(value, i);
                    }
                }
                // else { // 不需要记录 null key
                //    cellMap.put(null, i);
                // }
            }
            // 有数据时才处理 得到类的所有field对应的 @Excel 配置.
            List<Object[]> fields = this.getFields(); // 获取配置了 @Excel 注解的字段信息
            Map<Integer, Object[]> fieldsMap = new HashMap<Integer, Object[]>(); // Map<Excel列索引, [Field, Excel注解]>
            for (Object[] objects : fields) {
                Excel attr = (Excel) objects[1];
                // 根据 @Excel 的 name 属性查找对应的列索引
                Integer column = cellMap.get(attr.name());
                // 如果找到了对应的列，并且该注解适用于导入 (IMPORT 或 ALL)
                if (column != null && (attr.type() == Type.ALL || attr.type() == Type.IMPORT)) {
                    fieldsMap.put(column, objects);
                } else if (column == null) {
                    log.warn("Excel 表头中未找到与实体字段注解 name='{}' 匹配的列。", attr.name());
                    // 根据业务需求，如果某列必须存在，可以在这里抛出异常
                    // throw new IOException("Excel模板错误：缺少必需的列 '" + attr.name() + "'");
                }
            }

            // 校验是否找到了任何需要导入的列
            if (fieldsMap.isEmpty()) {
                log.error("未找到任何与实体字段注解匹配的Excel表头列，请检查注解 name 属性是否与表头一致。");
                throw new IOException("Excel模板错误或实体注解配置错误，无法匹配任何导入列。");
            }


            for (int i = titleNum + 1; i <= rows; i++) {
                // 从数据行开始取数据.
                Row row = sheet.getRow(i);
                // 判断当前行是否是空行
                if (isRowEmpty(row)) {
                    continue;
                }
                T entity = null; // 在处理每行第一个有效单元格时创建实例

                // 遍历映射好的 Excel 列索引和字段配置
                for (Map.Entry<Integer, Object[]> entry : fieldsMap.entrySet()) {
                    Integer columnIndex = entry.getKey();
                    Object val = this.getCellValue(row, columnIndex); // 获取原始单元格值

                    // 如果 entity 尚未创建 (即这是本行第一个非空单元格对应的字段)
                    // 注意：这种惰性创建可能导致空行（所有映射列都为空）也被创建为空对象加入列表，
                    // 如果不希望这样，需要在循环外判断 entity 是否最终为 null。
                    // 但通常 isRowEmpty 已经处理了完全空行。
                    if (entity == null) {
                        entity = clazz.newInstance();
                    }

                    Field field = (Field) entry.getValue()[0]; // 对应的 Java 字段
                    Excel attr = (Excel) entry.getValue()[1];  // 对应的 @Excel 注解实例

                    boolean handlerHasSetField = false; // 标记字段值是否已被 Handler 处理并设置
                    Object valueToSet = val; // 准备用于最终设置到字段的值，默认为原始单元格值

                    // 1. 检查是否指定了自定义 Handler 且不是默认的 Handler
                    if (!attr.handler().equals(DefaultExcelHandlerAdapter.class)) {
                        try {
                            ExcelHandlerAdapter adapter = null;
                            // 优先尝试 SpringUtils 获取 Bean (如果项目是 Spring 应用)
                            try {
                                adapter = SpringUtils.getBean(attr.handler());
                            } catch (Exception springEx) {
                                log.warn("无法从 Spring Context 获取 Bean: {}, 尝试 newInstance(). 错误: {}", attr.handler().getSimpleName(), springEx.getMessage());
                            }

                            // 如果无法从 Spring 获取，则尝试 newInstance()
                            if(adapter == null) {
                                adapter = attr.handler().newInstance();
                            }

                            // *** 调用修改后的 parse 方法，传入 entity ***
                            // Handler 负责解析、转换，并可能直接设置 entity 的字段
                            Object handlerResult = adapter.parse(val, attr.args(), entity);

                            // 如果 handler 返回 null，通常表示它已经完成了所有必要的设置
                            // 如果它返回非 null，则表示 ExcelUtil 应该使用这个返回的值来设置注解所在的字段
                            if (handlerResult == null) {
                                handlerHasSetField = true; // Handler 已处理，ExcelUtil 不再设置此字段
                            } else {
                                valueToSet = handlerResult; // 使用 Handler 返回的值
                                // handlerHasSetField 仍然为 false，让后续逻辑处理设置
                            }

                        } catch (Exception e) { // 捕获 Handler 内部可能抛出的业务异常 (如用户找不到)或实例化异常
                            log.error("执行自定义导入处理器 {} 时出错 (行号: {}, 列: {}, 字段: {}): {}",
                                    attr.handler().getSimpleName(), i + 1, columnIndex + 1, field.getName(), e.getMessage(), e);
                            // 将 Handler 的异常包装后重新抛出，中断导入过程
                            throw new RuntimeException("处理第 " + (i + 1) + " 行，列 '" + attr.name() + "' 的值 [" + val + "] 时发生错误: " + e.getMessage(), e);
                        }
                    }

                    // 仅当 Handler 没有处理该字段时，才执行后续的转换和设置逻辑
                    if (!handlerHasSetField) {
                        // 2. 如果没有使用自定义 Handler (或 Handler 返回了非 null 值让 ExcelUtil 设置)，
                        //    再检查并应用 readConverterExp
                        if (StringUtils.isNotEmpty(attr.readConverterExp())) {
                            valueToSet = reverseByExp(Convert.toStr(valueToSet), attr.readConverterExp(), attr.separator());
                        }

                        // 3. 类型转换和设置值
                        Class<?> fieldType = field.getType();
                        try {
                            // 使用 Convert 工具类进行类型转换，它能处理多种情况
                            Object convertedValue = Convert.convert(fieldType, valueToSet);

                            // 特殊处理：如果目标是 String，且原始值是数字带有 .0 后缀，则移除 .0
                            if (String.class == fieldType && valueToSet instanceof Number && Convert.toStr(valueToSet).endsWith(".0")) {
                                convertedValue = StringUtils.substringBefore(Convert.toStr(valueToSet), ".0");
                            }

                            // 4. 设置属性值
                            String propertyName = field.getName();
                            // 处理 targetAttr，支持嵌套设置，但通常不与复杂 handler 一起使用
                            if (StringUtils.isNotEmpty(attr.targetAttr())) {
                                propertyName = field.getName() + "." + attr.targetAttr();
                                // 使用 ReflectUtils 设置嵌套属性可能需要特殊处理
                                // 这里假设 ReflectUtils.invokeSetter 支持点状路径，如果不支持，需要修改
                            }
                            ReflectUtils.invokeSetter(entity, propertyName, convertedValue);

                        } catch (Exception conversionOrSetterEx) {
                            log.error("设置字段 '{}' (类型: {}) 的值 '{}' (来自Excel: '{}') 时出错 (行号: {}, 列: {}): {}",
                                    field.getName(), fieldType.getSimpleName(), valueToSet, val, i + 1, columnIndex + 1, conversionOrSetterEx.getMessage(), conversionOrSetterEx);
                            throw new RuntimeException("处理第 " + (i + 1) + " 行，列 '" + attr.name() + "' 时发生类型转换或设置错误: " + conversionOrSetterEx.getMessage(), conversionOrSetterEx);
                        }
                    }
                    // else: handlerHasSetField is true, do nothing more for this field
                }
                // 将处理完一行数据的实体添加到列表中 (只有当 entity 被创建时才添加)
                if (entity != null) {
                    list.add(entity);
                }
            }
        }
        return list;
    }


    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param response  返回数据
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @return 结果
     * @throws IOException
     */
    public void exportExcel(HttpServletResponse response, List<T> list, String sheetName) {
        exportExcel(response, list, sheetName, StringUtils.EMPTY);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param response  返回数据
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @param title     标题
     * @return 结果
     * @throws IOException
     */
    public void exportExcel(HttpServletResponse response, List<T> list, String sheetName, String title) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.init(list, sheetName, title, Type.EXPORT);
        exportExcel(response);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public void importTemplateExcel(HttpServletResponse response, String sheetName) {
        importTemplateExcel(response, sheetName, StringUtils.EMPTY);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param sheetName 工作表的名称
     * @param title     标题
     * @return 结果
     */
    public void importTemplateExcel(HttpServletResponse response, String sheetName, String title) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.init(null, sheetName, title, Type.IMPORT);
        exportExcel(response);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @return 结果
     */
    public void exportExcel(HttpServletResponse response) {
        try {
            writeSheet();
            wb.write(response.getOutputStream());
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
        }
    }

    /**
     * 创建写入数据到Sheet
     */
    /**
     * 创建写入数据到Sheet
     */
    public void writeSheet() {
        // 取出一共有多少个sheet.
        int sheetNo = Math.max(1, (int) Math.ceil(list.size() * 1.0 / sheetSize));
        for (int index = 0; index < sheetNo; index++) {
            createSheet(sheetNo, index);

            // 产生一行 (表头行)
            Row row = sheet.createRow(rownum);
            int column = 0;
            // 写入各个字段的列头名称
            for (Object[] os : fields) {
                Excel excel = (Excel) os[1];
                // 1. 创建表头单元格 (使用 header 样式，已经是文本格式)
                this.createCell(excel, row, column);

                // 2. ***** 新增：为该列设置默认样式为文本 *****
                //    这样用户在表头下方输入时，单元格默认为文本格式
                //    选用一个基础的文本样式，例如 "data" 样式
                if (styles.containsKey("data")) { // 确保样式存在
                    sheet.setDefaultColumnStyle(column, styles.get("data"));
                }
                // ******************************************

                column++; // 移动到下一列
            }

            // 只有在 EXPORT 模式下才填充数据和统计
            if (Type.EXPORT.equals(type)) {
                fillExcelData(index, row);
                addStatisticsRow();
            }
            // 在 IMPORT (模板) 模式下，不执行 fillExcelData 和 addStatisticsRow
        }
    }

    /**
     * 填充excel数据
     *
     * @param index 序号
     * @param row   单元格行
     */
    public void fillExcelData(int index, Row row) {
        int startNo = index * sheetSize;
        int endNo = Math.min(startNo + sheetSize, list.size());
        for (int i = startNo; i < endNo; i++) {
            row = sheet.createRow(i + 1 + rownum - startNo);
            // 得到导出对象.
            T vo = (T) list.get(i);
            int column = 0;
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                this.addCell(excel, row, vo, field, column++);
            }
        }
    }

    /**
     * 创建表格样式
     *
     * @param wb 工作薄对象
     * @return 样式列表
     */
    private Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<>();

        // 标题样式
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setFontName("Arial");
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setBold(true);
        style.setFont(titleFont);
        styles.put("title", style);

        // 数据样式（默认文本格式）
        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setDataFormat(wb.createDataFormat().getFormat("@")); // 设置为文本格式
        Font dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        styles.put("data", style);

        // 表头样式
        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font headerFont = wb.createFont();
        headerFont.setFontName("Arial");
        headerFont.setFontHeightInPoints((short) 10);
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(headerFont);
        styles.put("header", style);

        // 合计样式
        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setDataFormat(wb.createDataFormat().getFormat("@")); // 合计也使用文本格式
        Font totalFont = wb.createFont();
        totalFont.setFontName("Arial");
        totalFont.setFontHeightInPoints((short) 10);
        style.setFont(totalFont);
        styles.put("total", style);

        // 其他对齐方式的数据样式（全部设置为文本格式）
        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setDataFormat(wb.createDataFormat().getFormat("@"));
        styles.put("data1", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setDataFormat(wb.createDataFormat().getFormat("@"));
        styles.put("data2", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.RIGHT);
        style.setDataFormat(wb.createDataFormat().getFormat("@"));
        styles.put("data3", style);

        // 自动换行样式
        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setWrapText(true);
        styles.put("dataWrap", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data1"));
        style.setWrapText(true);
        styles.put("data1Wrap", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data2"));
        style.setWrapText(true);
        styles.put("data2Wrap", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data3"));
        style.setWrapText(true);
        styles.put("data3Wrap", style);

        return styles;
    }

    /**
     * 创建单元格
     */
    public Cell createCell(Excel attr, Row row, int column) {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        cell.setCellValue(attr.name());
        setDataValidation(attr, row, column);
        cell.setCellStyle(styles.get("header"));
        return cell;
    }

    /**
     * 设置单元格信息
     *
     * @param value 单元格值
     * @param attr  注解相关
     * @param cell  单元格信息
     */
    public void setCellVo(Object value, Excel attr, Cell cell) {
        // 统一将所有值作为文本处理
        String cellValue = Convert.toStr(value);
        if (StringUtils.isNull(cellValue)) {
            cellValue = attr.defaultValue();
        }
        // 对于任何以表达式触发字符 =-+@ 开头的单元格，添加制表符前缀防止 CSV 注入
        if (StringUtils.containsAny(cellValue, FORMULA_STR)) {
            cellValue = StringUtils.replaceEach(cellValue, FORMULA_STR, new String[]{"\t=", "\t-", "\t+", "\t@"});
        }
        cell.setCellValue(cellValue + attr.suffix());
        // 强制设置为文本格式
        cell.getCellStyle().setDataFormat(cell.getSheet().getWorkbook().createDataFormat().getFormat("@"));
    }

    /**
     * 获取图片类型,设置图片插入类型
     */
    public int getImageType(byte[] value) {
        String type = FileTypeUtils.getFileExtendName(value);
        if ("JPG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_JPEG;
        } else if ("PNG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_PNG;
        }
        return Workbook.PICTURE_TYPE_JPEG;
    }

    /**
     * 创建表格样式
     */
    public void setDataValidation(Excel attr, Row row, int column) {
        if (attr.name().indexOf("注：") >= 0) {
            sheet.setColumnWidth(column, 6000);
        } else {
            // 设置列宽
            sheet.setColumnWidth(column, (int) ((attr.width() + 0.72) * 256));
        }
        // 如果设置了提示信息则鼠标放上去提示.
        if (StringUtils.isNotEmpty(attr.prompt())) {
            // 这里默认设了2-101列提示.
            setXSSFPrompt(sheet, "", attr.prompt(), 1, 100, column, column);
        }
        // 如果设置了combo属性则本列只能选择不能输入
        if (attr.combo().length > 0) {
            // 这里默认设了2-101列只能选择不能输入.
            setXSSFValidation(sheet, attr.combo(), 1, 100, column, column);
        }
    }

    /**
     * 添加单元格
     */
    public Cell addCell(Excel attr, Row row, T vo, Field field, int column) {
        Cell cell = null;
        try {
            row.setHeight(maxHeight);
            if (attr.isExport()) {
                cell = row.createCell(column);
                
                // 获取单元格值
                Object value = getTargetValue(vo, field, attr);
                String dateFormat = attr.dateFormat();
                String readConverterExp = attr.readConverterExp();
                String separator = attr.separator();

                String cellValue;
                if (StringUtils.isNotEmpty(dateFormat) && StringUtils.isNotNull(value)) {
                    cellValue = parseDateToStr(dateFormat, value);
                } else if (StringUtils.isNotEmpty(readConverterExp) && StringUtils.isNotNull(value)) {
                    cellValue = convertByExp(Convert.toStr(value), readConverterExp, separator);
                } else if (value instanceof BigDecimal && -1 != attr.scale()) {
                    cellValue = (((BigDecimal) value).setScale(attr.scale(), attr.roundingMode())).toString();
                } else if (!attr.handler().equals(ExcelHandlerAdapter.class)) {
                    cellValue = dataFormatHandlerAdapter(value, attr);
                } else {
                    cellValue = Convert.toStr(value);
                }

                // 统一写入文本值
                if (StringUtils.containsAny(cellValue, FORMULA_STR)) {
                    cellValue = StringUtils.replaceEach(cellValue, FORMULA_STR, new String[]{"\t=", "\t-", "\t+", "\t@"});
                }
                
                // 设置单元格值
                cell.setCellValue(StringUtils.isNull(cellValue) ? attr.defaultValue() : cellValue + attr.suffix());
                
                // 设置单元格样式
                int align = attr.align().value();
                String styleName = "data" + (align >= 1 && align <= 3 ? align : "") + (attr.isWrapText() ? "Wrap" : "");
                CellStyle style = styles.get(styleName);
                cell.setCellStyle(style);
                
                // 设置单元格格式为文本
                style.setDataFormat(cell.getSheet().getWorkbook().createDataFormat().getFormat("@"));
                
                addStatisticsData(column, cellValue, attr);
            }
        } catch (Exception e) {
            log.error("导出Excel失败{}", e);
        }
        return cell;
    }

    /**
     * 设置 POI XSSFSheet 单元格提示
     *
     * @param sheet         表单
     * @param promptTitle   提示标题
     * @param promptContent 提示内容
     * @param firstRow      开始行
     * @param endRow        结束行
     * @param firstCol      开始列
     * @param endCol        结束列
     */
    public void setXSSFPrompt(Sheet sheet, String promptTitle, String promptContent, int firstRow, int endRow,
                              int firstCol, int endCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createCustomConstraint("DD1");
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        dataValidation.createPromptBox(promptTitle, promptContent);
        dataValidation.setShowPromptBox(true);
        sheet.addValidationData(dataValidation);
    }

    /**
     * 设置某些列的值只能输入预制的数据,显示下拉框.
     *
     * @param sheet    要设置的sheet.
     * @param textlist 下拉框显示的内容
     * @param firstRow 开始行
     * @param endRow   结束行
     * @param firstCol 开始列
     * @param endCol   结束列
     * @return 设置好的sheet.
     */
    public void setXSSFValidation(Sheet sheet, String[] textlist, int firstRow, int endRow, int firstCol, int endCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // 加载下拉列表内容
        DataValidationConstraint constraint = helper.createExplicitListConstraint(textlist);
        // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        // 数据有效性对象
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        // 处理Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation) {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }

        sheet.addValidationData(dataValidation);
    }

    /**
     * 数据处理器
     *
     * @param value 数据值
     * @param excel 数据注解
     * @return
     */
    public String dataFormatHandlerAdapter(Object value, Excel excel) {
        try {
            Object instance = excel.handler().newInstance();
            Method formatMethod = excel.handler().getMethod("format", new Class[]{Object.class, String[].class});
            value = formatMethod.invoke(instance, value, excel.args());
        } catch (Exception e) {
            log.error("不能格式化数据 " + excel.handler(), e.getMessage());
        }
        return Convert.toStr(value);
    }

    /**
     * 合计统计信息
     */
    private void addStatisticsData(Integer index, String text, Excel entity) {
        if (entity != null && entity.isStatistics()) {
            Double temp = 0D;
            if (!statistics.containsKey(index)) {
                statistics.put(index, temp);
            }
            try {
                temp = Double.valueOf(text);
            } catch (NumberFormatException e) {
            }
            statistics.put(index, statistics.get(index) + temp);
        }
    }

    /**
     * 创建统计行
     */
    public void addStatisticsRow() {
        if (statistics.size() > 0) {
            Row row = sheet.createRow(sheet.getLastRowNum() + 1);
            Set<Integer> keys = statistics.keySet();
            Cell cell = row.createCell(0);
            cell.setCellStyle(styles.get("total"));
            cell.setCellValue("合计");

            for (Integer key : keys) {
                cell = row.createCell(key);
                cell.setCellStyle(styles.get("total"));
                String formattedValue = DOUBLE_FORMAT.format(statistics.get(key));
                cell.setCellValue(formattedValue); // 以字符串写入
                cell.getCellStyle().setDataFormat(cell.getSheet().getWorkbook().createDataFormat().getFormat("@"));
            }
            statistics.clear();
        }
    }

    /**
     * 获取bean中的属性值
     *
     * @param vo    实体对象
     * @param field 字段
     * @param excel 注解
     * @return 最终的属性值
     * @throws Exception
     */
    private Object getTargetValue(T vo, Field field, Excel excel) throws Exception {
        Object o = field.get(vo);
        if (StringUtils.isNotEmpty(excel.targetAttr())) {
            String target = excel.targetAttr();
            if (target.contains(".")) {
                String[] targets = target.split("[.]");
                for (String name : targets) {
                    o = getValue(o, name);
                }
            } else {
                o = getValue(o, target);
            }
        }
        return o;
    }

    /**
     * 以类的属性的get方法方法形式获取值
     *
     * @param o
     * @param name
     * @return value
     * @throws Exception
     */
    private Object getValue(Object o, String name) throws Exception {
        if (StringUtils.isNotNull(o) && StringUtils.isNotEmpty(name)) {
            Class<?> clazz = o.getClass();
            Field field = clazz.getDeclaredField(name);
            field.setAccessible(true);
            o = field.get(o);
        }
        return o;
    }

    /**
     * 得到所有定义字段
     */
    private void createExcelField() {
        this.fields = getFields();
        this.fields = this.fields.stream().sorted(Comparator.comparing(objects -> ((Excel) objects[1]).sort())).collect(Collectors.toList());
        this.maxHeight = getRowHeight();
    }

    /**
     * 获取字段注解信息
     */
    public List<Object[]> getFields() {
        List<Object[]> fields = new ArrayList<Object[]>();
        List<Field> tempFields = new ArrayList<>();
        tempFields.addAll(Arrays.asList(clazz.getSuperclass().getDeclaredFields()));
        tempFields.addAll(Arrays.asList(clazz.getDeclaredFields()));
        for (Field field : tempFields) {
            // 单注解
            if (field.isAnnotationPresent(Excel.class)) {
                Excel attr = field.getAnnotation(Excel.class);
                if (attr != null && (attr.type() == Type.ALL || attr.type() == type)) {
                    field.setAccessible(true);
                    fields.add(new Object[]{field, attr});
                }
            }

            // 多注解
            if (field.isAnnotationPresent(Excels.class)) {
                Excels attrs = field.getAnnotation(Excels.class);
                Excel[] excels = attrs.value();
                for (Excel attr : excels) {
                    if (attr != null && (attr.type() == Type.ALL || attr.type() == type)) {
                        field.setAccessible(true);
                        fields.add(new Object[]{field, attr});
                    }
                }
            }
        }
        return fields;
    }

    /**
     * 根据注解获取最大行高
     */
    public short getRowHeight() {
        double maxHeight = 0;
        for (Object[] os : this.fields) {
            Excel excel = (Excel) os[1];
            maxHeight = Math.max(maxHeight, excel.height());
        }
        return (short) (maxHeight * 20);
    }

    /**
     * 创建一个工作簿
     */
    public void createWorkbook() {
        this.wb = new SXSSFWorkbook(500);
        this.sheet = wb.createSheet();
        wb.setSheetName(0, sheetName);
        this.styles = createStyles(wb);
    }

    /**
     * 创建工作表
     *
     * @param sheetNo sheet数量
     * @param index   序号
     */
    public void createSheet(int sheetNo, int index) {
        // 设置工作表的名称.
        if (sheetNo > 1 && index > 0) {
            this.sheet = wb.createSheet();
            this.createTitle();
            wb.setSheetName(index, sheetName + index);
        }
    }

    /**
     * 获取单元格值
     *
     * @param row    获取的行
     * @param column 获取单元格列号
     * @return 单元格值
     */
    public Object getCellValue(Row row, int column) {
        if (row == null) {
            return row;
        }
        Object val = "";
        try {
            Cell cell = row.getCell(column);
            if (StringUtils.isNotNull(cell)) {
                if (cell.getCellType() == CellType.NUMERIC || cell.getCellType() == CellType.FORMULA) {
                    val = cell.getNumericCellValue();
                    if (DateUtil.isCellDateFormatted(cell)) {
                        val = DateUtil.getJavaDate((Double) val); // POI Excel 日期格式转换
                    } else {
                        if ((Double) val % 1 != 0) {
                            val = new BigDecimal(val.toString());
                        } else {
                            val = new DecimalFormat("0").format(val);
                        }
                    }
                } else if (cell.getCellType() == CellType.STRING) {
                    val = cell.getStringCellValue();
                } else if (cell.getCellType() == CellType.BOOLEAN) {
                    val = cell.getBooleanCellValue();
                } else if (cell.getCellType() == CellType.ERROR) {
                    val = cell.getErrorCellValue();
                }

            }
        } catch (Exception e) {
            return val;
        }
        return val;
    }

    /**
     * 判断是否是空行
     *
     * @param row 判断的行
     * @return
     */
    private boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }
        for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }

    /**
     * 格式化不同类型的日期对象
     *
     * @param dateFormat 日期格式
     * @param val        被格式化的日期对象
     * @return 格式化后的日期字符
     */
    public String parseDateToStr(String dateFormat, Object val) {
        if (val == null) {
            return "";
        }
        String str;
        if (val instanceof Date) {
            str = DateUtils.parseDateToStr(dateFormat, (Date) val);
        } else if (val instanceof LocalDateTime) {
            str = DateUtils.parseDateToStr(dateFormat, DateUtils.toDate((LocalDateTime) val));
        } else if (val instanceof LocalDate) {
            str = DateUtils.parseDateToStr(dateFormat, DateUtils.toDate((LocalDate) val));
        } else {
            str = val.toString();
        }
        return str;
    }
}
