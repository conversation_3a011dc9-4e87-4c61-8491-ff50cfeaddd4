autoDetectedPackages:
- com.ibms.service.assets
- com.ibms.service.realty
- com.ibms.service.security
- com.ruoyi.auth
- com.ruoyi.common.core
- com.ruoyi.common.datascope
- com.ruoyi.common.datasource
- com.ruoyi.common.log
- com.ruoyi.common.mybatisplus
- com.ruoyi.common.redis
- com.ruoyi.common.security
- com.ruoyi.common.swagger
- com.ruoyi.custom
- com.ruoyi.daycare
- com.ruoyi.file
- com.ruoyi.gateway
- com.ruoyi.gen
- com.ruoyi.gw
- com.ruoyi.homecare
- com.ruoyi.job
- com.ruoyi.modules.monitor
- com.ruoyi.system
enableAutoDetect: true
entryDisplayConfig:
  excludedPathPatterns: []
  skipJsCss: true
funcDisplayConfig:
  skipConstructors: false
  skipFieldAccess: true
  skipFieldChange: true
  skipGetters: false
  skipNonProjectPackages: false
  skipPrivateMethods: false
  skipSetters: false
ignoreSameClassCall: null
ignoreSamePackageCall: null
includedPackagePrefixes: null
includedParentClasses: null
name: xcodemap-filter
recordMode: all
sourceDisplayConfig:
  color: blue
startOnDebug: false
