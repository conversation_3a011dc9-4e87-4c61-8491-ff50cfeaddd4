<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.security.mapper.lightinginfo.LightingInfoMapper">

    <resultMap id="BaseResultMap" type="com.ibms.service.security.domain.LightingInfo" >
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="floor_id" property="floorId" />
        <result column="venue_id" property="venueId" />
        <result column="device_name" property="deviceName" />
        <result column="device_id" property="deviceId" />
        <result column="slave_id" property="slaveId" />
        <result column="status" property="status" />
        <result column="value" property="value" />
        <result column="coefficient" property="coefficient" />
        <result column="defaults" property="defaults" />
        <result column="description" property="description" />
        <result column="remarks" property="remarks" />
        <result column="del_flag" property="delFlag" />
        <result column="property2" property="property2" />
        <result column="property1" property="property1" />
        <result column="y_position" property="yPosition" />
        <result column="x_position" property="xPosition" />
        <result column="type" property="type" />
        <result column="offset" property="offset" />
        <result column="ip" property="ip" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                name,
                floor_id,
                venue_id,
                device_name,
                device_id,
                slave_id,
                status,
                value,
                coefficient,
                defaults,
                description,
                remarks,
                del_flag,
                property2,
                property1,
                y_position,
                x_position,
                type,
                offset,
                ip,
                create_by,
                update_by,
                create_time,
                update_time
    </sql>
    <update id="operateAllLighting" parameterType="com.ibms.service.security.controller.lightinginfo.param.OperateAllLightingParam">
        update t_lighting_info set value = #{value}
    </update>
    <select id="getLightNumByType"
            resultType="com.ibms.service.security.controller.lightinginfo.vo.LightNumTypeVo">

        SELECT
			t.name,
			IFNULL( a.tdnum, 0 ) tdNum,
			ifnull( a.dmnum, 0 ) dmNum,
			ifnull( a.ddnum, 0 ) ddNum,
			ifnull( a.pbdnum, 0 ) pbdNum,
			ifnull( a.ctdnum, 0 ) ctdNum
		FROM
			t_venue_info t
			LEFT JOIN (
			SELECT
				venue_id,
				SUM(
				IF
				( type = 1, 1, 0 )) tdnum,
				SUM(
				IF
				( type = 2, 1, 0 )) dmnum,
				SUM(
				IF
				( type = 3, 1, 0 )) ddnum,
				SUM(
				IF
				( type = 4, 1, 0 )) pbdnum,
				SUM(
				IF
				( type = 5, 1, 0 )) ctdnum
			FROM
				t_lighting_info
			GROUP BY
				venue_id
			) a ON t.id = a.venue_id
		WHERE
			t.parent_id = '0'
			ORDER BY t.sort

    </select>
    <select id="getLightNumByStatus"
            resultType="com.ibms.service.security.controller.lightinginfo.vo.LightNumStatusVo">
                SELECT
                t.NAME AS name,
                t.id AS id,
                -- IFNULL((a.gds + a.kds), 0) as znum1,
                CASE
                when t.id = '74dbf987fa9d4201aa156388626f7be7' then IFNULL((a.gds + a.kds), 0)
                when t.id = '024e9fd809fb42d89a17df467ec07f30' then IFNULL((a.gds + a.kds), 0)
                when t.id = '626943825c3f439a9b3069c6a371dfa7' then IFNULL((a.gds + a.kds), 0)
                when t.id = 'a6ae341c3d424520bcf0bd9eff984443' then IFNULL((a.gds + a.kds), 0)
                when t.id = '4cb2bb0356764d0ea2e0ce2b367cf424' then IFNULL((a.gds + a.kds), 0)
                when t.id = '417626c96c654b719f60c1f762d06679' then IFNULL((a.gds + a.kds), 0)
                end as totalNum,
                -- IFNULL(floor( a.kds / (a.gds + a.kds) * 100 ),0) AS bfb1 ,
                CASE
                when t.id = '74dbf987fa9d4201aa156388626f7be7' then IFNULL(floor( a.kds / (a.gds + a.kds) * 100 ),0)
                when t.id = '024e9fd809fb42d89a17df467ec07f30' then IFNULL(floor( a.kds / (a.gds + a.kds) * 100 ),0)
                when t.id = '626943825c3f439a9b3069c6a371dfa7' then IFNULL(floor( a.kds / (a.gds + a.kds) * 100 ),0)
                when t.id = 'a6ae341c3d424520bcf0bd9eff984443' then IFNULL(floor( a.kds / (a.gds + a.kds) * 100 ),0)
                when t.id = '4cb2bb0356764d0ea2e0ce2b367cf424' then IFNULL(floor( a.kds / (a.gds + a.kds) * 100 ),0)
                when t.id = '417626c96c654b719f60c1f762d06679' then IFNULL(floor( a.kds / (a.gds + a.kds) * 100 ),0)
                end as percent,
                -- IFNULL(a.gds, 0) AS wknum1,
                CASE
                when t.id = '74dbf987fa9d4201aa156388626f7be7' then IFNULL(a.gds, 0)
                when t.id = '024e9fd809fb42d89a17df467ec07f30' then IFNULL(a.gds, 0)
                when t.id = '626943825c3f439a9b3069c6a371dfa7' then IFNULL(a.gds, 0)
                when t.id = 'a6ae341c3d424520bcf0bd9eff984443' then IFNULL(a.gds, 0)
                when t.id = '4cb2bb0356764d0ea2e0ce2b367cf424' then IFNULL(a.gds, 0)
                when t.id = '417626c96c654b719f60c1f762d06679' then IFNULL(a.gds, 0)
                end as closeNum,
                -- IFNULL(a.kds,0) AS yknum1,
                CASE
                when t.id = '74dbf987fa9d4201aa156388626f7be7' then IFNULL(a.kds,0)
                when t.id = '024e9fd809fb42d89a17df467ec07f30' then IFNULL(a.kds,0)
                when t.id = '626943825c3f439a9b3069c6a371dfa7' then IFNULL(a.kds,0)
                when t.id = 'a6ae341c3d424520bcf0bd9eff984443' then IFNULL(a.kds,0)
                when t.id = '4cb2bb0356764d0ea2e0ce2b367cf424' then IFNULL(a.kds,0)
                when t.id = '417626c96c654b719f60c1f762d06679' then IFNULL(a.kds,0)
                end as openNum,
                CASE
                when t.id = '74dbf987fa9d4201aa156388626f7be7' then 1
                when t.id = '024e9fd809fb42d89a17df467ec07f30' then 2
                when t.id = '626943825c3f439a9b3069c6a371dfa7' then 3
                when t.id = 'a6ae341c3d424520bcf0bd9eff984443' then 4
                when t.id = '4cb2bb0356764d0ea2e0ce2b367cf424' then 5
                when t.id = '417626c96c654b719f60c1f762d06679' then 6
                end as flag
                FROM
                t_venue_info t
                LEFT JOIN (
                SELECT
                venue_id,
                sum( CASE WHEN  IF(VALUE,VALUE,0)  = 0 THEN 1 ELSE 0 END ) gds,
                sum( CASE WHEN  IF(VALUE,VALUE,0)  != 0 THEN 1 ELSE 0 END ) kds
                FROM
                t_lighting_info
                GROUP BY
                venue_id
                ) a on a.venue_id = t.id where 1=1 and t.parent_id = '0'
                <if test="venueId != null and venueId != '' ">
                    and t.id = #{venueId}
                </if>
                order by flag
    </select>
    <select id="getLightFloorNumByType"
            resultType="com.ibms.service.security.controller.lightinginfo.vo.LightNumTypeVo" parameterType="String">
        SELECT
                    t.name,
                    IFNULL( a.tdnum, 0 ) tdNum,
                    ifnull( a.dmnum, 0 ) dmNum,
                    ifnull( a.ddnum, 0 ) ddNum,
                    ifnull( a.pbdnum, 0 ) pbdNum,
                    ifnull( a.ctdnum, 0 ) ctdNum

                FROM
                    t_venue_info t
                    LEFT JOIN (
                    SELECT
                        floor_id,
                        SUM(
                        IF
                        ( type = 1, 1, 0 )) tdnum,
                        SUM(
                        IF
                        ( type = 2, 1, 0 )) dmnum,
                        SUM(
                        IF
                        ( type = 3, 1, 0 )) ddnum,
                        SUM(
                        IF
                        ( type = 4, 1, 0 )) pbdnum,
                        SUM(
                        IF
                        ( type = 5, 1, 0 )) ctdnum
                    FROM
                        t_lighting_info
                    GROUP BY
                        floor_id
                    ) a ON t.id = a.floor_id
                WHERE
                    t.parent_id = #{venueId}
                ORDER BY
                    t.sort
    </select>
    <select id="getLightFloorNumByStatus"
            resultType="com.ibms.service.security.controller.lightinginfo.vo.LightNumStatusVo" parameterType="String">
        SELECT t.NAME AS name, t.id AS cgid, IFNULL(( a.gds + a.kds ),
						0
					) AS totalNum,
					IFNULL( floor( a.kds / ( a.gds + a.kds ) * 100 ), 0 ) AS bfb,
					IFNULL( a.gds, 0 ) AS closeNum,
					IFNULL( a.kds, 0 ) AS openNum
					FROM
						t_venue_info t
						LEFT JOIN (
						SELECT
							floor_id,
							sum( CASE WHEN IF(VALUE,VALUE,0) = 0 THEN 1 ELSE 0 END ) gds,
							sum( CASE WHEN IF(VALUE,VALUE,0) != 0 THEN 1 ELSE 0 END ) kds
						FROM
							t_lighting_info
						GROUP BY
							floor_id
						) a ON a.floor_id = t.id
					WHERE
						t.parent_id = #{venueId}
					ORDER BY sort
    </select>

</mapper>
