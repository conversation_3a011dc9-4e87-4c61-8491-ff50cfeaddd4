<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.security.mapper.networkjsoninfo.NetworkJsonInfoMapper">

    <resultMap id="BaseResultMap" type="com.ibms.service.security.domain.NetworkJsonInfo" >
        <result column="id" property="id" />
        <result column="ip" property="ip" />
        <result column="name" property="name" />
        <result column="category" property="category" />
        <result column="online1" property="online1" />
        <result column="icon" property="icon" />
        <result column="icon_src1" property="iconSrc1" />
        <result column="icon_src2" property="iconSrc2" />
        <result column="symbolsize" property="symbolsize" />
        <result column="value1" property="value1" />
        <result column="value2" property="value2" />
        <result column="type" property="type" />
        <result column="remarks" property="remarks" />
        <result column="del_flag" property="delFlag" />
        <result column="parent_id" property="parentId" />
        <result column="parent_ids" property="parentIds" />
        <result column="sort" property="sort" />
        <result column="venue_id" property="venueId" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                ip,
                name,
                category,
                online1,
                icon,
                icon_src1,
                icon_src2,
                symbolsize,
                value1,
                value2,
                type,
                remarks,
                del_flag,
                parent_id,
                parent_ids,
                sort,
                venue_id,
                create_by,
                update_by,
                create_time,
                update_time
    </sql>

</mapper>