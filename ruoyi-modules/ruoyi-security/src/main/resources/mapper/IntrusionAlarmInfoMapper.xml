<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.security.mapper.intrusionalarminfo.IntrusionAlarmInfoMapper">



    <resultMap id="BaseResultMap" type="com.ibms.service.security.domain.IntrusionAlarmInfo" >
        <result column="id" property="id" />
        <result column="dev_no" property="devNo" />
        <result column="device_name" property="deviceName" />
        <result column="alarm_type" property="alarmType" />
        <result column="occur_time" property="occurTime" />
        <result column="place" property="place" />
        <result column="described" property="described" />
        <result column="process_mode" property="processMode" />
        <result column="process_status" property="processStatus" />
        <result column="instructions" property="instructions" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="remarks" property="remarks" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                dev_no,
                device_name,
                alarm_type,
                occur_time,
                place,
                described,
                process_mode,
                process_status,
                instructions,
                create_time,
                update_time,
                remarks
    </sql>
    <select id="getIntrusionAlarmInfoNumByOneWeek"
            resultType="com.ibms.service.security.controller.intrusionalarminfo.param.OneWeekIntrusionAlarmInfoNumVo">


        SELECT
            a.date,
            ifnull( b.num, 0 ) AS num
        FROM
            (
            SELECT
                date_sub( CURDATE(), INTERVAL @i := @i + 1 day ) AS date
            FROM
                ( SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 ) AS a,
                ( SELECT @i := - 1 ) b
            ) AS a
            LEFT JOIN (
            SELECT
                count( 1 ) AS num,
                date_FORMAT( occur_time, '%Y-%m-%d' ) AS time
            FROM
                t_intrusion_alarm_info
            GROUP BY
                YEAR ( occur_time ),
                MONTH ( occur_time ),
                DAY ( occur_time )
            ) AS b ON a.date = b.time
            order by a.date asc

    </select>
    <select id="getIntrusionAlarmInfoNumByMonth"
            resultType="com.ibms.service.security.controller.intrusionalarminfo.param.MonthIntrusionAlarmInfoNumVo">


        SELECT
            a.date,
            ifnull( b.num, 0 ) AS num
        FROM
            (
            SELECT
                date_FORMAT( date_sub( CURDATE(), INTERVAL @i := @i + 1 month ), '%Y-%m' )  AS date
            FROM
                (  SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 ) AS a,
                ( SELECT @i := - 1 ) b
            ) AS a
            LEFT JOIN (
            SELECT
                count( 1 ) AS num,
                date_FORMAT( occur_time, '%Y-%m' ) AS time
            FROM
                t_intrusion_alarm_info
            GROUP BY
                YEAR ( occur_time ),
                MONTH ( occur_time )
            ) AS b ON a.date = b.time
                ORDER BY a.date asc

    </select>


</mapper>
