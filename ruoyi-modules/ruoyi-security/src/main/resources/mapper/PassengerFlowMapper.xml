<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.security.mapper.passengerflow.PassengerFlowMapper">

    <resultMap id="BaseResultMap" type="com.ibms.service.security.domain.PassengerFlow" >
        <result column="id" property="id" />
        <result column="region_code" property="regionCode" />
        <result column="region_name" property="regionName" />
        <result column="y_position" property="yPosition" />
        <result column="x_position" property="xPosition" />
        <result column="floor_id" property="floorId" />
        <result column="venue_id" property="venueId" />
        <result column="remainder" property="remainder" />
        <result column="create_time" property="createTime" />
        <result column="region_type" property="regionType" />
        <result column="plan_person_count" property="planPersonCount" />
        <result column="statistic_model" property="statisticModel" />
        <result column="up_person_count" property="upPersonCount" />
        <result column="enter_number" property="enterNumber" />
        <result column="lower_person_count" property="lowerPersonCount" />
        <result column="exceed_upper_time" property="exceedUpperTime" />
        <result column="update_time" property="updateTime" />
        <result column="alarm_channel_code" property="alarmChannelCode" />
        <result column="out_number" property="outNumber" />
        <result column="url" property="url" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                region_code,
                region_name,
                y_position,
                x_position,
                floor_id,
                venue_id,
                remainder,
                create_time,
                region_type,
                plan_person_count,
                statistic_model,
                up_person_count,
                enter_number,
                lower_person_count,
                exceed_upper_time,
                update_time,
                alarm_channel_code,
                out_number,
                url
    </sql>
    <select id="getFlowNumber" resultType="com.ibms.service.security.domain.PassengerFlow">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_passenger_flow
        order by  CAST(enter_number as SIGNED) desc
        limit 3
    </select>

</mapper>
