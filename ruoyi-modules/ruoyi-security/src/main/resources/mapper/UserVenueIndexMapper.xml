<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.security.mapper.uservenueindex.UserVenueIndexMapper">


    <resultMap id="BaseResultMap" type="com.ibms.service.security.domain.UserVenueIndex" >
        <result column="venue_id" property="venueId" />
        <result column="user_id" property="userId" />
        <result column="user_type" property="userType" />
    </resultMap>

    <sql id="Base_Column_List">
                venue_id,
                user_id,
                user_type
    </sql>


</mapper>
