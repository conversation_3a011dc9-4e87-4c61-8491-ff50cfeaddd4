<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.security.mapper.accessdeviceinfo.AccessDeviceInfoMapper">


    <resultMap id="BaseResultMap" type="com.ibms.service.security.domain.AccessDeviceInfo" >
        <result column="id" property="id" />
        <result column="floor_id" property="floorId" />
        <result column="venue_id" property="venueId" />
        <result column="device_id" property="deviceId" />
        <result column="dev_no" property="devNo" />
        <result column="door_no" property="doorNo" />
        <result column="door_area_id" property="doorAreaId" />
        <result column="name" property="name" />
        <result column="dev_type_code" property="devTypeCode" />
        <result column="collect_code" property="collectCode" />
        <result column="remark" property="remark" />
        <result column="g_id" property="gId" />
        <result column="r_id" property="rId" />
        <result column="del_flag" property="delFlag" />
        <result column="x_position" property="xPosition" />
        <result column="y_position" property="yPosition" />
        <result column="device_status" property="deviceStatus" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                floor_id,
                venue_id,
                device_id,
                dev_no,
                door_no,
                door_area_id,
                name,
                dev_type_code,
                collect_code,
                remark,
                g_id,
                r_id,
                del_flag,
                device_status,
                x_position,
                y_position
    </sql>


</mapper>
