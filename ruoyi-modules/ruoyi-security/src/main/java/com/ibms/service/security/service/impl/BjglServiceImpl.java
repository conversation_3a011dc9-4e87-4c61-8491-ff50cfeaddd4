package com.ibms.service.security.service.impl;


import cn.hutool.json.JSONObject;
import com.alibaba.nacos.common.utils.DateFormatUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ibms.service.security.controller.alarminfo.param.AlarmListParam;
import com.ibms.service.security.controller.alarminfo.param.AlarmNumBySubSystemParam;
import com.ibms.service.security.controller.alarminfo.param.AllAlarmNumByTotalParam;
import com.ibms.service.security.controller.alarminfo.vo.AllAlarmNumVo;
import com.ibms.service.security.controller.alarminfo.vo.OneWeekAlarmNumVo;
import com.ibms.service.security.controller.alarminfo.vo.SubSystemAlarmNumVo;
import com.ibms.service.security.controller.alarminfo.vo.TodayAlarmNumVo;
import com.ibms.service.security.domain.Bjgl;
import com.ibms.service.security.domain.VenueInfo;
import com.ibms.service.security.mapper.bjgl.BjglMapper;
import com.ibms.service.security.mapper.venueinfo.VenueInfoMapper;
import com.ibms.service.security.service.BjglService;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.DictUtils;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysDictData;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 报警管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@Service
public class BjglServiceImpl extends MPJBaseServiceImpl<BjglMapper, Bjgl> implements BjglService {
    @Autowired
    private BjglMapper bjglMapper;

    @Autowired
    private VenueInfoMapper venueInfoMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询报警管理
     *
     * @param id 报警管理主键
     * @return 报警管理
     */
    @Override
    public Bjgl selectBjglById(String id) {
        Bjgl bjgl = bjglMapper.selectBjglById(id);
        try {
            String handler = bjgl.getHandler();
            if (StringUtils.isBlank(handler)) {
                throw new ServiceException("处理人不存在!");
            }
            SysUser user = remoteUserService.getUserInfoById(Long.parseLong(handler), SecurityConstants.INNER);
            if (user != null) {
                bjgl.setHandler(user.getUserName());
            } else {
                throw new ServiceException("处理人不存在！");
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
            bjgl.setHandler(null);
        } catch (ServiceException e) {
            log.warn(e.getMessage());
            bjgl.setHandler(null);
        }
        return bjgl;
    }

    /**
     * 查询报警管理列表
     *
     * @param bjgl 报警管理
     * @return 报警管理
     */
    @Override
    public List<Bjgl> selectBjglList(Bjgl bjgl) {
        return bjglMapper.selectBjglList(bjgl);
    }

    /**
     * 新增报警管理
     *
     * @param bjgl 报警管理
     * @return 结果
     */
    @Override
    public int insertBjgl(Bjgl bjgl) {
        return bjglMapper.insertBjgl(bjgl);
    }

    /**
     * 修改报警管理
     *
     * @param bjgl 报警管理
     * @return 结果
     */
    @Override
    public int updateBjgl(Bjgl bjgl) {
        return bjglMapper.updateBjgl(bjgl);
    }

    /**
     * 批量删除报警管理
     *
     * @param ids 需要删除的报警管理主键
     * @return 结果
     */
    @Override
    public int deleteBjglByIds(String[] ids) {
        return bjglMapper.deleteBjglByIds(ids);
    }

    /**
     * 删除报警管理信息
     *
     * @param id 报警管理主键
     * @return 结果
     */
    @Override
    public int deleteBjglById(String id) {
        return bjglMapper.deleteBjglById(id);
    }

    @Override
    public List<Bjgl> appAllList(String beginDtae, String endDate) {
        return bjglMapper.appAllList(beginDtae, endDate);
    }

    @Override
    public List<Map<String, String>> getMonthWater(String venueId) {
        return bjglMapper.getMonthWater(venueId);
    }

    @Override
    public List<Map<String, String>> getOverall(JSONObject param) {
        return bjglMapper.getOverall(param);
    }

    @Override
    public List<Map<String, String>> getEnergyConservation(JSONObject param) {
        return bjglMapper.getEnergyConservation(param);
    }

    @Override
    public List<Map<String, String>> getWater(JSONObject param) {
        return bjglMapper.getWater(param);
    }

    @Override
    public List<Map<String, String>> getGiveAnAlarm(JSONObject param) {
        return bjglMapper.getGiveAnAlarm(param);
    }

    @Override
    public TodayAlarmNumVo getTodayAlarmNum() {
        return bjglMapper.getTodayAlarmNum();
    }

    @Override
    public TodayAlarmNumVo getAllAlarmNumByTotal(AllAlarmNumByTotalParam param) {
        return bjglMapper.getAllAlarmNumByTotal();
    }

    @Override
    public AllAlarmNumVo getAllAlarmNum() {
        return bjglMapper.getAllAlarmNum();
    }

    @Override
    public List<OneWeekAlarmNumVo> getOneWeekAlarmNum() {
        return bjglMapper.getOneWeekAlarmNum();
    }

    @Override
    public List<SubSystemAlarmNumVo> geAlarmNumBySubSystem(AlarmNumBySubSystemParam param) {
        List<SubSystemAlarmNumVo> voList = new ArrayList();
        if (StringUtils.isEmpty(param.getVenueId())) {
//            param.setVenueIds(VenueUtils.getPermissionCode());
        }

        List<SysDictData> zixitongList = DictUtils.getDictCache("zhyy_zixitong");
        List<SubSystemAlarmNumVo> numVos = bjglMapper.geAlarmNumBySubSystem(param);

        for (SysDictData sysDictData : zixitongList) {
            SubSystemAlarmNumVo subSystemAlarmNumVo = new SubSystemAlarmNumVo();
            subSystemAlarmNumVo.setDeviceNum(0L);
            subSystemAlarmNumVo.setRunNum(0L);
            subSystemAlarmNumVo.setLabel(sysDictData.getDictLabel());
            subSystemAlarmNumVo.setValue(sysDictData.getDictValue());
            voList.add(subSystemAlarmNumVo);
        }
        for (SubSystemAlarmNumVo numVo : numVos) {
            for (SubSystemAlarmNumVo subSystemAlarmNumVo : voList) {
                if (subSystemAlarmNumVo.getValue().equals(numVo.getSubSystem())) {
                    subSystemAlarmNumVo.setRunNum(numVo.getRunNum());
                    subSystemAlarmNumVo.setDeviceNum(numVo.getDeviceNum());
                }
            }
        }
        return voList;
    }

    @Override
    public List<Bjgl> selectList(AlarmListParam param) {
        QueryWrapper wrapper = new QueryWrapper();
        if (StringUtils.isNotEmpty(param.getAlarmName())) {
            wrapper.like("alarmname", param.getAlarmName());
        }
        if (StringUtils.isNotEmpty(param.getAlarmLevel())) {
            wrapper.eq("alarmlevel", param.getAlarmLevel());
        }
        if (StringUtils.isNotEmpty(param.getAlarmType())) {
            wrapper.eq("alarmtype", param.getAlarmType());
        }
        if (StringUtils.isNotEmpty(param.getVenueId())) {
            wrapper.eq("venue_id", param.getVenueId());
        }

//        if (CollectionUtil.isNotEmpty(param.getVenueIds())){
//            wrapper.in("venue_id",param.getVenueIds());
//        }
        if (StringUtils.isNotEmpty(param.getSubSystem())) {
            wrapper.eq("subsystsem", param.getSubSystem());
        }
        if (StringUtils.isNotEmpty(param.getProcessingStatus())) {
            wrapper.eq("processingstatus", param.getProcessingStatus());
        }
        if (null != param.getStartTime() && null != param.getEndTime()) {
            String start = DateFormatUtils.format(param.getStartTime(), "yyyy-MM-dd");
            String end = DateFormatUtils.format(param.getEndTime(), "yyyy-MM-dd");
            wrapper.between("DATE_FORMAT(alarmtime,'%Y-%m-%d')", start, end);
        }

        wrapper.orderByDesc("alarmime");

        List<Bjgl> bjglList = bjglMapper.selectList(wrapper);

        List<VenueInfo> venueInfos = venueInfoMapper.selectList(new QueryWrapper<>());
        for (Bjgl bjgl : bjglList) {
            for (VenueInfo venueInfo : venueInfos) {
                if (venueInfo.getId().equals(bjgl.getVenueId())) {
                    bjgl.setVenueName(venueInfo.getName());
                }
            }
        }

        return bjglList;


    }
}
