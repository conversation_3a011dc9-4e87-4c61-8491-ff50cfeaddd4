package com.ibms.service.security.controller.intrusionalarminfo.param;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel(value = "根据id获取入侵报警信息参数")
@Data
public class IntrusionAlarmInfoGetByIdParam {

    /**
     * 主键
     */
    @NotNull(message = "id必填")
    @ApiModelProperty("主键")
    private Integer id;


}
