package com.ibms.service.security.controller.accessdeviceinfo;

import com.ibms.service.security.controller.accessdeviceinfo.param.AccessDeviceDeleteParam;
import com.ibms.service.security.controller.accessdeviceinfo.param.AccessDeviceEditParam;
import com.ibms.service.security.controller.accessdeviceinfo.param.AccessDeviceListParam;
import com.ibms.service.security.controller.camerainfo.param.CameraGetByIdParam;
import com.ibms.service.security.domain.AccessDeviceInfo;
import com.ibms.service.security.domain.AccessInfo;
import com.ibms.service.security.domain.UserVenueIndex;
import com.ibms.service.security.service.AccessDeviceInfoService;
import com.ibms.service.security.service.AccessInfoService;
import com.ibms.service.security.utils.SysUserVenueUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(tags = "门禁设备Controller")
@RestController
@RequestMapping("accessDevice")
public class AccessDeviceInfoController extends BaseController {

    @Autowired
    private AccessDeviceInfoService accessDeviceInfoService;


    @ApiOperation(value = "门禁设备列表")
    @GetMapping("/list")
    public TableDataInfo<AccessDeviceInfo> list(AccessDeviceListParam param){
        if (StringUtils.isEmpty(param.getVenueId())){
            //特殊处理 如果是场馆管理员做分场馆数据
            Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();
            UserVenueIndex userVenue = SysUserVenueUtils.getUserVenueIndexByUserId(userId);
            if (userVenue != null && userVenue.getUserType() == 2 ){
                param.setVenueId(userVenue.getVenueId());
            }
        }
        startPage();
        List<AccessDeviceInfo> list = accessDeviceInfoService.selectList(param);

        return getDataTable(list);
    }

    @PostMapping("save")
    @ApiOperation(value = "保存门禁设备信息")
    public AjaxResult save(@Validated @RequestBody AccessDeviceInfo param){

        Integer num = accessDeviceInfoService.save(param);

        return toAjax(num);
    }

    @PostMapping("openDoor")
    @ApiOperation(value = "开门操作")
    public AjaxResult openDoor(){
        return AjaxResult.success();
    }

    @DeleteMapping("delete")
    @ApiOperation(value = "删除门禁设备信息")
    public AjaxResult delete(@Validated AccessDeviceDeleteParam param){

        Integer num = accessDeviceInfoService.delete(param);

        return toAjax(num);
    }

    @ApiOperation(value = "根据id获取详情")
    @GetMapping("/getById")
    public TAjaxResult<AccessDeviceInfo> getById(@Validated CameraGetByIdParam param){

        AccessDeviceInfo accessDeviceInfo = accessDeviceInfoService.getById(param);

        return new TAjaxResult().success(accessDeviceInfo);

    }




}
