package com.ibms.service.security.controller.camerainfo.param;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(value = "视频监控点位编辑参数")
@Data
public class CameraEditParam {

    /**
     * 主键
     */
    @NotBlank(message = "id必填")
    @ApiModelProperty("主键")
    private String id;

    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private String deviceId;

    /**
     * 通道id
     */
    @ApiModelProperty("通道id")
    private String channelId;

    /**
     * 摄像头名称
     */
    @ApiModelProperty("摄像头名称")
    private String name;

    /**
     * x位置
     */
    @ApiModelProperty("x位置")
    private String xPosition;

    /**
     * y位置
     */
    @ApiModelProperty("y位置")
    private String yPosition;

    /**
     * 楼层id
     */
    @ApiModelProperty("楼层id")
    private String floorId;

    /**
     * 场馆id
     */
    @ApiModelProperty("场馆id")
    private String venueId;

    /**
     * 摄像头状态
     */
    @ApiModelProperty("摄像头状态")
    private String status;

    /**
     * 备注信息
     */
    @ApiModelProperty("备注信息")
    private String remarks;

    /**
     * 摄像头类型
     */
    @ApiModelProperty("摄像头类型")
    private String cameraType;




}
