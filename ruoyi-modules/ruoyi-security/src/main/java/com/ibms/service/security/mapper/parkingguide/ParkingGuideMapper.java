package com.ibms.service.security.mapper.parkingguide;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ibms.service.security.controller.parkingguide.vo.GuideGroupNumVo;
import com.ibms.service.security.controller.parkingguide.vo.GuideStatusNumVo;
import com.ibms.service.security.domain.ParkingGuide;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ParkingGuideMapper extends BaseMapper<ParkingGuide> {
    GuideStatusNumVo getGuideStatusNum();

    List<GuideGroupNumVo> getGuideGroupNum();
}
