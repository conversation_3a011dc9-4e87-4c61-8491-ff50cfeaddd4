package com.ibms.service.security.controller.lightinginfo.param;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "操作灯具功能（开关灯）-参数")
public class OperateLightingParam {



    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 值
     */
    @ApiModelProperty("值(100：开，0：关)")
    private Integer value;

}
