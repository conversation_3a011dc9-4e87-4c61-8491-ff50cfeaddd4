package com.ibms.service.security.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/29 14:53
 */
@Data
@ApiModel("专家知识库表")
@TableName("t_knowledge_data")
public class KnowledgeData implements Serializable {


    private static final long serialVersionUID = 3507627646076869133L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 问题类型
     */
    @ApiModelProperty("问题类型")
    private String questionType;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String represent;

    /**
     * 附件
     */
    @ApiModelProperty("附件")
    private String annex;

    /**
     * 场馆id
     */
    @ApiModelProperty("场馆id")
    private String venueId;

    /**
     * 备注信息
     */
    @ApiModelProperty("备注信息")
    private String remarks;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateDate;

}
