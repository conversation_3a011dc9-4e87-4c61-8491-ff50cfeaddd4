package com.ibms.service.security.controller.camerainfo.param;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel(value = "多删除监控设备参数")
@Data
public class CameraDeleteIdsParam {

    /**
     * 主键
     */
    @NotBlank(message = "ids必填")
    @ApiModelProperty("主键")
    private String ids;


}
