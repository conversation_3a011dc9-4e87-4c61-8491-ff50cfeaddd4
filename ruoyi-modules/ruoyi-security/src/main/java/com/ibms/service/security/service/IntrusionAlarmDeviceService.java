package com.ibms.service.security.service;

import com.ibms.service.security.controller.IntrusionalarmDevice.param.InspectionGetByIdParam;
import com.ibms.service.security.controller.IntrusionalarmDevice.param.IntrusionDeleteParam;
import com.ibms.service.security.controller.IntrusionalarmDevice.param.RemovalParam;
import com.ibms.service.security.controller.inspectionrecord.param.InspectionListParam;
import com.ibms.service.security.domain.IntrusionAlarmDevice;

import java.util.List;

public interface IntrusionAlarmDeviceService {
    List<IntrusionAlarmDevice> selectList(InspectionListParam param);

    IntrusionAlarmDevice getById(InspectionGetByIdParam param);

    Integer deleteById(IntrusionDeleteParam param);

    Integer edit(IntrusionAlarmDevice param);

    Integer disposeClothRemoval(RemovalParam param);
}
