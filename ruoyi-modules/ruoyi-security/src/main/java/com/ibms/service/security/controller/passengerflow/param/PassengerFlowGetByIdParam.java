package com.ibms.service.security.controller.passengerflow.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "客流信息设备详情-参数")
public class PassengerFlowGetByIdParam {


    /**
     * 主键
     */
    @NotBlank(message = "id必填")
    @ApiModelProperty("主键")
    private String id;

}
