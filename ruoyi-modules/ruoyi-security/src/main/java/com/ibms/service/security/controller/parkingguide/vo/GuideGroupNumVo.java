package com.ibms.service.security.controller.parkingguide.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "每层停车场车位状态数量")
public class GuideGroupNumVo {


    @ApiModelProperty(value = "车位总数量")
    private Long total;

    @ApiModelProperty(value = "空闲车位")
    private Long freeNum;

    @ApiModelProperty(value = "占用车位")
    private Long occupyNum;

    @ApiModelProperty(value = "名称")
    private String name;

}
