package com.ibms.service.security.controller.venueinfo;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ibms.service.security.controller.venueinfo.param.VenueDeleteParam;
import com.ibms.service.security.controller.venueinfo.param.VenueGetByIdParam;
import com.ibms.service.security.controller.venueinfo.param.VenueListParam;
import com.ibms.service.security.domain.VenueInfo;
import com.ibms.service.security.mapper.venueinfo.VenueInfoMapper;
import com.ibms.service.security.service.VenueInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@Api(tags = "场馆楼层Controller")
@RestController
@RequestMapping("/venue")
public class VenueInfoController extends BaseController {

    @Autowired
    private VenueInfoService venueInfoService;
    @Autowired
    private VenueInfoMapper venueInfoMapper;


    @GetMapping("/treeData")
    @ApiOperation(value = "获取场馆楼层树状数据")
    public AjaxResult treeData(@ApiIgnore VenueListParam param)
    {
        List<VenueInfo> list = venueInfoService.selectList(param);
        return AjaxResult.success(venueInfoService.buildVenueTreeSelect(list));
    }

    @GetMapping("/zshhgjData")
    @ApiOperation(value = "正商环湖国际楼层数据")
    public AjaxResult zshhgjData() {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("parent_id", "74dbf987fa9d4201aa156388626f7be7");
        wrapper.orderByAsc("sort");
        List<VenueInfo> list = venueInfoMapper.selectList(wrapper);
        return AjaxResult.success(list);
    }

    @GetMapping("/treeAllListData")
    @ApiOperation(value = "获取所有场馆楼层树状数据")
    public AjaxResult treeAllListData(@ApiIgnore VenueListParam param)
    {
        List<VenueInfo> list = venueInfoService.treeAllListData(param);
        return AjaxResult.success(venueInfoService.buildVenueTreeSelect(list));
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存场馆楼层信息")
    public AjaxResult save(@Validated @RequestBody VenueInfo param)
    {
        Integer num = venueInfoService.save(param);
        return toAjax(num);
    }

    @ApiOperation(value = "根据id获取详情")
    @GetMapping("/getById")
    public TAjaxResult<VenueInfo> getById(@Validated VenueGetByIdParam param){

        VenueInfo venueInfo = venueInfoService.getById(param);

        return new TAjaxResult().success(venueInfo);

    }

    @DeleteMapping("delete")
    @ApiOperation(value = "删除场馆信息")
    public AjaxResult delete(@Validated VenueDeleteParam param){

        Integer num = venueInfoService.deleteById(param);

        return toAjax(num);
    }



}
