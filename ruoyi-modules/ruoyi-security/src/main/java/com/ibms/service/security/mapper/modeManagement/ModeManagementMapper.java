package com.ibms.service.security.mapper.modeManagement;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.security.domain.ModeManagement;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 模式管理Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-06
 */
@Mapper
public interface ModeManagementMapper extends MPJBaseMapper<ModeManagement> {
    /**
     * 查询模式管理
     *
     * @param id 模式管理主键
     * @return 模式管理
     */
    public ModeManagement selectModeManagementById(String id);

    /**
     * 查询模式管理列表
     *
     * @param modeManagement 模式管理
     * @return 模式管理集合
     */
    public List<ModeManagement> selectModeManagementList(ModeManagement modeManagement);

    /**
     * 新增模式管理
     *
     * @param modeManagement 模式管理
     * @return 结果
     */
    public int insertModeManagement(ModeManagement modeManagement);

    /**
     * 修改模式管理
     *
     * @param modeManagement 模式管理
     * @return 结果
     */
    public int updateModeManagement(ModeManagement modeManagement);

    /**
     * 删除模式管理
     *
     * @param id 模式管理主键
     * @return 结果
     */
    public int deleteModeManagementById(String id);

    /**
     * 批量删除模式管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteModeManagementByIds(String[] ids);
}
