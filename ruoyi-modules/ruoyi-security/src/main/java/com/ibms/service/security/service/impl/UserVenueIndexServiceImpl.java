package com.ibms.service.security.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ibms.service.security.domain.UserVenueIndex;
import com.ibms.service.security.mapper.uservenueindex.UserVenueIndexMapper;
import com.ibms.service.security.service.UserVenueIndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class UserVenueIndexServiceImpl implements UserVenueIndexService {

    @Autowired
    private UserVenueIndexMapper userVenueIndexMapper;


    @Override
    public UserVenueIndex selectOne(Long userId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("user_id",userId);
        UserVenueIndex userVenueIndex = userVenueIndexMapper.selectOne(wrapper);
        return userVenueIndex;
    }
}
