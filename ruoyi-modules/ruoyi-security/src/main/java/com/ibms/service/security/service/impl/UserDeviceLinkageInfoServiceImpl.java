package com.ibms.service.security.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ibms.service.security.controller.userDeviceLinkageInfo.param.UserDeviceLinkageInfoParam;
import com.ibms.service.security.domain.UserDeviceLinkageInfo;
import com.ibms.service.security.mapper.userDeviceLinkageInfo.UserDeviceLinkageInfoMapper;
import com.ibms.service.security.service.UserDeviceLinkageInfoService;
import com.ruoyi.common.core.web.domain.AjaxResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

/**
 * @description 用户设备联动信息表
 * <AUTHOR>
 * @date 2023-04-08
 */
@Service
public class UserDeviceLinkageInfoServiceImpl implements UserDeviceLinkageInfoService {

    @Resource
    private UserDeviceLinkageInfoMapper userDeviceLinkageInfoMapper;


    @Override
    public AjaxResult insert(UserDeviceLinkageInfo userDeviceLinkageInfo) {

        // valid
        if (userDeviceLinkageInfo == null) {
            return AjaxResult.error("必要参数缺失");
        }

        userDeviceLinkageInfoMapper.insert(userDeviceLinkageInfo);
        return AjaxResult.success();
    }


    @Override
    public AjaxResult delete(int id) {
        int ret = userDeviceLinkageInfoMapper.delete(id);
        return ret>0?AjaxResult.success():AjaxResult.error();
    }


    @Override
    public AjaxResult update(UserDeviceLinkageInfo userDeviceLinkageInfo) {
        int ret = userDeviceLinkageInfoMapper.update(userDeviceLinkageInfo);
        return ret>0?AjaxResult.success():AjaxResult.error();
    }


    @Override
    public UserDeviceLinkageInfo load(int id) {
        return userDeviceLinkageInfoMapper.load(id);
    }


    @Override
    public List<UserDeviceLinkageInfo> pageList(UserDeviceLinkageInfoParam userDeviceLinkageInfoParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        List<UserDeviceLinkageInfo> result = userDeviceLinkageInfoMapper.selectList(queryWrapper);
        return result;
    }

}