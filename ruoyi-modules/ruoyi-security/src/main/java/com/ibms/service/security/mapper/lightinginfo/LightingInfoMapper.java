package com.ibms.service.security.mapper.lightinginfo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ibms.service.security.controller.lightinginfo.param.OperateAllLightingParam;
import com.ibms.service.security.controller.lightinginfo.vo.LightNumStatusVo;
import com.ibms.service.security.controller.lightinginfo.vo.LightNumTypeVo;
import com.ibms.service.security.domain.LightingInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/10/21 11:44
 */
@Mapper
public interface LightingInfoMapper extends BaseMapper<LightingInfo> {


    List<LightNumTypeVo> getLightNumByType();

    List<LightNumStatusVo> getLightNumByStatus();

    List<LightNumTypeVo> getLightFloorNumByType(String venueId);

    List<LightNumStatusVo> getLightFloorNumByStatus(String venueId);

    Integer operateAllLighting(OperateAllLightingParam param);
}
