package com.ibms.service.security.service;

import com.ibms.service.security.controller.patrolrecord.param.ListParam;
import com.ibms.service.security.controller.patrolrecord.param.PatrolRecordByIdParam;
import com.ibms.service.security.controller.patrolrecord.vo.MonthRecordNumVo;
import com.ibms.service.security.domain.PatrolRecord;

import java.util.List;

public interface PatrolRecordService {


    List<PatrolRecord> selectList(ListParam param);

    List<MonthRecordNumVo> getPatrolRecordNumByMonth();

    PatrolRecord getById(PatrolRecordByIdParam param);
}
