package com.ibms.service.security.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "客流信息设备表")
@Data
@TableName("t_passenger_flow")
public class PassengerFlow implements Serializable {

    private static final long serialVersionUID = 7620159686335529415L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 区域编码
     */
    @ApiModelProperty("区域编码")
    private String regionCode;

    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    private String regionName;

    /**
     * y位置
     */
    @ApiModelProperty("y位置")
    private String yPosition;

    /**
     * x位置
     */
    @ApiModelProperty("x位置")
    private String xPosition;

    /**
     * 楼层id
     */
    @ApiModelProperty("楼层id")
    private String floorId;

    /**
     * 场馆id
     */
    @ApiModelProperty("场馆id")
    private String venueId;

    /**
     * 实时人数
     */
    @ApiModelProperty("实时人数")
    private Integer remainder;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 区域类型
     */
    @ApiModelProperty("区域类型")
    private Integer regionType;

    /**
     * 计划人数
     */
    @ApiModelProperty("计划人数")
    private Integer planPersonCount;

    /**
     * 统计模式
     */
    @ApiModelProperty("统计模式")
    private Integer statisticModel;

    /**
     * 人数上限
     */
    @ApiModelProperty("人数上限")
    private String upPersonCount;

    /**
     * 当天进入人数
     */
    @ApiModelProperty("当天进入人数")
    private String enterNumber;

    /**
     * 区域人数下限
     */
    @ApiModelProperty("区域人数下限")
    private String lowerPersonCount;

    /**
     * 时长配置--超过上限时长
     */
    @ApiModelProperty("时长配置--超过上限时长")
    private String exceedUpperTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 报警通道
     */
    @ApiModelProperty("报警通道")
    private String alarmChannelCode;

    /**
     * 当天离开人数
     */
    @ApiModelProperty("当天离开人数")
    private Integer outNumber;

    /**
     * 客流视频通道路径
     */
    @ApiModelProperty("客流视频通道路径")
    private String url;
}
