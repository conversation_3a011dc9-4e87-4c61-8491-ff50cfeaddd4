package com.ibms.service.security.controller.networkinfo.param;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.page.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(value = "计算机设备列表参数")
@Data
public class NetworkInfoListParam extends BasePage {

    @ApiModelProperty(value = "场馆id")
    private String venueId;
    @ApiModelProperty(value = "楼层id")
    private String floorId;



}
