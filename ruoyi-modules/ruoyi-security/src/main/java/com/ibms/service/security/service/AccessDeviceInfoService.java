package com.ibms.service.security.service;

import com.ibms.service.security.controller.accessdeviceinfo.param.AccessDeviceDeleteParam;
import com.ibms.service.security.controller.accessdeviceinfo.param.AccessDeviceEditParam;
import com.ibms.service.security.controller.accessdeviceinfo.param.AccessDeviceListParam;
import com.ibms.service.security.controller.camerainfo.param.CameraGetByIdParam;
import com.ibms.service.security.domain.AccessDeviceInfo;

import java.util.List;

public interface AccessDeviceInfoService {
    List<AccessDeviceInfo> selectList(AccessDeviceListParam param);

    Integer edit(AccessDeviceEditParam param);

    Integer delete(AccessDeviceDeleteParam param);

    AccessDeviceInfo getById(CameraGetByIdParam param);

    Integer save(AccessDeviceInfo param);
}
