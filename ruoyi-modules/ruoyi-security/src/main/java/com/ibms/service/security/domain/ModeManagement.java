package com.ibms.service.security.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 模式管理对象 t_mode_management
 *
 * <AUTHOR>
 * @date 2023-06-06
 */
@Data
@TableName("t_mode_management")
@ApiModel("模式管理")
public class ModeManagement {
    private static final long serialVersionUID = 7259824056531741239L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;

    /**
     * 是否启动（0是1否）
     */
    @Excel(name = "是否启动", readConverterExp = "0=是1否")
    @ApiModelProperty("是否启动")
    private Long status;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("结束时间")
    private Date endTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("开始时间")
    private Date startTime;

    /**
     * 模式名称
     */
    @Excel(name = "模式名称")
    @ApiModelProperty("模式名称")
    private String modeName;

    /**
     * 模式描述
     */
    @Excel(name = "模式描述")
    @ApiModelProperty("模式描述")
    private String modeDesc;

    /**
     * 所属场馆
     */
    @Excel(name = "所属场馆")
    @ApiModelProperty("所属场馆")
    private String venueId;

    /**
     * corn表达式
     */
    @Excel(name = "corn表达式")
    @ApiModelProperty("corn表达式")
    private String corn;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("更新时间")
    private Date updateDate;

    /**
     * 备注信息
     */
    @Excel(name = "备注信息")
    @ApiModelProperty("备注信息")
    private String remarks;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    @ApiModelProperty("逻辑删除标记")
    private String delFlag;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("模式设备")
    @TableField(exist = false)
    List<ModeDevice> modeDeviceList;

}

