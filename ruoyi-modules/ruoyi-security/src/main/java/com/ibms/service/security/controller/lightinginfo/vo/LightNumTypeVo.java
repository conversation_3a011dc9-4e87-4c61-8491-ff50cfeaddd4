package com.ibms.service.security.controller.lightinginfo.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "各场馆开关的状态概况返回值")
public class LightNumTypeVo {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "长条灯数量")
    private Long ctdNum;

    @ApiModelProperty(value = "灯带数量")
    private Long ddNum;

    @ApiModelProperty(value = "灯膜数量")
    private Long dmNum;

    @ApiModelProperty(value = "平板灯数量")
    private Long pbdNum;

    @ApiModelProperty(value = "筒灯数量")
    private Long tdNum;





}
