package com.ibms.service.security.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ibms.service.security.controller.calldevice.param.CallDeviceListParam;
import com.ibms.service.security.domain.CallDevice;
import com.ibms.service.security.domain.UserVenueIndex;
import com.ibms.service.security.mapper.calldevice.CallDeviceMapper;
import com.ibms.service.security.service.CallDeviceService;
import com.ibms.service.security.utils.SysUserVenueUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CallDeviceServiceImpl implements CallDeviceService {

    @Autowired
    private CallDeviceMapper callDeviceMapper;

    @Override
    public List<CallDevice> selectList(CallDeviceListParam param) {
        QueryWrapper wrapper = new QueryWrapper();
        if (StringUtils.isNotEmpty(param.getDeviceIp())){
            wrapper.like("device_ip",param.getDeviceIp());
        }
        if (StringUtils.isNotEmpty(param.getDeviceGw())){
            wrapper.like("device_gw",param.getDeviceGw());
        }
        if (StringUtils.isNotEmpty(param.getDeviceName())){
            wrapper.like("device_name",param.getDeviceName());
        }
        if (StringUtils.isNotEmpty(param.getDeviceNum())){
            wrapper.like("device_num",param.getDeviceNum());
        }
        return callDeviceMapper.selectList(wrapper);

    }
}
