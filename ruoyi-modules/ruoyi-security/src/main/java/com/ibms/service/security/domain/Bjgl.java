package com.ibms.service.security.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 报警管理对象 bjgl
 *
 * @Author: [维度之神]
 * @Date: 2022-08-16
 */
@Data
@TableName("bjgl")
@ApiModel("报警管理")
public class Bjgl {
    private static final long serialVersionUID = -2652194294733866214L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty("主键")
    private String id;

    /**
     * 报警时间
     */
    @Excel(name = "报警时间", sort = 2)
    @ApiModelProperty("报警时间")
    private String alarmime;

    /**
     * 报警级别
     */
    @Excel(name = "报警级别", readConverterExp = "1=紧急报警,2=重要报警,3=一般报警", sort = 3)
    @ApiModelProperty("报警级别")
    private String alarmlevel;

    /**
     * 报警类型
     */
    @Excel(name = "报警类型", readConverterExp = "0=运行报警,1=设备报警", sort = 4)
    @ApiModelProperty("报警类型")
    private String alarmtype;

    /**
     * 子系统
     */
    @Excel(name = "子系统", readConverterExp = "zhyy_zixitong_menjin=出入口控制系统,zhyy_zixitong_shipin=视频监控系统," +
            "zhyy_zixitong_tingchechang=停车场管理系统,zhyy_zixitong_dianzixungen=电子巡更系统,zhyy_zixitong_fangdao=入侵报警系统," +
            "zhyy_zixitong_fanxiangxunche=反向寻车系统,zhyy_zixitong_luoyuzik=建筑设备监控系统,zhyy_zixitong_zhaoming=智能照明系统" +
            ",zhyy_zixitong_nenghaoguanli=能耗管理系统,zhyy_zixitong_jisuanji=计算机网络系统,zhyy_zixitong_keliutongji=客流统计系统,zhyy_zixitong_qiuzhubangjing=求助报警系统",
            sort = 5)
    @ApiModelProperty("子系统")
    private String subsystsem;

    /**
     * 报警名称
     */
    @Excel(name = "报警名称",sort = 6)
    @ApiModelProperty("报警名称")
    private String alarmname;

    /**
     * 设备
     */
    @Excel(name = "设备",sort = 8)
    @ApiModelProperty("设备")
    private String equipment;

    /**
     * 处理时间
     */
    @Excel(name = "处理时间",sort = 9)
    @ApiModelProperty("处理时间")
    private String processingtime;

    /**
     * 处理情况
     */
    @Excel(name = "处理情况",sort = 10)
    @ApiModelProperty("处理情况")
    private String treatment;

    /**
     * 确认状态
     */
    @Excel(name = "确认状态",sort = 11)
    @ApiModelProperty("确认状态")
    private String confirmationstatus;

    /**
     * 确认人
     */
    @Excel(name = "确认人",sort = 12)
    @ApiModelProperty("确认人")
    private String confirmer;

    /**
     * 确认时间
     */
    @Excel(name = "确认时间",sort = 13)
    @ApiModelProperty("确认时间")
    private String confirmationtime;

    /**
     * 处理状态
     */
    @Excel(name = "处理状态",sort = 14,readConverterExp = "1=未处理，3=已处理")
    @ApiModelProperty("处理状态")
    private String processingstatus;

    /**
     * 处理人
     */
    @Excel(name = "处理人",sort = 15)
    @ApiModelProperty("处理人")
    private String handler;

    /**
     * 恢复值
     */
    @Excel(name = "恢复值",sort = 16)
    @ApiModelProperty("恢复值")
    private String recoveryvalue;

    /**
     * 报警点名称
     */
    @Excel(name = "报警点名称",sort = 17)
    @ApiModelProperty("报警点名称")
    private String bjdmc;

    /**
     * 位置
     */
    @Excel(name = "位置",sort = 18)
    @ApiModelProperty("位置")
    private String position;

    /**
     * 恢复状态
     */
    @Excel(name = "恢复状态",sort = 19)
    @ApiModelProperty("恢复状态")
    private String recoverystatus;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    /**
     * 备注信息
     */
    @Excel(name = "备注信息")
    private String remarks;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    /**
     * 是否推送工单以及工单id
     */
    private String isid;

    /**
     * 设备名称
     */
    @Excel(name = "设备名称")
    private String sbname;

    /**
     * 备用1
     */
    private String by1;

    /**
     * 场馆id
     */
    private String venueId;

    /**
     * 场馆Str
     */
    @TableField(exist = false)
    @Excel(name = "场馆", sort = 1)
    private String venueName;
}

