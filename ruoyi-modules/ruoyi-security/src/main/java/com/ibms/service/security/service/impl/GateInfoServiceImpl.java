package com.ibms.service.security.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ibms.service.security.controller.gateinfo.param.openGateParam;
import com.ibms.service.security.domain.GateInfo;
import com.ibms.service.security.mapper.gateinfo.GateInfoMapper;
import com.ibms.service.security.service.GateInfoService;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/14 15:11
 */
@Service
public class GateInfoServiceImpl implements GateInfoService {

    @Autowired
    private GateInfoMapper gateInfoMapper;


    @Override
    public List<GateInfo> selectList() {

        QueryWrapper query = Wrappers.query();
        query.orderByDesc("create_time");
        List<GateInfo> gateInfoList = gateInfoMapper.selectList(query);

        return gateInfoList;
    }

    @Override
    public int openGate(openGateParam param) {
        return 1;
    }
}
