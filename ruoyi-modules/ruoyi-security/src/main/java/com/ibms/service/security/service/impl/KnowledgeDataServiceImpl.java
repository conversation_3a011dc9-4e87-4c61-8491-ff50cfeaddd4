package com.ibms.service.security.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ibms.service.security.controller.inspectionrecord.param.InspectionListParam;
import com.ibms.service.security.controller.knowledgedata.param.KnowledgeDataAddOrUpdateParam;
import com.ibms.service.security.controller.knowledgedata.param.KnowledgeDataDeleteParam;
import com.ibms.service.security.controller.knowledgedata.param.KnowledgeDataGetByIdParam;
import com.ibms.service.security.controller.knowledgedata.param.KnowledgeDataListParam;
import com.ibms.service.security.domain.KnowledgeData;
import com.ibms.service.security.mapper.knowledgedata.KnowledgeDataMapper;
import com.ibms.service.security.service.KnowledgeDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/29 14:54
 */
@Service
@Slf4j
public class KnowledgeDataServiceImpl implements KnowledgeDataService {

    @Autowired
    private KnowledgeDataMapper knowledgeDataMapper;

    @Override
    public List<KnowledgeData> selectList(KnowledgeDataListParam param) {

        String venueId = param.getVenueId();
        String title = param.getTitle();
        String questionType = param.getQuestionType();

        QueryWrapper query = Wrappers.query();
        if (StrUtil.isNotBlank(venueId)){
            query.eq("venue_id",venueId);
        }
        if (StrUtil.isNotBlank(title)){
            query.like("title",title);
        }
        if (StringUtils.isNotBlank(questionType)){
            query.eq("question_type",questionType);
        }
        query.orderByDesc("update_date");

        List list = knowledgeDataMapper.selectList(query);

        return list;
    }

    @Override
    public Integer addOrUpdate(KnowledgeDataAddOrUpdateParam param) {

        Integer id = param.getId();
        Date currentDate = new Date();
        if (null!=id){
            KnowledgeData knowledgeData = knowledgeDataMapper.selectById(id);
            if (null==knowledgeData){
                return 0;
            }
            BeanUtil.copyProperties(param,knowledgeData);
            knowledgeData.setUpdateDate(currentDate);
            int update = knowledgeDataMapper.updateById(knowledgeData);
            return update;
        }

        KnowledgeData knowledgeData =new KnowledgeData();
        BeanUtil.copyProperties(param,knowledgeData);
        knowledgeData.setCreateDate(currentDate);
        knowledgeData.setUpdateDate(currentDate);
        int insert = knowledgeDataMapper.insert(knowledgeData);

        return insert;
    }

    @Override
    public Integer delete(KnowledgeDataDeleteParam param) {

        List<String> idList = Arrays.asList(param.getIds().split(","));
        int num = knowledgeDataMapper.deleteBatchIds(idList);

        return num;
    }

    @Override
    public KnowledgeData getById(KnowledgeDataGetByIdParam param) {

        Integer id = param.getId();

        KnowledgeData knowledgeData = knowledgeDataMapper.selectById(id);

        return knowledgeData;
    }
}
