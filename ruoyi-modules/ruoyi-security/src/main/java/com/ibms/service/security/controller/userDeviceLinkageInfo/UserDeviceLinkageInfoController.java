package com.ibms.service.security.controller.userDeviceLinkageInfo;

import com.ibms.service.security.controller.userDeviceLinkageInfo.param.UserDeviceLinkageInfoParam;
import com.ibms.service.security.controller.venuedevice.param.VenueDeviceListParam;
import com.ibms.service.security.domain.UserDeviceLinkageInfo;
import com.ibms.service.security.service.UserDeviceLinkageInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 用户设备联动信息表
 * @date 2023-04-08
 */
@RestController
@RequestMapping(value = "/userDeviceLinkageInfo")
public class UserDeviceLinkageInfoController extends BaseController {

    @Resource
    private UserDeviceLinkageInfoService userDeviceLinkageInfoService;

    /**
     * 新增
     *
     * <AUTHOR>
     * @date 2023/04/08
     **/
    @RequestMapping("/insert")
    public AjaxResult insert(UserDeviceLinkageInfo userDeviceLinkageInfo) {
        return userDeviceLinkageInfoService.insert(userDeviceLinkageInfo);
    }

    /**
     * 刪除
     *
     * <AUTHOR>
     * @date 2023/04/08
     **/
    @RequestMapping("/delete")
    public AjaxResult delete(int id) {
        return userDeviceLinkageInfoService.delete(id);
    }

    /**
     * 更新
     *
     * <AUTHOR>
     * @date 2023/04/08
     **/
    @RequestMapping("/update")
    public AjaxResult update(UserDeviceLinkageInfo userDeviceLinkageInfo) {
        return userDeviceLinkageInfoService.update(userDeviceLinkageInfo);
    }

    /**
     * 查询 根据主键 id 查询
     *
     * <AUTHOR>
     * @date 2023/04/08
     **/
    @RequestMapping("/load")
    public AjaxResult load(int id) {
        return AjaxResult.success(userDeviceLinkageInfoService.load(id));
    }

    /**
     * 查询 分页查询
     *
     * <AUTHOR>
     * @date 2023/04/08
     **/
    @RequestMapping("/pageList")
    public TableDataInfo pageList(UserDeviceLinkageInfoParam userDeviceLinkageInfoParam) {
        startPage();
        List<UserDeviceLinkageInfo> list = userDeviceLinkageInfoService.pageList(userDeviceLinkageInfoParam);
        return getDataTable(list);

    }

}