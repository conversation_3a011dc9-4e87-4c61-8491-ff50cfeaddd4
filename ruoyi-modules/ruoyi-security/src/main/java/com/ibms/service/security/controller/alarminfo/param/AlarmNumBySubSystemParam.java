package com.ibms.service.security.controller.alarminfo.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "获取各子系统的报警数量参数")
public class AlarmNumBySubSystemParam {


    /**
     * 报警级别
     */
    @ApiModelProperty("报警级别")
    private String alarmLevel;

    /**
     * 报警类型
     */
    @ApiModelProperty("报警类型")
    private String alarmType;


    /**
     * 场馆
     */
    @ApiModelProperty("场馆")
    private  String venueId;
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
}
