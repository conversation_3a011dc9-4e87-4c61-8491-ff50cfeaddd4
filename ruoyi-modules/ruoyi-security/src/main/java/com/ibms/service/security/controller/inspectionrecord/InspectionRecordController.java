package com.ibms.service.security.controller.inspectionrecord;


import com.ibms.service.security.controller.alarminfo.param.AlarmDeleteParam;
import com.ibms.service.security.controller.alarminfo.param.AlarmGetByIdParam;
import com.ibms.service.security.controller.inspectionrecord.param.InspectionListParam;
import com.ibms.service.security.controller.inspectionrecord.param.InspectionRecordDeleteParam;
import com.ibms.service.security.controller.inspectionrecord.param.InspectionRecordGetByIdParam;
import com.ibms.service.security.domain.AlarmInfo;
import com.ibms.service.security.domain.InspectionRecord;
import com.ibms.service.security.domain.UserVenueIndex;
import com.ibms.service.security.service.InspectionRecordServie;
import com.ibms.service.security.utils.SysUserVenueUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "巡检Controller")
@RequestMapping("/inspectionRecord")
@RestController
public class InspectionRecordController extends BaseController {

    @Autowired
    private InspectionRecordServie inspectionRecordServie;

    @ApiOperation(value = "巡检获取列表")
    @GetMapping("/list")
    public TableDataInfo<InspectionRecord> list(InspectionListParam param){
        if (StringUtils.isEmpty(param.getVenueId())){
            //特殊处理 如果是场馆管理员做分场馆数据
            Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();
            UserVenueIndex userVenue = SysUserVenueUtils.getUserVenueIndexByUserId(userId);
            if (userVenue != null && userVenue.getUserType() == 2 ){
                param.setVenueId(userVenue.getVenueId());
            }
        }
        startPage();
        List<InspectionRecord> list = inspectionRecordServie.selectList(param);
        return getDataTable(list);
    }
    @ApiOperation(value = "根据id获取详情")
    @GetMapping("/getById")
    public TAjaxResult<InspectionRecord> getById(@Validated InspectionRecordGetByIdParam param){

        InspectionRecord inspectionRecord = inspectionRecordServie.getById(param);

        return new TAjaxResult().success(inspectionRecord);

    }


    @DeleteMapping("delete")
    @ApiOperation(value = "删除巡检信息")
    public AjaxResult delete(@Validated InspectionRecordDeleteParam param){

        Integer num = inspectionRecordServie.deleteById(param);

        return toAjax(num);
    }


}
