package com.ibms.service.security.service;

import com.ibms.service.security.controller.lightinginfo.param.*;
import com.ibms.service.security.controller.lightinginfo.vo.LightNumStatusVo;
import com.ibms.service.security.controller.lightinginfo.vo.LightNumTypeVo;
import com.ibms.service.security.domain.CameraInfo;
import com.ibms.service.security.domain.LightingInfo;

import java.util.List;

public interface LightingInfoService {
    List<LightingInfo> selectList(LightingListParam param);

    List<LightNumTypeVo> getLightNumByType();

    List<LightNumStatusVo> getLightNumByStatus();

    Integer edit(LightingEditParam param);

    Integer delete(LightingDeleteParam param);

    LightingInfo getById(LightingGetByIdParam param);

    Integer save(LightingInfo param);

    Integer deleteByIds(LightingDeleteIdsParam param);

    Integer operateLighting(OperateLightingParam param);

    Integer operateAllLighting(OperateAllLightingParam param);
}
