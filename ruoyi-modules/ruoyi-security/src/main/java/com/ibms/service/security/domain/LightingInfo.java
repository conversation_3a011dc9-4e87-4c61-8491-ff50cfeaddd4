package com.ibms.service.security.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 照明信息表
 * <AUTHOR>
 * @Date 2022/10/21 11:38
 */
@Data
@ApiModel("照明信息表")
@TableName("t_lighting_info")
public class LightingInfo implements Serializable {

    private static final long serialVersionUID = -8287585793900193103L;


    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 楼层
     */
    @ApiModelProperty("楼层")
    private String floorId;

    /**
     * 场馆
     */
    @ApiModelProperty("场馆")
    private String venueId;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private String deviceId;

    /**
     * 位号
     */
    @ApiModelProperty("位号")
    private Integer slaveId;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 值
     */
    @ApiModelProperty("值")
    private Integer value;

    /**
     * 系数
     */
    @ApiModelProperty("系数")
    private Integer coefficient;

    /**
     * 默认值
     */
    @ApiModelProperty("默认值")
    private Integer defaults;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 备注信息
     */
    @ApiModelProperty("备注信息")
    private String remarks;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    @ApiModelProperty("逻辑删除标记（0：显示；1：隐藏")
    private String delFlag;

    /**
     * 备用字段2
     */
    @ApiModelProperty("备用字段2")
    private String property2;

    /**
     * 备用字段1
     */
    @ApiModelProperty("备用字段1")
    private String property1;

    /**
     * y位置
     */
    @ApiModelProperty("y位置")
    private Double yPosition;

    /**
     * x位置
     */
    @ApiModelProperty("x位置")
    private Double xPosition;

    /**
     * 类型 1:筒灯 2:灯带 3:灯膜 4:平板灯 5:长条灯
     */
    @ApiModelProperty("类型 1:筒灯 2:灯带 3:灯膜 4:平板灯 5:长条灯")
    private String type;

    /**
     * 位置
     */
    @ApiModelProperty("位置")
    private Integer offset;

    /**
     * ip
     */
    @ApiModelProperty("ip")
    private String ip;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
