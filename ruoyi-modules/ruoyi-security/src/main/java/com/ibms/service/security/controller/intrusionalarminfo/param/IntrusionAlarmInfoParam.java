package com.ibms.service.security.controller.intrusionalarminfo.param;

import com.ruoyi.common.core.web.page.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/29 14:58
 */
@ApiModel(value = "入侵报警信息列表参数")
@Data
public class IntrusionAlarmInfoParam  extends BasePage {

    /**
     * 设备编号
     */
    @ApiModelProperty("设备编号")
    private String devNo;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 报警类型
     */
    @ApiModelProperty("报警类型")
    private String alarmType;
}
