package com.ibms.service.security.controller.accessdeviceinfo.param;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "删除门禁设备参数")
public class AccessDeviceDeleteParam {


    /**
     * 主键
     */
    @NotBlank(message = "id必填")
    @ApiModelProperty("主键")
    private String id;


}
