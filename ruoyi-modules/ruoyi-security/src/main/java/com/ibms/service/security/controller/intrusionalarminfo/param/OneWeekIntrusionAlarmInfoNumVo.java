package com.ibms.service.security.controller.intrusionalarminfo.param;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "近七天入侵报警数量概况返回值")
public class OneWeekIntrusionAlarmInfoNumVo {

    @ApiModelProperty(value = "数量")
    private int num;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "MM-dd")
    private Date date;



}
