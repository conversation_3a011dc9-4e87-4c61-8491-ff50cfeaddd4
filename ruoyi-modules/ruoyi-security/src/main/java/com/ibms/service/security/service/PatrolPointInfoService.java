package com.ibms.service.security.service;

import com.ibms.service.security.controller.patrolpointinfo.param.PatrolCardByIdParam;
import com.ibms.service.security.controller.patrolpointinfo.param.PatrolCardDeleteParam;
import com.ibms.service.security.controller.patrolpointinfo.param.PatrolCardEditParam;
import com.ibms.service.security.controller.patrolpointinfo.param.PatrolCardListParam;
import com.ibms.service.security.domain.PatrolPointInfo;

import java.util.List;

public interface PatrolPointInfoService {
    List<PatrolPointInfo> selectList(PatrolCardListParam param);
    Integer edit(PatrolCardEditParam param);
    Integer delete(PatrolCardDeleteParam param);

    PatrolPointInfo getById(PatrolCardByIdParam param);

    Integer save(PatrolPointInfo param);

}
