package com.ibms.service.security.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 门禁进出记录
 * <AUTHOR>
 * @Date 2022/10/23 20:14
 */
@Data
@ApiModel("门禁进出记录")
@TableName("t_access_info")
public class AccessInfo implements Serializable {

    private static final long serialVersionUID = 4465196939921560017L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 原id(记录id)
     */
    @ApiModelProperty("原id(记录id)")
    private String originId;

    /**
     * tokenid
     */
    @ApiModelProperty("tokenid")
    private String tokenId;

    /**
     * tokentype
     */
    @ApiModelProperty("tokentype")
    private Integer tokenType;

    /**
     * flag
     */
    @ApiModelProperty("flag")
    private Integer flag;

    /**
     * tcmid
     */
    @ApiModelProperty("tcmid")
    private String tcmId;

    /**
     * 开门方式
     */
    @ApiModelProperty("开门方式")
    private String tcmName;

    /**
     * staffid
     */
    @ApiModelProperty("staffid")
    private String staffId;

    /**
     * staffno
     */
    @ApiModelProperty("staffno")
    private String staffNo;

    /**
     * staffname
     */
    @ApiModelProperty("staffname")
    private String staffName;

    /**
     * organizationno
     */
    @ApiModelProperty("organizationno")
    private String organizationNo;

    /**
     * organizationname
     */
    @ApiModelProperty("organizationname")
    private String organizationName;

    /**
     * devtypecode
     */
    @ApiModelProperty("devtypecode")
    private String devTypeCode;

    /**
     * devno
     */
    @ApiModelProperty("devno")
    private Integer devNo;

    /**
     * doorno
     */
    @ApiModelProperty("doorno")
    private Integer doorNo;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String devName;

    /**
     * 开门时间
     */
    @ApiModelProperty("开门时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date openDate;

    /**
     * openpicno
     */
    @ApiModelProperty("openpicno")
    private String openPicNo;

    /**
     * workstationid
     */
    @ApiModelProperty("workstationid")
    private String workStationid;

    /**
     * 工作站名称
     */
    @ApiModelProperty("工作站名称")
    private String workStationName;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateDate;

    /**
     * remark
     */
    @ApiModelProperty("remark")
    private String remark;

    /**
     * gid
     */
    @ApiModelProperty("gid")
    private String gId;


    /**
     * rid
     */
    @ApiModelProperty("rid")
    private String rId;

    /**
     * bodytemperature
     */
    @ApiModelProperty("bodytemperature")
    private Double bodyTemperature;

    /**
     * 楼层
     */
    @ApiModelProperty("楼层")
    private String floorId;


    /**
     * 场馆
     */
    @ApiModelProperty("场馆")
    private String venueId;



}
