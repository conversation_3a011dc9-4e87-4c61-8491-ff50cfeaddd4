package com.ibms.service.security.service;

import cn.hutool.json.JSONObject;
import com.ibms.service.security.controller.alarminfo.param.VehicleInoutGetByIdParam;
import com.ibms.service.security.controller.vehicleinout.param.InoutDayCountParam;
import com.ibms.service.security.controller.vehicleinout.param.VehicleInoutListParam;
import com.ibms.service.security.controller.vehicleinout.vo.DayTimeVehicleNumVo;
import com.ibms.service.security.controller.vehicleinout.vo.InoutDayCountVo;
import com.ibms.service.security.controller.vehicleinout.vo.TodayGateNumVo;
import com.ibms.service.security.controller.vehicleinout.vo.WeekVehicleNumVo;
import com.ibms.service.security.domain.VehicleInout;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/14 9:59
 */
public interface VehicleInoutService {

    /**
     * 根据参数分页获取车辆进出记录
     * @param vehicleInoutListParam
     * @return
     */
    List<VehicleInout> selectPageList(VehicleInoutListParam vehicleInoutListParam);

    InoutDayCountVo inoutDayCount(InoutDayCountParam inoutDayCountParam);

    JSONObject getTodayVehicleNum();

    List<TodayGateNumVo> getTodayGateNum();

    VehicleInout getById(VehicleInoutGetByIdParam param);

    List<WeekVehicleNumVo> getVehicleNumByWeek();

    List<DayTimeVehicleNumVo> getVehicleNumByDayTime();
}
