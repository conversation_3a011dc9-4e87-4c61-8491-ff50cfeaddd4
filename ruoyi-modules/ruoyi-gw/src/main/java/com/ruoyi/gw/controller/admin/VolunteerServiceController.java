package com.ruoyi.gw.controller.admin;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.gw.domain.VolunteerService;
import com.ruoyi.gw.service.IVolunteerServiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 志愿服务Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/volunteerService")
@Api(tags = "志愿服务")
public class VolunteerServiceController extends BaseController {
    @Autowired
    private IVolunteerServiceService volunteerServiceService;

    /**
     * 查询志愿服务列表
     */
    // @RequiresPermissions("gw:volunteer:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取志愿服务列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "name", value = "姓名", paramType = "query"),
        @ApiImplicitParam(name = "contactInfo", value = "联系方式", paramType = "query")
    })
    public TableDataInfo list(VolunteerService volunteerService) {
        startPage();
        List<VolunteerService> list = volunteerServiceService.selectVolunteerServiceList(volunteerService);
        return getDataTable(list);
    }

    /**
     * 获取志愿服务详细信息
     */
    // @RequiresPermissions("gw:volunteer:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取志愿服务详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(volunteerServiceService.selectVolunteerServiceById(id));
    }

    /**
     * 新增志愿服务
     */
    // @RequiresPermissions("gw:volunteer:add")
    @Log(title = "志愿服务", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增志愿服务")
    public AjaxResult add(@RequestBody VolunteerService volunteerService) {
        return toAjax(volunteerServiceService.insertVolunteerService(volunteerService));
    }

    /**
     * 修改志愿服务
     */
    // @RequiresPermissions("gw:volunteer:edit")
    @Log(title = "志愿服务", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改志愿服务")
    public AjaxResult edit(@RequestBody VolunteerService volunteerService) {
        return toAjax(volunteerServiceService.updateVolunteerService(volunteerService));
    }

    /**
     * 删除志愿服务
     */
    // @RequiresPermissions("gw:volunteer:remove")
    @Log(title = "志愿服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除志愿服务")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(volunteerServiceService.deleteVolunteerServiceByIds(ids));
    }
} 