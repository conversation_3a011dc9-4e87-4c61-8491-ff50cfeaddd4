package com.ruoyi.gw.service.impl;

import java.util.List;

import com.ruoyi.gw.domain.VolunteerService;
import com.ruoyi.gw.mapper.VolunteerServiceMapper;
import com.ruoyi.gw.service.IVolunteerServiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 志愿服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class VolunteerServiceServiceImpl implements IVolunteerServiceService {
    @Autowired
    private VolunteerServiceMapper volunteerServiceMapper;

    /**
     * 查询志愿服务
     *
     * @param id 志愿服务主键
     * @return 志愿服务
     */
    @Override
    public VolunteerService selectVolunteerServiceById(Long id) {
        return volunteerServiceMapper.selectVolunteerServiceById(id);
    }

    /**
     * 查询志愿服务列表
     *
     * @param volunteerService 志愿服务
     * @return 志愿服务
     */
    @Override
    public List<VolunteerService> selectVolunteerServiceList(VolunteerService volunteerService) {
        return volunteerServiceMapper.selectVolunteerServiceList(volunteerService);
    }

    /**
     * 新增志愿服务
     *
     * @param volunteerService 志愿服务
     * @return 结果
     */
    @Override
    public int insertVolunteerService(VolunteerService volunteerService) {
        return volunteerServiceMapper.insertVolunteerService(volunteerService);
    }

    /**
     * 修改志愿服务
     *
     * @param volunteerService 志愿服务
     * @return 结果
     */
    @Override
    public int updateVolunteerService(VolunteerService volunteerService) {
        return volunteerServiceMapper.updateVolunteerService(volunteerService);
    }

    /**
     * 批量删除志愿服务
     *
     * @param ids 需要删除的志愿服务主键
     * @return 结果
     */
    @Override
    public int deleteVolunteerServiceByIds(Long[] ids) {
        return volunteerServiceMapper.deleteVolunteerServiceByIds(ids);
    }

    /**
     * 删除志愿服务信息
     *
     * @param id 志愿服务主键
     * @return 结果
     */
    @Override
    public int deleteVolunteerServiceById(Long id) {
        return volunteerServiceMapper.deleteVolunteerServiceById(id);
    }
} 