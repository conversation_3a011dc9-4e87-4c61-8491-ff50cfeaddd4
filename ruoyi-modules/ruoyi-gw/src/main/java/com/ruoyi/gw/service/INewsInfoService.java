package com.ruoyi.gw.service;

import com.ruoyi.gw.domain.NewsInfo;

import java.util.List;


/**
 * 新闻资讯Service接口
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
public interface INewsInfoService {
    /**
     * 查询新闻资讯
     *
     * @param id 新闻资讯主键
     * @return 新闻资讯
     */
    public NewsInfo selectNewsInfoById(Long id);

    /**
     * 查询新闻资讯列表
     *
     * @param newsInfo 新闻资讯
     * @return 新闻资讯集合
     */
    public List<NewsInfo> selectNewsInfoList(NewsInfo newsInfo);

    /**
     * 新增新闻资讯
     *
     * @param newsInfo 新闻资讯
     * @return 结果
     */
    public int insertNewsInfo(NewsInfo newsInfo);

    /**
     * 修改新闻资讯
     *
     * @param newsInfo 新闻资讯
     * @return 结果
     */
    public int updateNewsInfo(NewsInfo newsInfo);

    /**
     * 批量删除新闻资讯
     *
     * @param ids 需要删除的新闻资讯主键集合
     * @return 结果
     */
    public int deleteNewsInfoByIds(Long[] ids);

    /**
     * 删除新闻资讯信息
     *
     * @param id 新闻资讯主键
     * @return 结果
     */
    public int deleteNewsInfoById(Long id);
}

