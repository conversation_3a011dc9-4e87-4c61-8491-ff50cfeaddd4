package com.ruoyi.gw.controller.api;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.gw.domain.*;
import com.ruoyi.gw.service.*;
import com.ruoyi.system.api.RemoteCustomService;
import com.ruoyi.system.api.domain.MarketingCustomerInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * api Controller
 *
 * <AUTHOR>
 * @date 2025-03-23
 */
@RestController
@RequestMapping("/api")
@Api(tags = "api")
public class ApiController extends BaseController {

    @Autowired
    private RemoteCustomService remoteCustomService;

    @Autowired
    private IHomeBannerService homeBannerService;

    @Autowired
    private IHomeFloatingWindowService homeFloatingWindowService;

    @Autowired
    private INewsInfoService newsInfoService;

    @Autowired
    private ISeniorActivityService seniorActivityService;

    @Autowired
    private INewsCategoryService newsCategoryService;

    @Autowired
    private IAboutUsRecruitService aboutUsRecruitService;

    @Autowired
    private IHomeCenterIntroService homeCenterIntroService;

    @Autowired
    private IHomeElderlyServiceService homeElderlyServiceService;

    @Autowired
    private IElderlyServiceService elderlyServiceService;

    @Autowired
    private IMedicalNursingIntegrationService medicalNursingIntegrationService;

    @Autowired
    private ICenterInfoService centerInfoService;

    @Autowired
    private IAboutUsService aboutUsService;

    @Autowired
    private IVolunteerServiceService volunteerServiceService;

    @PostMapping("/custom/consult")
    @ApiOperation(value = "客户咨询")
    public AjaxResult marketingCustomer(@RequestBody MarketingCustomerInfo marketingCustomerInfo) {
        // marketingCustomerInfo.setSourceChannel("1");
        marketingCustomerInfo.setConsultationDate(DateUtil.date());
        return remoteCustomService.addMarketingCustomerInfo(marketingCustomerInfo, SecurityConstants.INNER);
    }

    @PostMapping("/custom/assessmentBook")
    @ApiOperation(value = "客户评估预约")
    public AjaxResult assessmentBook(@RequestBody MarketingCustomerInfo marketingCustomerInfo) {
        marketingCustomerInfo.setConsultationDate(DateUtil.date());
        // marketingCustomerInfo.setSourceChannel("1"); // 官网
        marketingCustomerInfo.setCustomerType("1"); // 意向客户
        // marketingCustomerInfo.setAppointmentStatus("0"); // 预约未处理
        return remoteCustomService.addMarketingCustomerInfo(marketingCustomerInfo, SecurityConstants.INNER);
    }

    @GetMapping("/home/<USER>/list")
    @ApiOperation(value = "首页-轮播图设置列表")
    public TableDataInfo list(HomeBanner homeBanner) {
        startPage();
        homeBanner.setEnabled(0);
        List<HomeBanner> list = homeBannerService.selectHomeBannerList(homeBanner);
        return getDataTable(list);
    }

    @GetMapping("/home/<USER>")
    @ApiOperation(value = "首页-飘窗设置详细信息")
    public AjaxResult getInfo() {
        return AjaxResult.success(homeFloatingWindowService.selectHomeFloatingWindowById());
    }

    @GetMapping("/home/<USER>")
    @ApiOperation(value = "首页-中心介绍详细信息")
    public AjaxResult getCenterIntro() {
        return AjaxResult.success(homeCenterIntroService.selectHomeCenterIntro());
    }

    @GetMapping("/home/<USER>")
    @ApiOperation(value = "首页-为老服务")
    public TableDataInfo getHomeElderlyService() {
        startPage();
        List<HomeElderlyService> list = homeElderlyServiceService.selectHomeElderlyServiceList(new HomeElderlyService());
        return getDataTable(list);
    }

    @GetMapping("/newsInfo/list")
    @ApiOperation(value = "新闻资讯-列表")
    public TableDataInfo list(NewsInfo newsInfo) {
        startPage();
        List<NewsInfo> list = newsInfoService.selectNewsInfoList(newsInfo);
        return getDataTable(list);
    }

    @GetMapping("/newsInfo/{id}")
    @ApiOperation(value = "新闻资讯-详细信息")
    public AjaxResult newsInfoGetInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(newsInfoService.selectNewsInfoById(id));
    }

    @GetMapping("/newsCategory/list")
    @ApiOperation(value = "新闻资讯-分类列表")
    public TableDataInfo list(NewsCategory newsCategory) {
        startPage();
        List<NewsCategory> list = newsCategoryService.selectNewsCategoryList(newsCategory);
        return getDataTable(list);
    }

    @GetMapping("/seniorActivity/list")
    @ApiOperation(value = "乐龄活动-列表")
    public TableDataInfo list(SeniorActivity seniorActivity) {
        startPage();
        List<SeniorActivity> list = seniorActivityService.selectSeniorActivityList(seniorActivity);
        return getDataTable(list);
    }

    @GetMapping(value = "/seniorActivity/{id}")
    @ApiOperation(value = "乐龄活动-详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(seniorActivityService.selectSeniorActivityById(id));
    }

    @GetMapping("/aboutUs/recruit")
    @ApiOperation(value = "关于我们-招聘信息")
    public AjaxResult aboutUsRecruit() {
        return AjaxResult.success(aboutUsRecruitService.selectAboutRecruitUsById());
    }

    @GetMapping("/aboutUs/list")
    @ApiOperation(value = "关于我们-列表")
    public TableDataInfo list(AboutUs aboutUs) {
        startPage();
        List<AboutUs> list = aboutUsService.selectAboutUsList(aboutUs);
        return getDataTable(list);
    }

    @ApiOperation(value = "为老服务-列表")
    @GetMapping("/elderlyService/list")
    public TableDataInfo elderlyServiceList(ElderlyService elderlyService) {
        startPage();
        List<ElderlyService> list = elderlyServiceService.selectElderlyServiceList(elderlyService);
        return getDataTable(list);
    }

    @ApiOperation(value = "医养结合-列表")
    @GetMapping("/medicalNursingIntegration/list")
    public TableDataInfo list(MedicalNursingIntegration medicalNursingIntegration) {
        startPage();
        List<MedicalNursingIntegration> list = medicalNursingIntegrationService.selectMedicalNursingIntegrationList(medicalNursingIntegration);
        return getDataTable(list);
    }

    @ApiOperation(value = "中心介绍-列表")
    @GetMapping("/centerInfo/list")
    public TableDataInfo list(CenterInfo centerInfo) {
        startPage();
        List<CenterInfo> list = centerInfoService.selectCenterInfoList(centerInfo);
        return getDataTable(list);
    }

    @PostMapping("/volunteerService")
    @ApiOperation(value = "志愿服务-新增")
    public AjaxResult addVolunteerService(@RequestBody VolunteerService volunteerService) {
        return toAjax(volunteerServiceService.insertVolunteerService(volunteerService));
    }

}
