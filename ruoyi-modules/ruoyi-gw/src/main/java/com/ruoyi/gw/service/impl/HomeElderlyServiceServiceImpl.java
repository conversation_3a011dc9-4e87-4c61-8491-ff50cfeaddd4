package com.ruoyi.gw.service.impl;


import java.util.List;

import com.ruoyi.gw.domain.HomeElderlyService;
import com.ruoyi.gw.mapper.HomeElderlyServiceMapper;
import com.ruoyi.gw.service.IHomeElderlyServiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 首页为老服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-19
 */
@Service
public class HomeElderlyServiceServiceImpl implements IHomeElderlyServiceService {
    @Autowired
    private HomeElderlyServiceMapper homeElderlyServiceMapper;

    /**
     * 查询首页为老服务
     *
     * @param id 首页为老服务主键
     * @return 首页为老服务
     */
    @Override
    public HomeElderlyService selectHomeElderlyServiceById(Long id) {
        return homeElderlyServiceMapper.selectHomeElderlyServiceById(id);
    }

    /**
     * 查询首页为老服务列表
     *
     * @param homeElderlyService 首页为老服务
     * @return 首页为老服务
     */
    @Override
    public List<HomeElderlyService> selectHomeElderlyServiceList(HomeElderlyService homeElderlyService) {
        return homeElderlyServiceMapper.selectHomeElderlyServiceList(homeElderlyService);
    }

    /**
     * 新增首页为老服务
     *
     * @param homeElderlyService 首页为老服务
     * @return 结果
     */
    @Override
    public int insertHomeElderlyService(HomeElderlyService homeElderlyService) {
        return homeElderlyServiceMapper.insertHomeElderlyService(homeElderlyService);
    }

    /**
     * 修改首页为老服务
     *
     * @param homeElderlyService 首页为老服务
     * @return 结果
     */
    @Override
    public int updateHomeElderlyService(HomeElderlyService homeElderlyService) {
        return homeElderlyServiceMapper.updateHomeElderlyService(homeElderlyService);
    }

    /**
     * 批量删除首页为老服务
     *
     * @param ids 需要删除的首页为老服务主键
     * @return 结果
     */
    @Override
    public int deleteHomeElderlyServiceByIds(Long[] ids) {
        return homeElderlyServiceMapper.deleteHomeElderlyServiceByIds(ids);
    }

    /**
     * 删除首页为老服务信息
     *
     * @param id 首页为老服务主键
     * @return 结果
     */
    @Override
    public int deleteHomeElderlyServiceById(Long id) {
        return homeElderlyServiceMapper.deleteHomeElderlyServiceById(id);
    }
}

