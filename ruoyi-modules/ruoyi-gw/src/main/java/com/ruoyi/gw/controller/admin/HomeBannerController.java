package com.ruoyi.gw.controller.admin;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.gw.domain.HomeBanner;
import com.ruoyi.gw.service.IHomeBannerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 首页轮播图设置Controller
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@RestController
@RequestMapping("/home/<USER>")
@Api(tags = "首页轮播图设置")
public class HomeBannerController extends BaseController {
    @Autowired
    private IHomeBannerService homeBannerService;

    /**
     * 查询首页轮播图设置列表
     */
    // @RequiresPermissions("custom:banner:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询首页轮播图设置列表")
    public TableDataInfo list(HomeBanner homeBanner) {
        startPage();
        List<HomeBanner> list = homeBannerService.selectHomeBannerList(homeBanner);
        return getDataTable(list);
    }

    /**
     * 导出首页轮播图设置列表
     */
    // @RequiresPermissions("custom:banner:export")
    @Log(title = "首页轮播图设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出首页轮播图设置列表")
    public void export(HttpServletResponse response, HomeBanner homeBanner) {
        List<HomeBanner> list = homeBannerService.selectHomeBannerList(homeBanner);
        ExcelUtil<HomeBanner> util = new ExcelUtil<HomeBanner>(HomeBanner.class);
        util.exportExcel(response, list, "首页轮播图设置数据");
    }

    /**
     * 获取首页轮播图设置详细信息
     */
    // @RequiresPermissions("custom:banner:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取首页轮播图设置详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(homeBannerService.selectHomeBannerById(id));
    }

    /**
     * 新增首页轮播图设置
     */
    // @RequiresPermissions("custom:banner:add")
    @Log(title = "首页轮播图设置", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增首页轮播图设置")
    public AjaxResult add(@RequestBody HomeBanner homeBanner) {
        return toAjax(homeBannerService.insertHomeBanner(homeBanner));
    }

    /**
     * 修改首页轮播图设置
     */
    // @RequiresPermissions("custom:banner:edit")
    @Log(title = "首页轮播图设置", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改首页轮播图设置")
    public AjaxResult edit(@RequestBody HomeBanner homeBanner) {
        return toAjax(homeBannerService.updateHomeBanner(homeBanner));
    }

    /**
     * 删除首页轮播图设置
     */
    // @RequiresPermissions("custom:banner:remove")
    @Log(title = "首页轮播图设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除首页轮播图设置")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeBannerService.deleteHomeBannerByIds(ids));
    }
}

