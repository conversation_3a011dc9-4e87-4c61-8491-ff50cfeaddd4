package com.ruoyi.gw.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.lang.UUID;
import cn.hutool.json.JSONObject;
import com.ruoyi.gw.domain.MedicalNursingIntegration;
import com.ruoyi.gw.mapper.MedicalNursingIntegrationMapper;
import com.ruoyi.gw.service.IMedicalNursingIntegrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 医养结合Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-19
 */
@Service
public class MedicalNursingIntegrationServiceImpl implements IMedicalNursingIntegrationService {
    @Autowired
    private MedicalNursingIntegrationMapper medicalNursingIntegrationMapper;

    /**
     * 查询医养结合
     *
     * @param id 医养结合主键
     * @return 医养结合
     */
    @Override
    public MedicalNursingIntegration selectMedicalNursingIntegrationById(Long id) {
        return medicalNursingIntegrationMapper.selectMedicalNursingIntegrationById(id);
    }

    /**
     * 查询医养结合列表
     *
     * @param medicalNursingIntegration 医养结合
     * @return 医养结合
     */
    @Override
    public List<MedicalNursingIntegration> selectMedicalNursingIntegrationList(MedicalNursingIntegration medicalNursingIntegration) {
        return medicalNursingIntegrationMapper.selectMedicalNursingIntegrationList(medicalNursingIntegration);
    }

    /**
     * 新增医养结合
     *
     * @param medicalNursingIntegration 医养结合
     * @return 结果
     */
    @Override
    public int insertMedicalNursingIntegration(MedicalNursingIntegration medicalNursingIntegration) {
        return medicalNursingIntegrationMapper.insertMedicalNursingIntegration(medicalNursingIntegration);
    }

    /**
     * 修改医养结合
     *
     * @param data 医养结合
     * @return 结果
     */
    @Override
    public int updateMedicalNursingIntegration(JSONObject data) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", data.get("id"));
        param.put("title", data.getJSONObject("detail").get("title"));
        param.put("detail", data.getJSONObject("detail").toString());
        return medicalNursingIntegrationMapper.updateMedicalNursingIntegration(param);
    }

    /**
     * 批量删除医养结合
     *
     * @param ids 需要删除的医养结合主键
     * @return 结果
     */
    @Override
    public int deleteMedicalNursingIntegrationByIds(Long[] ids) {
        return medicalNursingIntegrationMapper.deleteMedicalNursingIntegrationByIds(ids);
    }

    /**
     * 删除医养结合信息
     *
     * @param id 医养结合主键
     * @return 结果
     */
    @Override
    public int deleteMedicalNursingIntegrationById(Long id) {
        return medicalNursingIntegrationMapper.deleteMedicalNursingIntegrationById(id);
    }

    @Override
    public List<JSONObject> doctorList() {
        return medicalNursingIntegrationMapper.doctorList();
    }

    @Override
    public int insertDoctor(JSONObject doctor) {
        return medicalNursingIntegrationMapper.insertDoctor(doctor);
    }

    @Override
    public int updateDoctor(JSONObject doctor) {
        return medicalNursingIntegrationMapper.updateDoctor(doctor);
    }

    @Override
    public int deleteDoctorById(String id) {
        return medicalNursingIntegrationMapper.deleteDoctorById(id);
    }
}

