package com.ruoyi.gw.controller.admin;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.gw.domain.ElderlyService;
import com.ruoyi.gw.service.IElderlyServiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 为老服务Controller
 *
 * <AUTHOR>
 * @date 2025-04-19
 */
@RestController
@RequestMapping("/elderlyService")
@Api(tags = "为老服务")
public class ElderlyServiceController extends BaseController {
    @Autowired
    private IElderlyServiceService elderlyServiceService;

    /**
     * 查询为老服务列表
     */
    // @RequiresPermissions("custom:service:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取为老服务列表")
    public TableDataInfo list(ElderlyService elderlyService) {
        startPage();
        List<ElderlyService> list = elderlyServiceService.selectElderlyServiceList(elderlyService);
        return getDataTable(list);
    }

    /**
     * 修改为老服务
     */
    // @RequiresPermissions("custom:service:edit")
    @Log(title = "为老服务", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改为老服务")
    public AjaxResult edit(@RequestBody ElderlyService elderlyService) {
        return toAjax(elderlyServiceService.updateElderlyService(elderlyService));
    }

    /**
     * 获取为老服务详细信息
     */
    // @RequiresPermissions("custom:service:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取为老服务详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(elderlyServiceService.selectElderlyServiceById(id));
    }

    // /**
    //  * 导出为老服务列表
    //  */
    // @RequiresPermissions("custom:service:export")
    // @Log(title = "为老服务", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, ElderlyService elderlyService) {
    //     List<ElderlyService> list = elderlyServiceService.selectElderlyServiceList(elderlyService);
    //     ExcelUtil<ElderlyService> util = new ExcelUtil<ElderlyService>(ElderlyService. class);
    //     util.exportExcel(response, list, "为老服务数据");

    // }

    // /**
    //  * 新增为老服务
    //  */
    // @RequiresPermissions("custom:service:add")
    // @Log(title = "为老服务", businessType = BusinessType.INSERT)
    // @PostMapping
    // public AjaxResult add(@RequestBody ElderlyService elderlyService) {
    //     return toAjax(elderlyServiceService.insertElderlyService(elderlyService));
    // }


    // /**
    //  * 删除为老服务
    //  */
    // @RequiresPermissions("custom:service:remove")
    // @Log(title = "为老服务", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{ids}")
    // public AjaxResult remove(@PathVariable Long[] ids) {
    //     return toAjax(elderlyServiceService.deleteElderlyServiceByIds(ids));
    // }
}
