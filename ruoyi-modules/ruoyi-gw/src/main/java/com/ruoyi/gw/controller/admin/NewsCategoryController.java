package com.ruoyi.gw.controller.admin;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.gw.domain.NewsCategory;
import com.ruoyi.gw.service.INewsCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 新闻分类Controller
 *
 * <AUTHOR>
 * @date 2025-04-10
 */
@RestController
@RequestMapping("/newsCategory")
@Api(tags = "新闻分类")
public class NewsCategoryController extends BaseController {
    @Autowired
    private INewsCategoryService newsCategoryService;

    /**
     * 查询新闻分类列表
     */
    // @RequiresPermissions("custom:category:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询新闻分类列表")
    public TableDataInfo list(NewsCategory newsCategory) {
        startPage();
        List<NewsCategory> list = newsCategoryService.selectNewsCategoryList(newsCategory);
        return getDataTable(list);
    }

    /**
     * 导出新闻分类列表
     */
    // @RequiresPermissions("custom:category:export")
    @Log(title = "新闻分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出新闻分类列表")
    public void export(HttpServletResponse response, NewsCategory newsCategory) {
        List<NewsCategory> list = newsCategoryService.selectNewsCategoryList(newsCategory);
        ExcelUtil<NewsCategory> util = new ExcelUtil<NewsCategory>(NewsCategory.class);
        util.exportExcel(response, list, "新闻分类数据");
    }

    /**
     * 获取新闻分类详细信息
     */
    // @RequiresPermissions("custom:category:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取新闻分类详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(newsCategoryService.selectNewsCategoryById(id));
    }

    /**
     * 新增新闻分类
     */
    // @RequiresPermissions("custom:category:add")
    @Log(title = "新闻分类", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增新闻分类")
    public AjaxResult add(@RequestBody NewsCategory newsCategory) {
        return toAjax(newsCategoryService.insertNewsCategory(newsCategory));
    }

    /**
     * 修改新闻分类
     */
    @RequiresPermissions("custom:category:edit")
    @Log(title = "新闻分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NewsCategory newsCategory) {
        return toAjax(newsCategoryService.updateNewsCategory(newsCategory));
    }

    /**
     * 删除新闻分类
     */
    // @RequiresPermissions("custom:category:remove")
    @Log(title = "新闻分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除新闻分类")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(newsCategoryService.deleteNewsCategoryByIds(ids));
    }
}

