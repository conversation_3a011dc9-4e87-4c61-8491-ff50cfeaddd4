package com.ruoyi.gw.mapper;

import com.ruoyi.gw.domain.VolunteerService;

import java.util.List;

/**
 * 志愿服务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface VolunteerServiceMapper {
    /**
     * 查询志愿服务
     *
     * @param id 志愿服务主键
     * @return 志愿服务
     */
    public VolunteerService selectVolunteerServiceById(Long id);

    /**
     * 查询志愿服务列表
     *
     * @param volunteerService 志愿服务
     * @return 志愿服务集合
     */
    public List<VolunteerService> selectVolunteerServiceList(VolunteerService volunteerService);

    /**
     * 新增志愿服务
     *
     * @param volunteerService 志愿服务
     * @return 结果
     */
    public int insertVolunteerService(VolunteerService volunteerService);

    /**
     * 修改志愿服务
     *
     * @param volunteerService 志愿服务
     * @return 结果
     */
    public int updateVolunteerService(VolunteerService volunteerService);

    /**
     * 删除志愿服务
     *
     * @param id 志愿服务主键
     * @return 结果
     */
    public int deleteVolunteerServiceById(Long id);

    /**
     * 批量删除志愿服务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVolunteerServiceByIds(Long[] ids);
} 