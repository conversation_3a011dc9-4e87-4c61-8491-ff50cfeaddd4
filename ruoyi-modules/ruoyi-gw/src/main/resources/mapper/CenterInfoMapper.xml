<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.gw.mapper.CenterInfoMapper">

    <resultMap type="CenterInfo" id="CenterInfoResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="content" column="content"/>
        <result property="imgUrl" column="img_url"/>
    </resultMap>

    <sql id="selectCenterInfoVo">
        SELECT id, type, content, img_url
        FROM t_center_info
    </sql>

    <select id="selectCenterInfoList" parameterType="CenterInfo" resultMap="CenterInfoResult">
        <include refid="selectCenterInfoVo"/>
        <where>
            <if test="type != null  and type != ''">
                and type = #{type}
            </if>
            <if test="content != null  and content != ''">
                and content = #{content}
            </if>
            <if test="imgUrl != null  and imgUrl != ''">
                and img_url = #{imgUrl}
            </if>
        </where>
        order by id
    </select>

    <select id="selectCenterInfoById" parameterType="Long"
            resultMap="CenterInfoResult">
        <include refid="selectCenterInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertCenterInfo" parameterType="CenterInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_center_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">type,
            </if>
            <if test="content != null and content != ''">content,
            </if>
            <if test="imgUrl != null">img_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">#{type},
            </if>
            <if test="content != null and content != ''">#{content},
            </if>
            <if test="imgUrl != null">#{imgUrl},
            </if>
        </trim>
    </insert>

    <update id="updateCenterInfo" parameterType="CenterInfo">
        update t_center_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null and type != ''">type =
                #{type},
            </if>
            <if test="content != null and content != ''">content =
                #{content},
            </if>
            <if test="imgUrl != null">img_url =
                #{imgUrl},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCenterInfoById" parameterType="Long">
        DELETE
        FROM t_center_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteCenterInfoByIds" parameterType="String">
        delete from t_center_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
