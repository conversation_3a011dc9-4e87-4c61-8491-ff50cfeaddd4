<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.gw.mapper.VolunteerServiceMapper">

    <resultMap type="VolunteerService" id="VolunteerServiceResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="contactInfo" column="contact_info"/>
        <result property="organization" column="organization"/>
        <result property="participantCount" column="participant_count"/>
        <result property="address" column="address"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectVolunteerServiceVo">
        SELECT id, name, contact_info, organization, participant_count, address, create_by, create_time, update_by, update_time, remark
        FROM t_volunteer_service
    </sql>

    <select id="selectVolunteerServiceList" parameterType="VolunteerService" resultMap="VolunteerServiceResult">
        <include refid="selectVolunteerServiceVo"/>
        <where>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="contactInfo != null and contactInfo != ''">
                AND contact_info like concat('%', #{contactInfo}, '%')
            </if>
            <if test="organization != null and organization != ''">
                AND organization = #{organization}
            </if>
            <if test="participantCount != null">
                AND participant_count = #{participantCount}
            </if>
            <if test="address != null and address != ''">
                AND address = #{address}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="selectVolunteerServiceById" parameterType="Long" resultMap="VolunteerServiceResult">
        <include refid="selectVolunteerServiceVo"/>
        where id = #{id}
    </select>

    <insert id="insertVolunteerService" parameterType="VolunteerService" useGeneratedKeys="true" keyProperty="id">
        insert into t_volunteer_service
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="contactInfo != null">contact_info,</if>
            <if test="organization != null">organization,</if>
            <if test="participantCount != null">participant_count,</if>
            <if test="address != null">address,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="contactInfo != null">#{contactInfo},</if>
            <if test="organization != null">#{organization},</if>
            <if test="participantCount != null">#{participantCount},</if>
            <if test="address != null">#{address},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateVolunteerService" parameterType="VolunteerService">
        update t_volunteer_service
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="contactInfo != null">contact_info = #{contactInfo},</if>
            <if test="organization != null">organization = #{organization},</if>
            <if test="participantCount != null">participant_count = #{participantCount},</if>
            <if test="address != null">address = #{address},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVolunteerServiceById" parameterType="Long">
        DELETE FROM t_volunteer_service WHERE id = #{id}
    </delete>

    <delete id="deleteVolunteerServiceByIds" parameterType="String">
        delete from t_volunteer_service where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 