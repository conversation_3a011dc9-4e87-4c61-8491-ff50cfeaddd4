<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.gw.mapper.ElderlyServiceMapper">

    <resultMap type="ElderlyService" id="ElderlyServiceResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="content" column="content"/>
        <result property="imageUrls" column="image_urls"/>
    </resultMap>

    <sql id="selectElderlyServiceVo">
        SELECT id, type, content, image_urls
        FROM t_elderly_service
    </sql>

    <select id="selectElderlyServiceList" parameterType="ElderlyService" resultMap="ElderlyServiceResult">
        <include refid="selectElderlyServiceVo"/>
        <where>
            <if test="type != null  and type != ''">
                and type = #{type}
            </if>
            <if test="imageUrls != null  and imageUrls != ''">
                and image_urls = #{imageUrls}
            </if>
        </where>
        order by id
    </select>

    <select id="selectElderlyServiceById" parameterType="Long"
            resultMap="ElderlyServiceResult">
        <include refid="selectElderlyServiceVo"/>
        where id = #{id}
    </select>

    <insert id="insertElderlyService" parameterType="ElderlyService" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_elderly_service
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,
            </if>
            <if test="content != null">content,
            </if>
            <if test="imageUrls != null">image_urls,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},
            </if>
            <if test="content != null">#{content},
            </if>
            <if test="imageUrls != null">#{imageUrls},
            </if>
        </trim>
    </insert>

    <update id="updateElderlyService" parameterType="ElderlyService">
        update t_elderly_service
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type =
                #{type},
            </if>
            <if test="content != null">content =
                #{content},
            </if>
            <if test="imageUrls != null">image_urls =
                #{imageUrls},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElderlyServiceById" parameterType="Long">
        DELETE
        FROM t_elderly_service
        WHERE id = #{id}
    </delete>

    <delete id="deleteElderlyServiceByIds" parameterType="String">
        delete from t_elderly_service where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
