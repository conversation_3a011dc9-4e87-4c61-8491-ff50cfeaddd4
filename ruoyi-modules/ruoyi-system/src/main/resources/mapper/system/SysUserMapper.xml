<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserMapper">

    <resultMap type="SysUser" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="companyName" column="company_name"/>
        <result property="position" column="position"/>
        <result property="platformStr" column="platformStr"/>
        <association property="dept" column="dept_id" javaType="SysDept" resultMap="deptResult"/>
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult"/>
    </resultMap>

    <resultMap id="deptResult" type="SysDept">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="ancestors" column="ancestors"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="status" column="dept_status"/>
    </resultMap>

    <resultMap id="RoleResult" type="SysRole">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="status" column="role_status"/>
        <result property="platforms" column="platforms"/>
    </resultMap>

    <sql id="selectUserVo">
        SELECT u.user_id,
               u.dept_id,
               u.user_name,
               u.nick_name,
               u.company_name,
               u.position,
               u.email,
               u.avatar,
               u.phonenumber,
               u.password,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               d.dept_id,
               d.parent_id,
               d.ancestors,
               d.dept_name,
               d.order_num,
               d.leader,
               d.status AS dept_status,
               r.role_id,
               r.role_name,
               r.role_key,
               r.role_sort,
               r.data_scope,
               r.status AS role_status,
               r.platforms
        FROM sys_user u
                 LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
                 LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
                 LEFT JOIN sys_role r ON r.role_id = ur.role_id
    </sql>

    <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.nick_name, u.company_name, u.position, u.user_name, u.email, u.avatar,
        u.phonenumber, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
        d.dept_name, d.leader from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        where u.del_flag = '0'
        <if test="userId != null and userId != 0">
            AND u.user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="deptId != null and deptId != 0">
            AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},
            ancestors) ))
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectAllocatedList" parameterType="SysUser" resultMap="SysUserResult">
        select distinct u.user_id,r.platforms, u.dept_id, u.user_name, u.nick_name, u.company_name, u.position, u.email,
        u.phonenumber, u.status, u.create_time
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_user_role ur on u.user_id = ur.user_id
        left join sys_role r on r.role_id = ur.role_id
        where u.del_flag = '0' and r.role_id = #{roleId}
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectUnallocatedList" parameterType="SysUser" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id,r.platforms, u.user_name, u.nick_name, u.company_name, u.position, u.email,
        u.phonenumber, u.status, u.create_time
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_user_role ur on u.user_id = ur.user_id
        left join sys_role r on r.role_id = ur.role_id
        where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
        and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and
        ur.role_id = #{roleId})
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_name = #{userName} and u.del_flag = '0'
    </select>

    <select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_id = #{userId}
    </select>

    <select id="checkUserNameUnique" parameterType="String" resultMap="SysUserResult">
        SELECT user_id, user_name
        FROM sys_user
        WHERE user_name = #{userName}
          AND del_flag = '0'
        LIMIT 1
    </select>

    <select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
        SELECT user_id, phonenumber
        FROM sys_user
        WHERE phonenumber = #{phonenumber}
          AND del_flag = '0'
        LIMIT 1
    </select>

    <select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
        SELECT user_id, email
        FROM sys_user
        WHERE email = #{email}
          AND del_flag = '0'
        LIMIT 1
    </select>
    <select id="selectUserByPostId" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_id in ( select user_id from sys_user_post p where p.post_id =#{postId} )
    </select>
    <select id="selectUserByRoleId" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_id in (select user_id from sys_user_role r where r.role_id =#{roleId} )
    </select>

    <insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
        insert into sys_user(
        <if test="userId != null and userId != 0">user_id,</if>
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="userName != null and userName != ''">user_name,</if>
        <if test="nickName != null and nickName != ''">nick_name,</if>
        <if test="companyName != null and companyName != ''">company_name,</if>
        <if test="position != null and position != ''">position,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="avatar != null and avatar != ''">avatar,</if>
        <if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
        <if test="sex != null and sex != ''">sex,</if>
        <if test="password != null and password != ''">password,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="remark != null and remark != ''">remark,</if>
        create_time
        )values(
        <if test="userId != null and userId != ''">#{userId},</if>
        <if test="deptId != null and deptId != ''">#{deptId},</if>
        <if test="userName != null and userName != ''">#{userName},</if>
        <if test="nickName != null and nickName != ''">#{nickName},</if>
        <if test="companyName != null and companyName != ''">#{company_name},</if>
        <if test="position != null and position != ''">#{position},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="avatar != null and avatar != ''">#{avatar},</if>
        <if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
        <if test="sex != null and sex != ''">#{sex},</if>
        <if test="password != null and password != ''">#{password},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        sysdate()
        )
    </insert>

    <update id="updateUser" parameterType="SysUser">
        update sys_user
        <set>
            <if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="position != null and position != ''">position = #{position},</if>
            <if test="email != null ">email = #{email},</if>
            <if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </set>
        where user_id = #{userId}
    </update>

    <update id="updateUserStatus" parameterType="SysUser">
        UPDATE sys_user
        SET status = #{status}
        WHERE user_id = #{userId}
    </update>

    <update id="updateUserAvatar" parameterType="SysUser">
        UPDATE sys_user
        SET avatar = #{avatar}
        WHERE user_name = #{userName}
    </update>

    <update id="resetUserPwd" parameterType="SysUser">
        UPDATE sys_user
        SET password = #{password}
        WHERE user_name = #{userName}
    </update>

    <delete id="deleteUserById" parameterType="Long">
        UPDATE sys_user
        SET del_flag = '2'
        WHERE user_id = #{userId}
    </delete>


    <delete id="deleteUserByIds" parameterType="Long">
        update sys_user set del_flag = '2' where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <select id="getPostPermissionCodeByUserId" parameterType="Long" resultType="String">
        SELECT permission_code
        FROM sys_post p
                 LEFT JOIN sys_user_post up ON p.post_id = up.post_id
        WHERE up.user_id = #{userId}

    </select>
    <select id="selectUserListNew" resultMap="SysUserResult">

        SELECT *
        FROM (SELECT u.user_id,
        u.dept_id,
        u.nick_name,
        u.company_name,
        u.position,
        u.user_name,
        u.email,
        u.avatar,
        u.phonenumber,
        u.sex,
        u.STATUS,
        u.del_flag,
        u.login_ip,
        u.login_date,
        u.create_by,
        u.create_time,
        u.remark,
        d.dept_name,
        d.leader,
        GROUP_CONCAT(r.platforms) AS platformStr,
        r.role_key
        FROM sys_user u
        LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
        LEFT JOIN sys_role r ON ur.role_id = r.role_id
        WHERE u.del_flag = '0'
        <if test="userId != null and userId != 0">
            AND u.user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="nickName != null and nickName != ''">
            AND u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="deptId != null and deptId != 0">
            AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},
            ancestors) ))
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <!-- 区分老人用户和其他用户,0：其他用户，1：老人用户 -->
        <if test="params.isElder != null and params.isElder != ''"><!-- 结束时间检索 -->
            <if test="params.isElder == 1">
                AND r.role_key = 'applet_lr'
            </if>
            <if test="params.isElder == 0">
                AND (r.role_key IS NULL OR r.role_key != 'applet_lr')
            </if>
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        GROUP BY u.user_id) u
        <where>
            <if test="platform != null and platform != 1">
                and (
                u.platformStr LIKE CONCAT('%,', #{platform}, ',%')
                OR u.platformStr LIKE CONCAT(#{platform}, ',','%')
                OR u.platformStr LIKE CONCAT('%', ',',#{platform})
                OR u.platformStr = #{platform}
                )
                <!-- 护工没有平台标识，用rolekey使护工可以被搜到 -->
                <if test="platform == 100">
                    or role_key = 'custom_hg'
                </if>
            </if>
        </where>
    </select>

    <select id="selectUserByIds" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where
        u.user_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getSysUserValueAndLabelList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT user_id AS value, nick_name AS label
        FROM sys_user
        WHERE status = '0'
          AND del_flag = '0'
    </select>

    <select id="selectUserListByRoleKey" resultType="com.ruoyi.system.api.domain.SysUser">
        SELECT u.*
        FROM sys_user u
                 LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
                 LEFT JOIN sys_role r ON r.role_id = ur.role_id
        WHERE r.role_key = #{roleKey}
        GROUP BY u.user_id
    </select>

    <delete id="removeUserById" parameterType="Long">
        DELETE
        FROM sys_user
        WHERE user_id = #{userId}
    </delete>


    <select id="getServiceStaff" resultType="map">
        WITH RECURSIVE DeptHierarchy AS (
            -- 基础查询：选择所有二级部门(假设一级部门的parent_id为NULL或0)
            SELECT
                dept_id,
                dept_name,
                parent_id,
                dept_id top_dept_id,
                dept_name AS top_dept_name
            FROM sys_dept
            WHERE parent_id IN (SELECT dept_id FROM sys_dept WHERE parent_id IS NULL OR parent_id = 0)
            UNION ALL
            -- 递归查询：选择所有子部门
            SELECT
                d.dept_id,
                d.dept_name,
                d.parent_id,
                dh.top_dept_id,
                dh.top_dept_name
            FROM sys_dept d
                     JOIN DeptHierarchy dh ON d.parent_id = dh.dept_id
        )

        SELECT
            top_dept_id AS dept_id,
            top_dept_name AS dept_name,
            COUNT(e.user_id) AS employee_count
        FROM DeptHierarchy dh
                 LEFT JOIN sys_user e ON e.dept_id = dh.dept_id
        GROUP BY top_dept_id, top_dept_name
        ORDER BY employee_count DESC
    </select>

</mapper>
