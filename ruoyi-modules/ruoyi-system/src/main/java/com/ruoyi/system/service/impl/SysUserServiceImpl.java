package com.ruoyi.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.bean.BeanValidators;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.datascope.annotation.DataScope;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.SysEnum.PlatformType;
import com.ruoyi.system.api.RemoteCustomService;
import com.ruoyi.system.api.domain.EmployeeInfo;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.SysUserRole;
import com.ruoyi.system.api.param.UserQueryIdParam;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.SysUserPost;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;

import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl implements ISysUserService {
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);
    @Autowired
    protected Validator validator;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private SysRoleMapper roleMapper;
    @Autowired
    private SysPostMapper postMapper;
    @Autowired
    private SysUserRoleMapper userRoleMapper;
    @Autowired
    private SysUserPostMapper userPostMapper;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private RemoteCustomService remoteCustomService;
    @Autowired
    private ISysDeptService deptService;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user) {

        // 查询用户列表
        List<SysUser> users = userMapper.selectUserList(user);

        String platformParam = user.getPlatform();

        // 构建用户平台权限中文，用户前端展示
        List<SysUser> returnUsers = users.stream().filter(a -> {
            Set<String> platforms = selectPlatformsById(a.getUserId());// 用户拥有的平台权限对应id
            // 如果查询条件中有平台权限并且不是中控，就根据平台权限过滤
            if (StringUtils.isNotBlank(platformParam) && !"1".equals(platformParam)) {
                return platforms.contains(platformParam);
            } else {
                // 构建前端展示
                String platformStr = platforms.stream()
                        .map(b -> PlatformType.getNameById(b))
                        .collect(Collectors.joining(", "));
                a.setPlatformStr(platformStr);
                return true;
            }
        }).collect(Collectors.toList());
        //
        return returnUsers;
    }

    @Override
    public List<SysUser> selectUserListNew(SysUser user) {
        List<SysUser> users = userMapper.selectUserListNew(user);
        // 构建用户平台权限中文，用户前端展示
        users.stream().forEach(a -> {
            String platforms = a.getPlatformStr();
            if (StringUtils.isNotBlank(platforms)) {
                Set<String> platformList = Arrays.stream(platforms.split(",")).collect(Collectors.toSet());
                String platformStr = platformList.stream().map(b -> PlatformType.getNameById(b)).collect(Collectors.joining(", "));
                a.setPlatformStr(platformStr);
            } else {
                a.setPlatformStr(null);
            }
        });

        return users;
    }

    @Override
    public List<SysUser> selectUserByIds(Long[] userIds) {
        return userMapper.selectUserByIds(userIds);
    }

    @Override
    public Map<Long, SysUser> getUserMapByIds(Long[] ids) {
        return userMapper.selectUserByIds(ids).stream().collect(Collectors.toMap(SysUser::getUserId, a -> a));
    }

    @Override
    public List<SysUser> selectUserListByRoleKey(String roleKey) {
        return userMapper.selectUserListByRoleKey(roleKey);
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectAllocatedList(SysUser user) {
        return userMapper.selectAllocatedList(user);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUnallocatedList(SysUser user) {
        return userMapper.selectUnallocatedList(user);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        return userMapper.selectUserById(userId);
    }

    /**
     * 通过用户ID查询用户平台权限
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public Set<String> selectPlatformsById(Long userId) {
        SysUser user = userMapper.selectUserById(userId);

        // PC权限体系
        Set<String> platformList = user.getRoles().stream().map(role -> {
            String roleStr = role.getPlatforms();
            String[] platforms = new String[0];
            if (StringUtils.isNotBlank(roleStr)) {
                platforms = roleStr.split(",");
            }
            return Arrays.asList(platforms);
        }).flatMap(List::stream).collect(Collectors.toSet());

        // 小程序&APP权限体系
        Set<String> roleKeyList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toSet());

        // 合并
        platformList.addAll(roleKeyList);

        return platformList;
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public String checkUserNameUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkUserNameUnique(user.getUserName());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkPhoneUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(user.getPhonenumber());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public String checkAppPhoneUnique(String phone) {
        SysUser info = userMapper.checkPhoneUnique(phone);
        if (StringUtils.isNotNull(info)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkEmailUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId())) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUser(SysUser user) {
        // 新增用户信息
        int rows = userMapper.insertUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);

        if (user.getSourceFrom() != null && "elderPeople".equals(user.getSourceFrom())) {
            return rows;
        }

        // 如果请求来源是从员工模块,则不需要再同步创建员工
        if (user.getSourceFrom() == null || !"employee".equals(user.getSourceFrom())) {
            // 调用远程服务创建员工记录
            try {
                // 同步创建员工信息
                EmployeeInfo employeeInfo = new EmployeeInfo();
                employeeInfo.setName(user.getNickName());
                employeeInfo.setGender(user.getSex());
                employeeInfo.setPhoneNumber(user.getPhonenumber());
                employeeInfo.setDeptId(user.getDeptId());
                if (user.getDeptId() != null) {
                    employeeInfo.setDeptName(deptService.selectDeptById(user.getDeptId()).getDeptName());
                }
                employeeInfo.setPostIds(user.getPostIds());
                if (user.getPostIds() != null && user.getPostIds().length > 0) {
                    List<SysPost> postList = postMapper.selectPostsByUserName(user.getUserName());
                    StringBuffer postName = new StringBuffer();
                    for (SysPost post : postList) {
                        postName.append(post.getPostName()).append(",");
                    }
                    employeeInfo.setPostNames(postName.substring(0, postName.length() - 1));
                }
                employeeInfo.setStatus(user.getStatus());
                // 设置请求来源为用户模块
                employeeInfo.setSourceFrom("user");
                employeeInfo.setUserId(user.getUserId());

                // 调用员工服务创建员工
                remoteCustomService.insertEmployeeInfo(employeeInfo, SecurityConstants.INNER);
            } catch (Exception e) {
                log.error("同步创建员工信息失败: ", e);
                // 这里可以选择忽略异常或者抛出,根据业务需要决定
            }
        }

        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        return userMapper.insertUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUser(SysUser user) {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);

        // 如果请求来源是从员工模块,则不需要同步更新员工
        if (user.getSourceFrom() == null || !"employee".equals(user.getSourceFrom())) {
            // 调用远程服务更新员工记录
            try {
                // 根据userId查询员工信息
                TableDataInfo tableDataInfo = remoteCustomService.selectEmployeeList(userId, SecurityConstants.INNER);

                EmployeeInfo employeeInfo = new EmployeeInfo();
                employeeInfo.setName(user.getNickName());
                employeeInfo.setGender(user.getSex());
                employeeInfo.setPhoneNumber(user.getPhonenumber());
                employeeInfo.setDeptId(user.getDeptId());
                if (user.getDeptId() != null) {
                    employeeInfo.setDeptName(deptService.selectDeptById(user.getDeptId()).getDeptName());
                }
                employeeInfo.setPostIds(user.getPostIds());
                if (user.getPostIds() != null && user.getPostIds().length > 0) {
                    List<SysPost> postList = postMapper.selectPostsByUserName(user.getUserName());
                    StringBuffer postName = new StringBuffer();
                    for (SysPost post : postList) {
                        postName.append(post.getPostName()).append(",");
                    }
                    employeeInfo.setPostNames(postName.substring(0, postName.length() - 1));
                }
                employeeInfo.setStatus(user.getStatus());
                // 设置请求来源为用户模块
                employeeInfo.setSourceFrom("user");
                employeeInfo.setUserId(user.getUserId());

                // 如果员工已存在，则更新，否则创建
                if (tableDataInfo != null && CollectionUtil.isNotEmpty(tableDataInfo.getRows())) {
                    Object row = tableDataInfo.getRows();
                    List<Map> employeeInfos = (List<Map>) row;
                    // 同步更新员工信息
                    employeeInfo.setId(Long.valueOf(employeeInfos.get(0).get("id").toString()));
                    // 调用员工服务更新员工
                    remoteCustomService.updateEmployeeInfo(employeeInfo, SecurityConstants.INNER);
                } else {
                    // 同步创建员工信息
                    remoteCustomService.insertEmployeeInfo(employeeInfo, SecurityConstants.INNER);
                }


            } catch (Exception e) {
                log.error("同步更新员工信息失败: ", e);
                // 这里可以选择忽略异常或者抛出,根据业务需要决定
            }
        }

        return userMapper.updateUser(user);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>();
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            if (list.size() > 0) {
                userPostMapper.batchUserPost(list);
            }
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            if (list.size() > 0) {
                userRoleMapper.batchUserRole(list);
            }
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.deleteUserById(userId);
    }

    /**
     * 通过用户ID删除用户（物理删除）
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.removeUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        return userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.selectConfigByKey("sys.user.initPassword");
        for (SysUser user : userList) {
            try {
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
                if (StringUtils.isNull(u)) {
                    BeanValidators.validateWithException(validator, user);
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    this.insertUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, user);
                    checkUserAllowed(user);
                    checkUserDataScope(user.getUserId());
                    user.setUpdateBy(operName);
                    this.updateUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public List<SysUser> getByQuery(UserQueryIdParam param) {

        Long postId = param.getPostId();
        if (postId != null) {
            return userMapper.selectUserByPostId(postId);

        }

        Long deptId = param.getDeptId();
        if (deptId != null) {
            SysUser sysUser = new SysUser();
            sysUser.setDeptId(deptId);
            return userMapper.selectUserList(sysUser);
        }

        Long roleId = param.getRoleId();
        if (roleId != null) {
            return userMapper.selectUserByRoleId(roleId);
        }

        return null;
    }

    @Override
    public Set<String> getPostPermissionCodeByUserId(Long userId) {
        return userMapper.getPostPermissionCodeByUserId(userId);

    }

    @Override
    public SysUser registerUser2(SysUser sysUser) {
        int r = userMapper.insertUser(sysUser);
        return sysUser;
    }

    /**
     * 全量用户keyValue格式
     *
     * @return
     */
    @Override
    public List<JSONObject> getSysUserValueAndLabelList() {
        return userMapper.getSysUserValueAndLabelList();
    }


    /**
     * 服务人员组成
     */
    @Override
    public List<Map<String, Object>> getServiceStaff(){
        return userMapper.getServiceStaff();
    }
}
