<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.train.mapper.TrainingInfoMapper">

    <resultMap type="TrainingInfo" id="TrainingInfoResult">
        <result property="id" column="id"/>
        <result property="trainingName" column="training_name"/>
        <result property="courseType" column="course_type"/>
        <result property="organizerId" column="organizer_id"/>
        <result property="organizerName" column="organizer_name"/>
        <result property="responsiblePersonId" column="responsible_person_id"/>
        <result property="responsiblePersonName" column="responsible_person_name"/>
        <result property="trainingScope" column="training_scope"/>
        <result property="trainingAgency" column="training_agency"/>
        <result property="trainingLocation" column="training_location"/>
        <result property="plannedStartTrainingTime" column="planned_start_training_time"/>
        <result property="plannedEndTrainingTime" column="planned_end_training_time"/>
        <result property="actualTrainingTime" column="actual_training_time"/>
        <result property="trainingContent" column="training_content"/>
        <result property="trainingPersonnelIds" column="training_personnel_ids"/>
        <result property="trainingEvaluation" column="training_evaluation"/>
        <result property="attachmentUrl" column="attachment_url"/>
    </resultMap>

    <sql id="selectTrainingInfoVo">
        SELECT id,
               training_name,
               course_type,
               organizer_id,
               organizer_name,
               responsible_person_id,
               responsible_person_name,
               training_scope,
               training_agency,
               training_location,
               planned_start_training_time,
               planned_end_training_time,
               actual_training_time,
               training_content,
               training_personnel_ids,
               training_evaluation,
               attachment_url
        FROM t_training_info
    </sql>

    <select id="selectTrainingInfoList" parameterType="TrainingInfo" resultMap="TrainingInfoResult">
        <include refid="selectTrainingInfoVo"/>
        <where>
            <if test="trainingName != null  and trainingName != ''">
                and training_name like concat('%', #{trainingName}, '%')
            </if>
            <if test="courseType != null  and courseType != ''">
                and course_type = #{courseType}
            </if>
            <if test="params.plannedStartTrainingTime != null and params.plannedStartTrainingTime != '' and params.plannedEndTrainingTime != null and params.plannedEndTrainingTime != ''">
                and planned_start_training_time between #{params.plannedStartTrainingTime} and
                #{params.plannedEndTrainingTime}
            </if>
        </where>
    </select>

    <select id="selectTrainingInfoById" parameterType="Long"
            resultMap="TrainingInfoResult">
        <include refid="selectTrainingInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectTrainingRecordList" resultType="com.ruoyi.custom.admin.train.domain.TrainingInfo">
        <include refid="selectTrainingInfoVo"/>
        <where>
            AND training_personnel_ids IS NOT NULL
            <if test="trainingName != null  and trainingName != ''">
                and training_name like concat('%', #{trainingName}, '%')
            </if>
            <if test="courseType != null  and courseType != ''">
                and course_type = #{courseType}
            </if>
            <if test="params.plannedStartTrainingTime != null and params.plannedStartTrainingTime != '' and params.plannedEndTrainingTime != null and params.plannedEndTrainingTime != ''">
                and planned_start_training_time between #{params.plannedStartTrainingTime} and
                #{params.plannedEndTrainingTime}
            </if>
            <if test="params.userId != null">
                and FIND_IN_SET(#{params.userId}, training_personnel_ids)
            </if>
        </where>
    </select>

    <insert id="insertTrainingInfo" parameterType="TrainingInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_training_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="trainingName != null and trainingName != ''">training_name,
            </if>
            <if test="courseType != null and courseType != ''">course_type,
            </if>
            <if test="organizerId != null">organizer_id,
            </if>
            <if test="organizerName != null and organizerName != ''">organizer_name,
            </if>
            <if test="responsiblePersonId != null">responsible_person_id,
            </if>
            <if test="responsiblePersonName != null and responsiblePersonName != ''">responsible_person_name,
            </if>
            <if test="trainingScope != null">training_scope,
            </if>
            <if test="trainingAgency != null">training_agency,
            </if>
            <if test="trainingLocation != null">training_location,
            </if>
            <if test="plannedStartTrainingTime != null">planned_start_training_time,
            </if>
            <if test="plannedEndTrainingTime != null">planned_end_training_time,
            </if>
            <if test="actualTrainingTime != null">actual_training_time,
            </if>
            <if test="trainingContent != null">training_content,
            </if>
            <if test="trainingPersonnelIds != null">training_personnel_ids,
            </if>
            <if test="trainingEvaluation != null">training_evaluation,
            </if>
            <if test="attachmentUrl != null">attachment_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="trainingName != null and trainingName != ''">#{trainingName},
            </if>
            <if test="courseType != null and courseType != ''">#{courseType},
            </if>
            <if test="organizerId != null">#{organizerId},
            </if>
            <if test="organizerName != null and organizerName != ''">#{organizerName},
            </if>
            <if test="responsiblePersonId != null">#{responsiblePersonId},
            </if>
            <if test="responsiblePersonName != null and responsiblePersonName != ''">#{responsiblePersonName},
            </if>
            <if test="trainingScope != null">#{trainingScope},
            </if>
            <if test="trainingAgency != null">#{trainingAgency},
            </if>
            <if test="trainingLocation != null">#{trainingLocation},
            </if>
            <if test="plannedStartTrainingTime != null">#{plannedStartTrainingTime},
            </if>
            <if test="plannedEndTrainingTime != null">#{plannedEndTrainingTime},
            </if>
            <if test="actualTrainingTime != null">#{actualTrainingTime},
            </if>
            <if test="trainingContent != null">#{trainingContent},
            </if>
            <if test="trainingPersonnelIds != null">#{trainingPersonnelIds},
            </if>
            <if test="trainingEvaluation != null">#{trainingEvaluation},
            </if>
            <if test="attachmentUrl != null">#{attachmentUrl},
            </if>
        </trim>
    </insert>

    <update id="updateTrainingInfo" parameterType="TrainingInfo">
        update t_training_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="trainingName != null and trainingName != ''">training_name =
                #{trainingName},
            </if>
            <if test="courseType != null and courseType != ''">course_type =
                #{courseType},
            </if>
            <if test="organizerId != null">organizer_id =
                #{organizerId},
            </if>
            <if test="organizerName != null and organizerName != ''">organizer_name =
                #{organizerName},
            </if>
            <if test="responsiblePersonId != null">responsible_person_id =
                #{responsiblePersonId},
            </if>
            <if test="responsiblePersonName != null and responsiblePersonName != ''">responsible_person_name =
                #{responsiblePersonName},
            </if>
            <if test="trainingScope != null">training_scope =
                #{trainingScope},
            </if>
            <if test="trainingAgency != null">training_agency =
                #{trainingAgency},
            </if>
            <if test="trainingLocation != null">training_location =
                #{trainingLocation},
            </if>
            <if test="plannedStartTrainingTime != null">planned_start_training_time =
                #{plannedStartTrainingTime},
            </if>
            <if test="plannedEndTrainingTime != null">planned_end_training_time =
                #{plannedEndTrainingTime},
            </if>
            <if test="actualTrainingTime != null">actual_training_time =
                #{actualTrainingTime},
            </if>
            <if test="trainingContent != null">training_content =
                #{trainingContent},
            </if>
            <if test="trainingPersonnelIds != null">training_personnel_ids =
                #{trainingPersonnelIds},
            </if>
            <if test="trainingEvaluation != null">training_evaluation =
                #{trainingEvaluation},
            </if>
            <if test="attachmentUrl != null">attachment_url =
                #{attachmentUrl},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTrainingInfoById" parameterType="Long">
        DELETE
        FROM t_training_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteTrainingInfoByIds" parameterType="String">
        delete from t_training_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
