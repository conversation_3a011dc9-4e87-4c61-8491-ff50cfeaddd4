<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.serviceWorkOrder.mapper.ServiceWorkOrderMapper">

    <resultMap type="OrderServiceWork" id="OrderServiceWorkResult">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="placeOrderWay" column="place_order_way"/>
        <result property="elderlyPeopleId" column="elderly_people_id"/>
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="workerId" column="worker_id"/>
        <result property="bedId" column="bed_id"/>
        <result property="liveId" column="live_id"/>
        <result property="status" column="status"/>
        <result property="reserveTime" column="reserve_time"/>
        <result property="serviceId" column="service_id"/>
        <result property="serviceName" column="service_name"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="startImg" column="start_img"/>
        <result property="liveImg" column="live_img"/>
        <result property="endImg" column="end_img"/>
        <result property="serviceTime" column="service_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="number" column="number"/>
        <result property="cycle" column="cycle"/>
    </resultMap>

    <sql id="selectOrderServiceWorkVo">
        SELECT id,
               order_id,
               place_order_way,
               elderly_people_id,
               name,
               phone,
               worker_id,
               bed_id,
               live_id,
               status,
               reserve_time,
               service_id,
               service_name,
               start_time,
               end_time,
               start_img,
               live_img,
               end_img,
               service_time,
               create_time,
               create_by,
               update_time,
               update_by,
               del_flag,
               remark,
               number,
               cycle
        FROM t_order_service_work
    </sql>

    <select id="selectOrderServiceWorkList" parameterType="OrderServiceWork" resultMap="OrderServiceWorkResult">
        <include refid="selectOrderServiceWorkVo"/>
        <where>
            <if test="orderId != null and orderId != ''">and order_id = #{orderId}</if>
            <if test="placeOrderWay != null">and place_order_way = #{placeOrderWay}</if>
            <if test="elderlyPeopleId != null and elderlyPeopleId != ''">and elderly_people_id = #{elderlyPeopleId}</if>
            <if test="workerId != null">and worker_id = #{workerId}</if>
            <if test="bedId != null">and bed_id = #{bedId}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="serviceId != null">and service_id = #{serviceId}</if>
            <if test="serviceTime != null">and service_time = #{serviceTime}</if>
            <if test="reserveTime != null">and reserve_time = #{reserveTime}</if>
        </where>
    </select>

    <select id="selectOrderServiceWorkById" parameterType="String" resultMap="OrderServiceWorkResult">
        <include refid="selectOrderServiceWorkVo"/>
        where id = #{id}
    </select>

    <insert id="insertOrderServiceWork" parameterType="OrderServiceWork">
        insert into t_order_service_work
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="placeOrderWay != null">place_order_way,</if>
            <if test="elderlyPeopleId != null">elderly_people_id,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="workerId != null">worker_id,</if>
            <if test="bedId != null">bed_id,</if>
            <if test="liveId != null">live_id,</if>
            <if test="status != null">status,</if>
            <if test="reserveTime != null">reserve_time,</if>
            <if test="serviceId != null">service_id,</if>
            <if test="serviceName != null">service_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="startImg != null">start_img,</if>
            <if test="liveImg != null">live_img,</if>
            <if test="endImg != null">end_img,</if>
            <if test="serviceTime != null">service_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="number != null">number,</if>
            <if test="cycle != null">cycle,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="placeOrderWay != null">#{placeOrderWay},</if>
            <if test="elderlyPeopleId != null">#{elderlyPeopleId},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="workerId != null">#{workerId},</if>
            <if test="bedId != null">#{bedId},</if>
            <if test="liveId != null">#{liveId},</if>
            <if test="status != null">#{status},</if>
            <if test="reserveTime != null">#{reserveTime},</if>
            <if test="serviceId != null">#{serviceId},</if>
            <if test="serviceName != null">#{serviceName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="startImg != null">#{startImg},</if>
            <if test="liveImg != null">#{liveImg},</if>
            <if test="endImg != null">#{endImg},</if>
            <if test="serviceTime != null">#{serviceTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="number != null">#{number},</if>
            <if test="cycle != null">#{cycle},</if>
        </trim>
    </insert>

    <update id="updateOrderServiceWork" parameterType="OrderServiceWork">
        update t_order_service_work
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="placeOrderWay != null">place_order_way = #{placeOrderWay},</if>
            <if test="elderlyPeopleId != null">elderly_people_id = #{elderlyPeopleId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="workerId != null">worker_id = #{workerId},</if>
            <if test="bedId != null">bed_id = #{bedId},</if>
            <if test="liveId != null">live_id = #{liveId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="reserveTime != null">reserve_time = #{reserveTime},</if>
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="serviceName != null">service_name = #{serviceName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="startImg != null">start_img = #{startImg},</if>
            <if test="liveImg != null">live_img = #{liveImg},</if>
            <if test="endImg != null">end_img = #{endImg},</if>
            <if test="serviceTime != null">service_time = #{serviceTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="number != null">number = #{number},</if>
            <if test="cycle != null">cycle = #{cycle},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOrderServiceWorkByIds" parameterType="Long[]">
        delete from t_order_service_work where id in
        <foreach collection="array" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <select id="appCareTaskList" parameterType="OrderServiceWork" resultType="cn.hutool.json.JSONObject">
        SELECT
        a.id as wordOrderId,
        u.id as userId,
        u.`name` as userName,
        u.sex,
        CONCAT(bud3.`name`,'-',bud2.`name`,'-',bud1.`name`,'-',b.`bed_name`) as groupName,
        c.id as careId,
        c.care_name as careName,
        a.reserve_time as reserveTime,
        a.start_img as startImg,
        a.end_img as endImg,
        a.status
        FROM
        t_order_service_work AS a
        LEFT JOIN t_bed_base_info as b ON b.id = a.bed_id
        LEFT JOIN t_storied_building_info bud1 ON b.room_id = bud1.id
        LEFT JOIN t_storied_building_info bud2 ON bud1.parent_id = bud2.id
        LEFT JOIN t_storied_building_info bud3 ON bud2.parent_id = bud3.id
        left join t_elderly_people_info as u on u.id = a.elderly_people_id
        left join t_care_project_base_info as c on c.id = a.service_id
        left join t_bed_care_info as d on d.bed_id = b.id
        <where>
            d.care_state = '0' and a.status in('2','3')
            <if test="name != null  and name != ''">and u.name like concat ('%',#{name},'%')</if>
            <if test="workerId != null  and workerId != ''">and d.care_worker_id = #{workerId}</if>
        </where>
        order by a.reserve_time
    </select>

    <update id="updateWorkOrderState">
        UPDATE t_order_service_work
        SET status = '5'
        WHERE place_order_way = '2'
          AND cycle = '0'
          AND status = '2'
          AND CURDATE() > reserve_time
    </update>
</mapper>
