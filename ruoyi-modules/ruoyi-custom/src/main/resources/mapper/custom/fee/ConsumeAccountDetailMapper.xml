<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.fee.mapper.ConsumeAccountDetailMapper">

    <resultMap type="ConsumeAccountDetail" id="ConsumeAccountDetailResult">
        <result property="id" column="id"/>
        <result property="elderlyId" column="elderly_id"/>
        <result property="elderlyName" column="elderly_name"/>
        <result property="elderlyPhone" column="elderly_phone"/>
        <result property="type" column="type"/>
        <result property="amount" column="amount"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectConsumeAccountDetailVo">
        SELECT id, elderly_id, elderly_name, elderly_phone, type, amount, create_time
        FROM t_consume_account_detail
    </sql>

    <select id="selectConsumeAccountDetailList" parameterType="ConsumeAccountDetail"
            resultMap="ConsumeAccountDetailResult">
        <include refid="selectConsumeAccountDetailVo"/>
        <where>
            <if test="elderlyId != null  and elderlyId != ''">
                and elderly_id = #{elderlyId}
            </if>
            <if test="elderlyName != null  and elderlyName != ''">
                and elderly_name like concat('%', #{elderlyName}, '%')
            </if>
            <if test="elderlyPhone != null  and elderlyPhone != ''">
                and elderly_phone = #{elderlyPhone}
            </if>
            <if test="type != null  and type != ''">
                and type = #{type}
            </if>
            <if test="amount != null ">
                and amount = #{amount}
            </if>
            <if test="params.startCreateTime != null and params.endCreateTime != null">
                and date_format(create_time, '%y-%m-%d') between date_format(#{params.startCreateTime},'%y-%m-%d') and
                date_format(#{params.endCreateTime},'%y-%m-%d')
            </if>
        </where>
    ORDER BY create_time DESC
    </select>

    <select id="selectConsumeAccountDetailById" parameterType="Long"
            resultMap="ConsumeAccountDetailResult">
        <include refid="selectConsumeAccountDetailVo"/>
        where id = #{id}
    </select>

    <insert id="insertConsumeAccountDetail" parameterType="ConsumeAccountDetail" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_consume_account_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderlyId != null and elderlyId != ''">elderly_id,
            </if>
            <if test="elderlyName != null">elderly_name,
            </if>
            <if test="elderlyPhone != null">elderly_phone,
            </if>
            <if test="type != null">type,
            </if>
            <if test="amount != null">amount,
            </if>
            <if test="createTime != null">create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderlyId != null and elderlyId != ''">#{elderlyId},
            </if>
            <if test="elderlyName != null">#{elderlyName},
            </if>
            <if test="elderlyPhone != null">#{elderlyPhone},
            </if>
            <if test="type != null">#{type},
            </if>
            <if test="amount != null">#{amount},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
        </trim>
    </insert>

    <update id="updateConsumeAccountDetail" parameterType="ConsumeAccountDetail">
        update t_consume_account_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderlyId != null and elderlyId != ''">elderly_id =
                #{elderlyId},
            </if>
            <if test="elderlyName != null">elderly_name =
                #{elderlyName},
            </if>
            <if test="elderlyPhone != null">elderly_phone =
                #{elderlyPhone},
            </if>
            <if test="type != null">type =
                #{type},
            </if>
            <if test="amount != null">amount =
                #{amount},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteConsumeAccountDetailById" parameterType="Long">
        DELETE
        FROM t_consume_account_detail
        WHERE id = #{id}
    </delete>

    <delete id="deleteConsumeAccountDetailByIds" parameterType="String">
        delete from t_consume_account_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
