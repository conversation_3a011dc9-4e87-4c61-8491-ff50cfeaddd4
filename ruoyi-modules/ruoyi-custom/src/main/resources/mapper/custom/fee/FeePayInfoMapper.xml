<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.fee.mapper.FeePayInfoMapper">

    <resultMap type="FeePayInfo" id="FeePayInfoResult">
        <result property="id" column="id"/>
        <result property="payNumber" column="pay_number"/>
        <result property="userId" column="user_id"/>
        <result property="payTime" column="pay_time"/>
        <result property="payType" column="pay_type"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="beginPaidAmount" column="begin_paid_amount"/>
        <result property="afterPaidAmount" column="after_paid_amount"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectFeePayInfoVo">
        SELECT id,
               pay_number,
               user_id,
               pay_time,
               pay_type,
               pay_amount,
               begin_paid_amount,
               after_paid_amount,
               create_time,
               create_by,
               update_time,
               update_by,
               del_flag,
               remark
        FROM t_fee_pay_info
    </sql>

    <select id="selectNewFeePayInfoById" parameterType="String" resultType="FeePayVo">
        SELECT p.*,
               e.NAME                                       userName,
               e.phone,
               TIMESTAMPDIFF(YEAR, e.date_birth, CURDATE()) age,
               p.create_by                                  operatorId,
               p.create_time                                payTime
        FROM t_fee_pay_info p
                 LEFT JOIN t_elderly_people_info e ON p.user_id = e.id
        WHERE p.id = #{id}
    </select>


    <select id="selectNewFeePayInfoList" parameterType="FeePayQueryVo" resultType="FeePayVo">
        SELECT
        p.*,
        e.NAME userName,
        e.phone userPhone,
        p.create_by operatorId,
        p.create_time payTime
        FROM
        t_fee_pay_info p
        LEFT JOIN t_elderly_people_info e ON p.user_id = e.id
        <where>
            <if test="userName != null  and userName != ''">and e.NAME like '%${userName}%'</if>
            <if test="userPhone != null  and userPhone != ''">and e.phone like '%${userPhone}%'</if>
            <if test="beginDate != null and endDate!=null">and date_format(p.create_time, '%y-%m-%d') BETWEEN date_format(#{beginDate},'%y-%m-%d') and date_format(#{endDate},'%y-%m-%d')</if>
            <if test="payType != null">and p.pay_type = #{payType}</if>
            <if test="operatorId != null">and p.create_by = #{operatorId}</if>
<!--            <if test="operatorName != null">and p.create_by like '%${operatorName}%'</if>-->
        </where>
    </select>

    <select id="selectFeePayInfoById" parameterType="String" resultMap="FeePayInfoResult">
        <include refid="selectFeePayInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertFeePayInfo" parameterType="FeePayInfo">
        insert into t_fee_pay_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="payNumber != null">pay_number,</if>
            <if test="userId != null">user_id,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="payType != null">pay_type,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="beginPaidAmount != null">begin_paid_amount,</if>
            <if test="afterPaidAmount != null">after_paid_amount,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="payNumber != null">#{payNumber},</if>
            <if test="userId != null">#{userId},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="beginPaidAmount != null">#{beginPaidAmount},</if>
            <if test="afterPaidAmount != null">#{afterPaidAmount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateFeePayInfo" parameterType="FeePayInfo">
        update t_fee_pay_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="payNumber != null">pay_number = #{payNumber},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="payAmount != null">pay_amount = #{payAmount},</if>
            <if test="beginPaidAmount != null">begin_paid_amount = #{beginPaidAmount},</if>
            <if test="afterPaidAmount != null">after_paid_amount = #{afterPaidAmount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFeePayInfoById" parameterType="String">
        DELETE
        FROM t_fee_pay_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteFeePayInfoByIds" parameterType="String">
        delete from t_fee_pay_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
