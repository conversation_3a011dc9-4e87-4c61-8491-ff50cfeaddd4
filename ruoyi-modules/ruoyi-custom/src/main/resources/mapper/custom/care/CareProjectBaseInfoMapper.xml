<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.care.mapper.CareProjectBaseInfoMapper">

    <resultMap type="CareProjectBaseInfo" id="CareProjectBaseInfoResult">
        <result property="id" column="id"/>
        <result property="serviceType" column="service_type"/>
        <result property="careName" column="care_name"/>
        <result property="staffType" column="staff_type"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>

        <result property="serviceTypeName" column="service_type_name"/>
    </resultMap>

    <sql id="selectCareProjectBaseInfoVo">
        SELECT t.id,
               t.service_type,
               t.care_name,
               t.staff_type,
               t.status,
               t.create_time,
               t.create_by,
               t.update_time,
               t.update_by,
               t.del_flag,
               t.remark,

               t1.type_name AS service_type_name
        FROM t_care_project_base_info AS t
                 LEFT JOIN t_service_type AS t1 ON t.service_type = t1.id
    </sql>

    <select id="selectCareProjectBaseInfoList" parameterType="CareProjectBaseInfo"
            resultMap="CareProjectBaseInfoResult">
        <include refid="selectCareProjectBaseInfoVo"/>
        <where>
            t.del_flag = '0'
            <if test="careName != null  and careName != ''">and t.care_name like concat('%', #{careName}, '%')</if>
            <if test="serviceType != null">and t.service_type = #{serviceType}</if>
            <if test="status != null  and status != ''">and t.status = #{status}</if>
        </where>
        order by t.create_time desc
    </select>

    <select id="selectCareProjectBaseInfoById" parameterType="Long" resultMap="CareProjectBaseInfoResult">
        <include refid="selectCareProjectBaseInfoVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertCareProjectBaseInfo" parameterType="CareProjectBaseInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_care_project_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceType != null">service_type,</if>
            <if test="careName != null">care_name,</if>
            <if test="staffType != null">staff_type,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceType != null">#{serviceType},</if>
            <if test="careName != null">#{careName},</if>
            <if test="staffType != null">#{staffType},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCareProjectBaseInfo" parameterType="CareProjectBaseInfo">
        update t_care_project_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="careName != null">care_name = #{careName},</if>
            <if test="staffType != null">staff_type = #{staffType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCareProjectBaseInfoById" parameterType="Long">
        delete from t_care_project_base_info where id = #{id}
    </delete>

    <delete id="deleteCareProjectBaseInfoByIds" parameterType="String">
        update t_care_project_base_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCareProjectList" resultType="cn.hutool.json.JSONObject">
        select id as value ,care_name as label from t_care_project_base_info where del_flag = '0'
    </select>
</mapper>
