<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.liveManage.mapper.LiveBedRecordsMapper">

    <resultMap type="com.ruoyi.custom.admin.liveManage.domain.LiveBedRecords" id="LiveBedRecordsResult">
        <result property="id" column="id"/>
        <result property="liveId" column="live_id"/>
        <result property="roomId" column="room_id"/>
        <result property="roomVersion" column="room_version"/>
        <result property="bedId" column="bed_id"/>
        <result property="beginDate" column="begin_date"/>
        <result property="endDate" column="end_date"/>
        <result property="billingDate" column="billing_date"/>
        <result property="liveState" column="live_state"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectLiveBedRecordsVo">
        select id, live_id, room_id, room_version, bed_id, begin_date, end_date, billing_date, live_state, remark,
        create_time, create_by, update_time, update_by, del_flag from t_live_bed_records
    </sql>

    <select id="selectLiveBedRecordsList" parameterType="com.ruoyi.custom.admin.liveManage.domain.LiveBedRecords"
            resultMap="LiveBedRecordsResult">
        <include refid="selectLiveBedRecordsVo"/>
        <where>
            <if test="liveId != null  and liveId != ''">and live_id = #{liveId}</if>
            <if test="roomId != null  and roomId != ''">and room_id = #{roomId}</if>
            <if test="roomVersion != null  and roomVersion != ''">and room_version = #{roomVersion}</if>
            <if test="bedId != null  and bedId != ''">and bed_id = #{bedId}</if>
            <if test="beginDate != null ">and begin_date = #{beginDate}</if>
            <if test="endDate != null ">and end_date = #{endDate}</if>
            <if test="billingDate != null ">and billing_date = #{billingDate}</if>
            <if test="liveState != null  and liveState != ''">and live_state = #{liveState}</if>
        </where>
    </select>

    <select id="selectLiveBedRecordsById" parameterType="String" resultMap="LiveBedRecordsResult">
        <include refid="selectLiveBedRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertLiveBedRecords" parameterType="com.ruoyi.custom.admin.liveManage.domain.LiveBedRecords">
        insert into t_live_bed_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="liveId != null">live_id,</if>
            <if test="roomId != null">room_id,</if>
            <if test="roomVersion != null">room_version,</if>
            <if test="bedId != null">bed_id,</if>
            <if test="beginDate != null">begin_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="billingDate != null">billing_date,</if>
            <if test="liveState != null">live_state,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="liveId != null">#{liveId},</if>
            <if test="roomId != null">#{roomId},</if>
            <if test="roomVersion != null">#{roomVersion},</if>
            <if test="bedId != null">#{bedId},</if>
            <if test="beginDate != null">#{beginDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="billingDate != null">#{billingDate},</if>
            <if test="liveState != null">#{liveState},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateLiveBedRecords" parameterType="com.ruoyi.custom.admin.liveManage.domain.LiveBedRecords">
        update t_live_bed_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="liveId != null">live_id = #{liveId},</if>
            <if test="roomId != null">room_id = #{roomId},</if>
            <if test="roomVersion != null">room_version = #{roomVersion},</if>
            <if test="bedId != null">bed_id = #{bedId},</if>
            <if test="beginDate != null">begin_date = #{beginDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="billingDate != null">billing_date = #{billingDate},</if>
            <if test="liveState != null">live_state = #{liveState},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateByLiveIdAndBedId">
        update t_live_bed_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="liveId != null">live_id = #{liveId},</if>
            <if test="roomId != null">room_id = #{roomId},</if>
            <if test="roomVersion != null">room_version = #{roomVersion},</if>
            <if test="bedId != null">bed_id = #{bedId},</if>
            <if test="beginDate != null">begin_date = #{beginDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="billingDate != null">billing_date = #{billingDate},</if>
            <if test="liveState != null">live_state = #{liveState},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where live_id = #{liveId} and bed_id = #{bedId}
    </update>

    <delete id="deleteLiveBedRecordsById" parameterType="String">
        delete from t_live_bed_records where id = #{id}
    </delete>

    <delete id="deleteLiveBedRecordsByIds" parameterType="String">
        delete from t_live_bed_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
