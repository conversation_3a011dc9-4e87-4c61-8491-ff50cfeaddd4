<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.liveManage.mapper.LiveBaseInfoMapper">
    <resultMap type="com.ruoyi.custom.admin.liveManage.domain.LiveBaseInfo" id="LiveBaseInfoResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="liveDate" column="live_date"/>
        <result property="billingDate" column="billing_date"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectLiveBaseInfoVo">
        SELECT id,
               user_id,
               live_date,
               billing_date,
               create_time,
               create_by,
               update_time,
               update_by,
               del_flag,
               remark
        FROM t_live_base_info
    </sql>

    <!--    <select id="selectLiveBaseInfoList" parameterType="com.ruoyi.custom.liveManage.domain.LiveBaseInfo" resultMap="LiveBaseInfoResult">
            <include refid="selectLiveBaseInfoVo"/>
            <where>
                <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                <if test="liveType != null  and liveType != ''"> and  = #{liveType}</if>
                <if test="liveDate != null "> and live_date = #{liveDate}</if>
                <if test="billingDate != null "> and billing_date = #{billingDate}</if>
                <if test="expiredDate != null "> and expired_date = #{expiredDate}</if>
                <if test="discountedAmount != null "> and discounted_amount = #{discountedAmount}</if>
            </where>
        </select>-->

    <select id="selectLiveBaseInfoList" parameterType="com.ruoyi.custom.admin.liveManage.domain.req.LiveQueryVo"
            resultType="LiveInfoRes">
        SELECT
        b.id,
        b.live_date,
        b.billing_date,
        b.state,

        ci.contract_number AS contractNumber,
        ci.contract_start_date AS contractStartDate,
        ci.contract_end_date AS contractEndDate,
        ci.contract_sign_date AS contractSignDate,

        bed.room_id,
        bud1.name AS roomName,
        bed.room_version AS roomVersion,
        bedinfo.id AS bedId,
        CONCAT(bud3.`name`, '-', bud2.`name`, '-', bud1.`name`, '-', bedinfo.bed_name) AS bedName,
        typb.name AS room_type,
        c.care_level,
        c.combo_id,
        c.combo_version,
        com.combo_name,
        b.user_id,
        p.name AS userName,
        p.phone,
        TIMESTAMPDIFF(YEAR, p.date_birth, CURDATE()) AS age,
        IF(bi.amount IS NULL, 0, bi.amount) AS amount,
        IF(balance2.amount IS NULL, 0, balance2.amount) AS securityAmount,

        typi.type_version_id AS roomTypeVersionId

        FROM
        -- 取 t_live_base_info 每个 user_id 最新的一条记录
        (
        SELECT *
        FROM (
        SELECT *,
        ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY create_time DESC) AS rn
        FROM t_live_base_info
        ) AS tmp_b
        WHERE tmp_b.rn = 1
        ) AS b

        INNER JOIN t_elderly_people_info  p ON b.user_id = p.id AND p.del_flag = '0'
        LEFT JOIN t_contract_info ci ON p.id = ci.elderly_people_id AND ci.is_renewable = 0

        -- 取 t_live_combo_records 中每个 live_id 最新的且状态为 0 或 2 的记录
        LEFT JOIN (
        SELECT *
        FROM (
        SELECT *,
        ROW_NUMBER() OVER (PARTITION BY live_id ORDER BY create_time DESC) AS rn
        FROM t_live_combo_records
        WHERE state IN ('0', '2')
        ) AS tmp_c
        WHERE tmp_c.rn = 1
        ) AS c ON b.id = c.live_id

        -- 取 t_live_bed_records 中每个 live_id 最新的且 live_state=0 的记录
        LEFT JOIN (
        SELECT *
        FROM (
        SELECT *,
        ROW_NUMBER() OVER (PARTITION BY live_id ORDER BY create_time DESC) AS rn
        FROM t_live_bed_records
        WHERE live_state = '0'
        ) AS tmp_bed
        WHERE tmp_bed.rn = 1
        ) AS bed ON b.id = bed.live_id

        LEFT JOIN t_bed_base_info bedinfo ON bed.bed_id = bedinfo.id
        LEFT JOIN t_combo_base_info com ON c.combo_id = com.id

        LEFT JOIN t_storied_building_info bud1 ON bed.room_id = bud1.id
        LEFT JOIN t_storied_building_info bud2 ON bud1.parent_id = bud2.id
        LEFT JOIN t_storied_building_info bud3 ON bud2.parent_id = bud3.id

        LEFT JOIN t_room_type_index_info typi
        ON bed.room_id = typi.room_id
        AND bed.room_version = typi.type_version
        AND typi.status = '0'

        LEFT JOIN t_room_type_base_info typb ON typi.type_id = typb.id
        LEFT JOIN t_balance_info bi ON b.user_id = bi.user_id
        LEFT JOIN t_security_balance_info balance2 ON b.user_id = balance2.elderly_id
        <where>
            <if test="userId != null  and userId != ''">and p.id = #{userId}</if>
            <if test="userName != null  and userName != ''">and p.name like '%${userName}%'</if>
            <if test="state != null  and state != ''">and b.state = #{state}</if>
            <if test="roomType != null  and roomType != ''">and typb.id = #{roomType}</if>
        </where>
        order by p.create_time desc
    </select>

    <!--<select id="selectLiveBaseInfoById" parameterType="String" resultMap="LiveBaseInfoResult">
        <include refid="selectLiveBaseInfoVo"/>
        where id = #{id}
    </select>-->

    <select id="selectLiveBaseInfoById" parameterType="com.ruoyi.custom.admin.liveManage.domain.LiveBaseInfo"
            resultType="LiveInfoRes">
        SELECT b.id,
               b.live_date,
               b.billing_date,
               b.state,

               bed.room_id,
               bud1.name                                                                         roomName,
               CONCAT(bud3.`name`, '-', bud2.`name`, '-', bud1.`name`, '-', bedinfo.bed_name) AS bedName,
               typb.name                                                                         room_type,
               c.care_level,
               c.combo_id,
               c.combo_version,
               com.combo_name,
               b.user_id,
               p.name                                                                         AS userName,
               p.phone,
               TIMESTAMPDIFF(YEAR, p.date_birth, CURDATE())                                      age,
               bi.amount,

               con.contract_start_date                                                        AS contractStartDate,
               con.contract_end_date                                                          AS contractEndDate

        FROM t_live_base_info b
                 LEFT JOIN t_elderly_people_info p ON b.user_id = p.id
                 LEFT JOIN t_live_combo_records c ON b.id = c.live_id AND (c.state = 0 OR c.state = 2)
                 LEFT JOIN t_live_bed_records bed ON bed.live_id = b.id AND (bed.live_state = 0 OR bed.live_state = 2)
                 LEFT JOIN t_bed_base_info bedinfo ON bed.bed_id = bedinfo.id
                 LEFT JOIN t_combo_base_info com ON c.combo_id = com.id
                 LEFT JOIN t_storied_building_info bud1 ON bed.room_id = bud1.id
                 LEFT JOIN t_storied_building_info bud2 ON bud1.parent_id = bud2.id
                 LEFT JOIN t_storied_building_info bud3 ON bud2.parent_id = bud3.id
                 LEFT JOIN t_room_type_index_info typi
                           ON bed.room_id = typi.room_id AND bed.room_version = typi.type_version AND
                              typi.status = '0'
                 LEFT JOIN t_room_type_base_info typb ON typi.type_id = typb.id
                 LEFT JOIN t_balance_info bi ON b.user_id = bi.user_id
                 LEFT JOIN t_contract_info con ON con.elderly_people_id = b.user_id AND con.is_renewable = 0
        WHERE b.id = #{id}
        ORDER BY con.contract_number DESC
        LIMIT 1
    </select>

    <select id="selectHistoryList" parameterType="String" resultType="LiveHistoryInfoRes">
        <choose>
            <when test="type==0">

                SELECT bed.begin_date,bed.end_date,bed.live_state
                state,CONCAT(bud3.`name`,'-',bud2.`name`,'-',bud1.`name`) as bedName from t_live_bed_records bed
                LEFT JOIN t_storied_building_info bud1 on bed.room_id= bud1.id
                LEFT JOIN t_storied_building_info bud2 on bud1.parent_id= bud2.id
                LEFT JOIN t_storied_building_info bud3 on bud2.parent_id= bud3.id
                WHERE bed.live_id=#{id} and bed.live_state!=0 and bed.del_flag = '0'
                ORDER BY bed.begin_date desc

            </when>

            <otherwise>

                SELECT com.combo_name,com.care_level,c.live_date begin_date,expired_date end_date,c.state from
                t_live_combo_records c
                LEFT JOIN t_combo_base_info com on c.combo_id=com.id and c.del_flag = '0'
                WHERE c.live_id=#{id} and c.state!=0
                ORDER BY
                c.create_time DESC

            </otherwise>
        </choose>
    </select>

    <select id="getCurrentLiveIdByElderIds" resultType="cn.hutool.json.JSONObject">
        select
        t.live_id as liveId,
        e.id as elderlyId,
        e.name as elderlyName
        from t_live_base_info t
        LEFT JOIN t_elderly_people_info e on t.user_id = e.id
        where t.user_id in
        <foreach collection="list" item="elderlyId" open="(" separator="," close=")">
            #{elderlyId}
        </foreach>
        and t.state = 0 and t.del_flag = '0'
    </select>

    <insert id="insertLiveBaseInfo" parameterType="com.ruoyi.custom.admin.liveManage.domain.LiveBaseInfo">
        insert into t_live_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="liveDate != null">live_date,</if>
            <if test="billingDate != null">billing_date,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="state != null">state,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="liveDate != null">#{liveDate},</if>
            <if test="billingDate != null">#{billingDate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="state != null">#{state},</if>
            <if test="contractDuration != null">#{contractDuration},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="initialPaymentAmount != null">#{initialPaymentAmount},</if>
        </trim>
    </insert>

    <update id="updateLiveBaseInfo" parameterType="com.ruoyi.custom.admin.liveManage.domain.LiveBaseInfo">
        update t_live_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="liveDate != null">live_date = #{liveDate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="state != null">state = #{state},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateLiveInfoResState" parameterType="com.ruoyi.custom.admin.liveManage.domain.res.LiveInfoRes">
        update t_live_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="state != null">state = #{state},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLiveBaseInfoById" parameterType="String">
        DELETE
        FROM t_live_base_info
        WHERE id = #{id}
    </delete>

    <update id="deleteLiveBaseInfoByIds" parameterType="String">
        update from t_live_base_info set del_flag=1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getLivePeopleList" resultType="map">
        SELECT id,
        elder_name name,
        elder_gender sex,
        elder_age age,
        id_card_number idCardNum,
        1 AS type
        FROM t_marketing_customer_info
        <where>
            (del_flag = 0 OR del_flag IS NULL)
            AND customer_type = 1
            AND (id NOT IN (SELECT customer_id FROM t_contract_info)
            AND (direct_check_in IS NULL OR direct_check_in != 1))
            <if test="name != null and name != ''">
                AND elder_name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>

        UNION ALL

        SELECT id,
        name,
        sex,
        age,
        id_card_num idCardNum,
        2 AS type
        FROM t_elderly_people_info
        <where>
            (del_flag = 0 OR del_flag IS NULL)
            AND status != 0
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>
    </select>

</mapper>
