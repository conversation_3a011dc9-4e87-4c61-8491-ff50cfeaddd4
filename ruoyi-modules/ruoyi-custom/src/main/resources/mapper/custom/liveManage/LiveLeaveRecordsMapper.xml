<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.liveManage.mapper.LiveLeaveRecordsMapper">

    <resultMap type="com.ruoyi.custom.admin.liveManage.domain.LiveLeaveRecords" id="LiveLeaveRecordsResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="liveId" column="live_id"/>
        <result property="userName" column="userName"/>
        <result property="age" column="age"/>
        <result property="phone" column="phone"/>
        <result property="leaveTime" column="leave_time"/>
        <result property="expectedBackTime" column="expected_back_time"/>
        <result property="backTime" column="back_time"/>
        <result property="days" column="days"/>
        <result property="leaveReason" column="leave_reason"/>
        <result property="state" column="state"/>
        <result property="auditState" column="audit_state"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="isFreeMeal" column="is_free_meal"/>
        <result property="mealDays" column="meal_days"/>
        <result property="avoidMealDays" column="avoid_meal_days"/>
        <result property="isFreeCare" column="is_free_care"/>
        <result property="avoidCareDays" column="avoid_care_days"/>
        <result property="applicantType" column="applicant_type"/>
        <result property="applicantName" column="applicant_name"/>
        <result property="companionName" column="companion_name"/>
        <result property="companionPhone" column="companion_phone"/>
        <result property="cancelUserId" column="cancel_user_id"/>
        <result property="cancelUserName" column="cancel_user_name"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!--    <sql id="selectLiveLeaveRecordsVo">
            select id, user_id, leave_time, expected_back_time, back_time, leave_type, leave_reason, create_time, create_by, update_time, update_by, del_flag, remark from t_live_leave_records
            LEFT JOIN  t_elderly_people_info on t_live_leave_records.user_id=t_elderly_people_info.id
        </sql>-->

    <sql id="selectLiveLeaveRecordsVo">
        SELECT l.*, p.name AS userName, p.phone, TIMESTAMPDIFF(YEAR, p.date_birth, CURDATE()) age
        FROM t_live_leave_records l
                 LEFT JOIN t_elderly_people_info p ON l.user_id = p.id
    </sql>

    <select id="selectLiveLeaveRecordsList" parameterType="com.ruoyi.custom.admin.liveManage.domain.LiveLeaveRecords"
            resultMap="LiveLeaveRecordsResult">
        SELECT aa.*
        FROM (
        SELECT
        l.id,
        l.user_id,
        l.live_id,
        l.leave_time,
        l.expected_back_time,
        l.back_time,
        IF(leave_time IS NULL,  0, TIMESTAMPDIFF(DAY, leave_time,
        NOW()) + 1  ) AS 'days',
        l.leave_reason,
        CASE
        WHEN l.state IS NOT NULL AND l.state != '' AND l.state = 0   AND NOW() > l.expected_back_time THEN 3
        ELSE l.state
        END AS 'state',
        l.audit_state,
        l.reject_reason,
        l.is_free_meal,
        l.meal_days,
        l.avoid_meal_days,
        l.is_free_care,
        l.avoid_care_days,
        l.applicant_type,
        l.applicant_name,
        l.companion_name,
        l.companion_phone,
        l.returning_name,
        l.returning_type,
        l.cancel_user_id,
        l.cancel_user_name,
        l.create_time,
        l.create_by,
        l.update_time,
        l.update_by,
        l.del_flag,
        l.remark,
        p.name AS userName,
        p.phone,
        TIMESTAMPDIFF(YEAR, p.date_birth, CURDATE()) age
        FROM t_live_leave_records l
        LEFT JOIN t_elderly_people_info p ON l.user_id = p.id
        <where>
            <if test="userId != null  and userId != ''">
                AND l.user_id = #{userId}
            </if>
            <if test="userName != null  and userName != ''">
                AND p.name LIKE '%${userName}%'
            </if>
            <if test="leaveTime != null ">
                AND l.leave_time = #{leaveTime}
            </if>
            <if test="expectedBackTime != null ">
                AND l.expected_back_time = #{expectedBackTime}
            </if>
            <if test="backTime != null ">
                AND l.back_time = #{backTime}
            </if>
            <if test="state != null  and state != ''">
                AND l.state = #{state}
            </if>
            <if test="leaveReason != null  and leaveReason != ''">
                AND l.leave_reason = #{leaveReason}
            </if>
            <if test="cancelUserName != null  and cancelUserName != ''">
                AND l.cancel_user_name LIKE '%${cancelUserName}%'
            </if>
            <if test="params.startLeaveTime != null and params.endLeaveTime != null">
                AND date_format(l.leave_time, '%Y-%m-%d') BETWEEN date_format(#{params.startLeaveTime}, '%Y-%m-%d') AND
                date_format(#{params.endLeaveTime}, '%Y-%m-%d')
            </if>
            <if test="params.notReject != null and params.notReject != ''">
                AND l.audit_state != 2
            </if>
        </where>
        ) aa
        ORDER BY aa.leave_time DESC
    </select>

    <select id="selectLiveLeaveRecordsById" parameterType="String" resultMap="LiveLeaveRecordsResult">
        <include refid="selectLiveLeaveRecordsVo"/>
        where l.id = #{id}
    </select>

    <insert id="insertLiveLeaveRecords" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.ruoyi.custom.admin.liveManage.domain.LiveLeaveRecords">
        INSERT INTO t_live_leave_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="liveId != null">live_id,</if>
            <if test="leaveTime != null">leave_time,</if>
            <if test="expectedBackTime != null">expected_back_time,</if>
            <if test="backTime != null">back_time,</if>
            <if test="leaveReason != null">leave_reason,</if>
            <if test="state != null">state,</if>
            <if test="auditState != null">audit_state,</if>
            <if test="rejectReason != null">reject_reason,</if>
            <if test="isFreeMeal != null">is_free_meal,</if>
            <if test="mealDays != null">meal_days,</if>
            <if test="avoidMealDays != null">avoid_meal_days,</if>
            <if test="isFreeCare != null">is_free_care,</if>
            <if test="avoidCareDays != null">avoid_care_days,</if>
            <if test="applicantType != null">applicant_type,</if>
            <if test="applicantName != null">applicant_name,</if>
            <if test="companionName != null">companion_name,</if>
            <if test="companionPhone != null">companion_phone,</if>
            <if test="returningName != null">returning_name,</if>
            <if test="returningType != null">returning_type,</if>
            <if test="cancelUserId != null">cancel_user_id,</if>
            <if test="cancelUserName != null">cancel_user_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="liveId != null">#{liveId},</if>
            <if test="leaveTime != null">#{leaveTime},</if>
            <if test="expectedBackTime != null">#{expectedBackTime},</if>
            <if test="backTime != null">#{backTime},</if>
            <if test="leaveReason != null">#{leaveReason},</if>
            <if test="state != null">#{state},</if>
            <if test="auditState != null">#{auditState},</if>
            <if test="rejectReason != null">#{rejectReason},</if>
            <if test="isFreeMeal != null">#{isFreeMeal},</if>
            <if test="mealDays != null">#{mealDays},</if>
            <if test="avoidMealDays != null">#{avoidMealDays},</if>
            <if test="isFreeCare != null">#{isFreeCare},</if>
            <if test="avoidCareDays != null">#{avoidCareDays},</if>
            <if test="applicantType != null">#{applicantType},</if>
            <if test="applicantName != null">#{applicantName},</if>
            <if test="companionName != null">#{companionName},</if>
            <if test="companionPhone != null">#{companionPhone},</if>
            <if test="returningName != null">#{returningName},</if>
            <if test="returningType != null">#{returningType},</if>
            <if test="cancelUserId != null">#{cancelUserId},</if>
            <if test="cancelUserName != null">#{cancelUserName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateLiveLeaveRecords" parameterType="com.ruoyi.custom.admin.liveManage.domain.LiveLeaveRecords">
        UPDATE t_live_leave_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="liveId != null">live_id = #{liveId},</if>
            <if test="leaveTime != null">leave_time = #{leaveTime},</if>
            <if test="expectedBackTime != null">expected_back_time = #{expectedBackTime},</if>
            <if test="backTime != null">back_time = #{backTime},</if>
            <if test="leaveReason != null">leave_reason = #{leaveReason},</if>
            <if test="state != null">state = #{state},</if>
            <if test="auditState != null">audit_state = #{auditState},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="isFreeMeal != null">is_free_meal = #{isFreeMeal},</if>
            <if test="mealDays != null">meal_days = #{mealDays},</if>
            <if test="avoidMealDays != null">avoid_meal_days = #{avoidMealDays},</if>
            <if test="isFreeCare != null">is_free_care = #{isFreeCare},</if>
            <if test="avoidCareDays != null">avoid_care_days = #{avoidCareDays},</if>
            <if test="applicantType != null">applicant_type = #{applicantType},</if>
            <if test="applicantName != null">applicant_name = #{applicantName},</if>
            <if test="companionName != null">companion_name = #{companionName},</if>
            <if test="companionPhone != null">companion_phone = #{companionPhone},</if>
            <if test="returningName != null">returning_name = #{returningName},</if>
            <if test="returningType != null">returning_type = #{returningType},</if>
            <if test="cancelUserId != null">cancel_user_id = #{cancelUserId},</if>
            <if test="cancelUserName != null">cancel_user_name = #{cancelUserName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <delete id="deleteLiveLeaveRecordsById" parameterType="String">
        delete from t_live_leave_records where id = #{id}
    </delete>

    <delete id="deleteLiveLeaveRecordsByIds" parameterType="String">
        delete from t_live_leave_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
