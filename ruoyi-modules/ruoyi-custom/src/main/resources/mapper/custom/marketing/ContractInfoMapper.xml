<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.marketing.mapper.ContractInfoMapper">

    <resultMap type="ContractInfo" id="ContractInfoResult">
        <result property="contractNumber" column="contract_number"/>
        <result property="customerId" column="customer_id"/>
        <result property="childNumber" column="child_number"/>
        <result property="elderlyPeopleId" column="elderly_people_id"/>
        <result property="contractEntryUserId" column="contract_entry_user_id"/>
        <result property="contractEntryUser" column="contract_entry_user"/>
        <result property="contractSignDate" column="contract_sign_date"/>
        <result property="contractStartDate" column="contract_start_date"/>
        <result property="contractEndDate" column="contract_end_date"/>
        <result property="attachmentUrl" column="attachment_url"/>
        <result property="bedDiscount" column="bed_discount"/>
        <result property="discountReason" column="discount_reason"/>
        <result property="isRenewable" column="is_renewable"/>
        <result property="feeType" column="fee_type"/>

        <association property="elderlyPeopleInfo" javaType="ElderlyPeopleInfo">
            <id column="id" property="id"/>
            <result column="name" property="name"/>
            <result column="sex" property="sex"/>
            <result column="id_card_num" property="idCardNum"/>
            <result column="phone" property="phone"/>
            <result column="date_birth" property="dateBirth"/>
            <result column="age" property="age"/>
            <result column="nation" property="nation"/>
            <result column="marriage_status" property="marriageStatus"/>
            <result column="living_situation" property="livingSituation"/>
            <result column="home_address" property="homeAddress"/>
            <result column="emergency_contact_name" property="emergencyContactName"/>
            <result column="emergency_contact_phone" property="emergencyContactPhone"/>
            <result column="relation" property="relation"/>
            <result column="economic_sources" property="economicSources"/>
            <result column="monthly_income" property="monthlyIncome"/>
            <result column="social_security_no" property="socialSecurityNo"/>
            <result column="status" property="status"/>
            <result column="img" property="img"/>
            <result column="create_time" property="createTime"/>
            <result column="create_by" property="createBy"/>
            <result column="update_time" property="updateTime"/>
            <result column="update_by" property="updateBy"/>
            <result column="del_flag" property="delFlag"/>
            <result column="remark" property="remark"/>
        </association>

        <association property="marketingCustomerInfo" javaType="MarketingCustomerInfo">
            <result column="marketer_id" property="marketerId"/>
            <result column="marketer" property="marketer"/>
        </association>

        <association property="childContractInfo" javaType="ContractInfo">
            <id column="child_contract_number" property="contractNumber"/>
            <result column="child_customer_id" property="customerId"/>
            <result column="child_child_number" property="childNumber"/>
            <result column="child_elderly_people_id" property="elderlyPeopleId"/>
            <result column="child_contract_entry_user_id" property="contractEntryUserId"/>
            <result column="child_contract_entry_user" property="contractEntryUser"/>
            <result column="child_contract_sign_date" property="contractSignDate"/>
            <result column="child_contract_start_date" property="contractStartDate"/>
            <result column="child_contract_end_date" property="contractEndDate"/>
            <result column="child_attachment_url" property="attachmentUrl"/>
            <result column="child_is_renewable" property="isRenewable"/>
        </association>
    </resultMap>

    <sql id="selectContractInfoVo">
        SELECT t.contract_number,
               t.customer_id,
               t.child_number,
               t.elderly_people_id,
               t.contract_entry_user_id,
               t.contract_entry_user,
               t.contract_sign_date,
               t.contract_start_date,
               t.contract_end_date,
               t.bed_discount,
               t.discount_reason,
               t.attachment_url,
               t.is_renewable,

               t1.id,
               t1.name,
               t1.sex,
               t1.id_card_num,
               t1.phone,
               t1.date_birth,
               t1.age,
               t1.nation,
               t1.marriage_status,
               t1.living_situation,
               t1.home_address,
               t1.emergency_contact_name,
               t1.emergency_contact_phone,
               t1.relation,
               t1.economic_sources,
               t1.monthly_income,
               t1.social_security_no,
               t1.status,
               t1.img,
               t1.create_time,
               t1.create_by,
               t1.update_time,
               t1.update_by,
               t1.del_flag,
               t1.remark,

               t2.marketer_id,
               t2.marketer,

               t3.contract_number        AS child_contract_number,
               t3.customer_id            AS child_customer_id,
               t3.child_number           AS child_child_number,
               t3.elderly_people_id      AS child_elderly_people_id,
               t3.contract_entry_user_id AS child_contract_entry_user_id,
               t3.contract_entry_user    AS child_contract_entry_user,
               t3.contract_sign_date     AS child_contract_sign_date,
               t3.contract_start_date    AS child_contract_start_date,
               t3.contract_end_date      AS child_contract_end_date,
               t3.attachment_url         AS child_attachment_url,
               t3.is_renewable           AS child_is_renewable,

               CASE WHEN t4.fee_type IS NULL THEN '0' ELSE t4.fee_type END AS fee_type

        FROM t_contract_info t
                 LEFT JOIN t_elderly_people_info t1 ON t.elderly_people_id = t1.id
                 LEFT JOIN t_marketing_customer_info t2 ON t.customer_id = t2.id
                 LEFT JOIN t_contract_info t3 ON t.child_number = t3.contract_number
                 LEFT JOIN (SELECT contract_number, fee_type
                            FROM (SELECT contract_number,
                                         fee_type,
                                         ROW_NUMBER() OVER (PARTITION BY contract_number ORDER BY payment_time DESC) AS rn
                                  FROM t_payment_record) tt
                            WHERE rn = 1) t4 ON t.contract_number = t4.contract_number
    </sql>

    <select id="selectContractInfoList" parameterType="ContractInfo" resultMap="ContractInfoResult">
        <include refid="selectContractInfoVo"/>
        <where>
<!--            t.contract_number LIKE 'HT-%'-->
            <if test="params.name != null and params.name != ''">
                and t1.name like concat('%', #{params.name}, '%')
            </if>
            <if test="contractSignDate != null">
                and t.contract_sign_date = #{contractSignDate}
            </if>
            <if test="params.startContractSignDate != null and params.startContractSignDate != '' and params.endContractSignDate != '' and params.endContractSignDate != null">
                and t.contract_sign_date between #{params.startContractSignDate} and #{params.endContractSignDate}
            </if>
            <if test="contractEntryUser != null and contractEntryUser != ''">
                and t.contract_entry_user like concat('%', #{contractEntryUser}, '%')
            </if>
            <if test="customerId != null and customerId != ''">
                and t.customer_id = #{customerId}
            </if>
            <if test="contractNumber != null and contractNumber != ''">
                and t.contract_number = #{contractNumber}
            </if>
            <if test="childNumber != null and childNumber != ''">
                and t.child_number = #{childNumber}
            </if>
            <if test="contractStartDate != null">
                and t.contract_start_date = #{contractStartDate}
            </if>
        </where>
        ORDER BY t.contract_number DESC
    </select>

    <select id="selectContractInfoByContractNumber" parameterType="String"
            resultMap="ContractInfoResult">
        <include refid="selectContractInfoVo"/>
        where t.contract_number = #{contractNumber}
    </select>

    <select id="getContractInfoByCustomerId" resultMap="ContractInfoResult">
        <include refid="selectContractInfoVo"/>
        where t.customer_id = #{customerId}
    </select>

    <select id="selectContractRenewInfoList" parameterType="ContractInfo" resultMap="ContractInfoResult">
        <include refid="selectContractInfoVo"/>
        <where>
            <if test="contractNumber != null and contractNumber != ''">
                and t.contract_number like concat('%', #{contractNumber}, '%')
            </if>
            <if test="contractSignDate != null">
                and t.contract_sign_date = #{contractSignDate}
            </if>
            <if test="contractStartDate != null">
                and t.contract_start_date = #{contractStartDate}
            </if>
            <if test="contractEndDate != null">
                and t.contract_end_date = #{contractEndDate}
            </if>
            <if test="params.elderlyName != null and params.elderlyName != ''">
                and t1.name like concat('%', #{params.elderlyName}, '%')
            </if>
            <if test="params.elderlyPhone != null and params.elderlyPhone != ''">
                and t1.phone like concat('%', #{params.elderlyPhone}, '%')
            </if>
            <if test="params.startContractEndDate != null and params.startContractEndDate != '' and params.endContractEndDate != null and params.endContractEndDate != ''">
                and t.contract_end_date between #{params.startContractEndDate} and #{params.endContractEndDate}
            </if>
        </where>
        ORDER BY t.contract_number DESC
    </select>

    <insert id="insertContractInfo" parameterType="ContractInfo">
        insert into t_contract_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractNumber != null">contract_number,
            </if>
            <if test="customerId != null">customer_id,
            </if>
            <if test="childNumber != null">child_number,
            </if>
            <if test="elderlyPeopleId != null">elderly_people_id,
            </if>
            <if test="contractEntryUserId != null">contract_entry_user_id,
            </if>
            <if test="contractEntryUser != null and contractEntryUser != ''">contract_entry_user,
            </if>
            <if test="contractSignDate != null">contract_sign_date,
            </if>
            <if test="contractStartDate != null">contract_start_date,
            </if>
            <if test="contractEndDate != null">contract_end_date,
            </if>
            <if test="bedDiscount != null">bed_discount,
            </if>
            <if test="discountReason != null and discountReason != ''">discount_reason,
            </if>
            <if test="attachmentUrl != null and attachmentUrl != ''">attachment_url,
            </if>
            <if test="isRenewable != null and isRenewable != ''">is_renewable,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contractNumber != null">#{contractNumber},
            </if>
            <if test="customerId != null">#{customerId},
            </if>
            <if test="childNumber != null">#{childNumber},
            </if>
            <if test="elderlyPeopleId != null">#{elderlyPeopleId},
            </if>
            <if test="contractEntryUserId != null">#{contractEntryUserId},
            </if>
            <if test="contractEntryUser != null and contractEntryUser != ''">#{contractEntryUser},
            </if>
            <if test="contractSignDate != null">#{contractSignDate},
            </if>
            <if test="contractStartDate != null">#{contractStartDate},
            </if>
            <if test="contractEndDate != null">#{contractEndDate},
            </if>
            <if test="bedDiscount != null">#{bedDiscount},
            </if>
            <if test="discountReason != null and discountReason != ''">#{discountReason},
            </if>
            <if test="attachmentUrl != null and attachmentUrl != ''">#{attachmentUrl},
            </if>
            <if test="isRenewable != null and isRenewable != ''">#{isRenewable},
            </if>
        </trim>
    </insert>

    <update id="updateContractInfo" parameterType="ContractInfo">
        update t_contract_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractNumber != null and contractNumber != ''">contract_number =
                #{contractNumber},
            </if>
            <if test="customerId != null">customer_id =
                #{customerId},
            </if>
            <if test="childNumber != null">child_number =
                #{childNumber},
            </if>
            <if test="elderlyPeopleId != null">elderly_people_id =
                #{elderlyPeopleId},
            </if>
            <if test="contractEntryUserId != null">contract_entry_user_id =
                #{contractEntryUserId},
            </if>
            <if test="contractEntryUser != null and contractEntryUser != ''">contract_entry_user =
                #{contractEntryUser},
            </if>
            <if test="contractSignDate != null">contract_sign_date =
                #{contractSignDate},
            </if>
            <if test="contractStartDate != null">contract_start_date =
                #{contractStartDate},
            </if>
            <if test="contractEndDate != null">contract_end_date =
                #{contractEndDate},
            </if>
            <if test="bedDiscount != null">bed_discount =
                #{bedDiscount},
            </if>
            <if test="discountReason != null and discountReason != ''">discount_reason =
                #{discountReason},
            </if>
            <if test="attachmentUrl != null">attachment_url =
                #{attachmentUrl},
            </if>
            <if test="isRenewable != null and isRenewable != ''">is_renewable =
                #{isRenewable},
            </if>
        </trim>
        where contract_number = #{oldContractNumber}
    </update>

    <delete id="deleteContractInfoByContractNumber" parameterType="String">
        DELETE
        FROM t_contract_info
        WHERE contract_number = #{contractNumber}
    </delete>

    <delete id="deleteContractInfoByContractNumbers" parameterType="String">
        delete from t_contract_info where contract_number in
        <foreach item="contractNumber" collection="array" open="(" separator="," close=")">
            #{contractNumber}
        </foreach>
    </delete>

    <select id="selectLiveInfo" resultType="com.ruoyi.custom.admin.marketing.dto.PaymentRecordDTO">
        SELECT
        t.contract_number AS contractNumber,
        t2.id AS elderlyId,
        t2.name AS elderlyName,
        t2.phone AS elderlyPhone,
        t.contract_start_date AS contractStartDate,
        t.contract_end_date AS contractEndDate,
        t.bed_discount AS bedDiscount,
        t.discount_reason AS discountReason,
        IF(contract_start_date IS NULL OR contract_end_date IS NULL, 0, TIMESTAMPDIFF(MONTH, contract_start_date,
        contract_end_date) + 1
        ) AS contractCycle,
        t5.combo_name AS comboName,
        t5.care_level AS careLevel,
        CONCAT(bud3.`name`, '-', bud2.`name`, '-', bud1.`name`, '-', t7.bed_name) AS bedName,
        typi2.name AS roomTypeName,
        typi1.fees AS roomCost,
        t8.month_amount AS comboCost,
        bi.amount AS accountBalance,
        t3.state AS liveState,
        t11.name AS mealName,
        t12.fee AS mealCost,

        t3.id AS liveId,
        t7.id AS bedId,

        -- 拼接请假周期，包含免除费用信息
        IFNULL(
        GROUP_CONCAT(
        CONCAT(
        DATE_FORMAT(t9.leave_time, '%Y/%c/%e'),
        '~',
        DATE_FORMAT(t9.back_time, '%Y/%c/%e'),
        -- 拼接免除费用信息部分
        CASE
        -- 只有当护理费或餐费其中一个被免除且天数大于0时才拼接括号内容
        WHEN (t9.is_free_care = '1' AND t9.avoid_care_days > 0) OR (t9.is_free_meal = '1' AND t9.avoid_meal_days > 0) THEN
        CONCAT(
        '(',
        -- 拼接护理费信息
        CASE WHEN t9.is_free_care = '1' AND t9.avoid_care_days > 0 THEN CONCAT('免除', t9.avoid_care_days, '天护理费') ELSE '' END,
        -- 如果护理费和餐费都被免除且天数大于0，则添加逗号
        IF((t9.is_free_care = '1' AND t9.avoid_care_days > 0) AND (t9.is_free_meal = '1' AND t9.avoid_meal_days > 0), '，', ''),
        -- 拼接餐费信息
        CASE WHEN t9.is_free_meal = '1' AND t9.avoid_meal_days > 0 THEN CONCAT('免除', t9.avoid_meal_days, '天餐费') ELSE '' END,
        ')'
        )
        ELSE '' -- 否则不拼接括号内容
        END
        )
        ORDER BY t9.leave_time ASC
        SEPARATOR '；'
        ),
        ''
        ) AS leaveDates,

        -- 统计总请假天数
        IFNULL(
        SUM(
        CASE
        WHEN t9.leave_time IS NOT NULL AND t9.back_time IS NOT NULL THEN TIMESTAMPDIFF(DAY, t9.leave_time, t9.back_time) + 1
        ELSE 0
        END
        ),
        0
        ) AS leaveDuration

        FROM t_contract_info t
        LEFT JOIN t_elderly_people_info t2 ON t.elderly_people_id = t2.id
        LEFT JOIN t_live_base_info t3 ON t2.id = t3.user_id and (t3.state = '0' OR t3.state = '2')
        LEFT JOIN t_live_combo_records t4 ON t3.id = t4.live_id AND t4.state = '0'
        LEFT JOIN t_combo_base_info t5 ON t4.combo_id = t5.id
        LEFT JOIN t_fee_combo_info t8 ON t5.id = t8.combo_id AND t4.combo_version = t8.version
        LEFT JOIN t_live_bed_records t6 ON t3.id = t6.live_id AND t6.live_state = '0'
        LEFT JOIN t_bed_base_info t7 ON t6.bed_id = t7.id
        LEFT JOIN t_storied_building_info bud1 ON t6.room_id = bud1.id
        LEFT JOIN t_storied_building_info bud2 ON bud1.parent_id = bud2.id
        LEFT JOIN t_storied_building_info bud3 ON bud2.parent_id = bud3.id
        LEFT JOIN t_room_type_index_info typi
        ON t6.room_id = typi.room_id AND t6.room_version = typi.type_version AND typi.status = '0'
        LEFT JOIN t_room_type_version_info typi1 ON typi.type_id = typi1.type_id AND typi1.status = '0'
        LEFT JOIN t_room_type_base_info typi2 ON typi1.type_id = typi2.id
        LEFT JOIN t_live_meal_combo_record t10 ON t3.id = t10.live_id AND t10.status = '0'
        LEFT JOIN t_meal_combo_base t11 ON t10.meal_id = t11.id
        LEFT JOIN t_meal_combo_fee t12 ON t10.meal_fee_id = t12.id
        LEFT JOIN t_balance_info bi ON t2.id = bi.user_id
        LEFT JOIN t_live_leave_records t9 ON t3.id = t9.live_id AND t9.state = '2' AND TIMESTAMPDIFF(DAY, t9.leave_time,
        t9.back_time) >= 7 -- state=1：已消假，leave_time到back_time大于等于7
        <where>
            <if test="contractNumber != null and contractNumber != ''">
                AND t.contract_number = #{contractNumber}
            </if>
        </where>
        GROUP BY t.contract_number
    </select>

    <select id="selectMaxContractNumberByYear" parameterType="String" resultType="String">
        SELECT contract_number
        FROM t_contract_info
        WHERE contract_number REGEXP CONCAT('^CXL-YY-FW-', #{year}, '-003\\([0-9]+\\)$')
        ORDER BY
            -- 提取年份（2024、2025...）并转为数字排序
            CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(contract_number, '-', 4), '-', -1) AS UNSIGNED) DESC,
            -- 再按括号内数字排序
            CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(contract_number, '(', -1), ')', 1) AS UNSIGNED) DESC
        LIMIT 1
    </select>

    <resultMap type="ContractInfo" id="ContractTreeResult">
        <result property="contractNumber" column="contract_number"/>
        <result property="customerId" column="customer_id"/>
        <result property="childNumber" column="child_number"/>
        <result property="elderlyPeopleId" column="elderly_people_id"/>
        <result property="contractEntryUserId" column="contract_entry_user_id"/>
        <result property="contractEntryUser" column="contract_entry_user"/>
        <result property="contractSignDate" column="contract_sign_date"/>
        <result property="contractStartDate" column="contract_start_date"/>
        <result property="contractEndDate" column="contract_end_date"/>
        <result property="attachmentUrl" column="attachment_url"/>
        <result property="bedDiscount" column="bed_discount"/>
        <result property="discountReason" column="discount_reason"/>
        <result property="isRenewable" column="is_renewable"/>
        <result property="feeType" column="fee_type"/>

        <association property="elderlyPeopleInfo" javaType="ElderlyPeopleInfo">
            <id column="id" property="id"/>
            <result column="name" property="name"/>
            <result column="sex" property="sex"/>
            <result column="id_card_num" property="idCardNum"/>
            <result column="phone" property="phone"/>
            <result column="date_birth" property="dateBirth"/>
            <result column="age" property="age"/>
            <result column="nation" property="nation"/>
            <result column="marriage_status" property="marriageStatus"/>
            <result column="living_situation" property="livingSituation"/>
            <result column="home_address" property="homeAddress"/>
            <result column="emergency_contact_name" property="emergencyContactName"/>
            <result column="emergency_contact_phone" property="emergencyContactPhone"/>
            <result column="relation" property="relation"/>
            <result column="economic_sources" property="economicSources"/>
            <result column="monthly_income" property="monthlyIncome"/>
            <result column="social_security_no" property="socialSecurityNo"/>
            <result column="status" property="status"/>
            <result column="img" property="img"/>
            <result column="create_time" property="createTime"/>
            <result column="create_by" property="createBy"/>
            <result column="update_time" property="updateTime"/>
            <result column="update_by" property="updateBy"/>
            <result column="del_flag" property="delFlag"/>
            <result column="remark" property="remark"/>
        </association>

        <association property="marketingCustomerInfo" javaType="MarketingCustomerInfo">
            <result column="marketer_id" property="marketerId"/>
            <result column="marketer" property="marketer"/>
        </association>

        <association property="childContractInfo" javaType="ContractInfo">
            <id column="child_contract_number" property="contractNumber"/>
            <result column="child_customer_id" property="customerId"/>
            <result column="child_child_number" property="childNumber"/>
            <result column="child_elderly_people_id" property="elderlyPeopleId"/>
            <result column="child_contract_entry_user_id" property="contractEntryUserId"/>
            <result column="child_contract_entry_user" property="contractEntryUser"/>
            <result column="child_contract_sign_date" property="contractSignDate"/>
            <result column="child_contract_start_date" property="contractStartDate"/>
            <result column="child_contract_end_date" property="contractEndDate"/>
            <result column="child_attachment_url" property="attachmentUrl"/>
            <result column="child_is_renewable" property="isRenewable"/>
        </association>

        <!-- 递归关联子合同 -->
        <collection property="childContractInfoList" column="contract_number" ofType="ContractInfo"
                    select="selectChildContracts"/>
    </resultMap>

    <!-- 查询子合同的方法 -->
    <select id="selectChildContracts" parameterType="String" resultMap="ContractInfoResult">
        <include refid="selectContractInfoVo"/>
        <where>
            and t.contract_number LIKE concat(#{contractNumber}, '续' , '%')
        </where>
        ORDER BY
        -- 1. 年份 DESC
        CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(t.contract_number, '-', 4), '-', -1) AS UNSIGNED) DESC,
        -- 2. 括号内数字 DESC
        CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(t.contract_number, '(', -2), ')', 1) AS UNSIGNED) DESC,
        -- 3. 续(x) 中的 x 值 DESC
        CAST(
        IF(
        t.contract_number LIKE '%续(%',
        SUBSTRING_INDEX(SUBSTRING_INDEX(t.contract_number, '续(', -1), ')', 1),
        0
        ) AS UNSIGNED
        ) DESC
    </select>


    <select id="selectContractInfoListNew" parameterType="ContractInfo" resultMap="ContractTreeResult">
        <include refid="selectContractInfoVo"/>
        <where>
<!--            and t.contract_number LIKE concat('CXL-YY-FW-','%')-->
            and t.contract_number NOT LIKE concat('%', '续', '%')
            <if test="params.name != null and params.name != ''">
                and t1.name like concat('%', #{params.name}, '%')
            </if>
            <if test="contractSignDate != null">
                and t.contract_sign_date = #{contractSignDate}
            </if>
            <if test="params.startContractSignDate != null and params.startContractSignDate != '' and params.endContractSignDate != '' and params.endContractSignDate != null">
                and t.contract_sign_date between #{params.startContractSignDate} and #{params.endContractSignDate}
            </if>
            <if test="contractEntryUser != null and contractEntryUser != ''">
                and t.contract_entry_user like concat('%', #{contractEntryUser}, '%')
            </if>
            <if test="customerId != null and customerId != ''">
                and t.customer_id = #{customerId}
            </if>
            <if test="contractNumber != null and contractNumber != ''">
                and t.contract_number = #{contractNumber}
            </if>
            <if test="childNumber != null and childNumber != ''">
                and t.child_number = #{childNumber}
            </if>
            <if test="contractStartDate != null">
                and t.contract_start_date = #{contractStartDate}
            </if>
        </where>
        ORDER BY
        -- 提取年份（2024、2025...）并转为数字排序
        CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(t.contract_number, '-', 4), '-', -1) AS UNSIGNED) DESC,
        -- 再按括号内数字排序
        CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(t.contract_number, '(', -1), ')', 1) AS UNSIGNED) DESC
    </select>

    <select id="selectContractRenewInfoListNew" parameterType="ContractInfo" resultMap="ContractTreeResult">
        <include refid="selectContractInfoVo"/>
        <where>
            <!--            and t.contract_number LIKE concat('CXL-YY-FW-','%')-->
            and t.contract_number NOT LIKE concat('%', '续', '%')
            <if test="contractNumber != null and contractNumber != ''">
                and t.contract_number like concat('%', #{contractNumber}, '%')
            </if>
            <if test="contractSignDate != null">
                and t.contract_sign_date = #{contractSignDate}
            </if>
            <if test="contractStartDate != null">
                and t.contract_start_date = #{contractStartDate}
            </if>
            <if test="contractEndDate != null">
                and t.contract_end_date = #{contractEndDate}
            </if>
            <if test="params.elderlyName != null and params.elderlyName != ''">
                and t1.name like concat('%', #{params.elderlyName}, '%')
            </if>
            <if test="params.elderlyPhone != null and params.elderlyPhone != ''">
                and t1.phone like concat('%', #{params.elderlyPhone}, '%')
            </if>
            <if test="params.startContractEndDate != null and params.startContractEndDate != '' and params.endContractEndDate != null and params.endContractEndDate != ''">
                and t.contract_end_date between #{params.startContractEndDate} and #{params.endContractEndDate}
            </if>
        </where>
        ORDER BY
        -- 提取年份（2024、2025...）并转为数字排序
        CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(t.contract_number, '-', 4), '-', -1) AS UNSIGNED) DESC,
        -- 再按括号内数字排序
        CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(t.contract_number, '(', -1), ')', 1) AS UNSIGNED) DESC
    </select>
</mapper>
