<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.marketing.mapper.MarketingCustomerInfoMapper">

    <resultMap type="MarketingCustomerInfo" id="MarketingCustomerInfoResult">
        <result property="id" column="id"/>
        <result property="consultantName" column="consultant_name"/>
        <result property="relationshipWithElder" column="relationship_with_elder"/>
        <result property="consultantPhone" column="consultant_phone"/>
        <result property="consultationDate" column="consultation_date"/>
        <result property="consultationMethod" column="consultation_method"/>
        <result property="otherConsultationMethod" column="other_consultation_method"/>
        <result property="sourceChannel" column="source_channel"/>
        <result property="otherSource" column="other_source"/>
        <result property="marketer" column="marketer"/>
        <result property="marketerId" column="marketer_id"/>
        <result property="selfCare" column="self_care"/>
        <result property="consultationContent" column="consultation_content"/>
        <result property="elderName" column="elder_name"/>
        <result property="elderGender" column="elder_gender"/>
        <result property="elderAge" column="elder_age"/>
        <result property="elderBirthday" column="elder_birthday"/>
        <result property="elderPhone" column="elder_phone"/>
        <result property="idCardNumber" column="id_card_number"/>
        <result property="nation" column="nation"/>
        <result property="maritalStatus" column="marital_status"/>
        <result property="residenceStatus" column="residence_status"/>
        <result property="homeAddress" column="home_address"/>
        <result property="remarks" column="remarks"/>
        <result property="customerType" column="customer_type"/>
        <result property="failedTime" column="failed_time"/>
        <result property="signStatus" column="sign_status"/>
        <result property="appointmentDate" column="appointment_date"/>
        <result property="appointmentPlace" column="appointment_place"/>
        <result property="directCheckIn" column="direct_check_in"/>
        <result property="delFlag" column="del_flag"/>

        <result property="assessmentPlanId" column="assessment_plan_id"/>
        <result property="assessmentStatusStr" column="assessment_status_str"/>
        <result property="contractSignDate" column="contract_sign_date"/>

        <result property="elderlyId" column="elderly_id"/>
        <result property="emergencyContactName" column="emergency_contact_name"/>
        <result property="emergencyContactRelation" column="emergency_contact_relation"/>
        <result property="emergencyContactPhone" column="emergency_contact_phone"/>


        <collection property="followUpList" javaType="List" ofType="MarketingFollowUp"
                    select="selectFollowUpByCustomerId" column="id">
            <id column="id" property="id"/>
            <result column="plan_time" property="planTime"/>
            <result column="follow_up_items" property="followUpItems"/>
            <result column="follow_up_time" property="followUpTime"/>
            <result column="follow_up_status" property="followUpStatus"/>
            <result column="follow_up_situation" property="followUpSituation"/>
        </collection>

<!--        老人家属信息-->
        <collection property="familyInfoList" javaType="List" ofType="ElderlyPeopleFamilyInfo"
                    select="selectFamilyInfoByElderlyId" column="elderly_id">
            <id column="id" property="id"/>
            <result column="name" property="name"/>
            <result column="phone" property="phone"/>
            <result column="relation" property="relation"/>
        </collection>
    </resultMap>

    <select id="selectFollowUpByCustomerId" resultType="MarketingFollowUp">
        SELECT id,
               plan_time,
               follow_up_items,
               follow_up_time,
               follow_up_status,
               follow_up_situation
        FROM t_marketing_follow_up
        WHERE customer_id = #{customerId}
    </select>

    <select id="selectFamilyInfoByElderlyId" resultType="ElderlyPeopleFamilyInfo">
        SELECT id,
               name,
               phone,
               relation
        FROM t_elderly_people_family_info
        WHERE user_id = #{elderlyId}
    </select>

    <sql id="selectMarketingCustomerInfoVo">
        WITH assessment_status AS (SELECT 0 AS id, '未开始' AS name
                                   UNION
                                   SELECT 1, '进行中'
                                   UNION
                                   SELECT 2, '已完成'),
             assessment_assessment_reason AS (SELECT 1 AS id, '初次评估' AS name
                                              UNION
                                              SELECT 2, '入住评估'
                                              UNION
                                              SELECT 3, '即时评估')

        SELECT t.id,
               t.consultant_name,
               t.relationship_with_elder,
               t.consultant_phone,
               t.consultation_date,
               t.consultation_method,
               t.other_consultation_method,
               t.source_channel,
               t.other_source,
               t.marketer,
               t.marketer_id,
               t.self_care,
               t.consultation_content,
               t.elder_name,
               t.elder_gender,
               t.elder_age,
               t.elder_birthday,
               t.elder_phone,
               t.id_card_number,
               t.nation,
               t.marital_status,
               t.residence_status,
               t.home_address,
               t.remarks,
               t.customer_type,
               t.failed_time,
               t.sign_status,
               t.appointment_date,
               t.appointment_place,
               t.direct_check_in,

               t2.id                                                                 AS assessment_plan_id,

               IF(t2.id IS NULL, '未评估',
                  CONCAT(COALESCE(t3.name, '未知'), '：', COALESCE(t4.name, '未知'))) AS assessment_status_str,

               t5.contract_sign_date,

               t6.id AS elderly_id,
               t6.emergency_contact_name,
               t6.relation AS emergency_contact_relation,
               t6.emergency_contact_phone

        FROM t_marketing_customer_info t
                 LEFT JOIN (SELECT *,
                                   ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY id DESC) AS rn
                            FROM t_assessment_plan) t2 ON t.id = t2.customer_id AND t2.rn = 1
                 LEFT JOIN assessment_status t3 ON t2.status = t3.id
                 LEFT JOIN assessment_assessment_reason t4 ON t2.assessment_reason = t4.id
                 LEFT JOIN t_contract_info t5 ON t.id = t5.customer_id and t5.is_renewable = 0
                 LEFT JOIN t_elderly_people_info t6 ON t.id = t6.customer_id
    </sql>

    <select id="selectMarketingCustomerInfoList" parameterType="MarketingCustomerInfo"
            resultMap="MarketingCustomerInfoResult">
        <include refid="selectMarketingCustomerInfoVo"/>
        <where>
            and (t.del_flag = 0 OR t.del_flag IS NULL)
            <if test="consultantName != null  and consultantName != ''">
                and t.consultant_name like concat('%', #{consultantName}, '%')
            </if>
            <if test="relationshipWithElder != null  and relationshipWithElder != ''">
                and t.relationship_with_elder = #{relationshipWithElder}
            </if>
            <if test="consultantPhone != null  and consultantPhone != ''">
                and t.consultant_phone = #{consultantPhone}
            </if>
            <if test="params.beginConsultationDate != null and params.beginConsultationDate != '' and params.endConsultationDate != null and params.endConsultationDate != ''">
                and t.consultation_date between #{params.beginConsultationDate} and #{params.endConsultationDate}
            </if>
            <if test="consultationMethod != null  and consultationMethod != ''">
                and t.consultation_method = #{consultationMethod}
            </if>
            <if test="sourceChannel != null  and sourceChannel != ''">
                and t.source_channel = #{sourceChannel}
            </if>
            <if test="marketer != null  and marketer != ''">
                and t.marketer like concat('%', #{marketer}, '%')
            </if>
            <if test="marketerId != null">
                and t.marketer_id = #{marketerId}
            </if>
            <if test="consultationContent != null  and consultationContent != ''">
                and t.consultation_content = #{consultationContent}
            </if>
            <if test="elderName != null  and elderName != ''">
                and t.elder_name like concat('%', #{elderName}, '%')
            </if>
            <if test="elderGender != null  and elderGender != ''">
                and t.elder_gender = #{elderGender}
            </if>
            <if test="elderAge != null ">
                and t.elder_age = #{elderAge}
            </if>
            <if test="elderPhone != null  and elderPhone != ''">
                and t.elder_phone = #{elderPhone}
            </if>
            <if test="idCardNumber != null  and idCardNumber != ''">
                and t.id_card_number = #{idCardNumber}
            </if>
            <if test="nation != null  and nation != ''">
                and t.nation = #{nation}
            </if>
            <if test="maritalStatus != null  and maritalStatus != ''">
                and t.marital_status = #{maritalStatus}
            </if>
            <if test="residenceStatus != null  and residenceStatus != ''">
                and t.residence_status = #{residenceStatus}
            </if>
            <if test="homeAddress != null  and homeAddress != ''">
                and t.home_address = #{homeAddress}
            </if>
            <if test="remarks != null  and remarks != ''">
                and t.remarks = #{remarks}
            </if>
            <if test="customerType != null  and customerType != ''">
                and t.customer_type = #{customerType}
            </if>
            <if test="params.beginFailedDate != null and params.beginFailedDate != '' and params.endFailedDate != null and params.endFailedDate != ''">
                and t.failed_time between #{params.beginFailedDate} and #{params.endFailedDate}
            </if>
            <if test="params.userFilterType != null and params.userFilterType == 1">
                and (t.id not in (select customer_id from t_contract_info) and (t.direct_check_in is null or
                t.direct_check_in != 1))
            </if>
            <if test="params.userFilterType != null and params.userFilterType == 2">
                and t.id not in (select customer_id from t_contract_info) and NULLIF(TRIM(t.elder_name), '') IS NOT NULL
            </if>
            <if test="signStatus != null and signStatus != ''">
                and t.sign_status = #{signStatus}
            </if>
            <if test="params.condition != null and params.condition != ''">
                and (t.consultant_name like concat('%', #{params.condition}, '%') OR t.consultant_phone like concat('%', #{params.condition}, '%'))
            </if>
            <if test="params.condition2 != null and params.condition2 != ''">
                and (t.elder_name like concat('%', #{params.condition2}, '%') OR t.id_card_number like concat('%', #{params.condition2}, '%'))
            </if>
            <if test="params.queryIds != null and params.queryIds != ''">
                and find_in_set(t.id, #{params.queryIds})
            </if>
        </where>
        ORDER BY t.consultation_date DESC, t.id DESC
    </select>

    <select id="selectMarketingCustomerInfoById" parameterType="Long"
            resultMap="MarketingCustomerInfoResult">
        <include refid="selectMarketingCustomerInfoVo"/>
        where t.id = #{id}
    </select>

    <select id="findIdByName" resultType="java.lang.Long">
        SELECT id
        FROM t_marketing_customer_info
        WHERE marketer = #{marketer}
        LIMIT 1
    </select>

    <select id="selectMarketingCustomerInfoCount" resultType="java.lang.Long">
        select count(1) from t_marketing_customer_info t
        LEFT JOIN t_elderly_people_info t1 ON t.id = t1.customer_id
        <where>
            <if test="marketerId == null">
                and t.marketer_id is null
            </if>
            <if test="marketerId != null">
                and t.marketer_id = #{marketerId}
            </if>
            <if test="customerType == null">
                and t.customer_type is null
            </if>
            <if test="customerType != null">
                and t.customer_type = #{customerType}
            </if>
            <if test="params.notSign != null and params.notSign == 'true'">
                and (t.sign_status IS NULL OR t.sign_status = 0)
            </if>
            <if test="params.notLive != null and params.notLive == 'true'">
                and t1.id IS NULL
            </if>
        </where>
    </select>

    <select id="selectMarketingCustomerInfoList2"
            resultType="com.ruoyi.custom.admin.marketing.domain.MarketingCustomerInfo">
        <include refid="selectMarketingCustomerInfoVo"/>
        <where>
            <if test="marketerId == null">
                and t.marketer_id is null
            </if>
            <if test="marketerId != null">
                and t.marketer_id = #{marketerId}
            </if>
            <if test="customerType == null">
                and t.customer_type is null
            </if>
            <if test="customerType != null">
                and t.customer_type = #{customerType}
            </if>
            <if test="idCardNumber != null and idCardNumber != ''">
                and t.id_card_number = #{idCardNumber}
            </if>
            <if test="signStatus != null">
                and t.sign_status = #{signStatus}
            </if>
            <if test="params.notSign != null and params.notSign == 'true'">
                and (t.sign_status IS NULL OR t.sign_status = 0)
            </if>
            <if test="params.notLive != null and params.notLive == 'true'">
                and t6.id IS NULL
            </if>
            <if test="params.userFilterType != null and params.userFilterType == 1">
                and (t.id not in (select customer_id from t_contract_info) and (t.direct_check_in is null or
                t.direct_check_in != 1))
            </if>
            <if test="params.condition != null and params.condition != ''">
                and (t.consultant_name like concat('%', #{params.condition}, '%') OR t.consultant_phone like concat('%', #{params.condition}, '%'))
            </if>
        </where>
        ORDER BY t.consultation_date DESC, t.id DESC
    </select>

    <insert id="insertMarketingCustomerInfo" parameterType="MarketingCustomerInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_marketing_customer_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="consultantName != null and consultantName != ''">consultant_name,
            </if>
            <if test="relationshipWithElder != null and relationshipWithElder != ''">relationship_with_elder,
            </if>
            <if test="consultantPhone != null and consultantPhone != ''">consultant_phone,
            </if>
            <if test="consultationDate != null">consultation_date,
            </if>
            <if test="consultationMethod != null and consultationMethod != ''">consultation_method,
            </if>
            <if test="otherConsultationMethod != null and otherConsultationMethod != ''">other_consultation_method,
            </if>
            <if test="sourceChannel != null and sourceChannel != ''">source_channel,
            </if>
            <if test="otherSource != null and otherSource != ''">other_source,
            </if>
            <if test="marketer != null and marketer != ''">marketer,
            </if>
            <if test="marketerId != null">marketer_id,
            </if>
            <if test="selfCare != null">self_care,
            </if>
            <if test="consultationContent != null">consultation_content,
            </if>
            <if test="elderName != null and elderName != ''">elder_name,
            </if>
            <if test="elderGender != null and elderGender != ''">elder_gender,
            </if>
            <if test="elderAge != null">elder_age,
            </if>
            <if test="elderBirthday != null">elder_birthday,
            </if>
            <if test="elderPhone != null">elder_phone,
            </if>
            <if test="idCardNumber != null">id_card_number,
            </if>
            <if test="nation != null">nation,
            </if>
            <if test="maritalStatus != null">marital_status,
            </if>
            <if test="residenceStatus != null">residence_status,
            </if>
            <if test="homeAddress != null">home_address,
            </if>
            <if test="remarks != null">remarks,
            </if>
            <if test="customerType != null and customerType != ''">customer_type,
            </if>
            <if test="failedTime != null">failed_time,
            </if>
            <if test="signStatus != null and signStatus != ''">sign_status,
            </if>
            <if test="appointmentDate != null">appointment_date,
            </if>
            <if test="appointmentPlace != null and appointmentPlace != ''">
                appointment_place,
            </if>
            <if test="directCheckIn != null">direct_check_in,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="consultantName != null and consultantName != ''">#{consultantName},
            </if>
            <if test="relationshipWithElder != null and relationshipWithElder != ''">#{relationshipWithElder},
            </if>
            <if test="consultantPhone != null and consultantPhone != ''">#{consultantPhone},
            </if>
            <if test="consultationDate != null">#{consultationDate},
            </if>
            <if test="consultationMethod != null and consultationMethod != ''">#{consultationMethod},
            </if>
            <if test="otherConsultationMethod != null and otherConsultationMethod != ''">#{otherConsultationMethod},
            </if>
            <if test="sourceChannel != null and sourceChannel != ''">#{sourceChannel},
            </if>
            <if test="otherSource != null and otherSource != ''">#{otherSource},
            </if>
            <if test="marketer != null and marketer != ''">#{marketer},
            </if>
            <if test="marketerId != null">#{marketerId},
            </if>
            <if test="selfCare != null">#{selfCare},
            </if>
            <if test="consultationContent != null">#{consultationContent},
            </if>
            <if test="elderName != null and elderName != ''">#{elderName},
            </if>
            <if test="elderGender != null and elderGender != ''">#{elderGender},
            </if>
            <if test="elderAge != null">#{elderAge},
            </if>
            <if test="elderBirthday != null">#{elderBirthday},
            </if>
            <if test="elderPhone != null">#{elderPhone},
            </if>
            <if test="idCardNumber != null">#{idCardNumber},
            </if>
            <if test="nation != null">#{nation},
            </if>
            <if test="maritalStatus != null">#{maritalStatus},
            </if>
            <if test="residenceStatus != null">#{residenceStatus},
            </if>
            <if test="homeAddress != null">#{homeAddress},
            </if>
            <if test="remarks != null">#{remarks},
            </if>
            <if test="customerType != null and customerType != ''">#{customerType},
            </if>
            <if test="failedTime != null">#{failedTime},
            </if>
            <if test="signStatus != null and signStatus != ''">#{signStatus},
            </if>
            <if test="appointmentDate != null">#{appointmentDate},
            </if>
            <if test="appointmentPlace != null and appointmentPlace != ''">
                #{appointmentPlace},
            </if>
            <if test="directCheckIn != null">#{directCheckIn},
            </if>
        </trim>
    </insert>

    <update id="updateMarketingCustomerInfo" parameterType="MarketingCustomerInfo">
        update t_marketing_customer_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="consultantName != null">consultant_name =
                #{consultantName},
            </if>
            <if test="relationshipWithElder != null">relationship_with_elder =
                #{relationshipWithElder},
            </if>
            <if test="consultantPhone != null">consultant_phone =
                #{consultantPhone},
            </if>
            <if test="consultationDate != null">consultation_date =
                #{consultationDate},
            </if>
            <if test="consultationMethod != null">consultation_method =
                #{consultationMethod},
            </if>
            <if test="otherConsultationMethod != null">other_consultation_method =
                #{otherConsultationMethod},
            </if>
            <if test="sourceChannel != null">source_channel =
                #{sourceChannel},
            </if>
            <if test="otherSource != null">other_source =
                #{otherSource},
            </if>
            <if test="marketer != null">marketer =
                #{marketer},
            </if>
            <if test="marketerId != null">marketer_id =
                #{marketerId},
            </if>
            <if test="selfCare != null">self_care =
                #{selfCare},
            </if>
            <if test="consultationContent != null">consultation_content =
                #{consultationContent},
            </if>
            <if test="elderName != null">elder_name =
                #{elderName},
            </if>
            <if test="elderGender != null">elder_gender =
                #{elderGender},
            </if>
            <if test="elderAge != null">elder_age =
                #{elderAge},
            </if>
            <if test="elderBirthday != null">elder_birthday =
                #{elderBirthday},
            </if>
            <if test="elderPhone != null">elder_phone =
                #{elderPhone},
            </if>
            <if test="idCardNumber != null">id_card_number =
                #{idCardNumber},
            </if>
            <if test="nation != null">nation =
                #{nation},
            </if>
            <if test="maritalStatus != null">marital_status =
                #{maritalStatus},
            </if>
            <if test="residenceStatus != null">residence_status =
                #{residenceStatus},
            </if>
            <if test="homeAddress != null">home_address =
                #{homeAddress},
            </if>
            <if test="remarks != null">remarks =
                #{remarks},
            </if>
            <if test="customerType != null">customer_type =
                #{customerType},
            </if>
            <if test="failedTime != null">failed_time =
                #{failedTime},
            </if>
            <if test="signStatus != null">sign_status =
                #{signStatus},
            </if>
            <if test="appointmentDate != null">appointment_date =
                #{appointmentDate},
            </if>
            <if test="appointmentPlace != null">
                appointment_place =
                #{appointmentPlace},
            </if>
            <if test="directCheckIn != null">direct_check_in =
                #{directCheckIn},
            </if>
        </trim>
        where id = #{id}
    </update>

    <update id="recover" parameterType="Long">
        update t_marketing_customer_info
        set customer_type = '1', failed_time = null
        <foreach collection="array" item="id" open="where id in (" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="updateMarketingCustomerInfo2" parameterType="MarketingCustomerInfo">
        update t_marketing_customer_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="consultantName != null and consultantName != ''">consultant_name =
                #{consultantName},
            </if>
            <if test="relationshipWithElder != null and relationshipWithElder != ''">relationship_with_elder =
                #{relationshipWithElder},
            </if>
            <if test="consultantPhone != null and consultantPhone != ''">consultant_phone =
                #{consultantPhone},
            </if>
            <if test="consultationDate != null">consultation_date =
                #{consultationDate},
            </if>
            <if test="consultationMethod != null and consultationMethod != ''">consultation_method =
                #{consultationMethod},
            </if>
            <if test="otherConsultationMethod != null and otherConsultationMethod != ''">other_consultation_method =
                #{otherConsultationMethod},
            </if>
            <if test="sourceChannel != null and sourceChannel != ''">source_channel =
                #{sourceChannel},
            </if>
            <if test="otherSource != null and otherSource != ''">other_source =
                #{otherSource},
            </if>
            marketer = #{marketer},
            marketer_id = #{marketerId},
            <if test="selfCare != null">self_care =
                #{selfCare},
            </if>
            <if test="consultationContent != null">consultation_content =
                #{consultationContent},
            </if>
            <if test="elderName != null and elderName != ''">elder_name =
                #{elderName},
            </if>
            <if test="elderGender != null and elderGender != ''">elder_gender =
                #{elderGender},
            </if>
            <if test="elderAge != null">elder_age =
                #{elderAge},
            </if>
            <if test="elderBirthday != null">elder_birthday =
                #{elderBirthday},
            </if>
            <if test="elderPhone != null">elder_phone =
                #{elderPhone},
            </if>
            <if test="idCardNumber != null">id_card_number =
                #{idCardNumber},
            </if>
            <if test="nation != null">nation =
                #{nation},
            </if>
            <if test="maritalStatus != null">marital_status =
                #{maritalStatus},
            </if>
            <if test="residenceStatus != null">residence_status =
                #{residenceStatus},
            </if>
            <if test="homeAddress != null">home_address =
                #{homeAddress},
            </if>
            <if test="remarks != null">remarks =
                #{remarks},
            </if>
            <if test="customerType != null and customerType != ''">customer_type =
                #{customerType},
            </if>
            <if test="failedTime != null">failed_time =
                #{failedTime},
            </if>
            <if test="signStatus != null and signStatus != ''">sign_status =
                #{signStatus},
            </if>
            <if test="appointmentDate != null">appointment_date =
                #{appointmentDate},
            </if>
            <if test="appointmentPlace != null and appointmentPlace != ''">
                appointment_place =
                #{appointmentPlace},
            </if>
            <if test="directCheckIn != null">direct_check_in =
                #{directCheckIn},
            </if>
        </trim>
        where id = #{id}
    </update>

    <!--    <delete id="deleteMarketingCustomerInfoById" parameterType="Long">-->
    <!--        DELETE-->
    <!--        FROM t_marketing_customer_info-->
    <!--        WHERE id = #{id}-->
    <!--    </delete>-->

    <!--    逻辑删除-->
    <update id="deleteMarketingCustomerInfoById" parameterType="Long">
        UPDATE t_marketing_customer_info
        SET del_flag = '1'
        WHERE id = #{id}
    </update>

    <delete id="deleteMarketingCustomerInfoByIds" parameterType="Long">
        delete from t_marketing_customer_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
