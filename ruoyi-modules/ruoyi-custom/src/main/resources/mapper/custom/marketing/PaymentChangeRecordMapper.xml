<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.marketing.mapper.PaymentChangeRecordMapper">

    <resultMap type="PaymentChangeRecord" id="PaymentChangeRecordResult">
        <result property="id" column="id"/>
        <result property="liveId" column="live_id"/>
        <result property="contractNumber" column="contract_number"/>
        <result property="elderlyId" column="elderly_id"/>
        <result property="elderlyName" column="elderly_name"/>
        <result property="contractStartDate" column="contract_start_date"/>
        <result property="contractEndDate" column="contract_end_date"/>
        <result property="contractCycle" column="contract_cycle"/>
        <result property="careLevel" column="care_level"/>
        <result property="bedName" column="bed_name"/>
        <result property="accountAddCost" column="account_add_cost"/>
        <result property="remark" column="remark"/>
        <result property="details" column="details" typeHandler="com.ruoyi.custom.config.mybatis.handler.ChangeDetailListJsonTypeHandler"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectPaymentChangeRecordVo">
        SELECT id,
               live_id,
               contract_number,
               elderly_id,
               elderly_name,
               contract_start_date,
               contract_end_date,
               contract_cycle,
               care_level,
               bed_name,
               account_add_cost,
               remark,
               create_time,
               details
        FROM t_payment_change_record
    </sql>

    <select id="selectPaymentChangeRecordList" parameterType="PaymentChangeRecord"
            resultMap="PaymentChangeRecordResult">
        <include refid="selectPaymentChangeRecordVo"/>
        <where>
            <if test="contractNumber != null  and contractNumber != ''">
                and contract_number = #{contractNumber}
            </if>
            <if test="elderlyId != null  and elderlyId != ''">
                and elderly_id = #{elderlyId}
            </if>
            <if test="elderlyName != null  and elderlyName != ''">
                and elderly_name like concat('%', #{elderlyName}, '%')
            </if>
            <if test="contractStartDate != null ">
                and contract_start_date = #{contractStartDate}
            </if>
            <if test="contractEndDate != null ">
                and contract_end_date = #{contractEndDate}
            </if>
            <if test="contractCycle != null ">
                and contract_cycle = #{contractCycle}
            </if>
            <if test="careLevel != null  and careLevel != ''">
                and care_level = #{careLevel}
            </if>
            <if test="bedName != null  and bedName != ''">
                and bed_name like concat('%', #{bedName}, '%')
            </if>
            <if test="details != null  and details != ''">
                and details = #{details}
            </if>
            <if test="params.isMealFee != null and params.isMealFee == true">
                AND JSON_CONTAINS(details, '{"type":"mealFee"}', '$')
            </if>
            <if test="params.queryType != null and params.queryType = '1'">
                AND NOT JSON_CONTAINS(details, '{"type":"mealFee"}', '$')
            </if>
            <if test="params.queryType != null and params.queryType = '2'">
                AND JSON_CONTAINS(details, '{"type":"mealFee"}', '$')
            </if>
            <if test="createTime != null">
                and date_format(create_time,'%y-%m-%d') = date_format(#{createTime},'%y-%m-%d')
            </if>
        </where>
    </select>

    <select id="selectPaymentChangeRecordById" parameterType="String"
            resultMap="PaymentChangeRecordResult">
        <include refid="selectPaymentChangeRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertPaymentChangeRecord" parameterType="PaymentChangeRecord">
        insert into t_payment_change_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="liveId != null">live_id,
            </if>
            <if test="contractNumber != null and contractNumber != ''">contract_number,
            </if>
            <if test="elderlyId != null and elderlyId != ''">elderly_id,
            </if>
            <if test="elderlyName != null">elderly_name,
            </if>
            <if test="contractStartDate != null">contract_start_date,
            </if>
            <if test="contractEndDate != null">contract_end_date,
            </if>
            <if test="contractCycle != null">contract_cycle,
            </if>
            <if test="careLevel != null">care_level,
            </if>
            <if test="bedName != null">bed_name,
            </if>
            <if test="accountAddCost != null">account_add_cost,
            </if>
            <if test="remark != null">remark,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="details != null">details,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="liveId != null">#{liveId},
            </if>
            <if test="contractNumber != null and contractNumber != ''">#{contractNumber},
            </if>
            <if test="elderlyId != null and elderlyId != ''">#{elderlyId},
            </if>
            <if test="elderlyName != null">#{elderlyName},
            </if>
            <if test="contractStartDate != null">#{contractStartDate},
            </if>
            <if test="contractEndDate != null">#{contractEndDate},
            </if>
            <if test="contractCycle != null">#{contractCycle},
            </if>
            <if test="careLevel != null">#{careLevel},
            </if>
            <if test="bedName != null">#{bedName},
            </if>
            <if test="accountAddCost != null">#{accountAddCost},
            </if>
            <if test="remark != null">#{remark},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="details != null">
                #{details,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.ChangeDetailListJsonTypeHandler},
            </if>
        </trim>
    </insert>

    <update id="updatePaymentChangeRecord" parameterType="PaymentChangeRecord">
        update t_payment_change_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="liveId != null">live_id = #{liveId},
            </if>
            <if test="contractNumber != null and contractNumber != ''">contract_number =
                #{contractNumber},
            </if>
            <if test="elderlyId != null and elderlyId != ''">elderly_id =
                #{elderlyId},
            </if>
            <if test="elderlyName != null">elderly_name =
                #{elderlyName},
            </if>
            <if test="contractStartDate != null">contract_start_date =
                #{contractStartDate},
            </if>
            <if test="contractEndDate != null">contract_end_date =
                #{contractEndDate},
            </if>
            <if test="contractCycle != null">contract_cycle =
                #{contractCycle},
            </if>
            <if test="careLevel != null">care_level =
                #{careLevel},
            </if>
            <if test="bedName != null">bed_name =
                #{bedName},
            </if>
            <if test="accountAddCost != null">account_add_cost =
                #{accountAddCost},
            </if>
            <if test="remark != null">remark =
                #{remark},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="details != null">details =
                #{details,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.ChangeDetailListJsonTypeHandler},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePaymentChangeRecordById" parameterType="String">
        DELETE
        FROM t_payment_change_record
        WHERE id = #{id}
    </delete>

    <delete id="deletePaymentChangeRecordByIds" parameterType="String">
        delete from t_payment_change_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectMaxId" resultType="java.lang.String">
        SELECT MAX(id)
        FROM t_payment_change_record
    </select>

    <select id="selectByContractNumberOrderByIdDesc" resultMap="PaymentChangeRecordResult">
        <include refid="selectPaymentChangeRecordVo"/>
        where contract_number = #{contractNumber}
        order by id DESC
    </select>
</mapper>
