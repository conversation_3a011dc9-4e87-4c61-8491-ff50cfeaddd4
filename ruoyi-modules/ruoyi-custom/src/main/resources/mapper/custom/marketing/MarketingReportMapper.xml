<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.marketing.mapper.MarketingReportMapper">

    <select id="conversionRate" resultType="cn.hutool.json.JSONObject">
        WITH month_list AS (
            SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 5 MONTH), '%Y-%m') AS month
            UNION ALL
            SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 4 MONTH), '%Y-%m')
            UNION ALL
            SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 3 MONTH), '%Y-%m')
            UNION ALL
            SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 2 MONTH), '%Y-%m')
            UNION ALL
            SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m')
            UNION ALL
            SELECT DATE_FORMAT(CURDATE(), '%Y-%m')
        ),

        -- 咨询客户数量统计
             consultation_count AS (
                 SELECT
                     DATE_FORMAT(m.consultation_date, '%Y-%m') AS month,
                     COUNT(m.id) AS consultationCount
                 FROM t_marketing_customer_info m
                 GROUP BY DATE_FORMAT(m.consultation_date, '%Y-%m')
             ),

        -- 合同数量统计（只统计满足合同编号规则的）
             contract_count AS (
                 SELECT
                     DATE_FORMAT(c.contract_sign_date, '%Y-%m') AS month,
                     COUNT(c.contract_number) AS contractCount
                 FROM t_contract_info c
                 WHERE c.contract_number REGEXP '^CXL-YY-FW-[0-9]{4}-003\\([0-9]+\\)$'
                 GROUP BY DATE_FORMAT(c.contract_sign_date, '%Y-%m')
             )

        -- 合并三个表
        SELECT
            ml.month,
            COALESCE(cc.contractCount, 0) AS contractCount,
            COALESCE(mc.consultationCount, 0) AS consultationCount,
            COALESCE(
                    ROUND(
                            (IFNULL(cc.contractCount, 0) / IFNULL(mc.consultationCount, 1)) * 100,
                            2
                    ), 0
            ) AS conversionRate
        FROM month_list ml
                 LEFT JOIN consultation_count mc ON ml.month = mc.month
                 LEFT JOIN contract_count cc ON ml.month = cc.month
        ORDER BY ml.month ASC;

    </select>

    <select id="followUpCount" resultType="cn.hutool.json.JSONObject">
        WITH date_list AS (
            SELECT CURDATE() - INTERVAL (14 - num) DAY AS date
            FROM (
                     SELECT 0 AS num UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4
                     UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9
                     UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14
                 ) AS numbers
        )

        SELECT
            DATE_FORMAT(d.date, '%m-%d') AS followUpDate,
            COUNT(f.id) AS completedFollowUps
        FROM
            date_list d
                LEFT JOIN
            t_marketing_follow_up f
            ON DATE(f.follow_up_time) = d.date AND f.follow_up_status = '1'
        GROUP BY
            d.date
        ORDER BY
            d.date ASC;
    </select>
</mapper>
