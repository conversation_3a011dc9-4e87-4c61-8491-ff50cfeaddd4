<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.storiedBuilding.mapper.StoriedBuildingInfoMapper">

    <resultMap type="com.ruoyi.custom.admin.storiedBuilding.domain.StoriedBuildingInfo" id="StoriedBuildingInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="sort" column="sort"/>
        <result property="totalFloorsNumber" column="total_floors_number"/>
        <result property="totalRoomsNumber" column="total_rooms_number"/>
        <result property="totalBedNumber" column="total_bed_number"/>
        <result property="occupancyNumber" column="occupancy_number"/>
        <result property="roomType" column="room_type"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="remark" column="remark"/>
        <result property="roomTypeName" column="roomTypeName"/>
    </resultMap>

    <sql id="selectStoriedBuildingInfoVo">
        SELECT a.id,
               a.name,
               a.type,
               a.sort,
               a.total_floors_number,
               a.total_rooms_number,
               a.total_bed_number,
               a.serial_number,
               IFNULL(a.occupancy_number, 0) AS occupancy_number,
               a.room_type,
               a.parent_id,
               a.ancestors,
               a.create_time,
               a.create_by,
               a.update_time,
               a.update_by,
               a.del_flag,
               a.remark,

               t.name                        AS roomTypeName
        FROM t_storied_building_info a
                 LEFT JOIN t_room_type_base_info AS t ON a.room_type = t.id
    </sql>

    <select id="selectStoriedBuildingInfoList"
            parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.StoriedBuildingInfo"
            resultMap="StoriedBuildingInfoResult">
        SELECT a.id,
        a.name,
        a.type,
        a.sort,
        a.total_floors_number,
        a.total_rooms_number,
        a.total_bed_number,
        a.serial_number,
        a.room_type,
        a.parent_id,
        a.ancestors,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark,

        t.name AS roomTypeName,
        SUM(IF(live_state = 0, 1, 0)) AS occupancy_number
        FROM t_storied_building_info a
        LEFT JOIN t_room_type_base_info AS t ON a.room_type = t.id
        LEFT JOIN t_live_bed_records AS ve ON ve.room_id = a.id
        <where>
            a.del_flag = '0'
            <if test="name != null  and name != ''">and a.name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''">and a.type = #{type}</if>
            <if test="serialNumber != null  and serialNumber != ''">and a.serial_number = #{serialNumber}</if>
            <if test="sort != null ">and a.sort = #{sort}</if>
            <if test="totalFloorsNumber != null ">and a.total_floors_number = #{totalFloorsNumber}</if>
            <if test="totalRoomsNumber != null ">and a.total_rooms_number = #{totalRoomsNumber}</if>
            <if test="totalBedNumber != null ">and a.total_bed_number = #{totalBedNumber}</if>
            <if test="occupancyNumber != null ">and a.occupancy_number = #{occupancyNumber}</if>
            <if test="roomType != null  and roomType != ''">and a.room_type = #{roomType}</if>
            <if test="parentId != null ">and a.parent_id = #{parentId}</if>
            <if test="ancestors != null  and ancestors != ''">and a.ancestors = #{ancestors}</if>
        </where>
        GROUP BY a.id
        ORDER BY ISNULL(a.sort) ASC, a.sort, a.id
    </select>

    <select id="selectStoriedBuildingInfoById" parameterType="Long" resultMap="StoriedBuildingInfoResult">
        <include refid="selectStoriedBuildingInfoVo"/>
        where a.id = #{id}
    </select>

    <select id="getInfoByPid" parameterType="Long" resultMap="StoriedBuildingInfoResult">
        <include refid="selectStoriedBuildingInfoVo"/>
        where a.parent_id = #{pid}
    </select>

    <insert id="insertStoriedBuildingInfo" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.StoriedBuildingInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_storied_building_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="sort != null">sort,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="totalFloorsNumber != null">total_floors_number,</if>
            <if test="totalRoomsNumber != null">total_rooms_number,</if>
            <if test="totalBedNumber != null">total_bed_number,</if>
            <if test="occupancyNumber != null">occupancy_number,</if>
            <if test="roomType != null">room_type,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="sort != null">#{sort},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="totalFloorsNumber != null">#{totalFloorsNumber},</if>
            <if test="totalRoomsNumber != null">#{totalRoomsNumber},</if>
            <if test="totalBedNumber != null">#{totalBedNumber},</if>
            <if test="occupancyNumber != null">#{occupancyNumber},</if>
            <if test="roomType != null">#{roomType},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
        <selectKey keyProperty="id" resultType="java.lang.Long">
            select LAST_INSERT_ID() as id
        </selectKey>
    </insert>

    <update id="updateStoriedBuildingInfo" parameterType="com.ruoyi.custom.admin.storiedBuilding.domain.StoriedBuildingInfo">
        update t_storied_building_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            sort = #{sort},
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="totalFloorsNumber != null">total_floors_number = #{totalFloorsNumber},</if>
            <if test="totalRoomsNumber != null">total_rooms_number = #{totalRoomsNumber},</if>
            <if test="totalBedNumber != null">total_bed_number = #{totalBedNumber},</if>
            <if test="occupancyNumber != null">occupancy_number = #{occupancyNumber},</if>
            <if test="roomType != null">room_type = #{roomType},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateStoriedBedNum" parameterType="LONG">
        update t_storied_building_info set total_bed_number = #{totalBedNumber},occupancy_number = #{occupancyNumber}
        where id = #{id}
    </update>

    <delete id="deleteStoriedBuildingInfoById" parameterType="Long">
        delete from t_storied_building_info where id = #{id}
    </delete>

    <delete id="deleteStoriedBuildingInfoByIds" parameterType="String">
        update t_storied_building_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByNum" parameterType="String" resultType="Map">
        SELECT IFNULL(SUM(IF(type = '2', 1, 0)), 0) AS floorNum
             , IFNULL(SUM(IF(type = '3', 1, 0)), 0) AS roomNum
             , IFNULL(SUM(total_bed_number), 0)     AS bedNum
             , IFNULL(SUM(occupancy_number), 0)     AS occupancyNumber
        FROM t_storied_building_info
        WHERE (FIND_IN_SET(#{pid}, ancestors)) AND del_flag = '0'
    </select>

    <select id="selectInfoByName" parameterType="Long" resultMap="StoriedBuildingInfoResult">
        <include refid="selectStoriedBuildingInfoVo"/>
        where name = #{name} and type = #{type}
        <if test="name != null  and name != ''">
            parent_id = #{parentId}
        </if>
    </select>

    <select id="hasChildById" parameterType="Long" resultType="int">
        select count(1) from t_storied_building_info
        where del_flag = '0' and parent_id = #{id} limit 1
    </select>

    <select id="getBuildingInfoList" parameterType="String" resultType="cn.hutool.json.JSONObject">
        select id,name,type,sort from t_storied_building_info
        <where>
            del_flag = '0'
            <if test="type != null and type != '' ">
                and type = #{type}
            </if>
            <if test="parentId != null and parentId != '' ">
                and parent_id = #{parentId}
            </if>
        </where>
        ORDER BY ISNULL(sort) ASC, sort, id
    </select>

</mapper>
