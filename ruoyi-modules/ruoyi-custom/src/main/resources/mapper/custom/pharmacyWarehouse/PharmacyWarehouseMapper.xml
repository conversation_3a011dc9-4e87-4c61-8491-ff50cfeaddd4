<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyWarehouseMapper">

    <resultMap type="PharmacyWarehouse" id="PharmacyWarehouseResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="location" column="location"/>
        <result property="managerId" column="manager_id"/>
        <result property="manager" column="manager"/>
    </resultMap>

    <sql id="selectPharmacyWarehouseVo">
        SELECT id, name, location, manager_id, manager
        FROM t_pharmacy_warehouse
    </sql>

    <select id="selectPharmacyWarehouseList" parameterType="PharmacyWarehouse" resultMap="PharmacyWarehouseResult">
        <include refid="selectPharmacyWarehouseVo"/>
        <where>
            <if test="name != null  and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="location != null  and location != ''">
                and location = #{location}
            </if>
            <if test="managerId != null ">
                and manager_id = #{managerId}
            </if>
            <if test="manager != null  and manager != ''">
                and manager = #{manager}
            </if>
        </where>
    </select>

    <select id="selectPharmacyWarehouseById" parameterType="Long"
            resultMap="PharmacyWarehouseResult">
        <include refid="selectPharmacyWarehouseVo"/>
        where id = #{id}
    </select>

    <insert id="insertPharmacyWarehouse" parameterType="PharmacyWarehouse" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_pharmacy_warehouse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,
            </if>
            <if test="location != null and location != ''">location,
            </if>
            <if test="managerId != null">manager_id,
            </if>
            <if test="manager != null and manager != ''">manager,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},
            </if>
            <if test="location != null and location != ''">#{location},
            </if>
            <if test="managerId != null">#{managerId},
            </if>
            <if test="manager != null and manager != ''">#{manager},
            </if>
        </trim>
    </insert>

    <update id="updatePharmacyWarehouse" parameterType="PharmacyWarehouse">
        update t_pharmacy_warehouse
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name =
                #{name},
            </if>
            <if test="location != null and location != ''">location =
                #{location},
            </if>
            <if test="managerId != null">manager_id =
                #{managerId},
            </if>
            <if test="manager != null and manager != ''">manager =
                #{manager},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePharmacyWarehouseById" parameterType="Long">
        DELETE
        FROM t_pharmacy_warehouse
        WHERE id = #{id}
    </delete>

    <delete id="deletePharmacyWarehouseByIds" parameterType="String">
        delete from t_pharmacy_warehouse where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
