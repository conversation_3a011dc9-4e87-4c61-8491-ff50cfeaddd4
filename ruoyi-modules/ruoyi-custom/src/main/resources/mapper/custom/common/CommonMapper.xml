<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.common.mapper.CommonMapper">

    <select id="getElderBed" resultType="java.lang.String">
        SELECT CONCAT(bud3.`name`, '-', bud2.`name`, '-', bud1.`name`, '-', bedinfo.bed_name) AS bedName
        FROM (SELECT *
              FROM (SELECT *,
                           ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY create_time DESC) AS rn
                    FROM t_live_base_info) t
              WHERE rn = 1) b
                 LEFT JOIN t_live_bed_records bed ON bed.live_id = b.id
                 LEFT JOIN t_bed_base_info bedinfo ON bed.bed_id = bedinfo.id
                 LEFT JOIN t_storied_building_info bud1 ON bed.room_id = bud1.id
                 LEFT JOIN t_storied_building_info bud2 ON bud1.parent_id = bud2.id
                 LEFT JOIN t_storied_building_info bud3 ON bud2.parent_id = bud3.id
        WHERE b.user_id = #{userId}
    </select>
</mapper>
