<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.dashboard.mapper.DashboardMapper">

    <select id="getElderAgeData" resultType="map">
        select CASE
        <![CDATA[ WHEN age <= 50 THEN '50岁及以下' ]]>
        WHEN age BETWEEN 51 AND 65 THEN '51-65岁'
        WHEN age BETWEEN 66 AND 80 THEN '66-80岁'
        ELSE '80岁以上' -- 可选，包含超出范围的年龄
        END AS age_group,
        count(*) total from (
        select
        COALESCE(a.age,
        IF(a.id_card_num IS NOT NULL AND LENGTH(a.id_card_num) >= 14,
        TIMESTAMPDIFF(YEAR, STR_TO_DATE(SUBSTRING(a.id_card_num, 7, 8), '%Y%m%d'), CURDATE()),
        NULL -- 如果身份证号为空或长度不足，则年龄仍为 NULL
        )
        ) AS age,
        status,
        del_flag
        from t_elderly_people_info a where a.status in ('0','2') and del_flag = 0) b
        group by  age_group
        ORDER BY
        CASE age_group
        WHEN '50岁以下' THEN 1
        WHEN '51-65岁' THEN 2
        WHEN '66-80岁' THEN 3
        WHEN '80岁以上' THEN 4
        ELSE 5
        END
    </select>


    <select id="getAbilityAssessment" resultType="map">
        SELECT case when jt.finalLevel = 1 then '能力完好'
                    when jt.finalLevel = 2 then '能力轻度受损'
                    when jt.finalLevel = 3 then '能力中度受损'
                    when jt.finalLevel = 4 then '能力重度受损'
                    when jt.finalLevel = 5 then '能力完全丧失'
                   end finalLevel,
               count(*) total
        FROM  t_assessment_plan p left join t_elderly_people_info t on p.customer_id = t.customer_id
                                  cross join
              JSON_TABLE(
                      assessment_result,
                      '$.finalLevel' COLUMNS(
                finalLevel varchar(30) PATH '$[0]'
                 )
                  ) AS jt  ON jt.finalLevel IS NOT NULL AND jt.finalLevel != ''
        where t.status in ('0','2') and t.del_flag = '0'
        group by  jt.finalLevel
        order by
            CASE finalLevel
            WHEN '能力完好' THEN 1
            WHEN '能力轻度受损' THEN 2
            WHEN '能力中度受损' THEN 3
            WHEN '能力重度受损' THEN 4
            WHEN '能力完全丧失' THEN 5
            ELSE 6
        END
    </select><select id="getCheckInFlow" resultType="map">
    SELECT
        MONTH(contract_start_date) AS month_num,
        MONTHNAME(contract_start_date) AS month_name,
        COUNT(*) AS contract_count
    FROM
        t_contract_info
    WHERE
        YEAR(contract_start_date) = YEAR(CURDATE())
    GROUP BY
        month_num, month_name
    ORDER BY
        month_num
    </select>

    <select id="getIncomeTrend" resultType="map">
        SELECT
            MONTH(payment_time) AS month_num,
            MONTHNAME(payment_time) AS month_name,
            sum(offer_Cost) offer_Cost
        FROM
            t_payment_record
        WHERE
            YEAR(payment_time) = YEAR(CURDATE())
        GROUP BY
            month_num, month_name
        ORDER BY
            month_num
    </select>




</mapper>
