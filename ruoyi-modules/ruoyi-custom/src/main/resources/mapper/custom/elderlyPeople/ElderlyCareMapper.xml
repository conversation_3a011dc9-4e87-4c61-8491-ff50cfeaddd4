<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.elderlyPeople.mapper.ElderlyCareMapper">

    <resultMap type="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyCare" id="ElderlyCareResult">
        <id property="id" column="id"/>
        <result property="elderId" column="elder_id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="imageUrls" column="image_urls"/>
        <result property="careDate" column="care_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="elderName" column="elder_name"/>
    </resultMap>

    <sql id="selectElderlyCareVo">
        SELECT
            ec.id,
            ec.elder_id,
            ec.title,
            ec.content,
            ec.image_urls,
            ec.care_date,
            ec.create_by,
            ec.create_time,
            ec.update_by,
            ec.update_time,
            ec.remark,
            epi.name as elder_name
        FROM t_elderly_care ec
        LEFT JOIN t_elderly_people_info epi ON ec.elder_id = epi.id
    </sql>

    <select id="selectElderlyCareList" parameterType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyCare" resultMap="ElderlyCareResult">
        <include refid="selectElderlyCareVo"/>
        <where>
            <if test="elderId != null and elderId != ''">
                AND ec.elder_id = #{elderId}
            </if>
            <if test="elderName != null and elderName != ''">
                AND epi.name like concat('%', #{elderName}, '%')
            </if>
            <if test="title != null and title != ''">
                AND ec.title like concat('%', #{title}, '%')
            </if>
            <if test="params.startCareDate != null and params.startCareDate != '' and params.endCareDate !=  null and params.endCareDate != ''">
                AND ec.care_date BETWEEN date_format(#{params.startCareDate}, '%Y-%m-%d') AND date_format(#{params.endCareDate}, '%Y-%m-%d')
            </if>
        </where>
        ORDER BY ec.care_date DESC
    </select>

    <select id="selectElderlyCareById" parameterType="Long" resultMap="ElderlyCareResult">
        <include refid="selectElderlyCareVo"/>
        WHERE ec.id = #{id}
    </select>

    <insert id="insertElderlyCare" parameterType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyCare" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_elderly_care
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderId != null">elder_id,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="imageUrls != null">image_urls,</if>
            <if test="careDate != null">care_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderId != null">#{elderId},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="imageUrls != null">#{imageUrls},</if>
            <if test="careDate != null">#{careDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateElderlyCare" parameterType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyCare">
        UPDATE t_elderly_care
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderId != null">elder_id = #{elderId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="imageUrls != null">image_urls = #{imageUrls},</if>
            <if test="careDate != null">care_date = #{careDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <delete id="deleteElderlyCareById" parameterType="Long">
        DELETE FROM t_elderly_care WHERE id = #{id}
    </delete>

    <delete id="deleteElderlyCareByIds" parameterType="String">
        DELETE FROM t_elderly_care WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
