<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.elderlyPeople.mapper.MedicalRecordMapper">

    <resultMap type="MedicalRecord" id="MedicalRecordResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="visitDate" column="visit_date"/>
        <result property="diagnosisResult" column="diagnosis_result"/>
        <result property="treatmentPlan" column="treatment_plan"/>
        <result property="attachmentUrls" column="attachment_urls"/>

        <result column="name" property="elderlyName"/>
        <result column="id_card_num" property="idCardNum"/>

<!--        <association property="elderlyPeopleInfo" javaType="ElderlyPeopleInfo">-->
<!--            <result column="user_id" property="id"/>-->
<!--            <result column="name" property="name"/>-->
<!--            <result column="id_card_num" property="idCardNum"/>-->
<!--        </association>-->
    </resultMap>

    <sql id="selectMedicalRecordVo">
        SELECT t.id,
               t.user_id,
               t.visit_date,
               t.diagnosis_result,
               t.treatment_plan,
               t.attachment_urls,

               t1.name,
               t1.id_card_num
        FROM t_medical_record t
            LEFT JOIN t_elderly_people_info t1 ON t.user_id = t1.id
    </sql>

    <select id="selectMedicalRecordList" parameterType="MedicalRecord" resultMap="MedicalRecordResult">
        <include refid="selectMedicalRecordVo"/>
        <where>
            <if test="userId != null  and userId != ''">
                and t.user_id = #{userId}
            </if>
            <if test="visitDate != null ">
                and t.visit_date = #{visitDate}
            </if>
            <if test="diagnosisResult != null  and diagnosisResult != ''">
                and t.diagnosis_result = #{diagnosisResult}
            </if>
            <if test="treatmentPlan != null  and treatmentPlan != ''">
                and t.treatment_plan = #{treatmentPlan}
            </if>
            <if test="attachmentUrls != null  and attachmentUrls != ''">
                and t.attachment_urls = #{attachmentUrls}
            </if>
            <if test="elderlyName != null and elderlyName != ''">
                and t1.name like concat('%', #{elderlyName}, '%')
            </if>
            <if test="idCardNum != null and idCardNum != ''">
                and t1.id_card_num like concat('%', #{idCardNum}, '%')
            </if>
            <if test="visitDate != null">
                and DATE_FORMAT(t.visit_date, '%Y-%m-%d') = DATE_FORMAT(#{visitDate}, '%Y-%m-%d')
            </if>
            <if test="params.startVisitDate != null and params.startVisitDate != '' and  params.endVisitDate != null and params.endVisitDate != ''">
                and t.visit_date between #{params.startVisitDate} and #{params.endVisitDate}
            </if>
        </where>
    </select>

    <select id="selectMedicalRecordById" parameterType="Long"
            resultMap="MedicalRecordResult">
        <include refid="selectMedicalRecordVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertMedicalRecord" parameterType="MedicalRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_medical_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id,
            </if>
            <if test="visitDate != null">visit_date,
            </if>
            <if test="diagnosisResult != null and diagnosisResult != ''">diagnosis_result,
            </if>
            <if test="treatmentPlan != null and treatmentPlan != ''">treatment_plan,
            </if>
            <if test="attachmentUrls != null">attachment_urls,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">#{userId},
            </if>
            <if test="visitDate != null">#{visitDate},
            </if>
            <if test="diagnosisResult != null and diagnosisResult != ''">#{diagnosisResult},
            </if>
            <if test="treatmentPlan != null and treatmentPlan != ''">#{treatmentPlan},
            </if>
            <if test="attachmentUrls != null">#{attachmentUrls},
            </if>
        </trim>
    </insert>

    <update id="updateMedicalRecord" parameterType="MedicalRecord">
        update t_medical_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id =
                #{userId},
            </if>
            <if test="visitDate != null">visit_date =
                #{visitDate},
            </if>
            <if test="diagnosisResult != null and diagnosisResult != ''">diagnosis_result =
                #{diagnosisResult},
            </if>
            <if test="treatmentPlan != null and treatmentPlan != ''">treatment_plan =
                #{treatmentPlan},
            </if>
            <if test="attachmentUrls != null">attachment_urls =
                #{attachmentUrls},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMedicalRecordById" parameterType="Long">
        DELETE
        FROM t_medical_record
        WHERE id = #{id}
    </delete>

    <delete id="deleteMedicalRecordByIds" parameterType="String">
        delete from t_medical_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
