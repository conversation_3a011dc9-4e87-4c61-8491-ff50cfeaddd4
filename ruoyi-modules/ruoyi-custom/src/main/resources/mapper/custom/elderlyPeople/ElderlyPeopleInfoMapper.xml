<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.elderlyPeople.mapper.ElderlyPeopleInfoMapper">

    <resultMap type="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo" id="ElderlyPeopleInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="idCardNum" column="id_card_num"/>
        <result property="phone" column="phone"/>
        <result property="dateBirth" column="date_birth"/>
        <result property="age" column="age"/>
        <result property="nation" column="nation"/>
        <result property="marriageStatus" column="marriage_status"/>
        <result property="livingSituation" column="living_situation"/>
        <result property="homeAddress" column="home_address"/>
        <result property="emergencyContactName" column="emergency_contact_name"/>
        <result property="emergencyContactPhone" column="emergency_contact_phone"/>
        <result property="relation" column="relation"/>
        <result property="economicSources" column="economic_sources"/>
        <result property="monthlyIncome" column="monthly_income"/>
        <result property="socialSecurityNo" column="social_security_no"/>
        <result property="status" column="status"/>
        <result property="img" column="img"/>
        <result property="sysUserId" column="sys_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="liveId" column="liveId"/>
        <result property="bedRecordId" column="bedRecordId"/>
        <result property="bedId" column="bed_id"/>
        <result property="bedName" column="bedName"/>
        <result property="contractStateStr" column="contractStateStr"/>
        <result property="customerId" column="customer_id"/>
        <result property="emergencyWorkUnit" column="emergency_work_unit"/>
        <result property="emergencyAddress" column="emergency_address"/>
        <result property="marketerId" column="marketer_id"/>
        <result property="marketer" column="marketer"/>
        <result property="amount" column="amount"/>
        <result property="securityAmount" column="security_amount"/>
        <result property="staffStatus" column="staff_status"/>
        <result property="careLevel" column="care_level"/>

        <association property="contractInfo" javaType="ContractInfo">
            <result property="contractNumber" column="contract_number"/>
            <result property="customerId" column="customer_id"/>
            <result property="childNumber" column="child_number"/>
            <result property="elderlyPeopleId" column="elderly_people_id"/>
            <result property="contractEntryUserId" column="contract_entry_user_id"/>
            <result property="contractEntryUser" column="contract_entry_user"/>
            <result property="contractSignDate" column="contract_sign_date"/>
            <result property="contractStartDate" column="contract_start_date"/>
            <result property="contractEndDate" column="contract_end_date"/>
            <result property="attachmentUrl" column="attachment_url"/>
            <result property="isRenewable" column="is_renewable"/>
        </association>
    </resultMap>

    <sql id="selectElderlyPeopleInfoVo">
        SELECT a.id,
               b.id                                                                           AS liveId,
               a.name,
               a.sex,
               a.id_card_num,
               a.phone,
               a.date_birth,
               COALESCE(a.age,
                        IF(a.id_card_num IS NOT NULL AND LENGTH(a.id_card_num) >= 14,
                           TIMESTAMPDIFF(YEAR, STR_TO_DATE(SUBSTRING(a.id_card_num, 7, 8), '%Y%m%d'), CURDATE()),
                           NULL -- 如果身份证号为空或长度不足，则年龄仍为 NULL
                        )
               )                                                                              AS age,
               a.nation,
               a.marriage_status,
               a.living_situation,
               a.home_address,
               a.emergency_contact_name,
               a.emergency_contact_phone,
               a.relation,
               a.economic_sources,
               a.monthly_income,
               a.social_security_no,
               a.status,
               a.img,
               a.sys_user_id,
               a.create_time,
               a.create_by,
               a.update_time,
               a.update_by,
               a.del_flag,
               a.remark,
               CASE
                   WHEN c.contract_number IS NULL THEN '未签约'
                   WHEN c.contract_end_date &lt; NOW() THEN '已到期'
                   ELSE '已签约' END                                                          AS contractStateStr,
               a.customer_id,
               a.emergency_work_unit,
               a.emergency_address,
               a.staff_status,

               bed.id                                                                         AS bedRecordId,
               bed.bed_id,
               CONCAT(bud3.`name`, '-', bud2.`name`, '-', bud1.`name`, '-', bedinfo.bed_name) AS bedName,

               cc.care_level,

               c.contract_number,
               c.child_number,
               c.elderly_people_id,
               c.contract_entry_user_id,
               c.contract_entry_user,
               c.contract_sign_date,
               c.contract_start_date,
               c.contract_end_date,
               c.attachment_url,
               c.is_renewable,

               cust.marketer_id,
               cust.marketer,

               IF(balance.amount IS NULL, 0, balance.amount)                                  AS amount,

               IF(balance2.amount IS NULL, 0, balance2.amount)                                AS security_amount


        FROM t_elderly_people_info AS a
                 LEFT JOIN (SELECT *
                            FROM (SELECT *,
                                         ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY create_time DESC) AS rn
                                  FROM t_live_base_info) t
                            WHERE rn = 1) b ON a.id = b.user_id
                 LEFT JOIN (SELECT *
                            FROM (SELECT *,
                                         ROW_NUMBER() OVER (PARTITION BY live_id ORDER BY create_time DESC) AS rn
                                  FROM t_live_combo_records
                                  WHERE state IN ('0', '2')) AS tmp_c
                            WHERE tmp_c.rn = 1) AS cc ON b.id = cc.live_id
                 LEFT JOIN (SELECT *
                            FROM (SELECT *,
                                         ROW_NUMBER() OVER (PARTITION BY live_id ORDER BY create_time DESC) AS rn
                                  FROM t_live_bed_records) t
                            WHERE rn = 1) bed ON bed.live_id = b.id
                 LEFT JOIN t_bed_base_info bedinfo ON bed.bed_id = bedinfo.id
                 LEFT JOIN t_storied_building_info bud1 ON bed.room_id = bud1.id
                 LEFT JOIN t_storied_building_info bud2 ON bud1.parent_id = bud2.id
                 LEFT JOIN t_storied_building_info bud3 ON bud2.parent_id = bud3.id
                 LEFT JOIN t_marketing_customer_info cust ON a.customer_id = cust.id
                 LEFT JOIN t_contract_info c ON a.id = c.elderly_people_id AND c.is_renewable = 0
                 LEFT JOIN t_balance_info balance ON a.id = balance.user_id
                 LEFT JOIN t_security_balance_info balance2 ON a.id = balance2.elderly_id
    </sql>

    <select id="selectElderlyPeopleInfoList"
            parameterType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo"
            resultMap="ElderlyPeopleInfoResult">
        <include refid="selectElderlyPeopleInfoVo"/>
        <where>
            a.del_flag = '0'
            <if test="name != null  and name != ''">and a.name like concat('%', #{name}, '%')</if>
            <if test="sex != null  and sex != ''">and a.sex = #{sex}</if>
            <if test="idCardNum != null  and idCardNum != ''">and a.id_card_num like concat('%', #{idCardNum}, '%')</if>
            <if test="phone != null  and phone != ''">and a.phone like concat('%', #{phone}, '%')</if>
            <if test="dateBirth != null ">and a.date_birth = #{dateBirth}</if>
            <if test="params.beginAge != null and params.beginAge != '' and params.endAge != null and params.endAge != ''">
                and a.age between #{params.beginAge} and #{params.endAge}
            </if>
            <if test="nation != null  and nation != ''">and a.nation = #{nation}</if>
            <if test="marriageStatus != null  and marriageStatus != ''">and a.marriage_status = #{marriageStatus}</if>
            <if test="livingSituation != null  and livingSituation != ''">and a.living_situation = #{livingSituation}
            </if>
            <if test="homeAddress != null  and homeAddress != ''">and a.home_address = #{homeAddress}</if>
            <if test="emergencyContactName != null  and emergencyContactName != ''">and a.emergency_contact_name like
                concat('%', #{emergencyContactName}, '%')
            </if>
            <if test="emergencyContactPhone != null  and emergencyContactPhone != ''">and a.emergency_contact_phone =
                #{emergencyContactPhone}
            </if>
            <if test="relation != null  and relation != ''">and a.relation = #{relation}</if>
            <if test="economicSources != null  and economicSources != ''">and a.economic_sources = #{economicSources}
            </if>
            <if test="monthlyIncome != null  and monthlyIncome != ''">and a.monthly_income = #{monthlyIncome}</if>
            <if test="socialSecurityNo != null  and socialSecurityNo != ''">and a.social_security_no =
                #{socialSecurityNo}
            </if>
            <if test="status != null  and status != ''">and a.status in (${status})</if>
            <if test="customerId != null">and a.customer_id = #{customerId}</if>
            <if test="marketer != null and marketer != ''">and cust.marketer like concat('%', #{marketer}, '%')</if>
            <if test="careLevel != null and careLevel != ''">and cc.care_level = #{careLevel}</if>
            <if test="params.contractNumber != null and params.contractNumber != ''">and c.contract_number like concat('%',
                #{params.contractNumber}, '%')
            </if>
        </where>
        <if test="contractStateStr != null and contractStateStr != ''">
            HAVING contractStateStr = #{contractStateStr}
        </if>
        order by a.create_time desc
    </select>

    <select id="selectElderlyPeopleInfoById" parameterType="String" resultMap="ElderlyPeopleInfoResult">
        <include refid="selectElderlyPeopleInfoVo"/>
        where a.id = #{id}
        LIMIT 1
    </select>
    <select id="getUserList" parameterType="String" resultType="cn.hutool.json.JSONObject">
        SELECT
        main.*
        FROM
        (
        SELECT
        b.id,
        b.name,
        b.sex,
        b.id_card_num,
        b.phone,
        b.age,
        IFNULL( a.state, 1 ) AS state,
        b.create_time,
        b.del_flag
        FROM
        t_live_base_info AS a
        LEFT JOIN t_elderly_people_info AS b ON a.user_id = b.id
        WHERE
        a.del_flag = '0'
        ORDER BY
        a.create_time DESC
        ) AS main
        <where>
            <if test="name != null and name != '' ">
                and main.name like concat('%', #{name}, '%')
            </if>
            <if test="state != null and state != '' ">
                and main.state = #{state}
            </if>
        </where>
        GROUP BY main.id
    </select>

    <insert id="insertElderlyPeopleInfo" parameterType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo">
        insert into t_elderly_people_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="sex != null">sex,</if>
            <if test="idCardNum != null">id_card_num,</if>
            <if test="phone != null">phone,</if>
            <if test="dateBirth != null">date_birth,</if>
            <if test="age != null">age,</if>
            <if test="nation != null">nation,</if>
            <if test="marriageStatus != null">marriage_status,</if>
            <if test="livingSituation != null">living_situation,</if>
            <if test="homeAddress != null">home_address,</if>
            <if test="emergencyContactName != null">emergency_contact_name,</if>
            <if test="emergencyContactPhone != null">emergency_contact_phone,</if>
            <if test="relation != null">relation,</if>
            <if test="economicSources != null">economic_sources,</if>
            <if test="monthlyIncome != null">monthly_income,</if>
            <if test="socialSecurityNo != null">social_security_no,</if>
            <if test="status != null">status,</if>
            <if test="img != null">img,</if>
            <if test="sysUserId != null">sys_user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="emergencyWorkUnit != null">emergency_work_unit,</if>
            <if test="emergencyAddress != null">emergency_address,</if>
            <if test="staffStatus != null">#{staffStatus},</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="sex != null">#{sex},</if>
            <if test="idCardNum != null">#{idCardNum},</if>
            <if test="phone != null">#{phone},</if>
            <if test="dateBirth != null">#{dateBirth},</if>
            <if test="age != null">#{age},</if>
            <if test="nation != null">#{nation},</if>
            <if test="marriageStatus != null">#{marriageStatus},</if>
            <if test="livingSituation != null">#{livingSituation},</if>
            <if test="homeAddress != null">#{homeAddress},</if>
            <if test="emergencyContactName != null">#{emergencyContactName},</if>
            <if test="emergencyContactPhone != null">#{emergencyContactPhone},</if>
            <if test="relation != null">#{relation},</if>
            <if test="economicSources != null">#{economicSources},</if>
            <if test="monthlyIncome != null">#{monthlyIncome},</if>
            <if test="socialSecurityNo != null">#{socialSecurityNo},</if>
            <if test="status != null">#{status},</if>
            <if test="img != null">#{img},</if>
            <if test="sysUserId != null">#{sysUserId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="customerId != null">#{customerId}</if>
            <if test="emergencyWorkUnit != null">#{emergencyWorkUnit},</if>
            <if test="emergencyAddress != null">#{emergencyAddress}</if>
            <if test="staffStatus != null">#{staffStatus}</if>
        </trim>
    </insert>

    <update id="updateElderlyPeopleInfo" parameterType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo">
        update t_elderly_people_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="idCardNum != null">id_card_num = #{idCardNum},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="dateBirth != null">date_birth = #{dateBirth},</if>
            <if test="age != null">age = #{age},</if>
            <if test="nation != null">nation = #{nation},</if>
            <if test="marriageStatus != null">marriage_status = #{marriageStatus},</if>
            <if test="livingSituation != null">living_situation = #{livingSituation},</if>
            <if test="homeAddress != null">home_address = #{homeAddress},</if>
            <if test="emergencyContactName != null">emergency_contact_name = #{emergencyContactName},</if>
            <if test="emergencyContactPhone != null">emergency_contact_phone = #{emergencyContactPhone},</if>
            <if test="relation != null">relation = #{relation},</if>
            <if test="economicSources != null">economic_sources = #{economicSources},</if>
            <if test="monthlyIncome != null">monthly_income = #{monthlyIncome},</if>
            <if test="socialSecurityNo != null">social_security_no = #{socialSecurityNo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="img != null">img = #{img},</if>
            <if test="sysUserId != null">sys_user_id = #{sysUserId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="emergencyWorkUnit != null">emergency_work_unit = #{emergencyWorkUnit},</if>
            <if test="emergencyAddress != null">emergency_address = #{emergencyAddress},</if>
            <if test="staffStatus != null">staff_status = #{staffStatus}</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElderlyPeopleInfoById" parameterType="Long">
        DELETE
        FROM t_elderly_people_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteElderlyPeopleInfoByIds" parameterType="String">
        delete from t_elderly_people_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <update id="logicalDeleteElderlyPeopleInfoByIds" parameterType="String">
        update t_elderly_people_info set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getPeopleInfoList" parameterType="ElderlyPeopleInfoVo" resultType="ElderlyPeopleInfoVo">
        SELECT a.id AS id,
        a.name AS userName,
        b.`state` AS state,
        g.contract_start_date AS liveDate,
        d.combo_name AS comboName,
        IFNULL(f.amount, 0) AS amount,
        IF(f.amount &lt;= 0, 1, 0) AS amountState,
        IFNULL(sb.amount, 0) AS amount,
        g.contract_end_date AS expiredDate
        FROM t_elderly_people_info AS a
        LEFT JOIN (SELECT *
        FROM (SELECT *,
        ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY create_time DESC) AS rn
        FROM t_live_base_info) AS b
        WHERE b.rn = 1) AS b ON a.id = b.user_id
        LEFT JOIN (SELECT *
        FROM (SELECT *,
        ROW_NUMBER() OVER (PARTITION BY live_id ORDER BY create_time DESC) AS rn
        FROM t_live_combo_records) AS c
        WHERE c.rn = 1) AS c ON c.live_id = b.id
        LEFT JOIN t_combo_base_info AS d ON d.id = c.combo_id
        LEFT JOIN (SELECT *
        FROM (SELECT *,
        ROW_NUMBER() OVER (PARTITION BY live_id ORDER BY create_time DESC) AS rn
        FROM t_live_bed_records) t
        WHERE rn = 1) AS e ON e.live_id = b.id
        LEFT JOIN t_balance_info AS f ON f.user_id = a.id
        LEFT JOIN t_security_balance_info AS sb ON sb.elderly_id = a.id
        LEFT JOIN t_contract_info AS g ON g.elderly_people_id = a.id AND g.is_renewable = 0
        <where>
            a.del_flag = '0'
            and c.state = '0'
            <if test="userName != null and userName != '' ">
                and a.name like concat('%',#{userName},'%')
            </if>
            <if test="state != null and state != '' ">
                and b.`state` = #{state}
            </if>
            <if test="liveDateBegin != null and liveDateEnd">
                and g.contract_start_date between #{liveDateBegin} and #{liveDateEnd}
            </if>
            <if test="expiredDateBegin != null and expiredDateEnd">
                and g.contract_end_date between #{expiredDateBegin} and #{expiredDateEnd}
            </if>
        </where>
        ORDER BY g.contract_start_date DESC
    </select>

    <select id="getElderlyPeopleInfoBySysUserId"
            resultType="com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo">
        <include refid="selectElderlyPeopleInfoVo"/>
        where a.sys_user_id = #{sysUserId}
    </select>

    <select id="getElderIdsByWorkerId" resultType="cn.hutool.json.JSONObject">
        SELECT t.id,
               t.name,
               t.id_card_num                                                                  AS idCardNum,
               CONCAT(bud3.`name`, '-', bud2.`name`, '-', bud1.`name`, '-', bedinfo.bed_name) AS bedName
        FROM t_elderly_people_info t
                 LEFT JOIN t_live_base_info t1 ON t.id = t1.user_id AND t1.state = '0'
                 LEFT JOIN t_live_bed_records t2 ON t1.id = t2.live_id AND t2.live_state = '0'
                 LEFT JOIN t_bed_care_info t3 ON t2.bed_id = t3.bed_id AND t3.care_state = '0'
                 LEFT JOIN t_bed_base_info bedinfo ON t2.bed_id = bedinfo.id
                 LEFT JOIN t_storied_building_info bud1 ON t2.room_id = bud1.id
                 LEFT JOIN t_storied_building_info bud2 ON bud1.parent_id = bud2.id
                 LEFT JOIN t_storied_building_info bud3 ON bud2.parent_id = bud3.id
        WHERE t3.care_worker_id = #{workId}
    </select>

    <select id="getLivingInfoList"
            resultType="com.ruoyi.custom.admin.elderlyPeople.domain.vo.ElderlyPeopleInfoLivingVo">
        WITH bedBase AS (SELECT bedinfo.bed_name AS bedName,
        CONCAT(bud3.name, '-', bud2.name, '-', bud1.name, '-', bedinfo.bed_name) AS fullBedName,
        care.name AS worker,
        b.user_id AS userId
        FROM (SELECT user_id, id
        FROM (SELECT user_id,
        id,
        ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY create_time DESC) AS rn
        FROM t_live_base_info) t
        WHERE rn = 1) b
        LEFT JOIN (SELECT *
        FROM (SELECT *,
        ROW_NUMBER() OVER (PARTITION BY live_id ORDER BY create_time DESC) AS rn
        FROM t_live_bed_records) t
        WHERE rn = 1) bed ON bed.live_id = b.id
        LEFT JOIN t_bed_base_info bedinfo ON bed.bed_id = bedinfo.id
        LEFT JOIN t_bed_care_info bedcare ON bedinfo.id = bedcare.bed_id
        LEFT JOIN t_care_worker_info care ON bedcare.care_worker_id = care.id
        LEFT JOIN t_storied_building_info bud1 ON bed.room_id = bud1.id
        LEFT JOIN t_storied_building_info bud2 ON bud1.parent_id = bud2.id
        LEFT JOIN t_storied_building_info bud3 ON bud2.parent_id = bud3.id),

        liveBase AS (SELECT user_id, id, live_date
        FROM (SELECT user_id,
        id,
        live_date,
        ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY create_time DESC) AS rn
        FROM t_live_base_info) t
        WHERE rn = 1),

        familyBase AS (SELECT user_id, phone
        FROM (SELECT user_id,
        phone,
        ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY create_time DESC) AS rn
        FROM t_elderly_people_family_info) t
        WHERE rn = 1)

        SELECT a.id AS id,
        a.name AS userName,
        a.img AS img,
        a.sex,
        CASE
        WHEN age IS NOT NULL THEN age
        WHEN a.date_birth IS NOT NULL THEN TIMESTAMPDIFF(YEAR, a.date_birth, CURDATE())
        WHEN a.id_card_num IS NOT NULL THEN
        TIMESTAMPDIFF(YEAR,
        STR_TO_DATE(SUBSTRING(id_card_num, 7, 8), '%Y%m%d'),
        CURDATE())
        END AS age,
        a.status AS state,
        f.phone AS familyPhone,
        b.fullBedName AS bed,
        b.worker AS worker,
        d.care_level AS serviceLevel,
        e.combo_name AS comboName,
        f2.name AS mealName,
        g.contract_start_date AS liveDate,
        g.contract_end_date AS expiredDate,
        CASE
        WHEN g.contract_number IS NULL THEN '未签约'
        WHEN contract_end_date >= DATE_FORMAT(CURDATE(), '%Y-%m-%d') THEN '已签约'
        ELSE '已到期'
        END AS signStatusStr

        FROM t_elderly_people_info AS a
        LEFT JOIN familyBase f ON a.id = f.user_id
        LEFT JOIN bedBase b ON a.id = b.userId
        LEFT JOIN liveBase c ON a.id = c.user_id
        LEFT JOIN t_live_combo_records d ON d.live_id = c.id
        LEFT JOIN t_combo_base_info e ON e.id = d.combo_id
        LEFT JOIN t_live_meal_combo_record f1 ON f1.live_id = c.id AND f1.status = '0'
        LEFT JOIN t_meal_combo_base f2 ON f2.id = f1.meal_id
        LEFT JOIN t_contract_info g ON g.elderly_people_id = a.id AND g.is_renewable = '0'
        WHERE a.del_flag = '0'
        AND (a.status = '0' OR a.status = '2')
        <if test="userName != null and userName != '' ">
            AND a.name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="serviceLevel != null and serviceLevel != '' ">
            AND d.care_level = #{serviceLevel}
        </if>
        <if test="liveDate != null">
            AND DATE_FORMAT(g.contract_start_date, '%Y-%m-%d') = DATE_FORMAT(#{liveDate}, '%Y-%m-%d')
        </if>
        <if test="worker != null and worker != '' ">
            AND b.worker LIKE CONCAT('%', #{worker}, '%')
        </if>
        <if test="expiredDate != null ">
            AND g.contract_end_date = DATE_FORMAT(#{expiredDate}, '%Y-%m-%d')
        </if>
        <if test="signStatusStr != null and signStatusStr != '' ">
            HAVING signStatusStr = #{signStatusStr}
        </if>
        ORDER BY a.create_time DESC
    </select>

    <select id="getCheckInFlowStatistics" resultType="cn.hutool.json.JSONObject">
        SELECT DATE_FORMAT(check_date, '%Y-%m-%d') AS date,
               COUNT(id)                           AS count
        FROM (SELECT a.id,
                     g.contract_start_date AS check_date
              FROM t_elderly_people_info a
                       LEFT JOIN
                   t_contract_info g ON g.elderly_people_id = a.id
              WHERE a.del_flag = '0'
                AND (g.contract_start_date IS NOT NULL AND g.contract_start_date BETWEEN #{startDate} AND #{endDate})) t
        GROUP BY DATE_FORMAT(check_date, '%Y-%m-%d')
        ORDER BY date ASC
    </select>

    <resultMap type="com.ruoyi.custom.admin.marketing.resp.ElderlyPeopleInfo2" id="ElderlyPeopleInfoResult2">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="idCardNum" column="id_card_num"/>
        <result property="phone" column="phone"/>
        <result property="dateBirth" column="date_birth"/>
        <result property="age" column="age"/>
        <result property="nation" column="nation"/>
        <result property="marriageStatus" column="marriage_status"/>
        <result property="livingSituation" column="living_situation"/>
        <result property="staffStatus" column="staff_status"/>
        <result property="marketer" column="marketer"/>
        <result property="homeAddress" column="home_address"/>
        <result property="img" column="img"/>
        <result property="emergencyContactName" column="emergency_contact_name"/>
        <result property="emergencyContactPhone" column="emergency_contact_phone"/>
        <result property="relation" column="relation"/>
        <result property="emergencyAddress" column="emergency_address"/>
        <result property="emergencyWorkUnit" column="emergency_work_unit"/>
        <result property="economicSources" column="economic_sources"/>
        <result property="monthlyIncome" column="monthly_income"/>
        <result property="socialSecurityNo" column="social_security_no"/>
        <result property="contractNumber" column="contract_number"/>
        <result property="contractSignDate" column="contract_sign_date"/>
        <result property="contractStartDate" column="contract_start_date"/>
        <result property="contractEndDate" column="contract_end_date"/>
        <result property="state" column="state"/>
        <result property="careLevel" column="care_level"/>
        <result property="dischargeDate" column="discharge_date"/>
        <result property="contractStateStr" column="contract_state_str"/>
        <result property="bedName" column="bedName"/>
    </resultMap>


    <sql id="selectElderlyPeopleInfoVo2">
        SELECT
        a.id,
        a.name,
        a.img,
        a.sex,
        a.id_card_num,
        a.phone,
        a.date_birth,
        COALESCE(a.age,
        IF(a.id_card_num IS NOT NULL AND LENGTH(a.id_card_num) >= 14,
        TIMESTAMPDIFF(YEAR, STR_TO_DATE(SUBSTRING(a.id_card_num, 7, 8), '%Y%m%d'), CURDATE()),
        NULL
        )
        ) AS age,
        a.nation,
        a.marriage_status,
        a.living_situation,
        a.home_address,
        a.emergency_contact_name,
        a.emergency_contact_phone,
        a.relation,
        a.economic_sources,
        a.monthly_income,
        a.social_security_no,
        a.emergency_work_unit,
        a.emergency_address,
        a.staff_status,
        cc.care_level,
        c.contract_number,
        c.contract_sign_date,
        c.contract_start_date,
        c.contract_end_date,
        CASE
        WHEN c.contract_number IS NULL THEN '未签约'
        WHEN c.contract_end_date >= DATE_FORMAT(CURDATE(), '%Y-%m-%d') THEN '已签约'
        ELSE '已到期'
        END AS contractStateStr,
        cust.marketer,
        cust.consultation_date,
        cust.source_channel,
        IF(a.status IS NULL OR a.status = '', '1',a.status) AS status,
        payment.discharge_date,
        CONCAT(bud3.`name`, '-', bud2.`name`, '-', bud1.`name`, '-', bedinfo.bed_name) AS bedName


        FROM t_elderly_people_info AS a
                 LEFT JOIN (SELECT *
                            FROM (SELECT *,
                                         ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY create_time DESC) AS rn
                                  FROM t_live_base_info) t
                            WHERE rn = 1) b ON a.id = b.user_id
                 LEFT JOIN (SELECT *
                            FROM (SELECT *,
                                         ROW_NUMBER() OVER (PARTITION BY live_id ORDER BY create_time DESC) AS rn
                                  FROM t_live_combo_records
                                  WHERE state IN ('0', '2')) AS tmp_c
                            WHERE tmp_c.rn = 1) AS cc ON b.id = cc.live_id
                 LEFT JOIN (SELECT *
                            FROM (SELECT *,
                                         ROW_NUMBER() OVER (PARTITION BY live_id ORDER BY create_time DESC) AS rn
                                  FROM t_live_bed_records) t
                            WHERE rn = 1) bed ON bed.live_id = b.id
                 LEFT JOIN t_bed_base_info bedinfo ON bed.bed_id = bedinfo.id
                 LEFT JOIN t_storied_building_info bud1 ON bed.room_id = bud1.id
                 LEFT JOIN t_storied_building_info bud2 ON bud1.parent_id = bud2.id
                 LEFT JOIN t_storied_building_info bud3 ON bud2.parent_id = bud3.id
                 LEFT JOIN t_marketing_customer_info cust ON a.customer_id = cust.id
                 LEFT JOIN t_contract_info c ON a.id = c.elderly_people_id AND c.is_renewable = 0
                 LEFT JOIN t_balance_info balance ON a.id = balance.user_id
                 LEFT JOIN t_security_balance_info balance2 ON a.id = balance2.elderly_id
                 LEFT JOIN t_payment_record payment ON c.contract_number = payment.contract_number AND payment.fee_type = '2'
    </sql>

    <select id="selectElderlyPeopleInfoList2"
            resultType="com.ruoyi.custom.admin.marketing.resp.ElderlyPeopleInfo2">
        <include refid="selectElderlyPeopleInfoVo2"/>
        <where>
            a.del_flag = '0'
            <if test="name != null  and name != ''">and a.name like concat('%', #{name}, '%')</if>
            <if test="idCardNum != null  and idCardNum != ''">and a.id_card_num like concat('%', #{idCardNum}, '%')</if>
            <if test="phone != null  and phone != ''">and a.phone like concat('%', #{phone}, '%')</if>
            <if test="params.beginAge != null and params.beginAge != '' and params.endAge != null and params.endAge != ''">
                and a.age between #{params.beginAge} and #{params.endAge}
            </if>
            <if test="params.startContractStartDate != null and params.endContractStartDate != null ">
                and DATE_FORMAT（c.contract_start_date, '%Y-%m-%d') between DATE_FORMAT(#{params.startContractStartDate}, '%Y-%m-%d') and DATE_FORMAT(#{params.endContractStartDate}, '%Y-%m-%d')
            </if>
            <if test="socialSecurityNo != null  and socialSecurityNo != ''">and a.social_security_no =
                #{socialSecurityNo}
            </if>
            <if test="status != null  and status != ''">and a.status in (${status})</if>
            <if test="marketer != null and marketer != ''">and cust.marketer like concat('%', #{marketer}, '%')</if>
            <if test="careLevel != null and careLevel != ''">and cc.care_level = #{careLevel}</if>
            <if test="contractNumber != null and contractNumber != ''">and c.contract_number like concat('%',
                #{contractNumber}, '%')
            </if>
        </where>
        <if test="contractStateStr != null and contractStateStr != ''">
            HAVING contractStateStr = #{contractStateStr}
        </if>
        order by a.create_time desc
    </select>

</mapper>
