<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.elderlyPeople.mapper.DiseaseInfoMapper">

    <resultMap type="DiseaseInfo" id="DiseaseInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="value" column="value"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="sort" column="sort"/>
        <result property="classType" column="class_type"/>
    </resultMap>

    <sql id="selectDiseaseInfoVo">
        select id, name, type, value, create_time, create_by, update_time, update_by, del_flag,sort,class_type, remark
        from t_disease_base_info
    </sql>

    <select id="selectDiseaseInfoList" parameterType="DiseaseInfo" resultMap="DiseaseInfoResult">
        <include refid="selectDiseaseInfoVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="classType != null  and classType != ''">and class_type = #{classType}</if>
            <if test="value != null  and value != ''">and value = #{value}</if>
        </where>
    </select>

    <select id="selectDiseaseInfoById" parameterType="String" resultMap="DiseaseInfoResult">
        <include refid="selectDiseaseInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertDiseaseInfo" parameterType="DiseaseInfo">
        insert into t_disease_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="value != null">value,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="sort != null">sort,</if>
            <if test="classType != null">class_type,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="value != null">#{value},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="sort != null">#{sort},</if>
            <if test="classType != null">#{classType},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDiseaseInfo" parameterType="DiseaseInfo">
        update t_disease_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="value != null">value = #{value},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="classType != null">class_type = #{classType},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDiseaseInfoById" parameterType="String">
        delete from t_disease_base_info where id = #{id}
    </delete>

    <delete id="deleteDiseaseInfoByIds" parameterType="String">
        update t_disease_base_info set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="getGroupConcatDisease" resultType="cn.hutool.json.JSONObject">
        select type,remark from t_disease_base_info GROUP BY type order by sort
    </select>

    <select id="getDiseaseInfoJson" resultType="cn.hutool.json.JSONObject" parameterType="String">
        SELECT id,CONCAT(value,'分：',name) as label,value,class_type as classType,type FROM t_disease_base_info where
        del_flag = '0' and type = #{type}
    </select>
</mapper>
