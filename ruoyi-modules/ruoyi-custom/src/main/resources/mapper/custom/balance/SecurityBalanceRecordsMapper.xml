<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.balance.mapper.SecurityBalanceRecordsMapper">

    <resultMap type="com.ruoyi.custom.admin.balance.domain.SecurityBalanceRecords" id="SecurityBalanceRecordsResult">
        <id property="id" column="id"/>
        <result property="elderlyId" column="elderly_id"/>
        <result property="lastAmount" column="last_amount"/>
        <result property="changedAmount" column="changed_amount"/>
        <result property="amount" column="amount"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="changedType" column="changed_type"/>
    </resultMap>

    <sql id="selectSecurityBalanceRecordsVo">
        select id, elderly_id, last_amount, changed_amount, amount, create_time, create_by, update_time, update_by, del_flag, remark, changed_type
        from t_security_balance_records
    </sql>

    <select id="selectSecurityBalanceRecordsList" parameterType="com.ruoyi.custom.admin.balance.domain.SecurityBalanceRecords"
            resultMap="SecurityBalanceRecordsResult">
        <include refid="selectSecurityBalanceRecordsVo"/>
        <where>
            <if test="elderlyId != null  and elderlyId != ''">
                and elderly_id = #{elderlyId}
            </if>
            <if test="lastAmount != null ">
                and last_amount = #{lastAmount}
            </if>
            <if test="changedAmount != null ">
                and changed_amount = #{changedAmount}
            </if>
            <if test="amount != null ">
                and amount = #{amount}
            </if>
            <if test="changedType != null and changedType != ''">
                and changed_type = #{changedType}
            </if>
            <if test="params.beginTime != null and params.beginTime != '' and params.endTime != null and params.endTime != ''">
                and date_format(create_time,'%Y-%m-%d') between date_format(#{params.beginTime},'%Y-%m-%d') and
                date_format(#{params.endTime},'%Y-%m-%d')
            </if>
            and del_flag = '0'
        </where>
        order by create_time desc
    </select>

    <select id="selectSecurityBalanceRecordsById" parameterType="Integer" resultMap="SecurityBalanceRecordsResult">
        <include refid="selectSecurityBalanceRecordsVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <insert id="insertSecurityBalanceRecords" parameterType="com.ruoyi.custom.admin.balance.domain.SecurityBalanceRecords" useGeneratedKeys="true" keyProperty="id">
        insert into t_security_balance_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderlyId != null and elderlyId != ''">elderly_id,</if>
            <if test="lastAmount != null">last_amount,</if>
            <if test="changedAmount != null">changed_amount,</if>
            <if test="amount != null">amount,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="changedType != null">changed_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderlyId != null and elderlyId != ''">#{elderlyId},</if>
            <if test="lastAmount != null">#{lastAmount},</if>
            <if test="changedAmount != null">#{changedAmount},</if>
            <if test="amount != null">#{amount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="changedType != null">#{changedType},</if>
        </trim>
    </insert>

    <update id="updateSecurityBalanceRecords" parameterType="com.ruoyi.custom.admin.balance.domain.SecurityBalanceRecords">
        update t_security_balance_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderlyId != null and elderlyId != ''">elderly_id = #{elderlyId},</if>
            <if test="lastAmount != null">last_amount = #{lastAmount},</if>
            <if test="changedAmount != null">changed_amount = #{changedAmount},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="changedType != null">changed_type = #{changedType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecurityBalanceRecordsById" parameterType="Integer">
        update t_security_balance_records set del_flag = '1' where id = #{id}
    </delete>

    <delete id="deleteSecurityBalanceRecordsByIds" parameterType="String">
        update t_security_balance_records set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
