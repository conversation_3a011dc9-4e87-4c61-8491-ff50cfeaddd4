<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.balance.mapper.SecurityBalanceInfoMapper">

    <resultMap type="com.ruoyi.custom.admin.balance.domain.SecurityBalanceInfo" id="SecurityBalanceInfoResult">
        <id property="id" column="id"/>
        <result property="elderlyId" column="elderly_id"/>
        <result property="lastAmount" column="last_amount"/>
        <result property="amount" column="amount"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectSecurityBalanceInfoVo">
        select id, elderly_id, last_amount, amount, create_time, create_by, update_time, update_by, del_flag, remark
        from t_security_balance_info
    </sql>

    <select id="selectSecurityBalanceInfoList" parameterType="com.ruoyi.custom.admin.balance.domain.SecurityBalanceInfo"
            resultMap="SecurityBalanceInfoResult">
        <include refid="selectSecurityBalanceInfoVo"/>
        <where>
            <if test="elderlyId != null  and elderlyId != ''">
                and elderly_id = #{elderlyId}
            </if>
            <if test="lastAmount != null ">
                and last_amount = #{lastAmount}
            </if>
            <if test="amount != null ">
                and amount = #{amount}
            </if>
            <if test="params.beginTime != null and params.beginTime != '' and params.endTime != null and params.endTime != ''">
                and date_format(create_time,'%Y-%m-%d') between date_format(#{params.beginTime},'%Y-%m-%d') and
                date_format(#{params.endTime},'%Y-%m-%d')
            </if>
            and del_flag = '0'
        </where>
    </select>

    <select id="selectSecurityBalanceInfoById" parameterType="Integer" resultMap="SecurityBalanceInfoResult">
        <include refid="selectSecurityBalanceInfoVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <select id="selectSecurityBalanceInfoByElderlyId" parameterType="String" resultMap="SecurityBalanceInfoResult">
        <include refid="selectSecurityBalanceInfoVo"/>
        where elderly_id = #{elderlyId} and del_flag = '0'
    </select>

    <insert id="insertSecurityBalanceInfo" parameterType="com.ruoyi.custom.admin.balance.domain.SecurityBalanceInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_security_balance_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderlyId != null and elderlyId != ''">elderly_id,</if>
            <if test="lastAmount != null">last_amount,</if>
            <if test="amount != null">amount,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderlyId != null and elderlyId != ''">#{elderlyId},</if>
            <if test="lastAmount != null">#{lastAmount},</if>
            <if test="amount != null">#{amount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSecurityBalanceInfo" parameterType="com.ruoyi.custom.admin.balance.domain.SecurityBalanceInfo">
        update t_security_balance_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderlyId != null and elderlyId != ''">elderly_id = #{elderlyId},</if>
            <if test="lastAmount != null">last_amount = #{lastAmount},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecurityBalanceInfoById" parameterType="Integer">
        update t_security_balance_info set del_flag = '1' where id = #{id}
    </delete>

    <delete id="deleteSecurityBalanceInfoByIds" parameterType="String">
        update t_security_balance_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
