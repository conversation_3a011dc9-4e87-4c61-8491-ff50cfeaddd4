<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.employee.mapper.EmployeeEntryStatusMapper">

    <resultMap type="EmployeeEntryStatus" id="EmployeeEntryStatusResult">
        <result property="id" column="id"/>
        <result property="employeeId" column="employee_id"/>
        <result property="type" column="type"/>
        <result property="statusDate" column="status_date"/>
        <result property="result" column="result"/>
        <result property="remark" column="remark"/>
        <result property="fileUrls" column="file_urls"/>
    </resultMap>

    <sql id="selectEmployeeEntryStatusVo">
        SELECT id, employee_id, type, status_date, result, remark, file_urls
        FROM t_employee_entry_status
    </sql>

    <select id="selectEmployeeEntryStatusList" parameterType="EmployeeEntryStatus"
            resultMap="EmployeeEntryStatusResult">
        <include refid="selectEmployeeEntryStatusVo"/>
        <where>
            <if test="employeeId != null ">
                and employee_id = #{employeeId}
            </if>
            <if test="type != null  and type != ''">
                and type = #{type}
            </if>
            <if test="statusDate != null ">
                and status_date = #{statusDate}
            </if>
            <if test="result != null  and result != ''">
                and result = #{result}
            </if>
            <if test="fileUrls != null  and fileUrls != ''">
                and file_urls = #{fileUrls}
            </if>
        </where>
    </select>

    <select id="selectEmployeeEntryStatusById" parameterType="Long"
            resultMap="EmployeeEntryStatusResult">
        <include refid="selectEmployeeEntryStatusVo"/>
        where id = #{id}
    </select>

    <insert id="insertEmployeeEntryStatus" parameterType="EmployeeEntryStatus" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_employee_entry_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="employeeId != null">employee_id,
            </if>
            <if test="type != null and type != ''">type,
            </if>
            <if test="statusDate != null">status_date,
            </if>
            <if test="result != null">result,
            </if>
            <if test="remark != null">remark,
            </if>
            <if test="fileUrls != null">file_urls,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="employeeId != null">#{employeeId},
            </if>
            <if test="type != null and type != ''">#{type},
            </if>
            <if test="statusDate != null">#{statusDate},
            </if>
            <if test="result != null">#{result},
            </if>
            <if test="remark != null">#{remark},
            </if>
            <if test="fileUrls != null">#{fileUrls},
            </if>
        </trim>
    </insert>

    <update id="updateEmployeeEntryStatus" parameterType="EmployeeEntryStatus">
        update t_employee_entry_status
        <trim prefix="SET" suffixOverrides=",">
            <if test="employeeId != null">employee_id =
                #{employeeId},
            </if>
            <if test="type != null and type != ''">type =
                #{type},
            </if>
            <if test="statusDate != null">status_date =
                #{statusDate},
            </if>
            <if test="result != null">result =
                #{result},
            </if>
            <if test="remark != null">remark =
                #{remark},
            </if>
            <if test="fileUrls != null">file_urls =
                #{fileUrls},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeEntryStatusById" parameterType="Long">
        DELETE
        FROM t_employee_entry_status
        WHERE id = #{id}
    </delete>

    <delete id="deleteEmployeeEntryStatusByIds" parameterType="String">
        delete from t_employee_entry_status where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
