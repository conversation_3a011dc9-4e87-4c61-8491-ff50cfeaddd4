<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.employee.mapper.EmployeeRewardPunishmentMapper">

    <resultMap type="EmployeeRewardPunishment" id="EmployeeRewardPunishmentResult">
        <result property="id" column="id"/>
        <result property="employeeId" column="employee_id"/>
        <result property="type" column="type"/>
        <result property="item" column="item"/>
        <result property="date" column="date"/>
        <result property="amount" column="amount"/>
        <result property="goods" column="goods"/>
        <result property="status" column="status"/>
        <result property="remarks" column="remarks"/>
        <result property="attachmentUrl" column="attachment_url"/>

        <result property="employeeName" column="employee_name"/>
    </resultMap>

    <sql id="selectEmployeeRewardPunishmentVo">
        SELECT t.id,
               t.employee_id,
               t.type,
               t.item,
               t.date,
               t.amount,
               t.goods,
               t.status,
               t.remarks,
               t.attachment_url,

               t1.name AS employee_name
        FROM t_employee_reward_punishment t
                 LEFT JOIN t_employee_info t1 ON t.employee_id = t1.id
    </sql>

    <select id="selectEmployeeRewardPunishmentList" parameterType="EmployeeRewardPunishment"
            resultMap="EmployeeRewardPunishmentResult">
        <include refid="selectEmployeeRewardPunishmentVo"/>
        <where>
            <if test="employeeId != null ">
                and t.employee_id = #{employeeId}
            </if>
            <if test="type != null  and type != ''">
                and t.type = #{type}
            </if>
            <if test="item != null  and item != ''">
                and t.item = #{item}
            </if>
            <if test="date != null ">
                and t.date = #{date}
            </if>
            <if test="amount != null ">
                and t.amount = #{amount}
            </if>
            <if test="goods != null  and goods != ''">
                and t.goods = #{goods}
            </if>
            <if test="status != null  and status != ''">
                and t.status = #{status}
            </if>
            <if test="remarks != null  and remarks != ''">
                and t.remarks = #{remarks}
            </if>
            <if test="attachmentUrl != null  and attachmentUrl != ''">
                and t.attachment_url = #{attachmentUrl}
            </if>
        </where>
    </select>

    <select id="selectEmployeeRewardPunishmentById" parameterType="Long"
            resultMap="EmployeeRewardPunishmentResult">
        <include refid="selectEmployeeRewardPunishmentVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertEmployeeRewardPunishment" parameterType="EmployeeRewardPunishment" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_employee_reward_punishment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="employeeId != null">employee_id,
            </if>
            <if test="type != null and type != ''">type,
            </if>
            <if test="item != null and item != ''">item,
            </if>
            <if test="date != null">date,
            </if>
            <if test="amount != null">amount,
            </if>
            <if test="goods != null">goods,
            </if>
            <if test="status != null and status != ''">status,
            </if>
            <if test="remarks != null">remarks,
            </if>
            <if test="attachmentUrl != null">attachment_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="employeeId != null">#{employeeId},
            </if>
            <if test="type != null and type != ''">#{type},
            </if>
            <if test="item != null and item != ''">#{item},
            </if>
            <if test="date != null">#{date},
            </if>
            <if test="amount != null">#{amount},
            </if>
            <if test="goods != null">#{goods},
            </if>
            <if test="status != null and status != ''">#{status},
            </if>
            <if test="remarks != null">#{remarks},
            </if>
            <if test="attachmentUrl != null">#{attachmentUrl},
            </if>
        </trim>
    </insert>

    <update id="updateEmployeeRewardPunishment" parameterType="EmployeeRewardPunishment">
        update t_employee_reward_punishment
        <trim prefix="SET" suffixOverrides=",">
            <if test="employeeId != null">employee_id =
                #{employeeId},
            </if>
            <if test="type != null and type != ''">type =
                #{type},
            </if>
            <if test="item != null and item != ''">item =
                #{item},
            </if>
            <if test="date != null">date =
                #{date},
            </if>
            <if test="amount != null">amount =
                #{amount},
            </if>
            <if test="goods != null">goods =
                #{goods},
            </if>
            <if test="status != null and status != ''">status =
                #{status},
            </if>
            <if test="remarks != null">remarks =
                #{remarks},
            </if>
            <if test="attachmentUrl != null">attachment_url =
                #{attachmentUrl},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeRewardPunishmentById" parameterType="Long">
        DELETE
        FROM t_employee_reward_punishment
        WHERE id = #{id}
    </delete>

    <delete id="deleteEmployeeRewardPunishmentByIds" parameterType="String">
        delete from t_employee_reward_punishment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
