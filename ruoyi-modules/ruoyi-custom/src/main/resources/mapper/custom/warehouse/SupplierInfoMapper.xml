<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.warehouse.mapper.SupplierInfoMapper">

    <resultMap type="SupplierInfo" id="SupplierInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="kind" column="kind"/>
        <result property="principal" column="principal"/>
        <result property="phone" column="phone"/>
        <result property="address" column="address"/>
        <result property="supplyState" column="supply_state"/>
        <result property="supplierLevel" column="supplier_level"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectSupplierInfoVo">
        select id, name, kind, principal, phone, address, supply_state, supplier_level, create_time, create_by,
        update_time, update_by, del_flag, remark from t_supplier_info
    </sql>

    <select id="selectSupplierInfoList" parameterType="SupplierInfo" resultMap="SupplierInfoResult">
        <include refid="selectSupplierInfoVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="kind != null  and kind != ''">and kind = #{kind}</if>
            <if test="principal != null  and principal != ''">and principal = #{principal}</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
            <if test="supplyState != null  and supplyState != ''">and supply_state = #{supplyState}</if>
            <if test="supplierLevel != null  and supplierLevel != ''">and supplier_level = #{supplierLevel}</if>
        </where>
    </select>

    <select id="selectSupplierInfoById" parameterType="Long" resultMap="SupplierInfoResult">
        <include refid="selectSupplierInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierInfo" parameterType="SupplierInfo">
        insert into t_supplier_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="kind != null">kind,</if>
            <if test="principal != null">principal,</if>
            <if test="phone != null">phone,</if>
            <if test="address != null">address,</if>
            <if test="supplyState != null">supply_state,</if>
            <if test="supplierLevel != null">supplier_level,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="kind != null">#{kind},</if>
            <if test="principal != null">#{principal},</if>
            <if test="phone != null">#{phone},</if>
            <if test="address != null">#{address},</if>
            <if test="supplyState != null">#{supplyState},</if>
            <if test="supplierLevel != null">#{supplierLevel},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSupplierInfo" parameterType="SupplierInfo">
        update t_supplier_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="kind != null">kind = #{kind},</if>
            <if test="principal != null">principal = #{principal},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="address != null">address = #{address},</if>
            <if test="supplyState != null">supply_state = #{supplyState},</if>
            <if test="supplierLevel != null">supplier_level = #{supplierLevel},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierInfoById" parameterType="Long">
        delete from t_supplier_info where id = #{id}
    </delete>

    <delete id="deleteSupplierInfoByIds" parameterType="String">
        update t_supplier_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="getSupplierLabelList" resultType="cn.hutool.json.JSONObject">
        select id as value , name as label from t_supplier_info where del_flag = '0'
    </select>
</mapper>
