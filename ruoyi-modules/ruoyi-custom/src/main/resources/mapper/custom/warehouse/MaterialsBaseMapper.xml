<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.warehouse.mapper.MaterialsBaseMapper">

    <resultMap type="MaterialsBase" id="MaterialsBaseResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="specification" column="specification"/>
        <result property="materialDescription" column="material_description"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="lowerWarningValue" column="lower_warning_value"/>
    </resultMap>

    <sql id="selectMaterialsBaseVo">
        select id, name, type, specification, material_description, lower_warning_value, create_time, create_by,
        update_time, update_by, del_flag, remark from t_materials_base
    </sql>

    <select id="selectMaterialsBaseList" parameterType="MaterialsBase" resultMap="MaterialsBaseResult">
        <include refid="selectMaterialsBaseVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="specification != null  and specification != ''">and specification = #{specification}</if>
            <if test="materialDescription != null  and materialDescription != ''">and material_description =
                #{materialDescription}
            </if>
        </where>
    </select>

    <select id="selectMaterialsBaseById" parameterType="Long" resultMap="MaterialsBaseResult">
        <include refid="selectMaterialsBaseVo"/>
        where id = #{id}
    </select>

    <insert id="insertMaterialsBase" parameterType="MaterialsBase">
        insert into t_materials_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="specification != null">specification,</if>
            <if test="materialDescription != null">material_description,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="lowerWarningValue != null">lower_warning_value,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="specification != null">#{specification},</if>
            <if test="materialDescription != null">#{materialDescription},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="lowerWarningValue != null">#{lowerWarningValue},</if>
        </trim>
    </insert>

    <update id="updateMaterialsBase" parameterType="MaterialsBase">
        update t_materials_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="materialDescription != null">material_description = #{materialDescription},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="lowerWarningValue != null">lower_warning_value = #{lowerWarningValue},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMaterialsBaseById" parameterType="Long">
        delete from t_materials_base where id = #{id}
    </delete>

    <delete id="deleteMaterialsBaseByIds" parameterType="String">
        update t_materials_base set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getMaterialsStockList" resultType="com.ruoyi.custom.admin.warehouse.domain.vo.MaterialsBaseVo"
            parameterType="com.ruoyi.custom.admin.warehouse.domain.vo.MaterialsBaseVo">
        SELECT
        a.id,
        a.name,
        a.type,
        IFNULL(b.inQuantity,0) as inQuantity,
        IFNULL(b.outQuantity,0) as outQuantity,
        IFNULL(c.quantity,0) as quantity,
        a.specification,
        a.material_description,
        a.lower_warning_value,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_materials_base as a inner join
        (SELECT
        materials_id,
        SUM( CASE WHEN type = '0' THEN quantity ELSE 0 END ) AS inQuantity,
        SUM( CASE WHEN type = '1' THEN quantity ELSE 0 END ) AS outQuantity
        FROM
        t_warehousing_materials_log_info
        <where>
            <if test="warehouseId != null ">
                and warehouse_id = #{warehouseId}
            </if>
        </where>
        group by materials_id ) as b on a.id = b.materials_id inner join
        (
        select base_id, SUM(quantity) as quantity from t_materials_remaining_stock
        <where>
            <if test="warehouseId != null ">
                and warehouse_id = #{warehouseId}
            </if>
        </where>
        GROUP BY base_id
        )
        as c on c.base_id = a.id
        <where>
            a.del_flag = '0'
            <if test="type != null and type != ''">
                and a.type = #{type}
            </if>
            <if test="name != null and name != ''">
                and a.name like concat('%', #{name}, '%')
            </if>
        </where>
        order by a.create_time desc
    </select>
    <select id="getMaterialsList" resultType="cn.hutool.json.JSONObject" parameterType="Long">
        SELECT
        a.id AS value,
        a.name AS label,
        IFNULL( b.quantity, 0 ) AS stockQuantity
        FROM
        t_materials_base AS a
        LEFT JOIN (select quantity,base_id from t_materials_remaining_stock where warehouse_id = #{warehouseId}) AS b ON
        a.id = b.base_id
        WHERE
        a.del_flag = '0'
    </select>

    <select id="getMaterialsListByWarehouseId" resultType="cn.hutool.json.JSONObject" parameterType="Long">
        SELECT
        b.id AS materialsId ,
        b.NAME AS materialsName,
        c.dict_label AS typeLabel,
        b.specification AS specification,
        a.quantity AS previousQuantity
        FROM
        t_materials_remaining_stock AS a
        LEFT JOIN t_materials_base AS b ON a.base_id = b.id
        LEFT JOIN ( SELECT dict_value, dict_label FROM sys_dict_data WHERE dict_type = 'material_category' ) AS c ON
        b.type = c.dict_value where b.del_flag = '0' and a.warehouse_id = #{warehouseId}
    </select>
</mapper>
