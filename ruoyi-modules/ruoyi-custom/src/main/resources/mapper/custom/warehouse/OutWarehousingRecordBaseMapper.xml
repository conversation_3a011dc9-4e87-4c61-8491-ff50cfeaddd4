<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.warehouse.mapper.OutWarehousingRecordBaseMapper">

    <resultMap type="OutWarehousingRecordBase" id="OutWarehousingRecordBaseResult">
        <result property="id" column="id"/>
        <result property="number" column="number"/>
        <result property="materialUsage" column="material_usage"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="outDate" column="out_date"/>
        <result property="receiverPerson" column="receiver_person"/>
        <result property="registerPerson" column="register_person"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="warehouseName" column="warehouseName"/>
        <result property="receiverPersonName" column="receiverPersonName"/>
        <result property="registerPersonName" column="registerPersonName"/>
    </resultMap>

    <sql id="selectOutWarehousingRecordBaseVo">
        SELECT
        a.id,
        a.number,
        a.material_usage,
        a.warehouse_id,
        b.name as warehouseName,
        a.out_date,
        a.receiver_person,
        a.register_person,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.remark
        FROM
        t_out_warehousing_record_base as a left join t_warehouse_base as b on a.warehouse_id = b.id
    </sql>

    <select id="selectOutWarehousingRecordBaseList" parameterType="OutWarehousingRecordBase"
            resultMap="OutWarehousingRecordBaseResult">
        <include refid="selectOutWarehousingRecordBaseVo"/>
        <where>
            a.del_flag = '0'
            <if test="number != null  and number != ''">and a.number like concat('%',#{number},'%')</if>
            <if test="materialUsage != null  and materialUsage != ''">and a.material_usage = #{materialUsage}</if>
            <if test="beginDate != null and endDate!=null">and date_format(a.out_date,'%y%m%d') BETWEEN
                date_format(#{beginDate},'%y%m%d') and date_format(#{endDate},'%y%m%d')
            </if>
            <if test="warehouseId != null ">and a.warehouse_id = #{warehouseId}</if>
            <if test="warehouseName != null and warehouseName != '' ">and b.name = #{warehouseName}</if>
            <if test="outDate != null ">and a.out_date = #{outDate}</if>
            <if test="receiverPerson != null  and receiverPerson != ''">and a.receiver_person = #{receiverPerson}</if>
            <if test="registerPerson != null  and registerPerson != ''">and a.register_person = #{registerPerson}</if>
        </where>
    </select>

    <select id="selectOutWarehousingRecordBaseById" parameterType="Long" resultMap="OutWarehousingRecordBaseResult">
        <include refid="selectOutWarehousingRecordBaseVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertOutWarehousingRecordBase" parameterType="OutWarehousingRecordBase" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_out_warehousing_record_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="number != null">number,</if>
            <if test="materialUsage != null">material_usage,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="outDate != null">out_date,</if>
            <if test="receiverPerson != null">receiver_person,</if>
            <if test="registerPerson != null">register_person,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="number != null">#{number},</if>
            <if test="materialUsage != null">#{materialUsage},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="outDate != null">#{outDate},</if>
            <if test="receiverPerson != null">#{receiverPerson},</if>
            <if test="registerPerson != null">#{registerPerson},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateOutWarehousingRecordBase" parameterType="OutWarehousingRecordBase">
        update t_out_warehousing_record_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="number != null">number = #{number},</if>
            <if test="materialUsage != null">material_usage = #{materialUsage},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="outDate != null">out_date = #{outDate},</if>
            <if test="receiverPerson != null">receiver_person = #{receiverPerson},</if>
            <if test="registerPerson != null">register_person = #{registerPerson},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOutWarehousingRecordBaseById" parameterType="Long">
        delete from t_out_warehousing_record_base where id = #{id}
    </delete>

    <delete id="deleteOutWarehousingRecordBaseByIds" parameterType="String">
        update t_out_warehousing_record_base set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
