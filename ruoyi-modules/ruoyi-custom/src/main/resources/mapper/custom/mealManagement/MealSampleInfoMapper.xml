<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.mealManagement.mapper.MealSampleInfoMapper">

    <resultMap type="MealSampleInfo" id="MealSampleInfoResult">
        <result property="id" column="id"/>
        <result property="foodName" column="food_name"/>
        <result property="mealType" column="meal_type"/>
        <result property="sampleTime" column="sample_time"/>
        <result property="labelInfo" column="label_info"/>
        <result property="storageLocation" column="storage_location"/>
        <result property="imgUrls" column="img_urls"/>
    </resultMap>

    <sql id="selectMealSampleInfoVo">
        SELECT id, food_name, meal_type, sample_time, label_info, storage_location, img_urls
        FROM t_meal_sample_info
    </sql>

    <select id="selectMealSampleInfoList" parameterType="MealSampleInfo" resultMap="MealSampleInfoResult">
        <include refid="selectMealSampleInfoVo"/>
        <where>
            <if test="foodName != null  and foodName != ''">
                and food_name like concat('%', #{foodName}, '%')
            </if>
            <if test="mealType != null  and mealType != ''">
                and meal_type = #{mealType}
            </if>
            <if test="sampleTime != null ">
                and date_format(sample_time,'%Y-%m-%d') = date_format(#{sampleTime},'%Y-%m-%d')
            </if>
            <if test="labelInfo != null  and labelInfo != ''">
                and label_info = #{labelInfo}
            </if>
            <if test="storageLocation != null  and storageLocation != ''">
                and storage_location = #{storageLocation}
            </if>
            <if test="params.startSampleTime != null and params.startSampleTime != '' and params.endSampleTime != null and params.endSampleTime != ''">
                and sample_time between #{startSampleTime} and #{endSampleTime}
            </if>
        </where>
        ORDER BY sample_time DESC
    </select>

    <select id="selectMealSampleInfoById" parameterType="Long"
            resultMap="MealSampleInfoResult">
        <include refid="selectMealSampleInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertMealSampleInfo" parameterType="MealSampleInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_meal_sample_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="foodName != null and foodName != ''">food_name,
            </if>
            <if test="mealType != null and mealType != ''">meal_type,
            </if>
            <if test="sampleTime != null">sample_time,
            </if>
            <if test="labelInfo != null">label_info,
            </if>
            <if test="storageLocation != null and storageLocation != ''">storage_location,
            </if>
            <if test="imgUrls != null and imgUrls != ''">img_urls,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="foodName != null and foodName != ''">#{foodName},
            </if>
            <if test="mealType != null and mealType != ''">#{mealType},
            </if>
            <if test="sampleTime != null">#{sampleTime},
            </if>
            <if test="labelInfo != null">#{labelInfo},
            </if>
            <if test="storageLocation != null and storageLocation != ''">#{storageLocation},
            </if>
            <if test="imgUrls != null and imgUrls != ''">#{imgUrls},
            </if>
        </trim>
    </insert>

    <update id="updateMealSampleInfo" parameterType="MealSampleInfo">
        update t_meal_sample_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="foodName != null and foodName != ''">food_name =
                #{foodName},
            </if>
            <if test="mealType != null and mealType != ''">meal_type =
                #{mealType},
            </if>
            <if test="sampleTime != null">sample_time =
                #{sampleTime},
            </if>
            <if test="labelInfo != null">label_info =
                #{labelInfo},
            </if>
            <if test="storageLocation != null and storageLocation != ''">storage_location =
                #{storageLocation},
            </if>
            <if test="imgUrls != null and imgUrls != ''">img_urls =
                #{imgUrls},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMealSampleInfoById" parameterType="Long">
        DELETE
        FROM t_meal_sample_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteMealSampleInfoByIds" parameterType="String">
        delete from t_meal_sample_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
