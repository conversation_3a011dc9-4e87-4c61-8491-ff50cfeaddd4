<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.assessment.mapper.ElderlyCapacityAssessmentMapper">

    <resultMap type="ElderlyCapacityAssessment" id="ElderlyCapacityAssessmentResult">
        <result property="id" column="id"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="assessmentObjBasicsInfo" column="assessment_obj_basics_info" typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler"/>
        <result property="msgSupplierContactInfo" column="msg_supplier_contact_info" typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler"/>
        <result property="diseaseDiagnosisDrugUsage" column="disease_diagnosis_drug_usage" typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler"/>
        <result property="healthRelatedIssues" column="health_related_issues" typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler"/>
        <result property="physiologyBodyAssessment" column="physiology_body_assessment" typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler"/>
        <result property="oldPeopleAbilityAssessment" column="old_people_ability_assessment" typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler"/>
        <result property="basicMotorAbilityAssessment" column="basic_motor_ability_assessment" typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler"/>
        <result property="mentalState" column="mental_state" typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler"/>
        <result property="perceptionSocialParticipation" column="perception_social_participation" typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler"/>
        <result property="healthProblems" column="health_problems" typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler"/>
        <result property="remarks" column="remarks"/>
        <result property="assessmentId" column="assessment_Id"/>
        <result property="fileVideoList" column="file_video_list"  typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler"/>
        <result property="fileImgList" column="file_img_list"  typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler"/>
    </resultMap>

    <sql id="selectElderlyCapacityAssessmentVo">
        select id, serial_number, assessment_obj_basics_info, msg_supplier_contact_info, disease_diagnosis_drug_usage, health_related_issues, physiology_body_assessment, old_people_ability_assessment, basic_motor_ability_assessment, mental_state, perception_social_participation, health_problems, remarks,assessment_Id, file_video_list, file_img_list
        from t_elderly_capacity_assessment
    </sql>

    <select id="selectElderlyCapacityAssessmentList" parameterType="ElderlyCapacityAssessment" resultMap="ElderlyCapacityAssessmentResult">
        <include refid="selectElderlyCapacityAssessmentVo"/>
        <where>
            <if test="serialNumber != null  and serialNumber != ''">
                and serial_number = #{serialNumber}
            </if>
            <if test="assessmentObjBasicsInfo != null  and assessmentObjBasicsInfo != ''">
                and assessment_obj_basics_info = #{assessmentObjBasicsInfo,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler}
            </if>
            <if test="msgSupplierContactInfo != null  and msgSupplierContactInfo != ''">
                and msg_supplier_contact_info = #{msgSupplierContactInfo,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler}
            </if>
            <if test="diseaseDiagnosisDrugUsage != null  and diseaseDiagnosisDrugUsage != ''">
                and disease_diagnosis_drug_usage = #{diseaseDiagnosisDrugUsage,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler}
            </if>
            <if test="healthRelatedIssues != null  and healthRelatedIssues != ''">
                and health_related_issues = #{healthRelatedIssues,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler}
            </if>
            <if test="physiologyBodyAssessment != null  and physiologyBodyAssessment != ''">
                and physiology_body_assessment = #{physiologyBodyAssessment,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler}
            </if>
            <if test="oldPeopleAbilityAssessment != null  and oldPeopleAbilityAssessment != ''">
                and old_people_ability_assessment = #{oldPeopleAbilityAssessment,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler}
            </if>
            <if test="basicMotorAbilityAssessment != null  and basicMotorAbilityAssessment != ''">
                and basic_motor_ability_assessment = #{basicMotorAbilityAssessment,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler}
            </if>
            <if test="mentalState != null  and mentalState != ''">
                and mental_state = #{mentalState,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler}
            </if>
            <if test="perceptionSocialParticipation != null  and perceptionSocialParticipation != ''">
                and perception_social_participation = #{perceptionSocialParticipation,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler}
            </if>
            <if test="healthProblems != null  and healthProblems != ''">
                and health_problems = #{healthProblems,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler}
            </if>
            <if test="remarks != null  and remarks != ''">
                and remarks = #{remarks}
            </if>
            <if test="assessmentId != null ">
                and assessment_Id = #{assessmentId}
            </if>
            <if test="fileVideoList != null  and fileVideoList != ''">
                and file_video_list = #{fileVideoList,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler}
            </if>
            <if test="fileImgList != null  and fileImgList != ''">
                and file_img_list = #{fileImgList,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler}
            </if>
        </where>
    </select>

    <select id="selectElderlyCapacityAssessmentById" parameterType="Long"
            resultMap="ElderlyCapacityAssessmentResult">
        <include refid="selectElderlyCapacityAssessmentVo"/>
        where id = #{id}
    </select>


    <select id="selectElderlyCapacityAssessmentBySerialNumber" parameterType="String"
            resultMap="ElderlyCapacityAssessmentResult">
        <include refid="selectElderlyCapacityAssessmentVo"/>
        where serial_number = #{serialNumber}
    </select>


    <insert id="insertElderlyCapacityAssessment" parameterType="ElderlyCapacityAssessment" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_elderly_capacity_assessment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number,
            </if>
            <if test="assessmentObjBasicsInfo != null">assessment_obj_basics_info,
            </if>
            <if test="msgSupplierContactInfo != null">msg_supplier_contact_info,
            </if>
            <if test="diseaseDiagnosisDrugUsage != null">disease_diagnosis_drug_usage,
            </if>
            <if test="healthRelatedIssues != null">health_related_issues,
            </if>
            <if test="physiologyBodyAssessment != null">physiology_body_assessment,
            </if>
            <if test="oldPeopleAbilityAssessment != null">old_people_ability_assessment,
            </if>
            <if test="basicMotorAbilityAssessment != null">basic_motor_ability_assessment,
            </if>
            <if test="mentalState != null">mental_state,
            </if>
            <if test="perceptionSocialParticipation != null">perception_social_participation,
            </if>
            <if test="healthProblems != null">health_problems,
            </if>
            <if test="remarks != null">remarks,
            </if>
            <if test="assessmentId != null">assessment_Id,
            </if>
            <if test="fileVideoList != null">file_video_list,
            </if>
            <if test="fileImgList != null">file_img_list,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">#{serialNumber},
            </if>
            <if test="assessmentObjBasicsInfo != null">#{assessmentObjBasicsInfo,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="msgSupplierContactInfo != null">#{msgSupplierContactInfo,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="diseaseDiagnosisDrugUsage != null">#{diseaseDiagnosisDrugUsage,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="healthRelatedIssues != null">#{healthRelatedIssues,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="physiologyBodyAssessment != null">#{physiologyBodyAssessment,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="oldPeopleAbilityAssessment != null">#{oldPeopleAbilityAssessment,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="basicMotorAbilityAssessment != null">#{basicMotorAbilityAssessment,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="mentalState != null">#{mentalState,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="perceptionSocialParticipation != null">#{perceptionSocialParticipation,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="healthProblems != null">#{healthProblems,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
            </if>
            <if test="remarks != null">#{remarks},
            </if>
            <if test="assessmentId != null ">#{assessmentId},
            </if>
            <if test="fileVideoList != null">#{fileVideoList,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
            </if>
            <if test="fileImgList != null">#{fileImgList,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
            </if>
        </trim>
    </insert>

    <update id="updateElderlyCapacityAssessment" parameterType="ElderlyCapacityAssessment">
        update t_elderly_capacity_assessment
        <trim prefix="SET" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number =
                #{serialNumber},
            </if>
            <if test="assessmentObjBasicsInfo != null">assessment_obj_basics_info =
                #{assessmentObjBasicsInfo,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="msgSupplierContactInfo != null">msg_supplier_contact_info =
                #{msgSupplierContactInfo,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="diseaseDiagnosisDrugUsage != null">disease_diagnosis_drug_usage =
                #{diseaseDiagnosisDrugUsage,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="healthRelatedIssues != null">health_related_issues =
                #{healthRelatedIssues,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="physiologyBodyAssessment != null">physiology_body_assessment =
                #{physiologyBodyAssessment,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="oldPeopleAbilityAssessment != null">old_people_ability_assessment =
                #{oldPeopleAbilityAssessment,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="basicMotorAbilityAssessment != null">basic_motor_ability_assessment =
                #{basicMotorAbilityAssessment,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="mentalState != null">mental_state =
                #{mentalState,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="perceptionSocialParticipation != null">perception_social_participation =
                #{perceptionSocialParticipation,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="healthProblems != null">health_problems =
                #{healthProblems,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
            </if>
            <if test="remarks != null">remarks =
                #{remarks},
            </if>
            <if test="assessmentId != null">assessment_Id =
                #{assessmentId},
            </if>
            <if test="fileVideoList != null">file_video_list =
                #{fileVideoList,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
            </if>
            <if test="fileImgList != null">file_img_list =
                #{fileImgList,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElderlyCapacityAssessmentById" parameterType="Long">
        delete from t_elderly_capacity_assessment where id = #{id}
    </delete>

    <delete id="deleteElderlyCapacityAssessmentByIds" parameterType="String">
        delete from t_elderly_capacity_assessment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteElderlyCapacityAssessmentBySerialNumber" parameterType="String">
        delete from t_elderly_capacity_assessment where id in
        <foreach item="serialNumber" collection="array" open="(" separator="," close=")">
            #{serialNumber}
        </foreach>
    </delete>
</mapper>
