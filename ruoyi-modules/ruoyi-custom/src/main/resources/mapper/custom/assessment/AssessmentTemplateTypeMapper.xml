<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.assessment.mapper.AssessmentTemplateTypeMapper">

    <resultMap type="AssessmentTemplateType" id="AssessmentTemplateTypeResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAssessmentTemplateTypeVo">
        SELECT id, name, del_flag, create_by, create_time, update_by, update_time
        FROM t_assessment_template_type
    </sql>

    <select id="selectAssessmentTemplateTypeList" parameterType="AssessmentTemplateType"
            resultMap="AssessmentTemplateTypeResult">
        <include refid="selectAssessmentTemplateTypeVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
        </where>
    </select>

    <select id="selectAssessmentTemplateTypeById" parameterType="Long"
            resultMap="AssessmentTemplateTypeResult">
        <include refid="selectAssessmentTemplateTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertAssessmentTemplateType" parameterType="AssessmentTemplateType" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_assessment_template_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,
            </if>
            <if test="delFlag != null and delFlag != ''">del_flag,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="updateBy != null">update_by,
            </if>
            <if test="updateTime != null">update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},
            </if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="updateBy != null">#{updateBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
        </trim>
    </insert>

    <update id="updateAssessmentTemplateType" parameterType="AssessmentTemplateType">
        update t_assessment_template_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name =
                #{name},
            </if>
            <if test="delFlag != null and delFlag != ''">del_flag =
                #{delFlag},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="updateBy != null">update_by =
                #{updateBy},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteAssessmentTemplateTypeById" parameterType="Long">
        UPDATE t_assessment_template_type
        SET del_flag = '1'
        WHERE id = #{id}
    </update>

    <update id="deleteAssessmentTemplateTypeByIds">
        UPDATE t_assessment_template_type
        SET del_flag = #{params.delFlag}, update_by = #{params.updateBy}, update_time = #{params.updateTime}
        WHERE id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>
