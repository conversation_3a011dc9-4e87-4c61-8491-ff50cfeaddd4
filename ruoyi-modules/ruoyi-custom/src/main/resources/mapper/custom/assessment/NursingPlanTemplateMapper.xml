<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.assessment.mapper.NursingPlanTemplateMapper">

    <resultMap type="com.ruoyi.custom.admin.assessment.domain.NursingPlanTemplate" id="NursingPlanTemplateResult">
        <id property="id" column="id"/>
        <result property="healthProblem" column="health_problem"/>
        <result property="templateType" column="template_type"/>
        <result property="details" column="details" typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectNursingPlanTemplateVo">
        select id, health_problem, template_type, details, del_flag, create_by, create_time, update_by, update_time
        from t_nursing_plan_template
    </sql>

    <select id="selectNursingPlanTemplateList" parameterType="com.ruoyi.custom.admin.assessment.domain.NursingPlanTemplate" resultMap="NursingPlanTemplateResult">
        <include refid="selectNursingPlanTemplateVo"/>
        <where>
            del_flag = '0'
            <if test="healthProblem != null and healthProblem != ''">
                AND health_problem like concat('%', #{healthProblem}, '%')
            </if>
            <if test="templateType != null">
                AND template_type = #{templateType}
            </if>
        </where>
    </select>

    <select id="selectNursingPlanTemplateById" parameterType="String" resultMap="NursingPlanTemplateResult">
        <include refid="selectNursingPlanTemplateVo"/>
        where id = #{id} and del_flag = '0'
    </select>
    
    <select id="selectNursingPlanTemplateByHealthProblem" parameterType="String" resultMap="NursingPlanTemplateResult">
        <include refid="selectNursingPlanTemplateVo"/>
        where health_problem = #{healthProblem} and del_flag = '0'
    </select>

    <insert id="insertNursingPlanTemplate" parameterType="com.ruoyi.custom.admin.assessment.domain.NursingPlanTemplate">
        insert into t_nursing_plan_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="healthProblem != null and healthProblem != ''">health_problem,</if>
            <if test="templateType != null">template_type,</if>
            <if test="details != null">details,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            del_flag,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="healthProblem != null and healthProblem != ''">#{healthProblem},</if>
            <if test="templateType != null">#{templateType},</if>
            <if test="details != null">#{details, typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            '0',
        </trim>
    </insert>

    <update id="updateNursingPlanTemplate" parameterType="com.ruoyi.custom.admin.assessment.domain.NursingPlanTemplate">
        update t_nursing_plan_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="healthProblem != null and healthProblem != ''">health_problem = #{healthProblem},</if>
            <if test="templateType != null">template_type = #{templateType},</if>
            <if test="details != null">details = #{details, typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteNursingPlanTemplateById" parameterType="String">
        update t_nursing_plan_template set del_flag = '1' where id = #{id}
    </update>

    <update id="deleteNursingPlanTemplateByIds" parameterType="String">
        update t_nursing_plan_template set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
