package com.ruoyi.custom.admin.marketing.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleFamilyInfo;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.utils.excel.handler.UserNameHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;
import java.util.List;

/**
 * 营销客户信息对象 t_marketing_customer_info
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
@ApiModel(value = "营销客户信息")
public class MarketingCustomerInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 咨询人姓名
     */
    @Excel(name = "*咨询人姓名")
    @ApiModelProperty(value = "咨询人姓名")
    @NotBlank(message = "咨询人姓名不能为空")
    private String consultantName;

    /**
     * 咨询人与老人关系，字典：custom:marketing_customer_info:relationship_with_elder；1：子女，2：父母，3：朋友，4：亲戚，5：夫妻
     */
    @Excel(name = "咨询人与老人关系", readConverterExp = "0=本人,1=子女,2=父母,3=朋友,4=亲戚,5=夫妻")
    @ApiModelProperty(value = "咨询人与老人关系，字典：custom_marketing_customer_info_relationship_with_elder；1：子女，2：父母，3：朋友，4：亲戚，5：夫妻")
    private String relationshipWithElder;

    /**
     * 咨询人联系电话
     */
    @Excel(name = "咨询人联系电话")
    @ApiModelProperty(value = "咨询人联系电话")
    // @NotBlank(message = "咨询人联系电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "咨询人联系电话格式不正确，必须为11位有效手机号码")
    private String consultantPhone;

    /**
     * 咨询日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "咨询日期", width = 30, dateFormat = "yyyy/MM/dd")
    @ApiModelProperty(value = "咨询日期")
    private Date consultationDate;

    /**
     * 咨询方式，字典：custom:marketing_customer_info:consultation_method；1：线上，2：线下
     */
    @Excel(name = "*咨询方式", readConverterExp = "1=线上,2=线下")
    @NotBlank(message = "咨询方式不能为空")
    @ApiModelProperty(value = "咨询方式，字典：custom_marketing_customer_info_consultation_method；1：线上，2：线下")
    private String consultationMethod;

    /**
     * 其他咨询方式
     */
    @Excel(name = "其他咨询方式")
    @ApiModelProperty(value = "其他咨询方式")
    private String otherConsultationMethod;

    /**
     * 来源渠道，字典：custom:marketing_customer_info:source_channel
     * 视频号直播
     * 视频号视频
     * 官网
     */
    @Excel(name = "*来源渠道", readConverterExp = "1=地推,2=视频,3=领导介绍,4=家属在附近,5=公交,6=内部员工,7=其他-手动补充,8=客户转介绍,9=抖音直播,10=抖音视频,11=视频号直播,12=视频号视频,13=官网")
    @NotBlank(message = "来源渠道不能为空")
    @ApiModelProperty(value = "来源渠道，字典：custom_marketing_customer_info_source_channel；1：地推，2：视频，3：领导介绍，4：家属在附近，5：公交，6：内部员工，7：其他-手动补充，8：客户转介绍，9：抖音直播，10：抖音视频，11：视频号直播，12：视频号视频，13：官网")
    private String sourceChannel;

    /**
     * 来源备注
     */
    @Excel(name = "*来源备注")
    @NotBlank(message = "来源备注不能为空")
    @ApiModelProperty(value = "来源备注")
    private String otherSource;

    /**
     * 接待人id
     */
    // @Excel(name = "接待人id")
    @Excel(name = "*接待人", handler = UserNameHandler.class)
    @NotNull(message = "接待人不能为空")
    @ApiModelProperty(value = "接待人id，系统用户")
    private Long marketerId;

    /**
     * 自理情况
     */
    @Excel(name = "自理情况")
    @ApiModelProperty(value = "自理情况")
    private String selfCare;

    /**
     * 接待人
     */
    // @Excel(name = "接待人", handler = UserNameHandler.class)
    @ApiModelProperty(value = "接待人")
    private String marketer;

    /**
     * 咨询内容
     */
    @Excel(name = "咨询内容")
    @ApiModelProperty(value = "咨询内容")
    private String consultationContent;

    /**
     * 老人姓名
     */
    @Excel(name = "老人姓名")
    @ApiModelProperty(value = "老人姓名")
    // @NotBlank(message = "老人姓名不能为空")
    private String elderName;

    /**
     * 老人性别，字典：sys_user_sex；0：男，1：女
     */
    @Excel(name = "老人性别", readConverterExp = "0=男,1=女")
    @ApiModelProperty(value = "老人性别，字典：sys_user_sex；0：男，1：女")
    private String elderGender;

    /**
     * 老人年龄
     */
    @Excel(name = "老人年龄")
    @ApiModelProperty(value = "老人年龄")
    private Integer elderAge;

    /**
     * 老人出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "老人出生日期", width = 30, dateFormat = "yyyy/MM/dd")
    @ApiModelProperty(value = "老人出生日期")
    private Date elderBirthday;

    /**
     * 老人电话
     */
    @Excel(name = "老人电话")
    @ApiModelProperty(value = "老人电话")
    private String elderPhone;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    @ApiModelProperty(value = "身份证号")
    private String idCardNumber;

    /**
     * 民族，字典：nation
     */
    @Excel(name = "民族", readConverterExp = "1=汉族,2=蒙古族,3=回族,4=藏族,5=维吾尔族,6=苗族,7=彝族,8=壮族,9=布依族,10=朝鲜族,11=满族,12=侗族,13=瑶族,14=白族,15=土家族,16=哈尼族,17=哈萨克族,18=傣族,19=黎族,20=僳僳族,21=佤族,22=畲族,23=高山族,24=拉祜族,25=水族,26=东乡族,27=纳西族,28=景颇族,29=柯尔克孜族,30=土族,31=达斡尔族,32=仫佬族,33=羌族,34=布朗族,35=撒拉族,36=毛南族,37=仡佬族,38=锡伯族,39=阿昌族,40=普米族,41=塔吉克族,42=怒族,43=乌兹别克族,44=俄罗斯族,45=鄂温克族,46=德昂族,47=保安族,48=裕固族,49=京族,50=塔塔尔族,51=独龙族,52=鄂伦春族,53=赫哲族,54=门巴族,55=珞巴族,56=基诺族")
    @ApiModelProperty(value = "民族，字典：nation")
    private String nation;

    /**
     * 婚姻状态，字典：custom:marketing_customer_info:marital_status；0=已婚,1=未婚,2=离异,3=丧偶
     */
    @Excel(name = "婚姻状态", readConverterExp = "0=已婚,1=未婚,2=离异,3=丧偶")
    @ApiModelProperty(value = "婚姻状态，字典：custom_marketing_customer_info_marital_status；0：已婚，1：未婚，2：离异，3：丧偶")
    private String maritalStatus;

    /**
     * 居住情况，字典：custom:marketing_customer_info:residence_status；1：独居，2：子女合住，3：夫妻合住
     */
    @Excel(name = "居住情况", readConverterExp = "0=独居,1=子女合住,2=夫妻合住")
    @ApiModelProperty(value = "居住情况，字典：custom_marketing_customer_info_residence_status；0：独居，1：子女合住，2：夫妻合住")
    private String residenceStatus;

    /**
     * 家庭住址
     */
    @Excel(name = "家庭住址")
    @ApiModelProperty(value = "家庭住址")
    private String homeAddress;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 类型，字典：custom:marketing_customer_info:customer_type；1：意向客户，2：失效客户
     */
    // @Excel(name = "类型，字典：custom_marketing_customer_info_customer_type；1：意向客户，2：失效客户")
    @ApiModelProperty(value = "类型，字典：custom_marketing_customer_info_customer_type；1：意向客户，2：失效客户")
    private String customerType;

    /**
     * 失效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    // @Excel(name = "失效时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "失效时间")
    private Date failedTime;

    /**
     * 签约状态
     */
    // @Excel(name = "签约状态")
    @ApiModelProperty(value = "签约状态。字典：custom_marketing_customer_info_sign_status；签约状态，0：未签约；1：已签约")
    private String signStatus;

    /**
     * 客户评估预约处理状态
     */
    // @Excel(name = "客户评估预约处理状态")
    // @ApiModelProperty(value = "客户评估预约处理状态。字典：custom_marketing_customer_info_appointment_status；0：未处理；1：已处理")
    // private String appointmentStatus;

    /**
     * 客户评估预约日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    // @Excel(name = "客户评估预约日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "客户评估预约日期")
    private Date appointmentDate;

    /**
     * 客户评估预约地点
     */
    // @Excel(name = "客户评估预约地点")
    @ApiModelProperty(value = "客户评估预约地点")
    private String appointmentPlace;

    /**
     * 是否是直接入住
     */
    @ApiModelProperty(value = "是否是直接入住。0：否；1：是")
    private String directCheckIn;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @ApiModelProperty(value = "删除标志（0代表存在 1代表删除）")
    private String delFlag;

    // ------------------ 分界线 ------------------
    //
    //         <result property="elderlyId" column="elderly_id"/>
    //     <result property="emergencyContactName" column="emergency_contact_name"/>
    //     <result property="relation" column="relation"/>
    //     <result property="emergencyContactPhone" column="emergency_contact_phone"/>

    /**
     * 老人id
     */
    @ApiModelProperty(value = "老人id")
    private String elderlyId;

    /**
     * 紧急联系人姓名
     */
    @ApiModelProperty(value = "紧急联系人姓名")
    private String emergencyContactName;

    /**
     * 紧急联系人关系
     */
    @ApiModelProperty(value = "紧急联系人关系")
    private String emergencyContactRelation;

    /**
     * 紧急联系人电话
     */
    @ApiModelProperty(value = "紧急联系人电话")
    private String emergencyContactPhone;

    /**
     * 合同签约日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "合同签约日期")
    private Date contractSignDate;

    /**
     * 评估计划id
     */
    @ApiModelProperty(value = "评估计划id")
    private Long assessmentPlanId;

    /**
     * 评估状态Str
     */
    @ApiModelProperty(value = "评估状态Str")
    private String assessmentStatusStr;

    /**
     * 回访计划list
     */
    @ApiModelProperty(value = "回访计划list")
    List<MarketingFollowUp> followUpList;

    /**
     * 待执行计划list
     */
    @ApiModelProperty(value = "待执行计划list")
    List<MarketingFollowUp> followUpPlanList;

    /**
     * 已执行计划list
     */
    @ApiModelProperty(value = "已执行计划list")
    List<MarketingFollowUp> followUpHistoryList;

    /**
     * 老人家属信息list
     */
    @ApiModelProperty(value = "老人家属信息list")
    List<ElderlyPeopleFamilyInfo> familyInfoList;
}
