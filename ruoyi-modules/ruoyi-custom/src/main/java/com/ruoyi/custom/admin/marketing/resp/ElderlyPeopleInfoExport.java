package com.ruoyi.custom.admin.marketing.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.custom.admin.marketing.domain.ContractInfo;
import com.ruoyi.custom.utils.DictUtils;
import io.seata.common.util.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 老人基础信息export
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
@Data
@ApiModel(value = "老人基础信息export")
public class ElderlyPeopleInfoExport extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "长者姓名")
    @Excel(name = "长者姓名")
    private String name;

    @Excel(name = "性别", readConverterExp = "0=男,1=女")
    @ApiModelProperty(value = "性别")
    private String sex;

    @Excel(name = "身份证号")
    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    @Excel(name = "手机号")
    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "出生日期")
    @Excel(name = "出生日期", dateFormat = "yyyy-MM-dd")
    private Date dateBirth;

    @Excel(name = "年龄")
    @ApiModelProperty(value = "年龄")
    private Integer age;

    @Excel(name = "民族", readConverterExp = "1=汉族,2=蒙古族,3=回族,4=藏族,5=维吾尔族,6=苗族,7=彝族,8=壮族,9=布依族,10=朝鲜族,11=满族,12=侗族,13=瑶族,14=白族,15=土家族,16=哈尼族,17=哈萨克族,18=傣族,19=黎族,20=僳僳族,21=佤族,22=畲族,23=高山族,24=拉祜族,25=水族,26=东乡族,27=纳西族,28=景颇族,29=柯尔克孜族,30=土族,31=达斡尔族,32=仫佬族,33=羌族,34=布朗族,35=撒拉族,36=毛南族,37=仡佬族,38=锡伯族,39=阿昌族,40=普米族,41=塔吉克族,42=怒族,43=乌兹别克族,44=俄罗斯族,45=鄂温克族,46=德昂族,47=保安族,48=裕固族,49=京族,50=塔塔尔族,51=独龙族,52=鄂伦春族,53=赫哲族,54=门巴族,55=珞巴族,56=基诺族")
    @ApiModelProperty(value = "民族")
    private String nation;

    @Excel(name = "婚姻状态", readConverterExp = "0=已婚,1=未婚,2=离异,3=丧偶")
    @ApiModelProperty(value = "婚姻状态")
    private String marriageStatus;

    @Excel(name = "居住情况", readConverterExp = "0=独居,1=子女合住,2=夫妻合住")
    @ApiModelProperty(value = "居住情况")
    private String livingSituation;

    @Excel(name = "人员情况", readConverterExp = "0=在世,1=离世")
    @ApiModelProperty(value = "人员情况")
    private String staffStatus;

    @Excel(name = "养老顾问")
    @ApiModelProperty(value = "养老顾问")
    private String marketer;

    @Excel(name = "家庭住址")
    @ApiModelProperty(value = "家庭住址")
    private String homeAddress;

    @Excel(name = "头像地址", isWrapText = true)
    @ApiModelProperty(value = "头像地址")
    private String img;

    @Excel(name = "咨询日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "咨询日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date consultationDate;

    @Excel(name = "咨询渠道", readConverterExp = "1=地推,2=视频,3=领导介绍,4=家属在附近,5=公交,6=内部员工,7=其他-手动补充,8=客户转介绍,9=抖音直播,10=抖音视频,11=视频号直播,12=视频号视频,13=官网")
    @ApiModelProperty(value = "咨询渠道")
    private String sourceChannel;

    @Excel(name = "紧急联系人姓名")
    @ApiModelProperty(value = "紧急联系人姓名")
    private String emergencyContactName;

    @Excel(name = "紧急联系人手机号")
    @ApiModelProperty(value = "紧急联系人手机号")
    private String emergencyContactPhone;

    @Excel(name = "紧急联系人关系", readConverterExp = "0=父女,1=父子,2=母女,3=母子,4=夫妻,5=亲人,6=朋友,7=其他")
    @ApiModelProperty(value = "紧急联系人关系")
    private String relation;

    @Excel(name = "紧急联系人住址")
    @ApiModelProperty(value = "紧急联系人住址")
    private String emergencyAddress;

    @Excel(name = "紧急联系人工作单位")
    @ApiModelProperty(value = "紧急联系人工作单位")
    private String emergencyWorkUnit;

    @Excel(name = "经济来源", readConverterExp = "0=退休金,1=子女赡养,2=工资,3=国家补贴")
    @ApiModelProperty(value = "经济来源")
    private String economicSources;

    @Excel(name = "月收入")
    @ApiModelProperty(value = "月收入")
    private String monthlyIncome;

    @Excel(name = "社保账户")
    @ApiModelProperty(value = "社保账户")
    private String socialSecurityNo;

    @Excel(name = "合同编号", width = 25)
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    @Excel(name = "合同签订日期", dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "合同签订日期")
    private Date contractSignDate;

    @Excel(name = "合同开始日期", dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "合同开始日期")
    private Date contractStartDate;

    @Excel(name = "合同结束日期", dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "合同结束日期")
    private Date contractEndDate;

    @Excel(name = "签约状态")
    @ApiModelProperty(value = "签约状态")
    private String contractStateStr;

    @Excel(name = "入住状态", readConverterExp = "0=入住中,1=未入住,2=请假中,3=已退住")
    @ApiModelProperty(value = "入住状态", name = "status", required = true, example = "0,1,2")
    private String status;

    @Excel(name = "护理级别", readConverterExp = "0=自理,1=半护1,2=半护2,3=全护1,4=全护2,5=轻度认知障碍,6=中度认知障碍,7=重度(含极重度认知障碍)")
    @ApiModelProperty(value = "护理级别")
    private String careLevel;

    @Excel(name = "离园日期", dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "离园日期", name = "dischargeDate", required = true, example = "1")
    private Date dischargeDate;

    @Excel(name = "入住房间号", width = 40)
    @ApiModelProperty(value = "入住房间号")
    private String bedName;

}
