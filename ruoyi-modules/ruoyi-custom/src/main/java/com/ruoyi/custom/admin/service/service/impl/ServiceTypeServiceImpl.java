package com.ruoyi.custom.admin.service.service.impl;


import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.custom.admin.service.domain.ServiceType;
import com.ruoyi.custom.admin.service.mapper.ServiceTypeMapper;
import com.ruoyi.custom.admin.service.service.IServiceTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 服务分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Service
public class ServiceTypeServiceImpl implements IServiceTypeService {
    @Autowired
    private ServiceTypeMapper serviceTypeMapper;

    /**
     * 查询服务分类
     *
     * @param id 服务分类主键
     * @return 服务分类
     */
    @Override
    public ServiceType selectServiceTypeById(Long id) {
        return serviceTypeMapper.selectServiceTypeById(id);
    }

    /**
     * 查询服务分类列表
     *
     * @param serviceType 服务分类
     * @return 服务分类
     */
    @Override
    public List<ServiceType> selectServiceTypeList(ServiceType serviceType) {
        return serviceTypeMapper.selectServiceTypeList(serviceType);
    }

    /**
     * 新增服务分类
     *
     * @param serviceType 服务分类
     * @return 结果
     */
    @Override
    public int insertServiceType(ServiceType serviceType) {
        serviceType.setCreateTime(DateUtils.getNowDate());
        return serviceTypeMapper.insertServiceType(serviceType);
    }

    /**
     * 修改服务分类
     *
     * @param serviceType 服务分类
     * @return 结果
     */
    @Override
    public int updateServiceType(ServiceType serviceType) {
        serviceType.setUpdateTime(DateUtils.getNowDate());
        return serviceTypeMapper.updateServiceType(serviceType);
    }

    /**
     * 批量删除服务分类
     *
     * @param ids 需要删除的服务分类主键
     * @return 结果
     */
    @Override
    public int deleteServiceTypeByIds(Long[] ids) {
        return serviceTypeMapper.deleteServiceTypeByIds(ids);
    }

}
