package com.ruoyi.custom.admin.elderlyPeople.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.admin.elderlyPeople.mapper.ElderlyCareMapper;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyCare;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyCareService;

/**
 * 长者入住关怀Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Service
public class ElderlyCareServiceImpl implements IElderlyCareService {
    @Autowired
    private ElderlyCareMapper elderlyCareMapper;

    /**
     * 查询长者入住关怀
     *
     * @param id 长者入住关怀主键
     * @return 长者入住关怀
     */
    @Override
    public ElderlyCare selectElderlyCareById(Long id) {
        return elderlyCareMapper.selectElderlyCareById(id);
    }

    /**
     * 查询长者入住关怀列表
     *
     * @param elderlyCare 长者入住关怀
     * @return 长者入住关怀
     */
    @Override
    public List<ElderlyCare> selectElderlyCareList(ElderlyCare elderlyCare) {
        return elderlyCareMapper.selectElderlyCareList(elderlyCare);
    }

    /**
     * 新增长者入住关怀
     *
     * @param elderlyCare 长者入住关怀
     * @return 结果
     */
    @Override
    public int insertElderlyCare(ElderlyCare elderlyCare) {
        return elderlyCareMapper.insertElderlyCare(elderlyCare);
    }

    /**
     * 修改长者入住关怀
     *
     * @param elderlyCare 长者入住关怀
     * @return 结果
     */
    @Override
    public int updateElderlyCare(ElderlyCare elderlyCare) {
        return elderlyCareMapper.updateElderlyCare(elderlyCare);
    }

    /**
     * 批量删除长者入住关怀
     *
     * @param ids 需要删除的长者入住关怀主键
     * @return 结果
     */
    @Override
    public int deleteElderlyCareByIds(Long[] ids) {
        return elderlyCareMapper.deleteElderlyCareByIds(ids);
    }

    /**
     * 删除长者入住关怀信息
     *
     * @param id 长者入住关怀主键
     * @return 结果
     */
    @Override
    public int deleteElderlyCareById(Long id) {
        return elderlyCareMapper.deleteElderlyCareById(id);
    }
} 