package com.ruoyi.custom.api.workerApp;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.map.MapUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.attendance.context.ShiftContext;
import com.ruoyi.custom.admin.attendance.domain.AttendanceRecord;
import com.ruoyi.custom.admin.attendance.domain.ShiftHandOverRecord;
import com.ruoyi.custom.admin.attendance.domain.ShiftInfo;
import com.ruoyi.custom.admin.attendance.domain.ShiftLocation;
import com.ruoyi.custom.admin.attendance.service.*;
import com.ruoyi.custom.admin.attendance.vo.ClockVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

import static java.util.Calendar.HOUR_OF_DAY;
import static java.util.Calendar.MINUTE;

/**
 * 护工APP -考勤&交接班接口
 */
@RestController("workerAppAttendanceController")
@RequestMapping("/workerApp/attendance")
@Api(tags = "护工APP-考勤&交接班")
public class AttendanceController extends BaseController {

    @Autowired
    private ShiftHandOverRecordService shiftHandOverRecordService;

    @Autowired
    private SchedulingGroupService schedulingGroupService;

    @Autowired
    private CycleManagementService cycleManagementService;

    @Autowired
    private ShiftInfoService shiftInfoService;

    @Autowired
    private AttendanceRecordService attendanceRecordService;


    /**
     * 交接班
     */
    @PostMapping(value = "/handOver")
    @ApiOperation(value = "交接班")
    public AjaxResult handOver(@RequestBody ShiftHandOverRecord shiftHandOverRecord) {
        Long workId = SecurityUtils.getLoginUser().getUserid();
        String nickName = SecurityUtils.getLoginUser().getSysUser().getNickName();
        shiftHandOverRecord.setHandoverId(workId);
        shiftHandOverRecord.setHandoverName(nickName);
        shiftHandOverRecordService.insertShiftHandOverRecord(shiftHandOverRecord);
        return success();
    }

    /**
     * 个人交接班记录
     */
    @GetMapping(value = "/handOver/ownerRecord")
    @ApiOperation(value = "个人交接班记录")
    public TableDataInfo<ShiftHandOverRecord> handOverRecord() {
        startPage();
        Long workId = SecurityUtils.getLoginUser().getUserid();
        ShiftHandOverRecord param = new ShiftHandOverRecord();
        param.setParams(MapUtil.of("handOrRecevId", workId));
        return getDataTable(shiftHandOverRecordService.selectShiftHandOverRecordList(param));
    }

    /**
     * 查询我的排班
     */
    @GetMapping("/myScheduling")
    @ApiModelProperty("查询我的排班")
    public AjaxResult myScheduling(@JsonFormat(pattern = "yyyy-MM") Date datePram) {
        return AjaxResult.success(schedulingGroupService.myScheduling(datePram));
    }

    /**
     * 打卡
     */
    @GetMapping("/clock")
    @ApiOperation(value = "打卡")
    public AjaxResult clock(@RequestParam String longitude, @RequestParam String latitude) {
        // 校验参数
        if (longitude == null || latitude == null) {
            return AjaxResult.error("无法获取位置信息");
        }

        // 查询基础信息
        Long userId = SecurityUtils.getLoginUser().getUserid();
        String nickName = SecurityUtils.getLoginUser().getSysUser().getNickName();

        // 获取排班上下文
        ShiftContext shiftContext = schedulingGroupService.getShiftContext(userId, longitude, latitude);

        // 校验距离
        if (shiftContext.getShiftLocation().stream().noneMatch(d -> d.getDistance() <= shiftContext.getShiftInfo().getCheckinLocationRange())) {
            return AjaxResult.error("打卡失败，超出打卡范围");
        }

        // 用户当天打卡记录
        AttendanceRecord attendanceRecord = attendanceRecordService.selectTodayAttendanceRecord(userId);

        // 处理打卡逻辑
        return handleClockLogic(attendanceRecord, shiftContext.getShiftInfo(), nickName, userId);
    }

    /**
     * 用户打卡信息
     */
    @GetMapping("/clock/info")
    @ApiOperation(value = "当前是否可打卡，0：不可以，1：可以")
    public AjaxResult isClock(@RequestParam String longitude, @RequestParam String latitude) {
        // 校验参数
        if (longitude == null || latitude == null) {
            return AjaxResult.error("无法获取位置信息");
        }

        // 获取用户基础信息
        Long userId = SecurityUtils.getLoginUser().getUserid();

        // 获取排班上下文
        ShiftContext shiftContext = schedulingGroupService.getShiftContext(userId, longitude, latitude);

        // 获取用户当天打卡记录
        AttendanceRecord attendanceRecord = attendanceRecordService.selectTodayAttendanceRecord(userId);

        // 地点是否在考勤范围内
        boolean isInRange = shiftContext.getShiftLocation().stream().anyMatch(d -> d.getDistance() <= shiftContext.getShiftInfo().getCheckinLocationRange());

        // 是否在上班打卡时间范围内
        boolean isInUpClock = isInClockRange(shiftContext.getShiftInfo(), "up");

        // 是否在下班打卡时间范围内
        boolean isInDownClock = isInClockRange(shiftContext.getShiftInfo(), "down");

        // 构建返回信息
        ClockVo clockVo = ClockVo.builder()
                .isUpClock(isInRange && isInUpClock ? 1 : 0)
                .isDownClock(isInRange && isInDownClock ? 1 : 0)
                .startTime(shiftContext.getShiftInfo().getStartTime())
                .startClock(attendanceRecord == null || attendanceRecord.getOnDutyCheckinTime() == null ? 0 : 1)
                .onDutyCheckinTime(attendanceRecord == null ? null : formatTime(attendanceRecord.getOnDutyCheckinTime()))
                .endTime(shiftContext.getShiftInfo().getEndTime())
                .endClock(attendanceRecord == null || attendanceRecord.getOffDutyCheckinTime() == null ? 0 : 1)
                .offDutyCheckinTime(attendanceRecord == null ? null : formatTime(attendanceRecord.getOffDutyCheckinTime()))
                .checkinLocation(shiftContext.getShiftLocation().stream().filter(d -> d.getDistance() <= shiftContext.getShiftInfo()
                        .getCheckinLocationRange()).findFirst().orElse(new ShiftLocation()).getCheckinLocation())
                .build();

        return AjaxResult.success(clockVo);
    }

    private boolean isInClockRange(ShiftInfo shiftInfo, String flag) {
        DateTime nowTime = DateTime.now();
        if ("up".equals(flag)) {
            return isInRange(nowTime, parseTime(shiftInfo.getStartTime()), parseTime(shiftInfo.getStartCheckinTerminus()));
        } else if ("down".equals(flag)) {
            return isInRange(nowTime, parseTime(shiftInfo.getEndTime()), parseTime(shiftInfo.getEndCheckinTerminus()));
        }
        throw new ServiceException("flag参数错误");
    }

    /**
     * 处理打卡逻辑
     */
    private AjaxResult handleClockLogic(AttendanceRecord attendanceRecord, ShiftInfo shiftInfo, String nickName, Long userId) {
        Date now = new Date();
        DateTime nowTime = DateTime.now();

        // 初始化或更新打卡记录
        AttendanceRecord record = attendanceRecord != null ? attendanceRecord : AttendanceRecord.builder()
                .userId(userId)
                .name(nickName)
                .dutyDate(now)
                .onDutyTime(shiftInfo.getStartTime())
                .offDutyTime(shiftInfo.getEndTime())
                .build();

        // 获取时间范围
        DateTime startTime = parseTime(shiftInfo.getStartTime());
        DateTime endTime = parseTime(shiftInfo.getEndTime());
        DateTime startCheckinOrigin = parseTime(shiftInfo.getStartCheckinOrigin());
        DateTime startCheckinTerminus = parseTime(shiftInfo.getStartCheckinTerminus());
        DateTime endCheckinOrigin = parseTime(shiftInfo.getEndCheckinOrigin());
        DateTime endCheckinTerminus = parseTime(shiftInfo.getEndCheckinTerminus());

        if (isInRange(nowTime, startCheckinOrigin, startCheckinTerminus)) { // 上班打卡
            record.setOnDutyCheckinTime(now);
            record.setOnDutyResult(isLate(nowTime, startTime, shiftInfo.getStartLateMinutes()) ? "1" : "0");
        } else if (isInRange(nowTime, endCheckinOrigin, endCheckinTerminus)) { // 下班打卡
            record.setOffDutyCheckinTime(now);
            record.setOffDutyResult(isEarly(nowTime, endTime, shiftInfo.getEndLateMinutes()) ? "2" : "0");
        } else {
            return AjaxResult.error("打卡失败，不在打卡时间范围内");
        }

        // 保存记录
        if (attendanceRecord == null) {
            attendanceRecordService.insertAttendanceRecord(record);
        } else {
            attendanceRecordService.updateAttendanceRecord(record);
        }

        return AjaxResult.success("打卡成功");
    }

    /**
     * 判断当前是否在时间范围内
     */
    private boolean isInRange(DateTime now, DateTime start, DateTime end) {
        return now.isAfterOrEquals(start) && now.isBeforeOrEquals(end);
    }

    /**
     * 判断是否迟到
     */
    private boolean isLate(DateTime now, DateTime start, int lateMinutes) {
        return now.between(start, DateUnit.MINUTE) > lateMinutes;
    }

    /**
     * 判断是否早退
     */
    private boolean isEarly(DateTime now, DateTime end, int earlyMinutes) {
        return now.between(end, DateUnit.MINUTE) < earlyMinutes;
    }

    /**
     * 解析时间字符串
     */
    private DateTime parseTime(String time) {
        String[] parts = time.split(":");
        return DateTime.now().setField(HOUR_OF_DAY, Integer.parseInt(parts[0]))
                .setField(MINUTE, Integer.parseInt(parts[1]));
    }

    /**
     * 反向解析时间字符串
     */
    private String formatTime(Date time) {
        if (time == null) {
            return null;
        }
        return DateTime.of(time).toString("HH:mm");
    }
}
