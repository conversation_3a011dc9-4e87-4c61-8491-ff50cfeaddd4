package com.ruoyi.custom.admin.goods.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @ClassName AddGoodsRequest
 * @Description 添加商品-参数
 * <AUTHOR>
 * @Date 2022/7/12 11:21
 */
@Data
@ApiModel(description = "参数")
public class AddOrUpdateGoodsParam {

    @ApiModelProperty(value = "商品id", required = true)
    private Long id;

    /**
     * 商品分类id
     */
    @NotNull(message = "商品分类id必填！")
    @ApiModelProperty(value = "商品分类id", required = true)
    private Long categoryId;

    /**
     * 商品名称
     */
    @NotBlank(message = "服务/餐品 名称必填！")
    @ApiModelProperty(value = "服务/餐品名称", required = true)
    private String goodsName;

    /**
     * 价格
     */
    @NotNull(message = "价格必填！")
    @ApiModelProperty(value = "价格", required = true)
    private BigDecimal price;

    /**
     * 商品图片
     */
    @NotBlank(message = "图片必填！")
    @ApiModelProperty(value = "商品图片", required = true)
    private String img;

    /**
     * 商品介绍
     */
    @NotBlank(message = "介绍必填！")
    @ApiModelProperty(value = "商品介绍", required = true)
    private String introduce;

    /**
     * 规格
     */
    @ApiModelProperty("规格")
    private String specification;

    /**
     * 库存
     */
    @ApiModelProperty(value = "库存", required = true)
    private Long inventory;

    /**
     * 备注
     */
    @ApiModelProperty("备注（商品介绍）")
    private String remark;

    /**
     * 商品类型：1:服务 2：菜品
     */
    @NotNull(message = "商品类型必填！")
    @ApiModelProperty("商品类型：1:服务 2：菜品")
    private Long type;

    /**
     * 备注2
     */
    @ApiModelProperty("备注2（禁忌说明）")
    private String remarkTwo;

}
