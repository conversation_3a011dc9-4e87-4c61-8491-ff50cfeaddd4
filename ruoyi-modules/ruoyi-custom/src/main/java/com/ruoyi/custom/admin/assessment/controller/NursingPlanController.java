package com.ruoyi.custom.admin.assessment.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.assessment.domain.AssessmentPlan;
import com.ruoyi.custom.admin.assessment.req.NursingPlansReq;
import com.ruoyi.custom.admin.assessment.service.IAssessmentPlanService;
import com.ruoyi.custom.admin.assessment.service.INursingPlanService;
import com.ruoyi.custom.utils.NursingPlanPoiTlUtil;
import com.ruoyi.custom.utils.ZipUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.io.IOException;
import java.util.Map;

/**
 * 护理计划Controller
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@RestController
@RequestMapping("/nursingPlan")
@Api(tags = "护理计划")
public class NursingPlanController extends BaseController {

    @Autowired
    private IAssessmentPlanService assessmentPlanService;

    @Autowired
    private INursingPlanService nursingPlanService;

    /**
     * 获取护理计划指标列表
     */
    @GetMapping("/list/{assessmentPlanId}")
    @ApiOperation(value = "获取护理计划指标列表")
    @ApiImplicitParam(name = "assessmentPlanId", value = "评估计划ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getNursingPlanIndicators(@PathVariable("assessmentPlanId") Long assessmentPlanId) {
        return AjaxResult.success(nursingPlanService.getNursingPlanIndicators(assessmentPlanId));
    }

    /**
     * 新增护理计划指标
     */
    @PostMapping("/{assessmentPlanId}")
    @Log(title = "护理计划", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增护理计划指标")
    @ApiImplicitParam(name = "assessmentPlanId", value = "评估计划ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult addNursingPlanIndicator(@PathVariable("assessmentPlanId") Long assessmentPlanId, @RequestBody JSONObject indicator) {
        return toAjax(nursingPlanService.addNursingPlanIndicator(assessmentPlanId, indicator));
    }

    /**
     * 修改护理计划指标
     */
    @PutMapping("/{assessmentPlanId}/{uuid}")
    @Log(title = "护理计划", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改护理计划指标")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "assessmentPlanId", value = "评估计划ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "uuid", value = "指标UUID", required = true, dataType = "String", paramType = "path")
    })
    public AjaxResult updateNursingPlanIndicator(@PathVariable("assessmentPlanId") Long assessmentPlanId,
                                              @PathVariable("uuid") String uuid,
                                              @RequestBody JSONObject indicator) {
        return toAjax(nursingPlanService.updateNursingPlanIndicator(assessmentPlanId, uuid, indicator));
    }

    /**
     * 删除护理计划指标
     */
    @DeleteMapping("/{assessmentPlanId}/{uuid}")
    @Log(title = "护理计划", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除护理计划指标")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "assessmentPlanId", value = "评估计划ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "uuid", value = "指标UUID", required = true, dataType = "String", paramType = "path")
    })
    public AjaxResult deleteNursingPlanIndicator(@PathVariable("assessmentPlanId") Long assessmentPlanId, @PathVariable("uuid") String uuid) {
        return toAjax(nursingPlanService.deleteNursingPlanIndicator(assessmentPlanId, uuid));
    }

    /**
     * 获取护理计划指标详情
     */
    @GetMapping("/{assessmentPlanId}/{uuid}")
    @ApiOperation(value = "获取护理计划指标详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "assessmentPlanId", value = "评估计划ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "uuid", value = "指标UUID", required = true, dataType = "String", paramType = "path")
    })
    public AjaxResult getNursingPlanIndicator(@PathVariable("assessmentPlanId") Long assessmentPlanId, @PathVariable("uuid") String uuid) {
        return AjaxResult.success(nursingPlanService.getNursingPlanIndicator(assessmentPlanId, uuid));
    }

    /**
     * 导出文件（重构版本）
     */
    private List<String> exportFiles(JSONArray selectedIndicators, AssessmentPlan assessmentPlan,
                                   String templatePath1, String templatePath2,
                                   String tempDir, String fileNamePrefix) throws IOException {
        return NursingPlanPoiTlUtil.batchExportNursingPlans(
                selectedIndicators, assessmentPlan, templatePath1, templatePath2, tempDir, fileNamePrefix);
    }

    /**
     * 批量导出护理计划
     */
    @PostMapping("/export/{assessmentPlanId}")
    @Log(title = "护理计划", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "批量导出护理计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "assessmentPlanId", value = "评估计划ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "nursingPlansReq", value = "护理计划req", required = true, dataType = "params", paramType = "body")
    })
    public void exportNursingPlans(@PathVariable("assessmentPlanId") Long assessmentPlanId, NursingPlansReq nursingPlansReq,
                                   HttpServletRequest request,
                                   HttpServletResponse response) {
        List<String> uuids = nursingPlansReq.getUuids();
        try {
            // 1. 获取评估计划和护理计划指标
            AssessmentPlan assessmentPlan = getAssessmentPlan(assessmentPlanId);
            JSONArray selectedIndicators = getSelectedIndicators(assessmentPlanId, uuids);

            // 2. 获取模板文件路径
            String[] templatePaths = getTemplatePaths();
            String templatePath1 = templatePaths[0];
            String templatePath2 = templatePaths[1];

            // 3. 创建临时目录并导出文件
            String tempDir = createTempDirectory();
            String fileNamePrefix = getFileNamePrefix(assessmentPlan);
            List<String> exportedFiles = exportFiles(selectedIndicators, assessmentPlan, templatePath1, templatePath2, tempDir, fileNamePrefix);

            // 4. 处理文件下载
            handleFileDownload(exportedFiles, tempDir, fileNamePrefix, response);

        } catch (Exception e) {
            handleExportError(e, response);
        }
    }

    /**
     * 获取评估计划
     */
    private AssessmentPlan getAssessmentPlan(Long assessmentPlanId) {
        AssessmentPlan assessmentPlan = assessmentPlanService.selectAssessmentPlanById(assessmentPlanId);
        if (assessmentPlan == null) {
            throw new RuntimeException("找不到指定的评估计划");
        }
        return assessmentPlan;
    }

    /**
     * 获取选定的护理计划指标
     */
    private JSONArray getSelectedIndicators(Long assessmentPlanId, List<String> uuids) {
        JSONArray allIndicators = nursingPlanService.getNursingPlanIndicators(assessmentPlanId);
        JSONArray selectedIndicators = new JSONArray();

        for (int i = 0; i < allIndicators.size(); i++) {
            JSONObject indicator = allIndicators.getJSONObject(i);
            String uuid = indicator.getString("uuid");
            if (uuids.contains(uuid)) {
                selectedIndicators.add(indicator);
            }
        }

        if (selectedIndicators.isEmpty()) {
            throw new RuntimeException("未找到选定的护理计划指标");
        }

        return selectedIndicators;
    }

    /**
     * 获取模板文件路径
     */
    private String[] getTemplatePaths() {
        String templatePath1 = getResourcePath("/templates/assessmentPlan1.docx");
        String templatePath2 = getResourcePath("/templates/assessmentPlan2.docx");

        validateTemplateExists(templatePath1, "护理计划模板文件1");
        validateTemplateExists(templatePath2, "护理计划模板文件2");

        return new String[]{templatePath1, templatePath2};
    }

    /**
     * 获取资源文件路径（支持jar包部署）
     */
    private String getResourcePath(String resourcePath) {
        try {
            // 首先尝试直接访问文件系统路径（本地开发环境）
            String path = getClass().getResource(resourcePath).getPath();
            if (path.startsWith("/")) {
                path = path.substring(1);
            }

            File file = new File(path);
            if (file.exists()) {
                return path;
            }

            // 如果文件不存在，说明在jar包中，需要复制到临时目录
            return copyResourceToTempFile(resourcePath);
            
        } catch (Exception e) {
            // 如果获取资源失败，尝试复制到临时目录
            return copyResourceToTempFile(resourcePath);
        }
    }

    /**
     * 将资源文件复制到临时目录
     */
    private String copyResourceToTempFile(String resourcePath) {
        try (InputStream inputStream = getClass().getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                throw new RuntimeException("找不到资源文件：" + resourcePath);
            }

            // 创建临时文件
            String fileName = resourcePath.substring(resourcePath.lastIndexOf('/') + 1);
            String tempDir = System.getProperty("java.io.tmpdir") + "/templates_" + System.currentTimeMillis();
            new File(tempDir).mkdirs();
            String tempFilePath = tempDir + "/" + fileName;
            
            // 复制资源文件到临时目录
            try (OutputStream outputStream = new FileOutputStream(tempFilePath)) {
                copyStream(inputStream, outputStream);
            }
            
            return tempFilePath;
            
        } catch (IOException e) {
            throw new RuntimeException("复制资源文件失败：" + resourcePath, e);
        }
    }

    /**
     * 验证模板文件是否存在
     */
    private void validateTemplateExists(String templatePath, String templateName) {
        File templateFile = new File(templatePath);
        if (!templateFile.exists()) {
            throw new RuntimeException("找不到" + templateName + "：" + templatePath);
        }
    }

    /**
     * 创建临时目录
     */
    private String createTempDirectory() {
        String tempDir = System.getProperty("java.io.tmpdir") + "/nursing_plan_" + System.currentTimeMillis();
        new File(tempDir).mkdirs();
        return tempDir;
    }

    /**
     * 获取文件名前缀
     */
    private String getFileNamePrefix(AssessmentPlan assessmentPlan) {
        String elderName = assessmentPlan.getElderlyName();
        return elderName != null ? elderName : "护理计划";
    }

    /**
     * 处理文件下载
     */
    private void handleFileDownload(List<String> exportedFiles, String tempDir, String fileNamePrefix,
                                  HttpServletResponse response) throws IOException {
        if (exportedFiles.size() == 1) {
            handleSingleFileDownload(exportedFiles.get(0), tempDir, response);
        } else {
            handleZipFileDownload(exportedFiles, tempDir, fileNamePrefix, response);
        }
    }

    /**
     * 处理单文件下载
     */
    private void handleSingleFileDownload(String filePath, String tempDir, HttpServletResponse response) throws IOException {
        File file = new File(filePath);
        String fileName = file.getName();

        // response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        response.setContentType("application/octet-stream");
        // 使用RFC 5987标准处理中文文件名
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-Disposition",
            "attachment; filename=\"" + fileName.replaceAll("[^\\x00-\\x7F]", "_") + "\"; filename*=UTF-8''" + encodedFileName);

        try (InputStream is = new FileInputStream(file);
             OutputStream os = response.getOutputStream()) {
            copyStream(is, os);
        }

        // 删除临时文件
        file.delete();
        new File(tempDir).delete();
    }

    /**
     * 处理ZIP文件下载
     */
    private void handleZipFileDownload(List<String> exportedFiles, String tempDir, String fileNamePrefix,
                                     HttpServletResponse response) throws IOException {
        String dateStr = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String zipFileName = fileNamePrefix + "_护理计划_" + dateStr + ".zip";
        String zipFilePath = tempDir + "/" + zipFileName;

        // 创建ZIP文件
        ZipUtil.toZip(exportedFiles, zipFilePath);

        // 下载ZIP文件
        File zipFile = new File(zipFilePath);
        response.setContentType("application/zip");

        // 使用RFC 5987标准处理中文文件名
        String encodedZipFileName = URLEncoder.encode(zipFileName, "UTF-8");
        response.setHeader("Content-Disposition",
            "attachment; filename=\"" + zipFileName.replaceAll("[^\\x00-\\x7F]", "_") + "\"; filename*=UTF-8''" + encodedZipFileName);

        try (InputStream is = new FileInputStream(zipFile);
             OutputStream os = response.getOutputStream()) {
            copyStream(is, os);
        }

        // 删除临时文件
        zipFile.delete();
        ZipUtil.deleteFiles(exportedFiles);
        new File(tempDir).delete();
    }

    /**
     * 复制流数据
     */
    private void copyStream(InputStream is, OutputStream os) throws IOException {
        byte[] buffer = new byte[1024];
        int length;
        while ((length = is.read(buffer)) != -1) {
            os.write(buffer, 0, length);
        }
        os.flush();
    }

    /**
     * 处理导出错误
     */
    private void handleExportError(Exception e, HttpServletResponse response) {
        e.printStackTrace();
        response.setStatus(500);
        response.setContentType("application/json");
        try {
            response.getWriter().write("{\"msg\":\"" + e.getMessage() + "\",\"code\":500}");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
