package com.ruoyi.custom.admin.liveManage.mapper;

import java.util.List;

import com.ruoyi.custom.admin.liveManage.domain.LiveLeaveRecords;

/**
 * 居住请假Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
public interface LiveLeaveRecordsMapper {
    /**
     * 查询居住请假
     *
     * @param id 居住请假主键
     * @return 居住请假
     */
    public LiveLeaveRecords selectLiveLeaveRecordsById(String id);

    /**
     * 查询居住请假列表
     *
     * @param liveLeaveRecords 居住请假
     * @return 居住请假集合
     */
    public List<LiveLeaveRecords> selectLiveLeaveRecordsList(LiveLeaveRecords liveLeaveRecords);

    /**
     * 新增居住请假
     *
     * @param liveLeaveRecords 居住请假
     * @return 结果
     */
    public int insertLiveLeaveRecords(LiveLeaveRecords liveLeaveRecords);

    /**
     * 修改居住请假
     *
     * @param liveLeaveRecords 居住请假
     * @return 结果
     */
    public int updateLiveLeaveRecords(LiveLeaveRecords liveLeaveRecords);

    /**
     * 删除居住请假
     *
     * @param id 居住请假主键
     * @return 结果
     */
    public int deleteLiveLeaveRecordsById(String id);

    /**
     * 批量删除居住请假
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLiveLeaveRecordsByIds(String[] ids);

}
