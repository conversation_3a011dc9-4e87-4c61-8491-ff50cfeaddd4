package com.ruoyi.custom.admin.goodsOrder.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName GoodsOrderInfo
 * @Description 商品/餐品 订单表
 * <AUTHOR>
 * @Date 2022/7/13 15:05
 */
@Data
@TableName("t_order_base_info")
@ApiModel("基础订单表")
public class OrderBaseInfo {

    // 订单状态 0：已关闭 1：待支付 2：已付款 3：已接单 4：已完成 5：退款
    public static final Integer STATUS_CLOSE = 0;// 已关闭
    public static final Integer STATUS_WAIT = 1;// 待支付
    public static final Integer STATUS_PAID = 2;// 已付款
    public static final Integer STATUS_TAKE_ORDERS = 3;// 已接单
    public static final Integer STATUS_FINISH = 4;// 已完成
    public static final Integer STATUS_REFUND = 5;// 退款
    // 商品类型 1:商品 2：餐品 3：服务
    public static final Integer TYPE_GOODS = 1;       // 商品
    public static final Integer TYPE_FOOD = 2;        // 餐品
    public static final Integer TYPE_SERVICE = 3;        // 服务
    // public static final Integer PAY_WAY_BALANCE = 1;// 余额付款
    public static final Integer PAY_WAY_WECHAT = 2;// 微信付款

    private static final long serialVersionUID = -2399332173891440440L;
    /**
     * 主键id 订单号
     */
    @ApiModelProperty("主键id 订单号")
    private String id;

    @ApiModelProperty("老人id")
    private String elderlyPeopleId;

    @ApiModelProperty("老人姓名")
    private String elderlyPeople;

    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 下单时间
     */
    @ApiModelProperty("下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date updateTime;

    /**
     * 接单时间
     */
    @ApiModelProperty("接单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date receiveTime;

    /**
     * 完成时间
     */
    @ApiModelProperty("完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date finishTime;

    /**
     * 付款时间
     */
    @ApiModelProperty("付款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date payTime;

    /**
     * 1:余额付款 2：微信在线支付
     */
    @ApiModelProperty("1:余额付款 2：微信在线支付")
    private int payWay;

    /**
     * 价格总和（单商品价格相加）
     */
    @ApiModelProperty("价格总和（单商品价格相加）")
    private BigDecimal totalPrice;

    /**
     * 微信支付流水单号
     */
    @ApiModelProperty("微信支付流水单号")
    private String wxPayOrder;

    /**
     * 展示json字符串
     */
    @ApiModelProperty("展示json字符串")
    private String showJson;

    /**
     * 支付状态 0：支付失败 1：支付成功
     */
    @ApiModelProperty("0：已关闭 1：待支付 2：已付款 3：已接单 4：已完成  5:退款")
    private Integer status;

    /**
     * 商品类型 1:商品 2：餐品 3：服务
     */
    @ApiModelProperty("商品类型 1:商品 2：餐品 3：服务 ")
    private Integer type;

    @ApiModelProperty("商品联合标题")
    private String goodsTitle;

    @ApiModelProperty("总数量")
    private Integer totalNumber;

    @ApiModelProperty("售后状态 0:未售后 1：正在售后 2：退款完成 3：退款失败")
    private Integer afterSalesStatus;

    @TableField(exist = false)
    private Object sepObj;


}
