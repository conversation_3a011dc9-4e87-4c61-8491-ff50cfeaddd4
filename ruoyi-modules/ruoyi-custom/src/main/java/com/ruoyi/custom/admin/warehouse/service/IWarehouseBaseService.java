package com.ruoyi.custom.admin.warehouse.service;

import java.util.List;

import cn.hutool.json.JSONObject;
import com.ruoyi.custom.admin.warehouse.domain.WarehouseBase;

/**
 * 仓库基础信息Service接口
 *
 * <AUTHOR>
 * @date 2022-05-06
 */
public interface IWarehouseBaseService {
    /**
     * 查询仓库基础信息
     *
     * @param id 仓库基础信息主键
     * @return 仓库基础信息
     */
    public WarehouseBase selectWarehouseBaseById(Long id);

    /**
     * 查询仓库基础信息列表
     *
     * @param warehouseBase 仓库基础信息
     * @return 仓库基础信息集合
     */
    public List<WarehouseBase> selectWarehouseBaseList(WarehouseBase warehouseBase);

    /**
     * 新增仓库基础信息
     *
     * @param warehouseBase 仓库基础信息
     * @return 结果
     */
    public int insertWarehouseBase(WarehouseBase warehouseBase);

    /**
     * 修改仓库基础信息
     *
     * @param warehouseBase 仓库基础信息
     * @return 结果
     */
    public int updateWarehouseBase(WarehouseBase warehouseBase);

    /**
     * 批量删除仓库基础信息
     *
     * @param ids 需要删除的仓库基础信息主键集合
     * @return 结果
     */
    public int deleteWarehouseBaseByIds(Long[] ids);

    /**
     * 删除仓库基础信息信息
     *
     * @param id 仓库基础信息主键
     * @return 结果
     */
    public int deleteWarehouseBaseById(Long id);

    /**
     * 获取全部label格式的仓库列表
     *
     * @return
     */
    List<JSONObject> getWarehouseLabelList();
}
