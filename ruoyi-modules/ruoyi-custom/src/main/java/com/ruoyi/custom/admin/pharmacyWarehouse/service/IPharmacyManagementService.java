package com.ruoyi.custom.admin.pharmacyWarehouse.service;


import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyManagement;

import java.util.List;


/**
 * 药品管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IPharmacyManagementService {
    /**
     * 查询药品管理
     *
     * @param id 药品管理主键
     * @return 药品管理
     */
    public PharmacyManagement selectPharmacyManagementById(Long id);

    /**
     * 查询药品管理列表
     *
     * @param pharmacyManagement 药品管理
     * @return 药品管理集合
     */
    public List<PharmacyManagement> selectPharmacyManagementList(PharmacyManagement pharmacyManagement);

    /**
     * 新增药品管理
     *
     * @param pharmacyManagement 药品管理
     * @return 结果
     */
    public int insertPharmacyManagement(PharmacyManagement pharmacyManagement);

    /**
     * 修改药品管理
     *
     * @param pharmacyManagement 药品管理
     * @return 结果
     */
    public int updatePharmacyManagement(PharmacyManagement pharmacyManagement);

    /**
     * 批量删除药品管理
     *
     * @param ids 需要删除的药品管理主键集合
     * @return 结果
     */
    public int deletePharmacyManagementByIds(Long[] ids);

    /**
     * 删除药品管理信息
     *
     * @param id 药品管理主键
     * @return 结果
     */
    public int deletePharmacyManagementById(Long id);
}

