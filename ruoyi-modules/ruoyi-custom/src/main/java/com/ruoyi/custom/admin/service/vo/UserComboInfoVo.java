package com.ruoyi.custom.admin.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @ClassName UserComboInfoVo
 * @Description
 * <AUTHOR>
 * @Date 2022/8/2 15:59
 */
@Data
@ApiModel("AppServiceProjectResVo")
public class UserComboInfoVo {

    @ApiModelProperty("套餐id")
    private String id;

    /**
     * 服务项目
     */
    @ApiModelProperty("服务项目")
    private String serviceProject;

    /**
     * 收费单价
     */
    @ApiModelProperty("收费单价")
    private BigDecimal price;

    /**
     * 项目图片
     */
    @ApiModelProperty("项目图片")
    private String projectImg;

    @ApiModelProperty("销量")
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private Long sales;

    public Long getSales() {
        if (null == this.sales) {
            return 0L;
        }
        return sales;
    }

    public void setSales(Long sales) {
        this.sales = sales;
    }
}
