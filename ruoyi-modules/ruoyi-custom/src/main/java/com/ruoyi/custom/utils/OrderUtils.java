package com.ruoyi.custom.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.goodsOrder.domain.OrderBaseInfo;

import java.security.SecureRandom;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * * 订单编码生成器，生成32位数字编码，
 * * @生成规则 1位单号类型+17位时间戳+14位(用户id加密&随机数)
 */
public class OrderUtils {
    /**
     * 商品、餐品、服务
     * Base Order
     * refund order
     */
    public static final String BaseOrder = "BO";
    /**
     * 服务工单
     * refund order
     */
    public static final String serviceWorkOrder = "SWO";
    /**
     * 用户余额退款
     * Balance Refund Order
     */
    public static final String balanceRefundOrder = "BRO";
    /**
     * 用户充值前缀
     * top up
     */
    public static final String topUpOrder = "TU";
    /**
     * 缴费类别头
     */
    public static final String PAY_ORDER = "JF";
    /**
     * 退费类别头
     */
    public static final String RETURN_ORDER = "TF";
    /**
     * 周期账单类别头
     */
    public static final String BILL_ORDER = "ZD";

    /**
     * 物资盘点编号
     */
    public static final String INVENTORY_ORDER = "YCJS-WZPD";
    /**
     * 物资出库编号
     */
    public static final String OUT_ORDER = "YCJS-WZCK";
    /**
     * 物资入库编号
     */
    public static final String IN_ORDER = "YCJS-WZRK";

    // ------------- 老人营销合同 -------------

    /**
     * 合同编号
     */
    public static final String CONTRACT_ORDER = "HT";
    /**
     * 续签合同编号
     */
    public static final String RENEW_CONTRACT_ORDER = "HTRN";

    // ------------- 药品库管类编号 -------------

    /**
     * 药品入库编码
     */
    public static final String IN_INVENTORY_CODE = "YPRK";

    /**
     * 药品出库编码
     */
    public static final String OUT_INVENTORY_CODE = "YPCK";

    /**
     * 药品报废编码
     */
    public static final String SCRAP_INVENTORY_CODE = "YPBF";

    /**
     * 药品盘点编码
     */
    public static final String INVENTORY_CHECK_CODE = "YPPD";

    // ------------- 处方编号 -------------

    /**
     * 处方编号
     */
    public static final String PRESCRIPTION_ORDER = "CF";

    // ------------- 医护工单 -------------

    /**
     * 医护服务工单编号
     */
    public static final String DOCTOR_SERVICE_WORK_ORDER = "DSWO";

    // ------------- 评估 -------------

    /**
     * 评估计划编号
     */
    public static final String EVALUATION_ORDER = "PGJH";

    // ------------- 费用单号 -------------

    /**
     * 账单编号
     */
    public static final String BILL_ORDER2= "BILL";

    /**
     * 账单变更编号
     */
    public static final String BILL_C= "BILL_C";

    // ------------- 其他 -------------

    /**
     * 随即编码
     */
    private static final int[] r = new int[]{7, 9, 6, 2, 8, 1, 3, 0, 5, 4};
    private static final DecimalFormat decimalFormat = new DecimalFormat("0000");
    /**
     * 用户id和随机数总长度
     */
    private static final int maxLength = 4;


    /**
     * 根据id进行加密+加随机数组成固定长度编码
     */
    private static String toCode(Long userId) {
        String idStr = userId.toString();
        StringBuilder idsbs = new StringBuilder();
        for (int i = idStr.length() - 1; i >= 0; i--) {
            idsbs.append(r[idStr.charAt(i) - '0']);
        }

        return idsbs.append(getRandom(maxLength - idStr.length())).toString();
    }

    public static void main(String[] args) {
        String s = toCode(12456569L);
        System.out.println(s);
    }

    /**
     * 生成时间戳
     */
    private static String getDateTime() {
        DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return sdf.format(new Date());
    }

    /**
     * 生成固定长度随机码
     *
     * @param n 长度
     */
    private static long getRandom(long n) {
        long min = 1, max = 9;
        for (int i = 1; i < n; i++) {
            min *= 10;
            max *= 10;
        }
        long rangeLong = (((long) (new SecureRandom().nextDouble() * (max - min)))) + min;
        return rangeLong;
    }


    /**
     * 生成不带类别标头的编码
     *
     * @param userId
     */
    private static synchronized String getCode(Long userId) {
        userId = userId == null ? 10000 : userId;
        return getDateTime() + toCode(userId);
    }


    /**
     * 生成缴费单号编码(调用方法)
     *
     * @param userId 网站中该用户唯一ID 防止重复
     */

    public static String getPayCode(Long userId) {
        return PAY_ORDER + getCode(userId);
    }


    /**
     * 生成退费单号编码（调用方法）
     *
     * @param userId 网站中该用户唯一ID 防止重复
     */
    public static String getReturnCode(Long userId) {
        return RETURN_ORDER + getCode(userId);
    }


    /**
     * 生成单号编码(调用方法)
     *
     * @param userId 网站中该用户唯一ID 防止重复
     */
    public static String getBillCode(Long userId) {
        return BILL_ORDER + getCode(userId);
    }

    /**
     * 生成物资盘点编号
     */
    public static String getInventoryCode() {
        return getNumber(INVENTORY_ORDER);
    }

    /**
     * 生成物资盘入库编号
     */
    public static String getInOutCode() {
        return getNumber(IN_ORDER);
    }

    /**
     * 生成物资出库编号
     */
    public static String getOutCode() {
        return getNumber(OUT_ORDER);
    }

    /**
     * 生成合同编号
     */
    public static String getContractCode() {
        return getNumber(CONTRACT_ORDER);
    }

    /**
     * 生成续签合同编号
     */
    public static String getRenewContractCode() {
        return getNumber(RENEW_CONTRACT_ORDER);
    }

    // ------------- 药品库管类编号 -------------

    /**
     * 生成药品入库编号
     */
    public static String getInInventoryCode() {
        return getNumber(IN_INVENTORY_CODE);
    }

    /**
     * 生成药品出库编号
     */
    public static String getOutInventoryCode() {
        return getNumber(OUT_INVENTORY_CODE);
    }

    /**
     * 生成药品报废编号
     */
    public static String getScrapInventoryCode() {
        return getNumber(SCRAP_INVENTORY_CODE);
    }

    /**
     * 生成药品盘点编号
     */
    public static String getInventoryCheckCode() {
        return getNumber(INVENTORY_CHECK_CODE);
    }

    /**
     * 生成医护工单编号
     */
    public static String getDoctorServiceWorkOrderCode() {
        return getNumber(DOCTOR_SERVICE_WORK_ORDER);
    }

    /**
     * 生成评估计划编号
     */
    public static String getEvaluationCode() {
        return getNumber(EVALUATION_ORDER);
    }

    /**
     * 新费用单号
     */
    public static String getBillCode() {
        return getNumber(BILL_ORDER2);
    }

    /**
     * 新费用变更单号
     */
    public static String getChangeBillCode() {
        return getNumber(BILL_C);
    }

    private static String getNumber(String redisKey) {
        RedisService redisService = SpringUtils.getBean(RedisService.class);
        String today = DateUtil.format(new Date(), "yyyyMMdd");
        String key = redisKey + "-" + today;
        int value = 1;
        if (null != redisService.getCacheObject(key)) {
            value = Integer.parseInt(redisService.getCacheObject(key)) + 1;
        }
        redisService.setCacheObject(key, decimalFormat.format(value));
        return key + "-" + decimalFormat.format(value);
    }

    /**
     * 设置最新的value
     */
    public static void setNumber(String redisKey, int value) {
        RedisService redisService = SpringUtils.getBean(RedisService.class);
        String today = DateUtil.format(new Date(), "yyyyMMdd");
        String key = redisKey + "-" + today;
        redisService.setCacheObject(key, decimalFormat.format(value));
    }

    /**
     * 根据状态获取步骤列表信息
     * 下单 接单
     * 0：已关闭 1：待支付 2：已付款 3：已接单 4：已完成  5:退款
     * 2：已付款 3：已接单 4：已完成
     */
    public static JSONArray setpList(OrderBaseInfo orderBaseInfo) {

        Integer status = orderBaseInfo.getStatus();
        JSONArray jsonArray = new JSONArray();
        if (status < OrderBaseInfo.STATUS_PAID || status > OrderBaseInfo.STATUS_REFUND) {// 如果不在范围则返回空 调整-做特殊处理 当已退款时展示
            return jsonArray;
        }

        JSONObject jsonObject01 = new JSONObject();
        jsonObject01.putOnce("title", "已付款");
        jsonObject01.putOnce("description", DateUtil.format(orderBaseInfo.getPayTime(), DatePattern.NORM_DATETIME_MINUTE_FORMATTER));
        if (status.equals(OrderBaseInfo.STATUS_PAID)) {
            jsonObject01.putOnce("status", orderBaseInfo.getStatus());
        }
        jsonArray.put(jsonObject01);

        JSONObject jsonObject02 = new JSONObject();
        jsonObject02.putOnce("title", "已接单");
        jsonObject02.putOnce("description", DateUtil.format(orderBaseInfo.getReceiveTime(), DatePattern.NORM_DATETIME_MINUTE_FORMATTER));
        if (status.equals(OrderBaseInfo.STATUS_TAKE_ORDERS)) {
            jsonObject02.putOnce("status", orderBaseInfo.getStatus());
        }
        jsonArray.put(jsonObject02);

        JSONObject jsonObject03 = new JSONObject();
        jsonObject03.putOnce("title", "已完成");
        jsonObject03.putOnce("description", DateUtil.format(orderBaseInfo.getFinishTime(), DatePattern.NORM_DATETIME_MINUTE_FORMATTER));
        if (status.equals(OrderBaseInfo.STATUS_FINISH)) {
            jsonObject03.putOnce("status", orderBaseInfo.getStatus());
        } else if (status.equals(OrderBaseInfo.STATUS_REFUND)) {// 做特殊处理 当已退款时展示
            jsonObject03.putOnce("status", 4);
        }
        jsonArray.put(jsonObject03);

        return jsonArray;

    }

    /**
     * 通用获取code
     *
     * @param prefixStr 前缀
     * @return
     */
    public static String getCommonOrderCode(String prefixStr) {
        Long userId = SecurityUtils.getUserId();
        return prefixStr + getCode(userId);
    }
}
