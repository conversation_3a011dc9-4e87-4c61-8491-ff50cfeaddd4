package com.ruoyi.custom.admin.assessment.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.custom.admin.assessment.domain.NursingPlanTemplate;

/**
 * 评估护理计划模版Service接口
 *
 * <AUTHOR>
 * @date 2024-07-17
 */
public interface INursingPlanTemplateService {
    /**
     * 查询评估护理计划模版
     *
     * @param id 评估护理计划模版主键
     * @return 评估护理计划模版
     */
    public NursingPlanTemplate selectNursingPlanTemplateById(String id);

    /**
     * 查询评估护理计划模版列表
     *
     * @param nursingPlanTemplate 评估护理计划模版
     * @return 评估护理计划模版集合
     */
    public List<NursingPlanTemplate> selectNursingPlanTemplateList(NursingPlanTemplate nursingPlanTemplate);

    /**
     * 新增评估护理计划模版
     *
     * @param nursingPlanTemplate 评估护理计划模版
     * @return 结果
     */
    public int insertNursingPlanTemplate(NursingPlanTemplate nursingPlanTemplate);

    /**
     * 修改评估护理计划模版
     *
     * @param nursingPlanTemplate 评估护理计划模版
     * @return 结果
     */
    public int updateNursingPlanTemplate(NursingPlanTemplate nursingPlanTemplate);

    /**
     * 批量删除评估护理计划模版
     *
     * @param ids 需要删除的评估护理计划模版主键集合
     * @return 结果
     */
    public int deleteNursingPlanTemplateByIds(String[] ids);

    /**
     * 删除评估护理计划模版信息
     *
     * @param id 评估护理计划模版主键
     * @return 结果
     */
    public int deleteNursingPlanTemplateById(String id);

    /**
     * 更新部件内容
     *
     * @param id 模版ID
     * @param templateType 模版类型
     * @param section 部件名称
     * @param content 部件完整内容
     * @return 结果
     */
    public int updateSectionContent(String id, Integer templateType, String section, Map<String, String> content);

    /**
     * 获取部件内容
     *
     * @param id 模版ID
     * @param templateType 模版类型
     * @param section 部件名称
     * @return 部件内容
     */
    public String getSectionContent(String id, Integer templateType, String section);


}
