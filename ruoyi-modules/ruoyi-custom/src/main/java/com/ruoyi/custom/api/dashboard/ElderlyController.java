package com.ruoyi.custom.api.dashboard;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.custom.admin.dashboard.service.DashboardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * 长者数据
 */
@RestController("dashboardElderlyController")
@RequestMapping("/dashboard/elderly")
@Api(tags = "大屏-长者数据")
public class ElderlyController extends BaseController {

    @Autowired
    private DashboardService dashboardService;

    /**
     * 入住流量分析
     */
    @GetMapping("/getCheckInFlow")
    @ApiOperation(value = "入住流量分析")
    private AjaxResult getCheckInFlow(){
        List<Map<String, Object>> checkInFlow = dashboardService.getCheckInFlow();
        return AjaxResult.success(checkInFlow);
    }
}
