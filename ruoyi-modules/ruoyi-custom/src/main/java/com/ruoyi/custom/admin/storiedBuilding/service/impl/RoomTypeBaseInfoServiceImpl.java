package com.ruoyi.custom.admin.storiedBuilding.service.impl;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeBaseInfo;
import com.ruoyi.custom.admin.storiedBuilding.domain.TypeVersionInfo;
import com.ruoyi.custom.admin.storiedBuilding.mapper.RoomTypeBaseInfoMapper;
import com.ruoyi.custom.admin.storiedBuilding.service.IRoomTypeBaseInfoService;
import com.ruoyi.custom.admin.storiedBuilding.service.ITypeVersionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 房间类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@Service
public class RoomTypeBaseInfoServiceImpl implements IRoomTypeBaseInfoService {
    @Autowired
    private RoomTypeBaseInfoMapper roomTypeBaseInfoMapper;

    @Autowired
    private ITypeVersionInfoService typeVersionInfoService;

    /**
     * 查询房间类型
     *
     * @param id 房间类型主键
     * @return 房间类型
     */
    @Override
    public RoomTypeBaseInfo selectRoomTypeBaseInfoById(Long id) {
        return roomTypeBaseInfoMapper.selectRoomTypeBaseInfoById(id);
    }

    /**
     * 查询房间类型列表
     *
     * @param roomTypeBaseInfo 房间类型
     * @return 房间类型
     */
    @Override
    public List<RoomTypeBaseInfo> selectRoomTypeBaseInfoList(RoomTypeBaseInfo roomTypeBaseInfo) {
        return roomTypeBaseInfoMapper.selectRoomTypeBaseInfoList(roomTypeBaseInfo);
    }

    /**
     * 新增房间类型
     *
     * @param roomTypeBaseInfo 房间类型
     * @return 结果
     */
    @Override
    public int insertRoomTypeBaseInfo(RoomTypeBaseInfo roomTypeBaseInfo) {
        roomTypeBaseInfo.setCreateTime(DateUtils.getNowDate());
        TypeVersionInfo typeVersionInfo = new TypeVersionInfo();
        typeVersionInfo.setBedNum(roomTypeBaseInfo.getBedNumber());
        typeVersionInfo.setFees(roomTypeBaseInfo.getFees());
        if (null == roomTypeBaseInfo.getVersion() || "".equals(roomTypeBaseInfo.getVersion())) {
            typeVersionInfo.setVersion("V.1");
        }
        typeVersionInfo.setStatus("0");
        roomTypeBaseInfoMapper.insertRoomTypeBaseInfo(roomTypeBaseInfo);
        typeVersionInfo.setTypeId(String.valueOf(roomTypeBaseInfo.getId()));
        return typeVersionInfoService.insertTypeVersionInfo(typeVersionInfo);
    }

    /**
     * 修改房间类型
     *
     * @param roomTypeBaseInfo 房间类型
     * @return 结果
     */
    @Override
    public int updateRoomTypeBaseInfo(RoomTypeBaseInfo roomTypeBaseInfo) {
        TypeVersionInfo typeVersionInfo = new TypeVersionInfo();
        typeVersionInfo.setTypeId(String.valueOf(roomTypeBaseInfo.getId()));
        typeVersionInfo.setBedNum(roomTypeBaseInfo.getBedNumber());
        typeVersionInfo.setFees(roomTypeBaseInfo.getFees());
        typeVersionInfo.setStatus("0");
        typeVersionInfoService.insertTypeVersionInfo(typeVersionInfo);
        roomTypeBaseInfo.setUpdateTime(DateUtils.getNowDate());
        return roomTypeBaseInfoMapper.updateRoomTypeBaseInfo(roomTypeBaseInfo);
    }

    /**
     * 批量删除房间类型
     *
     * @param ids 需要删除的房间类型主键
     * @return 结果
     */
    @Override
    public int deleteRoomTypeBaseInfoByIds(Long[] ids) {
        return roomTypeBaseInfoMapper.deleteRoomTypeBaseInfoByIds(ids);
    }

    /**
     * 删除房间类型信息
     *
     * @param id 房间类型主键
     * @return 结果
     */
    @Override
    public int deleteRoomTypeBaseInfoById(Long id) {
        return roomTypeBaseInfoMapper.deleteRoomTypeBaseInfoById(id);
    }

    @Override
    public JSONObject selectName() {
        return roomTypeBaseInfoMapper.selectName();
    }

    @Override
    public List<JSONObject> getRoomTypeLabelAndValue() {
        return roomTypeBaseInfoMapper.getRoomTypeLabelAndValue();
    }

    @Override
    public JSONObject getRoomTypeBase(Long roomId) {
        return roomTypeBaseInfoMapper.getRoomTypeBase(roomId);
    }
}
