package com.ruoyi.custom.api.assessmentApp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.assessment.service.IAssessmentPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 评估端APP-日程安排 Controller
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
@RestController("assessmentCalendarController")
@RequestMapping("/assessmentApp/calendar")
@Api(tags = "评估端APP-日程安排")
public class CalendarController extends BaseController {

    @Autowired
    private IAssessmentPlanService assessmentPlanService;


    /**
     * 根据月份&当前用户id返回选中月份需要评估的日期
     * req:{
     * "month": "2022-04",
     * "userId": 1
     * }
     * resp:[
     * "2022-04-01",
     * "2022-04-02",
     * ]
     */
    @GetMapping("/getCalendarList")
    @ApiOperation(value = "根据月份&当前用户id返回选中月份需要评估的日期")
    @ApiImplicitParam(name = "month", value = "月份", required = true, dataType = "String", paramType = "query")
    public AjaxResult getCalendarList(@RequestParam("month") @JsonFormat(pattern = "yyyy-MM") Date month) {
        return AjaxResult.success(assessmentPlanService.getCalendarList(month, SecurityUtils.getLoginUser().getUserid()));
    }

}
