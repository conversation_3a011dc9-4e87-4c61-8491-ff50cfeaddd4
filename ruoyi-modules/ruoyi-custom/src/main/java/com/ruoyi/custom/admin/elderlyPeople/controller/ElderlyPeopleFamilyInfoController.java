package com.ruoyi.custom.admin.elderlyPeople.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleFamilyInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleFamilyInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 老人家属信息Controller
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@RestController
@RequestMapping("/familyInfo")
@Api(value = "老人家属信息controller", tags = {"老人家属信息"})
public class ElderlyPeopleFamilyInfoController extends BaseController {
    @Autowired
    private IElderlyPeopleFamilyInfoService elderlyPeopleFamilyInfoService;

    /**
     * 查询老人家属信息列表
     */
    //@RequiresPermissions("elderlyPeople:familyInfo:list")
    @ApiOperation(value = "老人家属信息列表")
    @GetMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userId", value = "老人基础信息id", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo list(@ApiIgnore ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo) {
        startPage();
        List<ElderlyPeopleFamilyInfo> list = elderlyPeopleFamilyInfoService.selectElderlyPeopleFamilyInfoList(elderlyPeopleFamilyInfo);
        return getDataTable(list);
    }

    /**
     * 导出老人家属信息列表
     */
    //@RequiresPermissions("elderlyPeople:familyInfo:export")
    @Log(platform = "1", title = "老人家属信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出老人家属信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo) {
        List<ElderlyPeopleFamilyInfo> list = elderlyPeopleFamilyInfoService.selectElderlyPeopleFamilyInfoList(elderlyPeopleFamilyInfo);
        ExcelUtil<ElderlyPeopleFamilyInfo> util = new ExcelUtil<ElderlyPeopleFamilyInfo>(ElderlyPeopleFamilyInfo.class);
        util.exportExcel(response, list, "老人家属信息数据");
    }

    /**
     * 获取老人家属信息详细信息
     */
    //@RequiresPermissions("elderlyPeople:familyInfo:query")
    @ApiOperation(value = "获取老人家属信息详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(elderlyPeopleFamilyInfoService.selectElderlyPeopleFamilyInfoById(id));
    }

    /**
     * 新增老人家属信息
     */
    //@RequiresPermissions("elderlyPeople:familyInfo:add")
    @Log(platform = "1", title = "老人家属信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增老人家属信息")
    @PostMapping
    public AjaxResult add(@RequestBody ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo) {
        return toAjax(elderlyPeopleFamilyInfoService.insertElderlyPeopleFamilyInfo(elderlyPeopleFamilyInfo));
    }

    /**
     * 修改老人家属信息
     */
    //@RequiresPermissions("elderlyPeople:familyInfo:edit")
    @Log(platform = "1", title = "老人家属信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改老人家属信息")
    @PutMapping
    public AjaxResult edit(@RequestBody ElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo) {
        return toAjax(elderlyPeopleFamilyInfoService.updateElderlyPeopleFamilyInfo(elderlyPeopleFamilyInfo));
    }

    /**
     * 删除老人家属信息
     */
    //@RequiresPermissions("elderlyPeople:familyInfo:remove")
    @Log(platform = "1", title = "老人家属信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除老人家属信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(elderlyPeopleFamilyInfoService.deleteElderlyPeopleFamilyInfoByIds(ids));
    }
}
