package com.ruoyi.custom.admin.marketing.service;


import com.ruoyi.custom.admin.marketing.domain.ContractInfo;
import com.ruoyi.custom.admin.marketing.domain.PaymentRecord;

import java.util.List;


/**
 * 合同信息Service接口
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
public interface ContractInfoService {
    /**
     * 查询合同信息
     *
     * @param contractNumber 合同信息主键
     * @return 合同信息
     */
    public ContractInfo selectContractInfoByContractNumber(String contractNumber);

    /**
     * 查询合同信息列表
     *
     * @param contractInfo 合同信息
     * @return 合同信息集合
     */
    public List<ContractInfo> selectContractInfoList(ContractInfo contractInfo);

    /**
     * 新增合同信息
     *
     * @param contractInfo 合同信息
     * @return 结果
     */
    public int insertContractInfo(ContractInfo contractInfo);

    /**
     * 修改合同信息
     *
     * @param contractInfo 合同信息
     * @return 结果
     */
    public int updateContractInfo(ContractInfo contractInfo);

    /**
     * 批量删除合同信息
     *
     * @param contractNumbers 需要删除的合同信息主键集合
     * @return 结果
     */
    public int deleteContractInfoByContractNumbers(String contractNumbers);

    /**
     * 删除合同信息信息
     *
     * @param contractNumber 合同信息主键
     * @return 结果
     */
    public int deleteContractInfoByContractNumber(String contractNumber);

    /**
     * 根据客户id获取合同信息
     * @param customerId
     * @return
     */
    ContractInfo getContractInfoByCustomerId(Integer customerId);

    /**
     * 查询所有合同信息列表
     *
     * @param contractInfo
     * @return
     */
    List<ContractInfo> selectContractRenewInfoList(ContractInfo contractInfo);

    /**
     * 根据合同编号查询合同续签信息
     * @param contractNumber
     * @return
     */
    ContractInfo selectContractRenewInfoByContractNumber(String contractNumber);

    /**
     * 新增合同续签信息
     * @param contractRenewInfo
     * @return
     */
    int insertContractRenewInfo(ContractInfo contractRenewInfo);

    /**
     * 生成合同编号
     *
     * @return 合同编号
     */
    String generateContractNumber();

    /**
     * 生成续签合同编号
     *
     * @param originalContractNumber 原合同编号
     * @return 续签合同编号
     */
    String generateRenewContractNumber(String originalContractNumber);

    /**
     * 查询合同信息列表（new）
     *
     * @param contractInfo
     * @return
     */
    List<ContractInfo> selectContractInfoListNew(ContractInfo contractInfo);

    /**
     * 查询合同信息列表（new）
     *
     * @param contractInfo
     * @return
     */
    List<ContractInfo> selectContractRenewInfoListNew(ContractInfo contractInfo);
}

