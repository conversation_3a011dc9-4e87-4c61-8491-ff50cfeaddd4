package com.ruoyi.custom.admin.marketing.req;

import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ApiModel(value = "FeeStatisticsReq", description = "费用统计req")
public class FeeStatisticsReq extends BaseEntity {

    /**
     * 费用单号
     */
    @ApiModelProperty(value = "费用单号")
    private String id;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 长者姓名
     */
    @ApiModelProperty(value = "长者姓名")
    private String elderlyName;

    /**
     * 长者电话
     */
    @ApiModelProperty(value = "长者电话")
    private String elderlyPhone;

    /**
     * 床位号
     */
    @ApiModelProperty(value = "床位号")
    private String bedNumber;

    /**
     * 缴费类型
     */
    @ApiModelProperty(value = "缴费类型")
    private String feeType;

    /**
     * 费用类型
     */
    @ApiModelProperty(value = "费用类型")
    private String paymentType;

    /**
     * 缴费金额
     */
    @ApiModelProperty(value = "缴费金额")
    private BigDecimal paymentAmount;

    /**
     * 缴费日期
     */
    @ApiModelProperty(value = "缴费日期")
    private LocalDate paymentTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}

