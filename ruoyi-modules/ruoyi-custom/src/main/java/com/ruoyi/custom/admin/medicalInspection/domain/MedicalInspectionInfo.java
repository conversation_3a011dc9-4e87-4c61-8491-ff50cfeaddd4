package com.ruoyi.custom.admin.medicalInspection.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "医护巡查信息表")
public class MedicalInspectionInfo extends BaseEntity {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "老人id，对应t_elderly_people_info")
    private String elderlyId;

    @ApiModelProperty(value = "老人姓名")
    private String elderlyName;

    @ApiModelProperty(value = "巡查频率")
    private String inspectionFrequency;

    @ApiModelProperty(value = "巡查内容")
    private String inspectionContent;

    @ApiModelProperty(value = "特殊注意")
    private String specialAttention;

    @ApiModelProperty(value = "分配人员ids，多个逗号隔开")
    private String assignedPersonnelIds;

    @ApiModelProperty(value = "分配人员names，多个逗号隔开")
    private String assignedPersonnelNames;

    @ApiModelProperty(value = "床位")
    @TableField(exist = false)
    private String bedName;

    @ApiModelProperty(value = "分配人员")
    @TableField(exist = false)
    private List<AssignPersonnel> assignPersonnels;

    @Data
    @ApiModel(value = "分配人员")
    public static class AssignPersonnel {

        @ApiModelProperty(value = "id")
        private Long id;

        @ApiModelProperty(value = "姓名")
        private String name;

    }

}

