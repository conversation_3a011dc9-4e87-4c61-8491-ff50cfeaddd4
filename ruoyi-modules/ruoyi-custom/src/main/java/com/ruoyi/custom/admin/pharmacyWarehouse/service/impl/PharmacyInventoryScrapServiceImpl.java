package com.ruoyi.custom.admin.pharmacyWarehouse.service.impl;


import java.util.Date;
import java.util.List;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryScrap;
import com.ruoyi.custom.admin.pharmacyWarehouse.mapper.PharmacyInventoryScrapMapper;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryScrapService;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyInventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 药品报废明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Service
public class PharmacyInventoryScrapServiceImpl implements IPharmacyInventoryScrapService {
    @Autowired
    private PharmacyInventoryScrapMapper pharmacyInventoryScrapMapper;

    @Autowired
    private IPharmacyInventoryService pharmacyInventoryService;

    /**
     * 查询药品报废明细
     *
     * @param id 药品报废明细主键
     * @return 药品报废明细
     */
    @Override
    public PharmacyInventoryScrap selectPharmacyInventoryScrapById(Long id) {
        return pharmacyInventoryScrapMapper.selectPharmacyInventoryScrapById(id);
    }

    /**
     * 查询药品报废明细列表
     *
     * @param pharmacyInventoryScrap 药品报废明细
     * @return 药品报废明细
     */
    @Override
    public List<PharmacyInventoryScrap> selectPharmacyInventoryScrapList(PharmacyInventoryScrap pharmacyInventoryScrap) {
        return pharmacyInventoryScrapMapper.selectPharmacyInventoryScrapList(pharmacyInventoryScrap);
    }

    /**
     * 报废
     *
     * @param pharmacyInventoryScraps 报废
     * @return 结果
     */
    @Override
    public int insertPharmacyInventoryScrap(List<PharmacyInventoryScrap> pharmacyInventoryScraps) {
        Date now = DateUtils.getNowDate();

        // 操作库存
        pharmacyInventoryScraps.forEach(item -> {
            item.setCreateTime(now);
            updateQuantityWithLock(item.getInventoryId(), -item.getScrapQuantity());
        });

        // 批量插入报废流水记录
        return pharmacyInventoryScrapMapper.insertBatch(pharmacyInventoryScraps);
    }

    // 修改更新库存的方法，加入悲观锁
    private void updateQuantityWithLock(Long inventoryId, int quantity) {
        // 查询库存并加锁，之后再更新库存
        int currentQuantity = pharmacyInventoryService.getCurrQuantityById(inventoryId);
        if (currentQuantity + quantity < 0) {
            throw new ServiceException("库存不足");
        }
        pharmacyInventoryService.updateQuantity(inventoryId, quantity);
    }

    /**
     * 批量删除药品报废明细
     *
     * @param ids 需要删除的药品报废明细主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryScrapByIds(Long[] ids) {
        return pharmacyInventoryScrapMapper.deletePharmacyInventoryScrapByIds(ids);
    }

    /**
     * 删除药品报废明细信息
     *
     * @param id 药品报废明细主键
     * @return 结果
     */
    @Override
    public int deletePharmacyInventoryScrapById(Long id) {
        return pharmacyInventoryScrapMapper.deletePharmacyInventoryScrapById(id);
    }

    @Override
    public List<PharmacyInventoryScrap> selectPharmacyInventoryScrapList2(PharmacyInventoryScrap pharmacyInventoryScrap) {
        return pharmacyInventoryScrapMapper.selectPharmacyInventoryScrapList2(pharmacyInventoryScrap);
    }
}

