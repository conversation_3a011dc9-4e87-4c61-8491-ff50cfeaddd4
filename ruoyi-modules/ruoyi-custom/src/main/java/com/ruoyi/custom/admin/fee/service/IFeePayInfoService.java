package com.ruoyi.custom.admin.fee.service;

import com.ruoyi.custom.admin.fee.domain.FeePayInfo;
import com.ruoyi.custom.admin.fee.domain.req.FeePayQueryVo;
import com.ruoyi.custom.admin.fee.domain.req.FeePaySaveVo;
import com.ruoyi.custom.admin.fee.domain.res.FeePayVo;

import java.util.List;

/**
 * 费用支付记录Service接口
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
public interface IFeePayInfoService {
    /**
     * 查询费用支付记录
     *
     * @param id 费用支付记录主键
     * @return 费用支付记录
     */
    public FeePayInfo selectFeePayInfoById(String id);

    public FeePayVo selectNewFeePayInfoById(String id);

    /**
     * 查询费用支付记录列表
     *
     * @param feePayInfo 费用支付记录
     * @return 费用支付记录集合
     */
    public List<FeePayInfo> selectFeePayInfoList(FeePayInfo feePayInfo);

    /**
     * 新增费用支付记录
     *
     * @param feePayInfo 费用支付记录
     * @return 结果
     */
    public int insertFeePayInfo(FeePayInfo feePayInfo);

    /**
     * 修改费用支付记录
     *
     * @param feePayInfo 费用支付记录
     * @return 结果
     */
    public int updateFeePayInfo(FeePayInfo feePayInfo);

    /**
     * 批量删除费用支付记录
     *
     * @param ids 需要删除的费用支付记录主键集合
     * @return 结果
     */
    public int deleteFeePayInfoByIds(String[] ids);

    /**
     * 删除费用支付记录信息
     *
     * @param id 费用支付记录主键
     * @return 结果
     */
    public int deleteFeePayInfoById(String id);

    int insertFeePaySaveVo(FeePaySaveVo feePaySaveVo, String consumeType);

    List<FeePayVo> selectNewFeePayInfoList(FeePayQueryVo feePayQueryVo);
}
