package com.ruoyi.custom.utils;

import java.io.*;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * ZIP文件处理工具类
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public class ZipUtil {

    /**
     * 将多个文件打包成ZIP文件
     * 
     * @param srcFiles 源文件列表
     * @param zipFile 目标ZIP文件
     * @throws IOException IO异常
     */
    public static void toZip(List<String> srcFiles, String zipFile) throws IOException {
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
            for (String srcFile : srcFiles) {
                File file = new File(srcFile);
                if (file.exists()) {
                    byte[] buf = new byte[1024];
                    zos.putNextEntry(new ZipEntry(file.getName()));
                    int len;
                    try (FileInputStream in = new FileInputStream(srcFile)) {
                        while ((len = in.read(buf)) != -1) {
                            zos.write(buf, 0, len);
                        }
                        zos.closeEntry();
                    }
                }
            }
        }
    }

    /**
     * 删除文件及其父目录（如果父目录为空）
     * 
     * @param filePaths 文件路径列表
     */
    public static void deleteFiles(List<String> filePaths) {
        for (String filePath : filePaths) {
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
                
                // 尝试删除父目录（如果为空）
                File parentDir = file.getParentFile();
                if (parentDir != null && parentDir.exists() && parentDir.list().length == 0) {
                    parentDir.delete();
                }
            }
        }
    }
} 