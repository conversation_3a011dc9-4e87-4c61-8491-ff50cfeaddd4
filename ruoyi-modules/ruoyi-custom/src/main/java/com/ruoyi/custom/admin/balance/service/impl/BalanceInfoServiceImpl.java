package com.ruoyi.custom.admin.balance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.custom.admin.balance.domain.BalanceInfo;
import com.ruoyi.custom.admin.balance.domain.BalanceRecords;
import com.ruoyi.custom.admin.balance.mapper.BalanceInfoMapper;
import com.ruoyi.custom.admin.balance.mapper.BalanceRecordsMapper;
import com.ruoyi.custom.admin.balance.service.IBalanceInfoService;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.fee.domain.ConsumeAccountDetail;
import com.ruoyi.custom.admin.fee.service.IConsumeAccountDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 余额信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
@Service
public class BalanceInfoServiceImpl implements IBalanceInfoService {
    @Autowired
    private BalanceInfoMapper balanceInfoMapper;

    @Autowired
    private BalanceRecordsMapper recordsMapper;

    @Autowired
    private IConsumeAccountDetailService consumeAccountDetailService;

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    /**
     * 查询余额信息
     *
     * @param id 余额信息主键
     * @return 余额信息
     */
    @Override
    public BalanceInfo selectBalanceInfoById(String id) {
        return balanceInfoMapper.selectBalanceInfoById(id);
    }

    /**
     * 查询余额信息列表
     *
     * @param balanceInfo 余额信息
     * @return 余额信息
     */
    @Override
    public List<BalanceInfo> selectBalanceInfoList(BalanceInfo balanceInfo) {
        return balanceInfoMapper.selectBalanceInfoList(balanceInfo);
    }

    /**
     * 新增余额信息
     *
     * @param balanceInfo 余额信息
     * @return 结果
     */
    @Override
    public int insertBalanceInfo(BalanceInfo balanceInfo) {
        balanceInfo.setCreateTime(DateUtils.getNowDate());
        return balanceInfoMapper.insertBalanceInfo(balanceInfo);
    }

    /**
     * 修改余额信息
     *
     * @param balanceInfo 余额信息
     * @return 结果
     */
    @Override
    public int updateBalanceInfo(BalanceInfo balanceInfo) {
        balanceInfo.setUpdateTime(DateUtils.getNowDate());
        return balanceInfoMapper.updateBalanceInfo(balanceInfo);
    }

    /**
     * 批量删除余额信息
     *
     * @param ids 需要删除的余额信息主键
     * @return 结果
     */
    @Override
    public int deleteBalanceInfoByIds(String[] ids) {
        return balanceInfoMapper.deleteBalanceInfoByIds(ids);
    }

    /**
     * 删除余额信息信息
     *
     * @param id 余额信息主键
     * @return 结果
     */
    @Override
    public int deleteBalanceInfoById(String id) {
        return balanceInfoMapper.deleteBalanceInfoById(id);
    }

    @Override
    public BalanceRecords doPay(String userId, String operatorId, BigDecimal pagAmount) {

        BalanceInfo balanceInfo = new BalanceInfo();
        BalanceInfo param = new BalanceInfo();
        param.setUserId(userId);
        List<BalanceInfo> balanceInfos = balanceInfoMapper.selectBalanceInfoList(param);
        if (balanceInfos.size() == 0) {
            balanceInfo.setId(IdUtils.fastSimpleUUID());
            balanceInfo.setUserId(userId);
            balanceInfo.setLastAmount(new BigDecimal("0"));
            balanceInfo.setAmount(pagAmount);
            balanceInfo.setCreateBy(operatorId);
            balanceInfo.setCreateTime(new Date());
            balanceInfoMapper.insertBalanceInfo(balanceInfo);
        } else if (balanceInfos.size() == 1) {

            balanceInfo = balanceInfos.get(0);
            BigDecimal lastAmount = balanceInfo.getAmount();
            if (lastAmount == null) {
                lastAmount = new BigDecimal("0");
            }
            balanceInfo.setLastAmount(lastAmount);
            balanceInfo.setAmount(lastAmount.add(pagAmount));
            balanceInfo.setUpdateBy(operatorId);
            balanceInfo.setUpdateTime(new Date());
            balanceInfoMapper.updateBalanceInfo(balanceInfo);
        } else {
            throw new ServiceException("该用户信息异常请联系管理员排查");
        }

        BalanceRecords balanceRecords = BeanUtil.copyProperties(balanceInfo, BalanceRecords.class);
        balanceRecords.setId(IdUtils.fastSimpleUUID());
        balanceRecords.setCreateBy(operatorId);
        balanceRecords.setCreateTime(new Date());
        balanceRecords.setChangedAmount(pagAmount);
        balanceRecords.setChangedType("0");
        balanceRecords.setDelFlag("0");
        recordsMapper.insertBalanceRecords(balanceRecords);

        return balanceRecords;

    }

    @Override
    public BalanceRecords doDeduction(String userId, String operatorId, BigDecimal pagAmount, String consumeType) {

        // 保存账户基础信息
        BalanceInfo balanceInfo;
        BalanceInfo param = new BalanceInfo();
        param.setUserId(userId);
        List<BalanceInfo> balanceInfos = balanceInfoMapper.selectBalanceInfoList(param);
        if (balanceInfos.size() == 1) {
            balanceInfo = balanceInfos.get(0);
            BigDecimal lastAmount = balanceInfo.getAmount();
            balanceInfo.setLastAmount(lastAmount);
            balanceInfo.setAmount(lastAmount.subtract(pagAmount));
            balanceInfo.setUpdateBy(operatorId);
            balanceInfo.setUpdateTime(new Date());
            balanceInfoMapper.updateBalanceInfo(balanceInfo);
        } else { // 创建新账户
            BigDecimal lastAmount = new BigDecimal("0");
            balanceInfo = new BalanceInfo();
            balanceInfo.setId(IdUtils.fastSimpleUUID());
            balanceInfo.setUserId(userId);
            balanceInfo.setLastAmount(lastAmount);
            balanceInfo.setAmount(lastAmount.subtract(pagAmount));
            balanceInfo.setCreateBy(operatorId);
            balanceInfo.setCreateTime(new Date());
            balanceInfoMapper.insertBalanceInfo(balanceInfo);
        }

        // 生成扣款记录
        BalanceRecords balanceRecords = BeanUtil.copyProperties(balanceInfo, BalanceRecords.class);
        balanceRecords.setId(IdUtils.fastSimpleUUID());
        balanceRecords.setCreateBy(operatorId);
        balanceRecords.setCreateTime(new Date());
        balanceRecords.setChangedAmount(pagAmount);
        balanceRecords.setChangedType("2"); // 扣款
        balanceRecords.setDelFlag("0");
        recordsMapper.insertBalanceRecords(balanceRecords);

        // 生成消费账户明细
        ElderlyPeopleInfo elderlyPeopleInfo = elderlyPeopleInfoService.selectElderlyPeopleInfoById(userId);
        ConsumeAccountDetail consumeAccountDetail = new ConsumeAccountDetail();
        consumeAccountDetail.setElderlyId(userId);
        consumeAccountDetail.setElderlyName(elderlyPeopleInfo.getName());
        consumeAccountDetail.setElderlyPhone(elderlyPeopleInfo.getPhone());
        consumeAccountDetail.setType(consumeType);
        consumeAccountDetail.setAmount(pagAmount);
        elderlyPeopleInfo.setCreateTime(new Date());
        consumeAccountDetailService.insertConsumeAccountDetail(consumeAccountDetail);

        return balanceRecords;
    }


    @Override
    @Transactional
    public BalanceRecords cleanBalance(String userId, String operatorId, BigDecimal returnAmount,String consumeType) {

        // 保存账户基础信息
        BalanceInfo balanceInfo = new BalanceInfo();
        BalanceInfo param = new BalanceInfo();
        param.setUserId(userId);
        List<BalanceInfo> balanceInfos = balanceInfoMapper.selectBalanceInfoList(param);
        if (balanceInfos.size() == 1) {
            balanceInfo = balanceInfos.get(0);
            BigDecimal lastAmount = balanceInfo.getAmount();
            // 查看余额是否满足扣除条件
            int i = lastAmount.compareTo(returnAmount);
            if (i < 0) {
                throw new ServiceException("该用户余额不足，请确定好退款金额");
            }
            balanceInfo.setLastAmount(lastAmount);
            balanceInfo.setAmount(lastAmount.add(returnAmount));
            balanceInfo.setUpdateBy(operatorId);
            balanceInfo.setUpdateTime(new Date());
            balanceInfoMapper.updateBalanceInfo(balanceInfo);
        } else {
            throw new ServiceException("该用户信息异常请联系管理员排查");
        }

        // 生成退款记录
        BalanceRecords balanceRecords = BeanUtil.copyProperties(balanceInfo, BalanceRecords.class);
        balanceRecords.setId(IdUtils.fastSimpleUUID());
        balanceRecords.setCreateBy(operatorId);
        balanceRecords.setCreateTime(new Date());
        balanceRecords.setChangedAmount(returnAmount);
        balanceRecords.setChangedType("2");
        balanceRecords.setDelFlag("0");
        recordsMapper.insertBalanceRecords(balanceRecords);

        // 生成消费账户明细
        ElderlyPeopleInfo elderlyPeopleInfo = elderlyPeopleInfoService.selectElderlyPeopleInfoById(userId);
        ConsumeAccountDetail consumeAccountDetail = new ConsumeAccountDetail();
        consumeAccountDetail.setElderlyId(userId);
        consumeAccountDetail.setElderlyName(elderlyPeopleInfo.getName());
        consumeAccountDetail.setElderlyPhone(elderlyPeopleInfo.getPhone());
        consumeAccountDetail.setType(consumeType);
        consumeAccountDetail.setAmount(returnAmount);
        elderlyPeopleInfo.setCreateTime(new Date());
        consumeAccountDetailService.insertConsumeAccountDetail(consumeAccountDetail);

        return balanceRecords;
    }
}
