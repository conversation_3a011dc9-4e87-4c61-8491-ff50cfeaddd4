package com.ruoyi.custom.admin.assessment.domain;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 评估护理计划模版对象 t_nursing_plan_template
 *
 * <AUTHOR>
 * @date 2024-07-17
 */
@Data
@ApiModel(value = "评估护理计划模版")
public class NursingPlanTemplate extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 健康问题
     */
    @Excel(name = "健康问题")
    @ApiModelProperty(value = "健康问题")
    private String healthProblem;

    /**
     * 模版类型
     */
    @Excel(name = "模版类型")
    @ApiModelProperty(value = "模版类型")
    private Integer templateType;

    /**
     * 详情
     */
    @ApiModelProperty(value = "详情")
    private JSONObject details;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @ApiModelProperty(value = "删除标志（0代表存在 1代表删除）")
    private String delFlag;
} 