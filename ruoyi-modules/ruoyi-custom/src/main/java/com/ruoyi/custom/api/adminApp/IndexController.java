package com.ruoyi.custom.api.adminApp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.marketing.domain.MarketingCustomerInfo;
import com.ruoyi.custom.admin.marketing.domain.MarketingFollowUp;
import com.ruoyi.custom.admin.marketing.service.MarketingCustomerInfoService;
import com.ruoyi.custom.admin.marketing.service.MarketingFollowUpService;
import com.ruoyi.custom.admin.storiedBuilding.service.IBedBaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * 管理端APP-首页 Controller
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
@RestController("adminAppIndexController")
@RequestMapping("/adminApp/index")
@Api(tags = "管理端APP-首页")
public class IndexController extends BaseController {

    @Autowired
    private MarketingCustomerInfoService marketingCustomerInfoService;

    @Autowired
    private MarketingFollowUpService marketingFollowUpService;

    @Autowired
    private IBedBaseInfoService bedBaseInfoService;

    /**
     * 当前用户下已处理咨询客户、已处理意向客户、已回访、待回访、空闲床位、已入住床位数量
     */
    @GetMapping("/number")
    @ApiOperation(value = "已处理咨询客户、已处理意向客户、已回访、待回访、空闲床位、已入住床位数量")
    public AjaxResult number() {
        Long userid = SecurityUtils.getLoginUser().getUserid(); // 当前用户id

        // 当前用户已处理咨询客户数量
        MarketingCustomerInfo consultationCustomer = new MarketingCustomerInfo();
        consultationCustomer.setMarketerId(userid);
        consultationCustomer.setCustomerType(null);
        consultationCustomer.setParams(MapUtil.builder(new HashMap<String, Object>())
                .put("notSign", "true") // 未签约
                .put("notLive", "true") // 未入住
                .build());
        Long consultationCustomerCount = marketingCustomerInfoService.selectMarketingCustomerInfoCount(consultationCustomer);

        // 当前用户已处理意向客户数量
        MarketingCustomerInfo intentionCustomer = new MarketingCustomerInfo();
        intentionCustomer.setMarketerId(userid);
        intentionCustomer.setCustomerType("1");
        intentionCustomer.setParams(MapUtil.builder(new HashMap<String, Object>())
                .put("notSign", "true") // 未签约
                .put("notLive", "true") // 未入住
                .build());
        Long intentionCustomerCount = marketingCustomerInfoService.selectMarketingCustomerInfoCount(intentionCustomer);

        // 当前用户已回访数量
        MarketingFollowUp followUp1 = new MarketingFollowUp();
        followUp1.setParams(MapUtil.builder(new HashMap<String, Object>()).put("marketerId", userid).build());
        followUp1.setFollowUpStatus("1"); // 回访状态：1已回访
        Long followUpCount = marketingFollowUpService.selectMarketingFollowUpCount(followUp1);

        // 当前用户待回访数量
        MarketingFollowUp followUp2 = new MarketingFollowUp();
        followUp2.setParams(MapUtil.builder(new HashMap<String, Object>()).put("marketerId", userid).build());
        followUp2.setFollowUpStatus("0");
        Long followUpWaitCount = marketingFollowUpService.selectMarketingFollowUpCount(followUp2);

        // 当前用户空闲、入住床位数量
        JSONObject object = bedBaseInfoService.getBedCountSummary(MapUtil.empty());
        Long freeBeds = object.getLong("freeBeds"); // 空闲床位数量
        Long occupiedBeds = object.getLong("occupiedBeds"); // 已入住床位数量


        // 组装返回数据
        JSONObject data = new JSONObject();
        data.set("consultationCustomerCount", consultationCustomerCount);
        data.set("intentionCustomerCount", intentionCustomerCount);
        data.set("followUpCount", followUpCount);
        data.set("followUpWaitCount", followUpWaitCount);
        data.set("freeBeds", freeBeds);
        data.set("occupiedBeds", occupiedBeds);
        return AjaxResult.success(data);
    }

}
