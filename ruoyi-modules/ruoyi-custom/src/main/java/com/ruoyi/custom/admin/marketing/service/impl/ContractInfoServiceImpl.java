package com.ruoyi.custom.admin.marketing.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleFamilyInfoService;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.liveManage.service.ILiveLeaveRecordsService;
import com.ruoyi.custom.admin.marketing.domain.ContractInfo;
import com.ruoyi.custom.admin.marketing.domain.MarketingCustomerInfo;
import com.ruoyi.custom.admin.marketing.mapper.ContractInfoMapper;
import com.ruoyi.custom.admin.marketing.service.ContractInfoService;
import com.ruoyi.custom.admin.marketing.service.IPaymentRecordService;
import com.ruoyi.custom.admin.marketing.service.MarketingCustomerInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 合同信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@Service
public class ContractInfoServiceImpl implements ContractInfoService {
    @Autowired
    private ContractInfoMapper contractInfoMapper;

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    @Autowired
    private IElderlyPeopleFamilyInfoService elderlyPeopleFamilyInfoService;

    @Autowired
    private MarketingCustomerInfoService marketingCustomerInfoService;

    @Autowired
    private IPaymentRecordService paymentRecordService;

    @Autowired
    private ILiveLeaveRecordsService liveLeaveRecordsService;


    /**
     * 查询合同信息
     *
     * @param contractNumber 合同信息主键
     * @return 合同信息
     */
    @Override
    public ContractInfo selectContractInfoByContractNumber(String contractNumber) {
        ContractInfo contractInfo = contractInfoMapper.selectContractInfoByContractNumber(contractNumber);
        contractInfo.setOldContractNumber(contractInfo.getContractNumber());
        return contractInfo;
    }

    /**
     * 查询合同信息列表
     *
     * @param contractInfo 合同信息
     * @return 合同信息
     */
    @Override
    public List<ContractInfo> selectContractInfoList(ContractInfo contractInfo) {
        return contractInfoMapper.selectContractInfoList(contractInfo);
    }

    /**
     * 新增合同信息
     *
     * @param contractInfo 合同信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertContractInfo(ContractInfo contractInfo) {
        // 校验营销老人是否已有合同
        ContractInfo contractInfoParams = new ContractInfo();
        contractInfoParams.setCustomerId(contractInfo.getCustomerId());
        List<ContractInfo> contractInfoList = contractInfoMapper.selectContractInfoList(contractInfoParams);
        if (CollUtil.isNotEmpty(contractInfoList)) {
            throw new ServiceException("该老人已存在合同，请勿重复添加合同");
        }

        // 校验合同编号是否已存在
        ContractInfo contractInfoParams1 = new ContractInfo();
        contractInfoParams1.setContractNumber(contractInfo.getContractNumber());
        if (CollUtil.isNotEmpty(contractInfoMapper.selectContractInfoList(contractInfoParams1))) {
            throw new ServiceException("该合同编号已存在，请重新输入");
        }


        // 同步老人信息到营销客户
        if (contractInfo.getElderlyPeopleInfo() == null) {
            contractInfo.setElderlyPeopleInfo(new ElderlyPeopleInfo());
        }
        if (contractInfo.getMarketingCustomerInfo() == null) {
            contractInfo.setMarketingCustomerInfo(new MarketingCustomerInfo());
        }
        MarketingCustomerInfo marketingCustomerInfo1 = new MarketingCustomerInfo();
        marketingCustomerInfo1.setId(contractInfo.getCustomerId());
        marketingCustomerInfo1.setElderGender(contractInfo.getElderlyPeopleInfo().getSex());
        marketingCustomerInfo1.setElderPhone(contractInfo.getElderlyPeopleInfo().getPhone());
        marketingCustomerInfo1.setHomeAddress(contractInfo.getElderlyPeopleInfo().getHomeAddress());
        marketingCustomerInfo1.setMarketerId(contractInfo.getMarketingCustomerInfo().getMarketerId());
        marketingCustomerInfo1.setMarketer(contractInfo.getMarketingCustomerInfo().getMarketer());
        marketingCustomerInfo1.setSignStatus("1"); // 已签约
        marketingCustomerInfoService.updateMarketingCustomerInfo2(marketingCustomerInfo1);

        // 同步老人信息到 t_elderly_people_info
        MarketingCustomerInfo marketingCustomerInfo2 = marketingCustomerInfoService.selectMarketingCustomerInfoById(contractInfo.getCustomerId());
        ElderlyPeopleInfo elderlyPeopleInfo = contractInfo.getElderlyPeopleInfo();
        elderlyPeopleInfo.setNation(marketingCustomerInfo2.getNation());
        elderlyPeopleInfo.setMarriageStatus(marketingCustomerInfo2.getMaritalStatus());
        elderlyPeopleInfo.setLivingSituation(marketingCustomerInfo2.getResidenceStatus());
        elderlyPeopleInfo.setHomeAddress(marketingCustomerInfo2.getHomeAddress());
        elderlyPeopleInfo.setCustomerId(contractInfo.getCustomerId());

        // 检查是否已存在老人信息，若存在则更新，不存在则插入
        ElderlyPeopleInfo elderlyPeopleInfoParams = new ElderlyPeopleInfo();
        elderlyPeopleInfoParams.setCustomerId(contractInfo.getCustomerId());
        List<ElderlyPeopleInfo> elderlyPeopleInfoList = elderlyPeopleInfoService.selectElderlyPeopleInfoList(elderlyPeopleInfoParams);
        if (CollUtil.isNotEmpty(elderlyPeopleInfoList)) {
            elderlyPeopleInfo.setId(elderlyPeopleInfoList.get(0).getId());
            elderlyPeopleInfoService.updateElderlyPeopleInfo(elderlyPeopleInfo);
        } else {
            elderlyPeopleInfoService.insertElderlyPeopleInfo(elderlyPeopleInfo);
        }

        // 保存合同信息
        contractInfo.setIsRenewable("0");
        contractInfo.setElderlyPeopleId(contractInfo.getElderlyPeopleInfo().getId());
        contractInfoMapper.insertContractInfo(contractInfo);
        return 1;
    }

    /**
     * 修改合同信息
     *
     * @param contractInfo 合同信息
     * @return 结果
     */
    @Override
    public int updateContractInfo(ContractInfo contractInfo) {
        // 校验新合同编号是否已存在
        if (!contractInfo.getContractNumber().equals(contractInfo.getOldContractNumber())) {
            ContractInfo contractInfoValidate = contractInfoMapper.selectContractInfoByContractNumber(contractInfo.getContractNumber());
            if (contractInfoValidate != null) {
                throw new ServiceException("该合同编号已存在，请重新输入");
            }

            // 修改父合同的child_number为新合同编号
            ContractInfo contractInfoParams = new ContractInfo();
            contractInfoParams.setChildNumber(contractInfo.getOldContractNumber());
            List<ContractInfo> contractInfoList = contractInfoMapper.selectContractInfoList(contractInfoParams);
            if (CollUtil.isNotEmpty(contractInfoList)) {
                contractInfoList.forEach(contractInfo1 -> {
                    contractInfo1.setChildNumber(contractInfo.getContractNumber());
                    contractInfoMapper.updateContractInfo(contractInfo1);
                });
            }
        }

        // 校验isRenewable是否为1，为1时不可编辑
        if (contractInfo.getIsRenewable() != null && "1".equals(contractInfo.getIsRenewable())) {
            throw new ServiceException("该合同信息不可编辑");
        }

        // 保存合同信息
        contractInfo.setIsRenewable(null);
        contractInfoMapper.updateContractInfo(contractInfo);

        return 1;
    }

    /**
     * 批量删除合同信息
     *
     * @param contractNumbers 需要删除的合同信息主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteContractInfoByContractNumbers(String contractNumbers) {
        // 根据编号获取合同信息
        ContractInfo contractInfo = contractInfoMapper.selectContractInfoByContractNumber(contractNumbers);
        if (contractInfo == null) {
            throw new ServiceException("该合同信息不存在");
        }

        // 删除合同信息
        contractInfoMapper.deleteContractInfoByContractNumber(contractNumbers);

        // 修改父合同的child_number为空，is_renewable为0
        ContractInfo contractInfoParams = new ContractInfo();
        contractInfoParams.setChildNumber(contractNumbers);
        List<ContractInfo> contractInfoList = contractInfoMapper.selectContractInfoList(contractInfoParams);
        if (CollUtil.isNotEmpty(contractInfoList)) {
            contractInfoList.forEach(contractInfo1 -> {
                contractInfo1.setOldContractNumber(contractInfo1.getContractNumber());
                contractInfo1.setChildNumber("");
                contractInfo1.setIsRenewable("0");
                contractInfoMapper.updateContractInfo(contractInfo1);
            });
        } else {
            // 修改营销人员的签约状态为0
            MarketingCustomerInfo marketingCustomerInfo = new MarketingCustomerInfo();
            marketingCustomerInfo.setId(contractInfo.getCustomerId());
            marketingCustomerInfo.setSignStatus("0");
            marketingCustomerInfoService.updateMarketingCustomerInfo(marketingCustomerInfo);
        }

        return 1;
    }

    /**
     * 删除合同信息信息
     *
     * @param contractNumber 合同信息主键
     * @return 结果
     */
    @Override
    public int deleteContractInfoByContractNumber(String contractNumber) {
        return contractInfoMapper.deleteContractInfoByContractNumber(contractNumber);
    }

    @Override
    public ContractInfo getContractInfoByCustomerId(Integer customerId) {
        return contractInfoMapper.getContractInfoByCustomerId(customerId);
    }

    @Override
    public List<ContractInfo> selectContractRenewInfoList(ContractInfo contractInfo) {
        return contractInfoMapper.selectContractRenewInfoList(contractInfo);
    }

    @Override
    public ContractInfo selectContractRenewInfoByContractNumber(String contractNumber) {
        return contractInfoMapper.selectContractInfoByContractNumber(contractNumber);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertContractRenewInfo(ContractInfo contractRenewInfo) {
        // 检查续签合同编号是否已存在
        ContractInfo contractInfoValidate = contractInfoMapper.selectContractInfoByContractNumber(contractRenewInfo.getChildContractInfo().getContractNumber());
        if (contractInfoValidate != null) {
            throw new ServiceException("该续签合同编号已存在，请重新输入");
        }

        // 更新父合同isRenewable为1
        ContractInfo parentContractInfo = new ContractInfo();
        parentContractInfo.setOldContractNumber(contractRenewInfo.getContractNumber());
        parentContractInfo.setContractNumber(contractRenewInfo.getContractNumber());
        parentContractInfo.setIsRenewable("1");
        parentContractInfo.setChildNumber(contractRenewInfo.getChildContractInfo().getContractNumber());
        contractInfoMapper.updateContractInfo(parentContractInfo);

        // 保存续签合同信息
        contractRenewInfo.getChildContractInfo().setIsRenewable("0");
        contractInfoMapper.insertContractInfo(contractRenewInfo.getChildContractInfo());
        return 1;
    }

    @Override
    public String generateContractNumber() {
        // 获取当前年份
        String currentYear = String.valueOf(LocalDate.now().getYear());

        // 查询当前年份最大的合同编号
        String maxContractNumber = contractInfoMapper.selectMaxContractNumberByYear(currentYear);

        // 编号前缀
        String prefix = "CXL-YY-FW-" + currentYear + "-003";

        // 如果没有找到当前年份的合同编号，则从1开始
        if (maxContractNumber == null || maxContractNumber.isEmpty()) {
            return prefix + "(01)";
        }

        // 从合同编号中提取序号，确保只匹配英文括号
        Pattern pattern = Pattern.compile("CXL-YY-FW-" + currentYear + "-003\\((\\d+)\\)$");
        Matcher matcher = pattern.matcher(maxContractNumber);

        if (matcher.find()) {
            // 获取序号并加1
            int sequence = Integer.parseInt(matcher.group(1));
            sequence++;

            // 格式化为2位数
            String formattedSequence = String.format("%02d", sequence);
            return prefix + "(" + formattedSequence + ")";
        } else {
            // 如果提取失败，从1开始
            return prefix + "(01)";
        }
    }

    @Override
    public String generateRenewContractNumber(String originalContractNumber) {
        if (originalContractNumber == null || originalContractNumber.isEmpty()) {
            throw new ServiceException("原合同编号不能为空");
        }

        // 检查是否已经是续签合同编号(包含"续"字)
        Pattern renewPattern = Pattern.compile("^(CXL-YY-FW-\\d{4}-003\\(\\d+\\))续\\((\\d+)\\)$");
        Matcher renewMatcher = renewPattern.matcher(originalContractNumber);

        if (renewMatcher.find()) {
            // 已经是续签合同，提取基础编号和续签次数
            String baseNumber = renewMatcher.group(1);
            int renewCount = Integer.parseInt(renewMatcher.group(2));

            // 续签次数加1
            renewCount++;

            // 返回新的续签编号
            return baseNumber + "续(" + renewCount + ")";
        } else {
            // 检查是否是基础合同编号
            Pattern basePattern = Pattern.compile("^(CXL-YY-FW-\\d{4}-003\\(\\d+\\))$");
            Matcher baseMatcher = basePattern.matcher(originalContractNumber);

            if (baseMatcher.find()) {
                // 是基础合同，添加续签信息
                String baseNumber = baseMatcher.group(1);
                return baseNumber + "续(1)";
            } else {
                // 不符合任何已知格式
                throw new ServiceException("原合同编号格式不正确，无法生成续签编号");
            }
        }
    }

    @Override
    public List<ContractInfo> selectContractInfoListNew(ContractInfo contractInfo) {
        return contractInfoMapper.selectContractInfoListNew(contractInfo);
    }

    @Override
    public List<ContractInfo> selectContractRenewInfoListNew(ContractInfo contractInfo) {
        return contractInfoMapper.selectContractRenewInfoListNew(contractInfo);
    }
}

