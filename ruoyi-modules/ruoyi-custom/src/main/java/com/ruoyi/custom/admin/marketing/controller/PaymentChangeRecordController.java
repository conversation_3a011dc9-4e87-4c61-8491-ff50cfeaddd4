package com.ruoyi.custom.admin.marketing.controller;

import cn.hutool.core.map.MapUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.marketing.domain.PaymentChangeRecord;
import com.ruoyi.custom.admin.marketing.service.IPaymentChangeRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 费用变更单Controller
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@RestController
@RequestMapping("/paymentChangeRecord")
@Api(value = "费用变更记录", tags = "费用变更记录")
public class PaymentChangeRecordController extends BaseController {
    @Autowired
    private IPaymentChangeRecordService paymentChangeRecordService;

    /**
     * 查询费用变更单列表
     */
    // @RequiresPermissions("custom:record:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询费用变更列表")
    public TableDataInfo list(PaymentChangeRecord paymentChangeRecord) {
        startPage();
        paymentChangeRecord.setParams(MapUtil.of("queryType","1")); //不包含餐费的账单
        List<PaymentChangeRecord> list = paymentChangeRecordService.selectPaymentChangeRecordList(paymentChangeRecord);
        return getDataTable(list);
    }

    /**
     * 查询餐费费用变更单列表
     */
    @GetMapping("/meal/list")
    @ApiOperation(value = "查询餐费费用变更单列表")
    public TableDataInfo mealList(PaymentChangeRecord paymentChangeRecord) {
        startPage();
        paymentChangeRecord.setParams(MapUtil.builder(new HashMap<String, Object>()).put("queryType","2").build()); //只有餐费的账单
        List<PaymentChangeRecord> list = paymentChangeRecordService.selectPaymentChangeRecordList(paymentChangeRecord);
        return getDataTable(list);
    }

    /**
     * 获取费用变更单详细信息
     */
    // @RequiresPermissions("custom:record:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取费用变更单详细信息")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(paymentChangeRecordService.selectPaymentChangeRecordById(id));
    }

    /**
     * 根据合同id，生成账单变更信息
     */
    @GetMapping("/change/info")
    @ApiOperation(value = "根据合同id，生成账单变更信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contractNumber", value = "合同编号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "1：护理套餐、床位变更；2：餐费变更", required = true, dataType = "String")
    })
    public AjaxResult generatePaymentChangeInfo(String contractNumber, String type) {
        PaymentChangeRecord paymentChangeRecord = paymentChangeRecordService.generatePaymentChangeInfo(contractNumber, type);
        return AjaxResult.success(paymentChangeRecord);
    }

    /**
     * 费用变更（床位套餐、护理套餐、餐费套餐通用）
     */
    @PostMapping("/change")
    @ApiOperation(value = "费用变更")
    public AjaxResult change(@RequestBody PaymentChangeRecord paymentChangeRecord) {
        return toAjax(paymentChangeRecordService.insertPaymentChangeRecord(paymentChangeRecord));
    }

}

