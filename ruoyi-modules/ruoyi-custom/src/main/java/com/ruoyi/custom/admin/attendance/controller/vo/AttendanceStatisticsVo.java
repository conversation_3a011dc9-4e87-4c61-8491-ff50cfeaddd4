package com.ruoyi.custom.admin.attendance.controller.vo;


import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("考勤统计Vo")
public class AttendanceStatisticsVo {

    @ApiModelProperty("姓名")
    @Excel(name = "姓名", sort = 0)
    private String name;

    @ApiModelProperty("出勤天数")
    @Excel(name = "出勤天数", sort = 1)
    private Integer attendanceDays;

    @ApiModelProperty("休息天数")
    @Excel(name = "休息天数", sort = 2)
    private Integer restDays;

    @ApiModelProperty("迟到天数")
    @Excel(name = "迟到天数", sort = 3)
    private Integer lateDays;

    @ApiModelProperty("迟到时长（分钟）")
    @Excel(name = "迟到时长（分钟）", sort = 4)
    private Integer lateMinutes;

    @ApiModelProperty("早退次数")
    @Excel(name = "早退次数", sort = 5)
    private Integer earlyLeaveDays;

    @ApiModelProperty("早退时长（分钟）")
    @Excel(name = "早退时长（分钟）", sort = 6)
    private Integer earlyLeaveMinutes;

    @ApiModelProperty("上班缺卡次数")
    @Excel(name = "上班缺卡次数", sort = 7)
    private Integer onDutyLackCardDays;

    @ApiModelProperty("下班缺卡次数")
    @Excel(name = "下班缺卡次数", sort = 8)
    private Integer offDutyLackCardDays;

}
