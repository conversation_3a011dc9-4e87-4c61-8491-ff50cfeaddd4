package com.ruoyi.custom.admin.medicalInspection.mapper;


import com.ruoyi.custom.admin.medicalInspection.domain.MedicalInspectionInfo;

import java.util.List;

/**
 * 医护巡查信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface MedicalInspectionInfoMapper {
    /**
     * 查询医护巡查信息
     *
     * @param id 医护巡查信息主键
     * @return 医护巡查信息
     */
    public MedicalInspectionInfo selectMedicalInspectionInfoById(Long id);

    /**
     * 查询医护巡查信息列表
     *
     * @param medicalInspectionInfo 医护巡查信息
     * @return 医护巡查信息集合
     */
    public List<MedicalInspectionInfo> selectMedicalInspectionInfoList(MedicalInspectionInfo medicalInspectionInfo);

    /**
     * 新增医护巡查信息
     *
     * @param medicalInspectionInfo 医护巡查信息
     * @return 结果
     */
    public int insertMedicalInspectionInfo(MedicalInspectionInfo medicalInspectionInfo);

    /**
     * 修改医护巡查信息
     *
     * @param medicalInspectionInfo 医护巡查信息
     * @return 结果
     */
    public int updateMedicalInspectionInfo(MedicalInspectionInfo medicalInspectionInfo);

    /**
     * 删除医护巡查信息
     *
     * @param id 医护巡查信息主键
     * @return 结果
     */
    public int deleteMedicalInspectionInfoById(Long id);

    /**
     * 批量删除医护巡查信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMedicalInspectionInfoByIds(Long[] ids);
}

