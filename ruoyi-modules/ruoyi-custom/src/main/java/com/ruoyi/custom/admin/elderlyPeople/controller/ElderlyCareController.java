package com.ruoyi.custom.admin.elderlyPeople.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyCare;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyCareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 长者入住关怀Controller
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@RestController
@RequestMapping("/elderlyCare")
@Api(value = "长者入住关怀Controller", tags = {"长者入住关怀"})
public class ElderlyCareController extends BaseController {
    @Autowired
    private IElderlyCareService elderlyCareService;

    /**
     * 查询长者入住关怀列表
     */
    // @RequiresPermissions("elderlyPeople:elderlyCare:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询长者入住关怀列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "elderId", value = "老人id", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "elderName", value = "老人姓名", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "title", value = "标题", dataTypeClass = String.class)
    })
    public TableDataInfo list(ElderlyCare elderlyCare) {
        startPage();
        List<ElderlyCare> list = elderlyCareService.selectElderlyCareList(elderlyCare);
        return getDataTable(list);
    }

    /**
     * 导出长者入住关怀列表
     */
    // @RequiresPermissions("elderlyPeople:elderlyCare:export")
    @Log(platform = "1", title = "长者入住关怀", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出长者入住关怀列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "elderId", value = "老人id", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "elderName", value = "老人姓名", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "title", value = "标题", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "params.startCareDate", value = "开始日期", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "params.endCareDate", value = "结束日期", dataTypeClass = String.class)
    })
    public void export(HttpServletResponse response, ElderlyCare elderlyCare) {
        List<ElderlyCare> list = elderlyCareService.selectElderlyCareList(elderlyCare);
        ExcelUtil<ElderlyCare> util = new ExcelUtil<>(ElderlyCare.class);
        util.exportExcel(response, list, "长者入住关怀数据");
    }

    /**
     * 获取长者入住关怀详细信息
     */
    // @RequiresPermissions("elderlyPeople:elderlyCare:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取长者入住关怀详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(elderlyCareService.selectElderlyCareById(id));
    }

    /**
     * 新增长者入住关怀
     */
    // @RequiresPermissions("elderlyPeople:elderlyCare:add")
    @Log(platform = "1", title = "长者入住关怀", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增长者入住关怀")
    public AjaxResult add(@RequestBody ElderlyCare elderlyCare) {
        return toAjax(elderlyCareService.insertElderlyCare(elderlyCare));
    }

    /**
     * 修改长者入住关怀
     */
    // @RequiresPermissions("elderlyPeople:elderlyCare:edit")
    @Log(platform = "1", title = "长者入住关怀", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改长者入住关怀")
    public AjaxResult edit(@RequestBody ElderlyCare elderlyCare) {
        return toAjax(elderlyCareService.updateElderlyCare(elderlyCare));
    }

    /**
     * 删除长者入住关怀
     */
    // @RequiresPermissions("elderlyPeople:elderlyCare:remove")
    @Log(platform = "1", title = "长者入住关怀", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除长者入住关怀")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(elderlyCareService.deleteElderlyCareByIds(ids));
    }
}
