package com.ruoyi.custom.admin.warehouse.domain;

import com.ruoyi.custom.utils.DictUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 供应商管理对象 t_supplier_info
 *
 * <AUTHOR>
 * @date 2022-05-09
 */
@ApiModel(value = "供应商")
public class SupplierInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    @ApiModelProperty(value = "供应商名称")
    private String name;

    /**
     * 供应商性质
     */
    @Excel(name = "供应商性质")
    @ApiModelProperty(value = "供应商性质")
    private String kind;

    @Excel(name = "供应商性质Label")
    @ApiModelProperty(value = "供应商性质Label")
    private String kindLabel;

    /**
     * 负责人
     */
    @Excel(name = "负责人")
    @ApiModelProperty(value = "负责人")
    private String principal;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 联系地址
     */
    @Excel(name = "联系地址")
    @ApiModelProperty(value = "联系地址")
    private String address;

    /**
     * 供应状态
     */
    @Excel(name = "供应状态")
    @ApiModelProperty(value = "供应状态")
    private String supplyState;

    @Excel(name = "供应状态Label")
    @ApiModelProperty(value = "供应状态Label")
    private String supplyStateLabel;

    /**
     * 供应商等级
     */
    @Excel(name = "供应商等级")
    @ApiModelProperty(value = "供应商等级")
    private String supplierLevel;

    @Excel(name = "供应商等级Label")
    @ApiModelProperty(value = "供应商等级Label")
    private String supplierLevelLabel;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSupplyState() {
        return supplyState;
    }

    public void setSupplyState(String supplyState) {
        this.supplyState = supplyState;
    }

    public String getSupplierLevel() {
        return supplierLevel;
    }

    public void setSupplierLevel(String supplierLevel) {
        this.supplierLevel = supplierLevel;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

    public String getKindLabel() {
        if (null == this.kind || this.kind.isEmpty()) {
            return "";
        }
        return getLabel(this.kind, "supplier_kind");
    }

    public void setKindLabel(String kindLabel) {
        this.kindLabel = kindLabel;
    }

    public String getSupplierLevelLabel() {
        if (null == this.supplierLevel || this.supplierLevel.isEmpty()) {
            return "";
        }
        return getLabel(this.supplierLevel, "supplier_level");
    }

    public void setSupplierLevelLabel(String supplierLevelLabel) {
        this.supplierLevelLabel = supplierLevelLabel;
    }

    public String getSupplyStateLabel() {
        if (null == this.supplyState || this.supplyState.isEmpty()) {
            return "";
        }
        return getLabel(this.supplyState, "supplier_state");
    }

    public void setSupplyStateLabel(String supplyStateLabel) {
        this.supplyStateLabel = supplyStateLabel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("kind", getKind())
                .append("principal", getPrincipal())
                .append("phone", getPhone())
                .append("address", getAddress())
                .append("supplyState", getSupplyState())
                .append("supplierLevel", getSupplierLevel())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }

    private String getLabel(String value, String type) {
        String[] split = value.split(",");
        String data = "";
        for (int i = 0; i < split.length; i++) {
            if (i == 0) {
                data += DictUtils.selectDictLabel(type, split[i]);
            } else {
                data += "、" + DictUtils.selectDictLabel(type, split[i]);
            }
        }
        return data;
    }
}
