package com.ruoyi.custom.admin.marketing.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 营销试住对象 t_marketing_trial_stay
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
@ApiModel(value = "营销试住信息")
public class MarketingTrialStay extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 客户id，关联t_marketing_customer_info
     */
    @Excel(name = "客户id")
    @ApiModelProperty(value = "客户id，关联t_marketing_customer_info")
    private Long customerId;

    /**
     * 试住开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "试住开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "试住开始日期")
    private Date trialStartDate;

    /**
     * 试住结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "试住结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "试住结束日期")
    private Date trialEndDate;

    // ------------------ 分界线 ------------------

    @ApiModelProperty(value = "老人姓名")
    private String elderName;

    @ApiModelProperty(value = "联系方式")
    private String consultantPhone;

    /**
     * 家属信息列表
     */
    @ApiModelProperty(value = "家属信息列表")
    private List<MarketingFamilyInfo> familyInfoList;

    /**
     * 合同信息
     */
    @ApiModelProperty(value = "合同信息")
    private ContractInfo contractInfo;

    /**
     * 老人信息
     */
    @ApiModelProperty(value = "老人信息")
    private ElderlyPeopleInfo elderlyPeopleInfo;

}

