package com.ruoyi.custom.admin.assessment.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.assessment.domain.NursingPlanTemplate;
import com.ruoyi.custom.admin.assessment.mapper.NursingPlanTemplateMapper;
import com.ruoyi.custom.admin.assessment.service.INursingPlanTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 评估护理计划模版Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-17
 */
@Service
public class NursingPlanTemplateServiceImpl implements INursingPlanTemplateService {
    @Autowired
    private NursingPlanTemplateMapper nursingPlanTemplateMapper;

    /**
     * 查询评估护理计划模版
     *
     * @param id 评估护理计划模版主键
     * @return 评估护理计划模版
     */
    @Override
    public NursingPlanTemplate selectNursingPlanTemplateById(String id) {
        return nursingPlanTemplateMapper.selectNursingPlanTemplateById(id);
    }

    /**
     * 查询评估护理计划模版列表
     *
     * @param nursingPlanTemplate 评估护理计划模版
     * @return 评估护理计划模版
     */
    @Override
    public List<NursingPlanTemplate> selectNursingPlanTemplateList(NursingPlanTemplate nursingPlanTemplate) {
        return nursingPlanTemplateMapper.selectNursingPlanTemplateList(nursingPlanTemplate);
    }

    /**
     * 新增评估护理计划模版
     *
     * @param nursingPlanTemplate 评估护理计划模版
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertNursingPlanTemplate(NursingPlanTemplate nursingPlanTemplate) {
        // 生成UUID
        nursingPlanTemplate.setId(UUID.randomUUID().toString());
        nursingPlanTemplate.setCreateTime(new Date());
        nursingPlanTemplate.setCreateBy(SecurityUtils.getUsername());

        // 根据模版类型初始化详情JSON结构
        JSONObject details = new JSONObject();

        if (nursingPlanTemplate.getTemplateType() == 2) {
            // 类型2：导因、预期目标、护理处置
            details.put("cause", new JSONArray());  // 导因
            details.put("expectedGoal", new JSONArray());  // 预期目标
            details.put("nursingDisposition", new JSONArray());  // 护理处置
        } else {
            // 类型1（默认）：导因、鉴定特征、护理目标、护理措施、评值
            details.put("cause", new JSONArray());  // 导因
            details.put("feature", new JSONArray());  // 鉴定特征
            details.put("goal", new JSONArray());  // 护理目标
            details.put("measure", new JSONArray());  // 护理措施
            details.put("evaluation", new JSONArray());  // 评值
        }

        nursingPlanTemplate.setDetails(details);

        return nursingPlanTemplateMapper.insertNursingPlanTemplate(nursingPlanTemplate);
    }

    /**
     * 修改评估护理计划模版
     *
     * @param nursingPlanTemplate 评估护理计划模版
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateNursingPlanTemplate(NursingPlanTemplate nursingPlanTemplate) {
        nursingPlanTemplate.setUpdateTime(new Date());
        nursingPlanTemplate.setUpdateBy(SecurityUtils.getUsername());

        NursingPlanTemplate original = nursingPlanTemplateMapper.selectNursingPlanTemplateById(nursingPlanTemplate.getId());
        if (original == null) {
            throw new ServiceException("找不到指定的护理计划模版");
        }

        // 检查模版类型是否发生变化
        if (!original.getTemplateType().equals(nursingPlanTemplate.getTemplateType())) {
            // 模版类型发生变化，重新初始化详情结构
            JSONObject details = new JSONObject();

            if (nursingPlanTemplate.getTemplateType() == 2) {
                // 类型2：导因、预期目标、护理处置
                details.put("cause", new JSONArray());  // 导因
                details.put("expectedGoal", new JSONArray());  // 预期目标
                details.put("nursingDisposition", new JSONArray());  // 护理处置
            } else {
                // 类型1（默认）：导因、鉴定特征、护理目标、护理措施、评值
                details.put("cause", new JSONArray());  // 导因
                details.put("feature", new JSONArray());  // 鉴定特征
                details.put("goal", new JSONArray());  // 护理目标
                details.put("measure", new JSONArray());  // 护理措施
                details.put("evaluation", new JSONArray());  // 评值
            }

            nursingPlanTemplate.setDetails(details);
        } else {
            // 模版类型未变化，保留原有详情
            nursingPlanTemplate.setDetails(original.getDetails());
        }

        return nursingPlanTemplateMapper.updateNursingPlanTemplate(nursingPlanTemplate);
    }

    /**
     * 批量删除评估护理计划模版
     *
     * @param ids 需要删除的评估护理计划模版主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteNursingPlanTemplateByIds(String[] ids) {
        return nursingPlanTemplateMapper.deleteNursingPlanTemplateByIds(ids);
    }

    /**
     * 删除评估护理计划模版信息
     *
     * @param id 评估护理计划模版主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteNursingPlanTemplateById(String id) {
        return nursingPlanTemplateMapper.deleteNursingPlanTemplateById(id);
    }

    /**
     * 更新部件内容
     *
     * @param id      模版ID
     * @param templateType 模版类型
     * @param section 部件名称
     * @param content 部件完整内容
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateSectionContent(String id, Integer templateType, String section, Map<String, String> content) {
        // 验证section是否适用于当前模版类型
        if (!isValidSectionForTemplateType(section, templateType)) {
            throw new ServiceException("部件名称'" + section + "'不适用于模版类型" + templateType);
        }

        NursingPlanTemplate template = nursingPlanTemplateMapper.selectNursingPlanTemplateById(id);
        if (template == null) {
            throw new ServiceException("找不到指定的护理计划模版");
        }

        // 验证传入的templateType与数据库中的templateType是否一致
        if (!template.getTemplateType().equals(templateType)) {
            throw new ServiceException("传入的模版类型与实际模版类型不匹配");
        }

        JSONObject details = template.getDetails();
        if (!details.containsKey(section)) {
            throw new ServiceException("模版结构错误，找不到指定的部件");
        }

        try {
            // 检查内容格式并适当处理
            Object contentData;

            // 直接解析为JSON数组
            contentData = JSON.parseArray(content.get("content"));

            // 更新指定部件的内容
            details.put(section, contentData);

            // 更新模版
            template.setUpdateTime(new Date());
            template.setUpdateBy(SecurityUtils.getUsername());

            return nursingPlanTemplateMapper.updateNursingPlanTemplate(template);
        } catch (Exception e) {
            throw new ServiceException("更新部件内容失败：" + e.getMessage());
        }
    }

    /**
     * 获取部件内容
     *
     * @param id      模版ID
     * @param templateType 模版类型
     * @param section 部件名称
     * @return 部件内容
     */
    @Override
    public String getSectionContent(String id, Integer templateType, String section) {
        // 验证section是否适用于当前模版类型
        if (!isValidSectionForTemplateType(section, templateType)) {
            throw new ServiceException("部件名称'" + section + "'不适用于模版类型" + templateType);
        }

        NursingPlanTemplate template = nursingPlanTemplateMapper.selectNursingPlanTemplateById(id);
        if (template == null) {
            throw new ServiceException("找不到指定的护理计划模版");
        }

        // 验证传入的templateType与数据库中的templateType是否一致
        if (!template.getTemplateType().equals(templateType)) {
            throw new ServiceException("传入的模版类型与实际模版类型不匹配");
        }

        JSONObject details = template.getDetails();

        // 获取指定部件的内容并转为字符串
        JSONArray sectionContent = details.getJSONArray(section);
        return sectionContent.toJSONString();
    }

    /**
     * 验证section是否适用于指定的模版类型
     *
     * @param section 部件名称
     * @param templateType 模版类型
     * @return 是否有效
     */
    private boolean isValidSectionForTemplateType(String section, Integer templateType) {
        if (templateType == 2) {
            // 类型2支持的部件：导因、预期目标、护理处置
            return "cause".equals(section) || "expectedGoal".equals(section) || "nursingDisposition".equals(section);
        } else {
            // 类型1支持的部件：导因、鉴定特征、护理目标、护理措施、评值
            return "cause".equals(section) || "feature".equals(section) || "goal".equals(section) ||
                   "measure".equals(section) || "evaluation".equals(section);
        }
    }
}
