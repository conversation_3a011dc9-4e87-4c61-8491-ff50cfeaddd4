package com.ruoyi.custom.admin.storiedBuilding.mapper;

import java.util.List;
import java.util.Map;

import cn.hutool.json.JSONObject;
import com.ruoyi.custom.admin.storiedBuilding.domain.BedBaseInfo;
import com.ruoyi.custom.admin.storiedBuilding.domain.vo.BedRecordsInfoVo;
import com.ruoyi.custom.api.adminApp.rep.BedStateDataResp;
import org.apache.ibatis.annotations.Param;

/**
 * 床位基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
public interface BedBaseInfoMapper {
    /**
     * 查询床位基础信息
     *
     * @param id 床位基础信息主键
     * @return 床位基础信息
     */
    public BedBaseInfo selectBedBaseInfoById(Long id);

    /**
     * 查询床位基础信息列表
     *
     * @param bedBaseInfo 床位基础信息
     * @return 床位基础信息集合
     */
    public List<BedBaseInfo> selectBedBaseInfoList(BedBaseInfo bedBaseInfo);

    /**
     * 新增床位基础信息
     *
     * @param bedBaseInfo 床位基础信息
     * @return 结果
     */
    public int insertBedBaseInfo(BedBaseInfo bedBaseInfo);

    /**
     * 修改床位基础信息
     *
     * @param bedBaseInfo 床位基础信息
     * @return 结果
     */
    public int updateBedBaseInfo(BedBaseInfo bedBaseInfo);

    /**
     * 删除床位基础信息
     *
     * @param id 床位基础信息主键
     * @return 结果
     */
    public int deleteBedBaseInfoById(Long id);

    /**
     * 批量删除床位基础信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBedBaseInfoByIds(Long[] ids);

    /**
     * 通过房间id查询是否有床位
     *
     * @param id
     * @return
     */
    public int hasBedByRoomId(Long id);

    /**
     * 通过房间id获取床位信息
     *
     * @param roomId
     * @return
     */
    List<JSONObject> getBedInfo(Long roomId);


    /**
     * 获取房间的所有床位信息
     *
     * @param roomId
     * @return
     */
    public List<JSONObject> getRoomBedList(Long roomId);


    public JSONObject getRoomBedNum(Long roomId);

    /**
     * 房态图数据
     *
     * @param id
     * @param typeId
     * @return
     */
    public List<JSONObject> getBedStateData(@Param("id") Long id, @Param("typeId") Long typeId);

    /**
     * 房态图历史记录
     *
     * @param bedId
     * @param name
     * @return
     */
    public List<BedRecordsInfoVo> getBedRecordsList(@Param("bedId") String bedId, @Param("name") String name);

    /**
     * 根据房间id获取床位名称和id
     *
     * @param roomId
     * @return
     */
    List<JSONObject> getBedNameByRoomId(Long roomId);

    /**
     * 房态图数据2
     * @param bedState
     * @return
     */
    List<BedStateDataResp> getBedStateData2(String bedState);

    /**
     * 获取床位数量概要
     * @return
     */
    JSONObject getBedCountSummary(Map params);
}
