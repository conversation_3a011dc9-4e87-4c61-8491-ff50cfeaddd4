package com.ruoyi.custom.admin.dashboard.service;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;
import java.util.Map;

/**
 * 大屏接口
 */
public interface DashboardService {



    /**
     * 长者年龄分布
     * @return
     */
    List<Map<String,Object>> getElderAgeData();

    /**
     * 能力评估
     */
    List<Map<String,Object>> getAbilityAssessment();


    /**
     * 入住流量分析
     */
    List<Map<String, Object>>  getCheckInFlow();

    /**
     * 收入趋势
     */
    List<Map<String, Object>>  getIncomeTrend();


}
