package com.ruoyi.custom.admin.index.controller;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.storiedBuilding.domain.StoriedBuildingInfo;
import com.ruoyi.custom.admin.storiedBuilding.service.IBedBaseInfoService;
import com.ruoyi.custom.admin.storiedBuilding.service.IStoriedBuildingInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description 首页
 * <AUTHOR>
 * @Date 2025/7/10
 */
@RestController
@RequestMapping("/index")
@Api(tags = "首页")
public class IndexController {

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    @Autowired
    private IStoriedBuildingInfoService storiedBuildingInfoService;

    @Autowired
    private IBedBaseInfoService bedBaseInfoService;


    /**
     * 长者信息统计
     */
    @ApiOperation(value = "长者信息统计")
    @RequestMapping("/elderlyInfoStatistics")
    public JSONObject elderlyInfoStatistics() {
        // init data
        Integer elderlyTotal = 0; // 长者总数
        Long livingElderly = 0L; // 在住人数
        Long leaveElderly = 0L; // 请假人数

        // 计算
        List<ElderlyPeopleInfo> elderlyPeopleInfoList = elderlyPeopleInfoService.selectElderlyPeopleInfoList(new ElderlyPeopleInfo());
        elderlyTotal = elderlyPeopleInfoList.size();
        livingElderly = elderlyPeopleInfoList.stream().filter(elderlyPeopleInfo -> "0".equals(elderlyPeopleInfo.getStatus())).count();
        leaveElderly = elderlyPeopleInfoList.stream().filter(elderlyPeopleInfo -> "2".equals(elderlyPeopleInfo.getStatus())).count();

        // 包装返回
        return JSONUtil.createObj().set("elderlyTotal", elderlyTotal).set("livingElderly", livingElderly).set("leaveElderly", leaveElderly);
    }

    /**
     * 床位信息统计
     */
    @ApiOperation(value = "床位信息统计")
    @RequestMapping("/bedInfoStatistics")
    public JSONObject bedInfoStatistics() {
        // init data
        Long roomTotal = 0L; // 房间数量
        Integer bedTotal = 0; // 床位总数
        Integer freeBeds = 0; // 空闲床位

        // 计算
        StoriedBuildingInfo StoriedBuildingInfoParams = new StoriedBuildingInfo();
        StoriedBuildingInfoParams.setType("1");
        StoriedBuildingInfoParams.setParentId(0L);
        List<StoriedBuildingInfo> storiedBuildingInfoList = storiedBuildingInfoService.selectStoriedBuildingInfoList(StoriedBuildingInfoParams);
        roomTotal = storiedBuildingInfoList.stream().map(StoriedBuildingInfo::getTotalRoomsNumber).reduce(0L, Long::sum);
        JSONObject bedCountSummary = bedBaseInfoService.getBedCountSummary(new JSONObject());
        bedTotal = bedCountSummary.getInt("totalBeds");
        freeBeds = bedCountSummary.getInt("freeBeds");

        // 包装返回
        return JSONUtil.createObj().set("roomTotal", roomTotal).set("bedTotal", bedTotal).set("freeBeds", freeBeds);
    }

    /**
     * 入住流量分析（近一个月每天数据）
     */
    @ApiOperation(value = "入住流量分析（近一个月每天数据）")
    @RequestMapping("/getCheckInFlow")
    public JSONObject getCheckInFlow() {
        // 计算近一个月的开始和结束日期
        Date endDate = new Date(); // 当前日期
        Date startDate = DateUtil.offset(endDate, DateField.MONTH, -1); // 一个月前

        String startDateStr = DateUtil.formatDate(startDate);
        String endDateStr = DateUtil.formatDate(endDate);

        // 查询一个月内每天的入住数据
        List<JSONObject> checkInFlowData = elderlyPeopleInfoService.getCheckInFlowStatistics(startDateStr, endDateStr);

        // 填充可能没有数据的日期
        List<String> dateList = new ArrayList<>();
        List<Integer> countList = new ArrayList<>();

        // 生成日期序列（最近一个月的每一天）
        Date tempDate = startDate;
        while (!tempDate.after(endDate)) {
            String dateStr = DateUtil.formatDate(tempDate);
            dateList.add(dateStr);

            // 查找该日期是否有数据
            boolean hasData = false;
            for (JSONObject data : checkInFlowData) {
                if (dateStr.equals(data.getStr("date"))) {
                    countList.add(data.getInt("count"));
                    hasData = true;
                    break;
                }
            }

            // 如果没有数据则填充0
            if (!hasData) {
                countList.add(0);
            }

            // 移动到下一天
            tempDate = DateUtil.offsetDay(tempDate, 1);
        }

        // 包装返回数据
        return JSONUtil.createObj()
                .set("dates", dateList)
                .set("counts", countList);
    }

    /**
     * 楼栋各楼层入住情况统计
     */
    @ApiOperation(value = "楼栋各楼层入住情况统计")
    @RequestMapping("/getFloorCheckInStatistics")
    @ApiImplicitParams(
            @ApiImplicitParam(name = "id", value = "楼栋ID", required = true, dataType = "Long")
    )
    public List<JSONObject> getFloorCheckInStatistics(@RequestParam("id") Long id) {
        // init
        List<JSONObject> result = new ArrayList<>();

        // 根据楼栋id查询楼层ids
        List<JSONObject> floorInfos = storiedBuildingInfoService.getTreeData("2", String.valueOf(id));
        for (JSONObject floorInfo : floorInfos) {
            JSONObject params = JSONUtil.createObj().set("id", floorInfo.getLong("id"));
            JSONObject bedCountSummary = bedBaseInfoService.getBedCountSummary(params);
            bedCountSummary.set("name", floorInfo.getStr("name"));
            bedCountSummary.set("id", floorInfo.getLong("id"));
            result.add(bedCountSummary);
        }

        // 返回数据
        return result;
    }


}
