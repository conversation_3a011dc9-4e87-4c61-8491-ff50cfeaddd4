package com.ruoyi.custom.admin.assessment.domain;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@Data
@ApiModel(value = "评估计划表")
public class AssessmentPlan extends BaseEntity {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "编号，自定义")
    private String serialNumber;

    @ApiModelProperty(value = "营销意向客户id，对应t_marketing_customer_info")
    private Long customerId;

    @ApiModelProperty(value = "评估人id，对应sys_user")
    private Long assessorId;

    @ApiModelProperty(value = "评估人姓名")
    private String assessorName;

    @ApiModelProperty(value = "模版id, 对应t_assessment_template")
    private Long templateId;

    @ApiModelProperty(value = "指标json")
    private JSONArray indicator;

    @ApiModelProperty(value = "模版等级json")
    private JSONObject grade;

    @ApiModelProperty(value = "评估原因，字典：custom_assessment_plan_reason")
    private String assessmentReason;

    @ApiModelProperty(value = "评估地点")
    private String assessmentLocation;

    @ApiModelProperty(value = "计划开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date plannedStartDate;

    @ApiModelProperty(value = "实际开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualStartTime;

    @ApiModelProperty(value = "实际完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualEndTime;

    @ApiModelProperty(value = "状态，字典：custom_assessment_plan_status")
    private String status;

    @ApiModelProperty(value = "评估结果JSON")
    private JSONObject assessmentResult;
    
    @ApiModelProperty(value = "护理计划指标")
    private JSONArray nursingPlanIndicators;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "附件地址，多个逗号隔开")
    private String attachmentUrl;

    @ApiModelProperty(value = "老人姓名")
    @TableField(exist = false)
    private String elderlyName;

    @ApiModelProperty(value = "老人年龄")
    @TableField(exist = false)
    private Integer elderlyAge;

    @ApiModelProperty(value = "老人身份证号")
    @TableField(exist = false)
    private String elderlyIdCardNum;

    @ApiModelProperty(value = "老人性别，字典：custom_elderly_people_sex")
    @TableField(exist = false)
    private String elderlySex;

    @ApiModelProperty(value = "老人出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(exist = false)
    private Date elderlyDateBirth;

    @ApiModelProperty(value = "评估模版名称")
    @TableField(exist = false)
    private String templateName;

}

