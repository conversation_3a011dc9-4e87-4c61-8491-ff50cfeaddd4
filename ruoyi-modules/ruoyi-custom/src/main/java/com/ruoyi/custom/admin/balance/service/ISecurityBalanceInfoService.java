package com.ruoyi.custom.admin.balance.service;

import com.ruoyi.custom.admin.balance.domain.SecurityBalanceInfo;
import com.ruoyi.custom.admin.balance.domain.SecurityBalanceRecords;

import java.math.BigDecimal;
import java.util.List;

/**
 * 保障金账户信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface ISecurityBalanceInfoService {
    /**
     * 查询保障金账户信息
     *
     * @param id 保障金账户信息主键
     * @return 保障金账户信息
     */
    public SecurityBalanceInfo selectSecurityBalanceInfoById(Integer id);

    /**
     * 查询保障金账户信息列表
     *
     * @param securityBalanceInfo 保障金账户信息
     * @return 保障金账户信息集合
     */
    public List<SecurityBalanceInfo> selectSecurityBalanceInfoList(SecurityBalanceInfo securityBalanceInfo);

    /**
     * 新增保障金账户信息
     *
     * @param securityBalanceInfo 保障金账户信息
     * @return 结果
     */
    public int insertSecurityBalanceInfo(SecurityBalanceInfo securityBalanceInfo);

    /**
     * 修改保障金账户信息
     *
     * @param securityBalanceInfo 保障金账户信息
     * @return 结果
     */
    public int updateSecurityBalanceInfo(SecurityBalanceInfo securityBalanceInfo);

    /**
     * 批量删除保障金账户信息
     *
     * @param ids 需要删除的保障金账户信息主键集合
     * @return 结果
     */
    public int deleteSecurityBalanceInfoByIds(Integer[] ids);

    /**
     * 删除保障金账户信息
     *
     * @param id 保障金账户信息主键
     * @return 结果
     */
    public int deleteSecurityBalanceInfoById(Integer id);
    
    /**
     * 充值保障金
     * 
     * @param elderlyId 老人ID
     * @param operatorId 操作人ID
     * @param payAmount 充值金额
     * @return 保障金账户记录
     */
    SecurityBalanceRecords doPay(String elderlyId, String operatorId, BigDecimal payAmount);
    
    /**
     * 扣款保障金
     *
     * @param elderlyId 老人ID
     * @param operatorId 操作人ID
     * @param deductAmount 扣款金额
     * @return 保障金账户记录
     */
    SecurityBalanceRecords doDeduction(String elderlyId, String operatorId, BigDecimal deductAmount);
    
    /**
     * 退还保障金
     *
     * @param elderlyId 老人ID
     * @param operatorId 操作人ID
     * @param returnAmount 退款金额
     * @return 保障金账户记录
     */
    SecurityBalanceRecords returnSecurityBalance(String elderlyId, String operatorId, BigDecimal returnAmount);
} 