package com.ruoyi.custom.admin.storiedBuilding.service.impl;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.custom.admin.storiedBuilding.domain.BedBaseInfo;
import com.ruoyi.custom.admin.storiedBuilding.domain.vo.BedRecordsInfoVo;
import com.ruoyi.custom.admin.storiedBuilding.mapper.BedBaseInfoMapper;
import com.ruoyi.custom.admin.storiedBuilding.service.IBedBaseInfoService;
import com.ruoyi.custom.admin.storiedBuilding.service.IStoriedBuildingInfoService;
import com.ruoyi.custom.api.adminApp.rep.BedStateDataResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 床位基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@Service
public class BedBaseInfoServiceImpl implements IBedBaseInfoService {
    @Autowired
    private BedBaseInfoMapper bedBaseInfoMapper;

    @Autowired
    private IStoriedBuildingInfoService storiedBuildingInfoService;

    /**
     * 查询床位基础信息
     *
     * @param id 床位基础信息主键
     * @return 床位基础信息
     */
    @Override
    public BedBaseInfo selectBedBaseInfoById(Long id) {
        return bedBaseInfoMapper.selectBedBaseInfoById(id);
    }

    /**
     * 查询床位基础信息列表
     *
     * @param bedBaseInfo 床位基础信息
     * @return 床位基础信息
     */
    @Override
    public List<BedBaseInfo> selectBedBaseInfoList(BedBaseInfo bedBaseInfo) {
        return bedBaseInfoMapper.selectBedBaseInfoList(bedBaseInfo);
    }

    /**
     * 新增床位基础信息
     *
     * @param bedBaseInfo 床位基础信息
     * @return 结果
     */
    @Override
    public int insertBedBaseInfo(BedBaseInfo bedBaseInfo) {
        bedBaseInfo.setCreateTime(DateUtils.getNowDate());
        // 获取房间的床位数
        bedBaseInfo.setBedState("1");
        bedBaseInfoMapper.insertBedBaseInfo(bedBaseInfo);
        JSONObject roomBedNum = bedBaseInfoMapper.getRoomBedNum(bedBaseInfo.getRoomId());
        return saveRoomInfo(bedBaseInfo.getRoomId(), roomBedNum.getLong("totalBedNumber"), roomBedNum.getLong("occupancyNumber"));
    }

    /**
     * 修改床位基础信息
     *
     * @param bedBaseInfo 床位基础信息
     * @return 结果
     */
    @Override
    public int updateBedBaseInfo(BedBaseInfo bedBaseInfo) {
        bedBaseInfo.setUpdateTime(DateUtils.getNowDate());
        return bedBaseInfoMapper.updateBedBaseInfo(bedBaseInfo);
    }

    /**
     * 批量删除床位基础信息
     *
     * @param ids 需要删除的床位基础信息主键
     * @return 结果
     */
    @Override
    public int deleteBedBaseInfoByIds(Long[] ids) {
        int i = bedBaseInfoMapper.deleteBedBaseInfoByIds(ids);
        for (int j = 0; j < ids.length; j++) {
            // 获取房间的床位数
            JSONObject roomBedNum = bedBaseInfoMapper.getRoomBedNum(selectBedBaseInfoById(ids[j]).getRoomId());
            saveRoomInfo(selectBedBaseInfoById(ids[j]).getRoomId(), roomBedNum.getLong("totalBedNumber"), roomBedNum.getLong("occupancyNumber"));
        }
        return i;
    }

    /**
     * 删除床位基础信息信息
     *
     * @param id 床位基础信息主键
     * @return 结果
     */
    @Override
    public int deleteBedBaseInfoById(Long id) {
        return bedBaseInfoMapper.deleteBedBaseInfoById(id);
    }

    @Override
    public boolean hasBedByRoomId(Long id) {
        int result = bedBaseInfoMapper.hasBedByRoomId(id);
        return result > 0 ? true : false;
    }

    @Override
    public List<JSONObject> getBedInfo(Long roomId) {
        return bedBaseInfoMapper.getBedInfo(roomId);
    }

    @Override
    public List<JSONObject> getRoomBedList(Long roomId) {
        return bedBaseInfoMapper.getRoomBedList(roomId);
    }

    @Override
    public List<JSONObject> getBedStateData(Long id, Long typeId) {
        return bedBaseInfoMapper.getBedStateData(id, typeId);
    }

    @Override
    public List<BedRecordsInfoVo> getBedRecordsList(String bedId, String name) {
        return bedBaseInfoMapper.getBedRecordsList(bedId, name);
    }

    @Override
    public List<JSONObject> getBedNameByRoomId(Long roomId) {
        return bedBaseInfoMapper.getBedNameByRoomId(roomId);
    }

    @Override
    public List<BedStateDataResp> getBedStateData2(String bedState) {
        List<BedStateDataResp> list = bedBaseInfoMapper.getBedStateData2(bedState);
        list.stream()
                .map(BedStateDataResp::getFloorInfoList)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .map(BedStateDataResp.FloorInfo::getRoomInfoList)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .map(BedStateDataResp.RoomInfo::getBedInfoList)
                .filter(Objects::nonNull)
                .forEach(beds -> beds.removeIf(bed -> bed.getBedId() == null));

        return list;
    }

    @Override
    public JSONObject getBedCountSummary(Map params) {
        return bedBaseInfoMapper.getBedCountSummary(params);
    }

    /**
     * 修改房间的床位总数和入住总数
     *
     * @param id
     * @param totalBedNumber
     * @param occupancyNumber
     * @return
     */
    public int saveRoomInfo(Long id, Long totalBedNumber, Long occupancyNumber) {
        return storiedBuildingInfoService.updateStoriedBedNum(id, totalBedNumber, occupancyNumber);
    }

}
