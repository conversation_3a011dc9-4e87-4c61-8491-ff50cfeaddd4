package com.ruoyi.custom.admin.fee.domain.res;

import cn.hutool.json.JSONArray;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.custom.utils.DictUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
public class FeeComboVo extends BaseEntity {


    @ApiModelProperty(value = "套餐id", name = "comboId", example = "1231241231")
    private String comboId;

    @ApiModelProperty(value = "套餐名称", name = "comboName", example = "一级护理标准套餐")
    private String comboName;

    @ApiModelProperty(value = "护理等级", name = "careLevel", example = "0")
    private String careLevel;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    @ApiModelProperty(value = "护理等级", name = "careLevelStr", example = "一级护理")
    private String careLevelStr;

    @ApiModelProperty(value = "费用（月费用）", name = "monthAmount", example = "3000")
    private Long monthAmount;

    @ApiModelProperty(value = "明细（月费用）", name = "monthDetails", example = "[{\"name\":\"护理费\",\"value\":\"200\"},{\"name\":\"清洗费\",\"value\":\"50\"}]")
    private JSONArray monthDetails;
//
//    @ApiModelProperty(value = "短住费用（日费用）", name = "dayAmount", example = "100")
//    private Long dayAmount;
//
//    @ApiModelProperty(value = "短住明细（日费用）", name = "dayDetails", example = "[{\"name\":\"护理费\",\"value\":\"200\"},{\"name\":\"清洗费\",\"value\":\"50\"}]")
//    private JSONArray dayDetails;

    public String getCareLevelStr() {
        return DictUtils.selectDictLabel("care_level", this.careLevel);
    }

    public void setCareLevelStr(String careLevelStr) {
        this.careLevelStr = careLevelStr;
    }
}
