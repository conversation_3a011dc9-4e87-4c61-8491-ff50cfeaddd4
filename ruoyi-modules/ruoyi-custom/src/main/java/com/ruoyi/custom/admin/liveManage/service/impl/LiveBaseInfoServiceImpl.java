package com.ruoyi.custom.admin.liveManage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.liveManage.domain.LiveBaseInfo;
import com.ruoyi.custom.admin.liveManage.domain.LiveBedRecords;
import com.ruoyi.custom.admin.liveManage.domain.LiveComboRecords;
import com.ruoyi.custom.admin.liveManage.domain.LiveMealComboRecord;
import com.ruoyi.custom.admin.liveManage.domain.req.LiveInfoSaveReqVO;
import com.ruoyi.custom.admin.liveManage.domain.req.LiveQueryVo;
import com.ruoyi.custom.admin.liveManage.domain.res.LiveHistoryInfoRes;
import com.ruoyi.custom.admin.liveManage.domain.res.LiveInfoRes;
import com.ruoyi.custom.admin.liveManage.mapper.LiveBaseInfoMapper;
import com.ruoyi.custom.admin.liveManage.mapper.MealComboBaseMapper;
import com.ruoyi.custom.admin.liveManage.service.ILiveBaseInfoService;
import com.ruoyi.custom.admin.liveManage.service.ILiveBedRecordsService;
import com.ruoyi.custom.admin.liveManage.service.ILiveComboRecordsService;
import com.ruoyi.custom.admin.marketing.domain.ContractInfo;
import com.ruoyi.custom.admin.serviceWorkOrder.service.ServiceWorkOrderService;
import com.ruoyi.custom.admin.storiedBuilding.domain.BedBaseInfo;
import com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeIndexInfo;
import com.ruoyi.custom.admin.storiedBuilding.domain.StoriedBuildingInfo;
import com.ruoyi.custom.admin.storiedBuilding.service.IBedBaseInfoService;
import com.ruoyi.custom.admin.storiedBuilding.service.IRoomTypeIndexInfoService;
import com.ruoyi.custom.admin.storiedBuilding.service.IStoriedBuildingInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 居住信息基础Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@Service
public class LiveBaseInfoServiceImpl implements ILiveBaseInfoService {
    @Autowired
    private LiveBaseInfoMapper liveBaseInfoMapper;

    @Autowired
    private ILiveBedRecordsService liveBedRecordsService;

    @Autowired
    private ILiveComboRecordsService liveComboRecordsService;

    @Autowired
    private ServiceWorkOrderService serviceWorkOrderService;

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    @Autowired
    private IStoriedBuildingInfoService storiedBuildingInfoService;

    @Autowired
    private IBedBaseInfoService bedBaseInfoService;

    @Autowired
    private IRoomTypeIndexInfoService roomTypeIndexInfoService;

    @Autowired
    private MealComboBaseMapper mealComboBaseService;


    // 判断是否是当天时间
    public static boolean isToday(Date date) {

        Calendar c1 = Calendar.getInstance();
        c1.setTime(date);
        int year1 = c1.get(Calendar.YEAR);
        int month1 = c1.get(Calendar.MONTH) + 1;
        int day1 = c1.get(Calendar.DAY_OF_MONTH);
        Calendar c2 = Calendar.getInstance();
        c2.setTime(new Date());
        int year2 = c2.get(Calendar.YEAR);
        int month2 = c2.get(Calendar.MONTH) + 1;
        int day2 = c2.get(Calendar.DAY_OF_MONTH);
        if (year1 == year2 && month1 == month2 && day1 == day2) {
            return true;
        }
        return false;
    }

    /**
     * 查询居住信息基础
     *
     * @param id 居住信息基础主键
     * @return 居住信息基础
     */
    @Override
    public LiveInfoRes selectLiveBaseInfoById(String id) {
        LiveInfoRes liveInfoRes = liveBaseInfoMapper.selectLiveBaseInfoById(id);
        liveInfoRes.setCheckOutDate(new Date());
        return liveInfoRes;
    }

    /**
     * 查询居住信息基础列表
     *
     * @param liveQueryVo 居住信息基础
     * @return 居住信息基础
     */
    @Override
    public List<LiveInfoRes> selectLiveBaseInfoList(LiveQueryVo liveQueryVo) {
        return liveBaseInfoMapper.selectLiveBaseInfoList(liveQueryVo);
    }

    /**
     * 新增居住信息基础
     *
     * @param liveBaseInfo 居住信息基础
     * @return 结果
     */
    @Override
    public String insertLiveBaseInfo(LiveBaseInfo liveBaseInfo) {
        liveBaseInfo.setId(IdUtils.fastSimpleUUID());
        liveBaseInfo.setState("0");
        liveBaseInfo.setCreateBy(SecurityUtils.getUserId().toString());
        liveBaseInfo.setCreateTime(DateUtils.getNowDate());
        if (liveBaseInfoMapper.insertLiveBaseInfo(liveBaseInfo) > 0) {
            return liveBaseInfo.getId();
        }
        return null;
    }

    /**
     * 修改居住信息基础
     *
     * @param liveBaseInfo 居住信息基础
     * @return 结果
     */
    @Override
    public int updateLiveBaseInfo(LiveBaseInfo liveBaseInfo) {
        liveBaseInfo.setUpdateTime(DateUtils.getNowDate());
        return liveBaseInfoMapper.updateLiveBaseInfo(liveBaseInfo);
    }

    /**
     * 批量删除居住信息基础
     *
     * @param ids 需要删除的居住信息基础主键
     * @return 结果
     */
    @Override
    public int deleteLiveBaseInfoByIds(String[] ids) {
        return liveBaseInfoMapper.deleteLiveBaseInfoByIds(ids);
    }

    /**
     * 删除居住信息基础信息
     *
     * @param id 居住信息基础主键
     * @return 结果
     */
    @Override
    public int deleteLiveBaseInfoById(String id) {
        return liveBaseInfoMapper.deleteLiveBaseInfoById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int add(LiveInfoSaveReqVO reqVO) {
        // 入住基本信息
        LiveBaseInfo liveBaseInfo = BeanUtil.copyProperties(reqVO, LiveBaseInfo.class);
        // 床位记录
        LiveBedRecords liveBedRecords = BeanUtil.copyProperties(reqVO, LiveBedRecords.class);
        // 套餐记录
        LiveComboRecords liveComboRecords = BeanUtil.copyProperties(reqVO, LiveComboRecords.class);
        // 餐费套餐记录
        LiveMealComboRecord liveMealComboRecord = BeanUtil.copyProperties(reqVO, LiveMealComboRecord.class);

        // 根据床位信息查询是否有在用人员
        LiveBedRecords bedParam = new LiveBedRecords();
        bedParam.setBedId(reqVO.getBedId());
        bedParam.setLiveState("0");
        List<LiveBedRecords> bedRecordsList = liveBedRecordsService.selectLiveBedRecordsList(bedParam);
        if (bedRecordsList.size() > 0) {
            throw new ServiceException("该床位已有居住人员，请更换床位");
        }

        // 检查床位是否分配护工
        BedBaseInfo bedBaseInfo = bedBaseInfoService.selectBedBaseInfoById(Long.parseLong(reqVO.getBedId()));

        // 插入入住基本信息
        liveBaseInfo.setState("0");
        String baseId = insertLiveBaseInfo(liveBaseInfo); // 插入入住基本信息
        if (StrUtil.isEmpty(baseId)) {
            throw new ServiceException();
        }

        // 插入居住床位记录
        ElderlyPeopleInfo e = elderlyPeopleInfoService.selectElderlyPeopleInfoById(reqVO.getUserId()); // 查询老人合同信息
        ContractInfo contractInfo = e.getContractInfo();
        Date beginDate = Optional.ofNullable(contractInfo).map(ContractInfo::getContractStartDate).orElse(null); // 入住日期
        liveBedRecords.setLiveId(baseId);
        liveBedRecords.setBeginDate(beginDate);
        if (liveBedRecordsService.insertLiveBedRecords(liveBedRecords) == 0) {
            throw new ServiceException();
        }

        // 插入居住套餐信息
        liveComboRecords.setLiveId(baseId);
        if (liveComboRecordsService.insertLiveComboRecords(liveComboRecords) == 0) {
            throw new ServiceException();
        }

        // 插入居住餐费套餐记录
        liveMealComboRecord.setLiveId(baseId);
        liveMealComboRecord.setStatus("0");
        if (mealComboBaseService.insertMealComboRecord(liveMealComboRecord) == 0) {
            throw new ServiceException();
        }

        // 根据规则生成工单
        // serviceWorkOrderService.saveList(reqVO, liveBaseInfo.getId(), bedBaseInfo == null ? null : bedBaseInfo.getCareIndex());

        // 改变老人状态
        ElderlyPeopleInfo elderlyPeopleInfo = new ElderlyPeopleInfo();
        elderlyPeopleInfo.setId(reqVO.getUserId());
        elderlyPeopleInfo.setStatus("0");
        elderlyPeopleInfoService.updateElderlyPeopleInfo(elderlyPeopleInfo);

        return 1;
    }

    @Override
    public int editBedRecords(LiveBedRecords liveBedRecords) {
        String liveId = liveBedRecords.getLiveId();
        LiveBedRecords bedRecords = selectNowBedRecordsByLiveId(liveId);


        // 查询
        String newBedId = liveBedRecords.getBedId();

        // 根据床位信息查询是否有在用人员
        LiveBedRecords bedParam = new LiveBedRecords();
        bedParam.setBedId(liveBedRecords.getBedId());
        bedParam.setLiveState("0");
        List<LiveBedRecords> bedRecordsList = liveBedRecordsService.selectLiveBedRecordsList(bedParam);
        if (bedRecordsList.size() == 1) {

            changeBed(bedRecords, newBedId);
            changeBed(bedRecordsList.get(0), bedRecords.getBedId());

        } else if (bedRecordsList.size() == 0) {
            changeBed(bedRecords, newBedId);
        } else {
            throw new ServiceException("床位信息异常，请联系开发人员排查数据");
        }

        return 1;

    }

    private LiveBedRecords selectNowBedRecordsByLiveId(String liveId) {
        LiveBedRecords bedRecordsParam = new LiveBedRecords();
        bedRecordsParam.setLiveId(liveId);
        bedRecordsParam.setLiveState("0");
        List<LiveBedRecords> bedRecords = liveBedRecordsService.selectLiveBedRecordsList(bedRecordsParam);
        if (bedRecords.size() == 1) {
            return bedRecords.get(0);
        } else {
            throw new ServiceException("床位信息有误：liveid::" + liveId);
        }
    }

    private void changeBed(LiveBedRecords oldBedRecords, String newBedId) {
        // 通过bedId 查询房间号及房间类型
        BedBaseInfo bedBaseInfo = bedBaseInfoService.selectBedBaseInfoById(Long.valueOf(newBedId));
        Long roomId = bedBaseInfo.getRoomId();
        StoriedBuildingInfo roomInfo = storiedBuildingInfoService.selectStoriedBuildingInfoById(roomId);
        if (roomInfo == null || StrUtil.isEmpty(roomInfo.getRoomType())) {
            throw new ServiceException("房间信息不完整，请完善房间类型后再进行操作");
        }

        // 根据liveId查询当前在用的床位信息 并修改
        LiveBedRecords param = new LiveBedRecords();
        param.setLiveId(oldBedRecords.getLiveId());
        param.setLiveState("0");
        List<LiveBedRecords> list = liveBedRecordsService.selectLiveBedRecordsList(param);
        // 循环遍历在用记录为已修改
        for (LiveBedRecords bedRecords : list) {
            bedRecords.setLiveState("1");
            // 判断是否属于当天换床位
            boolean todayFlag = isToday(bedRecords.getBeginDate());
            if (todayFlag) {
                bedRecords.setDelFlag("1");
                bedRecords.setEndDate(new Date());
            } else {
                // 获取前一天日期
                Date today = new Date();
                Calendar instance = Calendar.getInstance();
                instance.setTime(today);
                instance.add(Calendar.DAY_OF_MONTH, -1);
                Date time = instance.getTime();
                bedRecords.setEndDate(time);
            }
            liveBedRecordsService.updateLiveBedRecords(bedRecords);
        }

        // 查询老的房间类型
        String oldRoomId = oldBedRecords.getRoomId();
        StoriedBuildingInfo oldRoomInfo = storiedBuildingInfoService.selectStoriedBuildingInfoById(Long.valueOf(oldRoomId));
        if (roomInfo.getRoomType().equals(oldRoomInfo.getRoomType())) {
            LiveBedRecords bedRecords = new LiveBedRecords();
            bedRecords.setLiveId(oldBedRecords.getLiveId());
            bedRecords.setBedId(newBedId);
            bedRecords.setRoomId(String.valueOf(roomInfo.getId()));
            bedRecords.setRoomVersion(oldBedRecords.getRoomVersion());
            bedRecords.setBeginDate(new Date());
            liveBedRecordsService.insertLiveBedRecords(bedRecords);
        } else {
            LiveBedRecords bedRecords = new LiveBedRecords();
            bedRecords.setLiveId(oldBedRecords.getLiveId());
            bedRecords.setBedId(newBedId);
            bedRecords.setRoomId(String.valueOf(roomInfo.getId()));
            RoomTypeIndexInfo indexParam = new RoomTypeIndexInfo();
            indexParam.setRoomId(roomInfo.getId());
            indexParam.setStatus("0");
            bedRecords.setBeginDate(new Date());
            List<RoomTypeIndexInfo> roomTypeIndexInfos = roomTypeIndexInfoService.selectRoomTypeIndexInfoList(indexParam);
            if (roomTypeIndexInfos.size() == 1 && StrUtil.isNotEmpty(roomTypeIndexInfos.get(0).getTypeVersion())) {
                bedRecords.setRoomVersion(roomTypeIndexInfos.get(0).getTypeVersion());
            } else {
                throw new ServiceException("该房间绑定类型有误，请联系管理员排查问题，房间id" + roomInfo.getId());
            }
            liveBedRecordsService.insertLiveBedRecords(bedRecords);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int changeUserCombo(LiveComboRecords liveComboRecords) {
        // 查询状态为0的状态
        LiveComboRecords param = new LiveComboRecords();
        param.setLiveId(liveComboRecords.getLiveId());
        param.setState("0"); // 在用
        List<LiveComboRecords> list = liveComboRecordsService.selectLiveComboRecordsList(param);

        // 修改老记录为“变更”
        for (LiveComboRecords comboRecords : list) {
            comboRecords.setState("1"); // 变更
            liveComboRecordsService.updateLiveComboRecords(comboRecords);
        }

        // 插入新的套餐记录
        int i = liveComboRecordsService.insertLiveComboRecords(liveComboRecords);
        if (i == 0) {
            throw new ServiceException("套餐记录插入失败");
        }

        return 1;
    }

    // @Override
    // @Transactional(rollbackFor = Exception.class)
    // public int updateLiveInfo(LiveInfoUpdateVO param) {
    //
    //     LiveBaseInfo liveBaseInfo = BeanUtil.copyProperties(param, LiveBaseInfo.class);
    //     liveBaseInfo.setUpdateTime(DateUtils.getNowDate());
    //     liveBaseInfo.setUpdateBy(SecurityUtils.getUserId().toString());
    //     liveBaseInfoMapper.updateLiveBaseInfo(liveBaseInfo);
    //
    //     LiveBedRecords liveBedRecords = param.getLiveBedRecords();
    //     if (liveBedRecords != null) {
    //         liveBedRecords.setLiveId(param.getId());
    //         editBedRecords(liveBedRecords);
    //     }
    //     LiveComboRecords liveComboRecords = param.getLiveComboRecords();
    //
    //     if (liveComboRecords != null) {
    //         liveComboRecords.setLiveId(param.getId());
    //         editComboRecords(liveComboRecords);
    //     }
    //
    //
    //     return 1;
    // }

    @Override
    public List<LiveHistoryInfoRes> selectHistoryList(String id, String type) {
        return liveBaseInfoMapper.selectHistoryList(id, type);
    }

    @Override
    public List<JSONObject> getCurrentLiveInfoByElderIds(List<String> elderlyIds) {
        return liveBaseInfoMapper.getCurrentLiveIdByElderIds(elderlyIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int changeBed(String liveId, Long oldBedId, Long newBedId, String newRoomId, String newRoomVersion) {
        // 更新原t_bed_base_info的状态为“空闲”（1）
        BedBaseInfo bedBaseInfo1 = new BedBaseInfo();
        bedBaseInfo1.setId(oldBedId);
        bedBaseInfo1.setBedState("1");
        bedBaseInfo1.setUpdateBy(SecurityUtils.getUserId().toString());
        bedBaseInfo1.setUpdateTime(DateUtils.getNowDate());
        bedBaseInfoService.updateBedBaseInfo(bedBaseInfo1);

        // 更新新的t_bed_base_info的状态为“入住”（0）
        BedBaseInfo bedBaseInfo2 = new BedBaseInfo();
        bedBaseInfo2.setId(newBedId);
        bedBaseInfo2.setBedState("0");
        bedBaseInfo2.setUpdateBy(SecurityUtils.getUserId().toString());
        bedBaseInfo2.setUpdateTime(DateUtils.getNowDate());

        // 修改老住记录的t_live_bed_records的live_state为变更（1）
        LiveBedRecords params = new LiveBedRecords();
        params.setLiveId(liveId);
        params.setBedId(String.valueOf(oldBedId));
        List<LiveBedRecords> liveBedRecords1s = liveBedRecordsService.selectLiveBedRecordsList(params);
        // 获取创建时间最新的一条
        Optional<LiveBedRecords> newestLiveBedRecords = liveBedRecords1s.stream()
                .max(Comparator.comparing(LiveBedRecords::getCreateTime));
        LiveBedRecords record;
        if (newestLiveBedRecords.isPresent()) {
            // 获取到最新的记录
            record = newestLiveBedRecords.get();
            record.setLiveState("1");
            record.setEndDate(DateUtils.getNowDate());
            record.setUpdateBy(SecurityUtils.getUserId().toString());
            record.setUpdateTime(DateUtils.getNowDate());
            liveBedRecordsService.updateByLiveIdAndBedId(record);
        } else {
           throw new ServiceException("未找到对应床位入住的记录");
        }

        // 插入新的t_live_bed_records记录
        LiveBedRecords liveBedRecords2 = BeanUtil.copyProperties(record, LiveBedRecords.class);
        liveBedRecords2.setBedId(String.valueOf(newBedId));
        liveBedRecords2.setRoomId(newRoomId);
        liveBedRecords2.setRoomVersion(newRoomVersion);
        liveBedRecords2.setLiveState("0");
        liveBedRecordsService.insertLiveBedRecords(liveBedRecords2);

        return 1;
    }

    @Override
    public List<Map> getLivePeopleList(String name) {
        return liveBaseInfoMapper.getLivePeopleList(name);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int changeMealCombo(LiveMealComboRecord reqVO) {
        // 修改老记录为“失效”
        LiveMealComboRecord param = new LiveMealComboRecord();
        param.setLiveId(reqVO.getLiveId());
        param.setStatus("1"); // 失效
        mealComboBaseService.updateMealComboRecord(param);

        // 插入新的套餐记录
        mealComboBaseService.insertMealComboRecord(reqVO);

        return 1;
    }

}
