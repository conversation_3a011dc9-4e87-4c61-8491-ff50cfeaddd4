package com.ruoyi.custom.admin.storiedBuilding.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.storiedBuilding.domain.TypeVersionInfo;
import com.ruoyi.custom.admin.storiedBuilding.service.ITypeVersionInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 房间类型版本Controller
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@RestController
@RequestMapping("/TypeVersionInfo")
public class TypeVersionInfoController extends BaseController {
    @Autowired
    private ITypeVersionInfoService TypeVersionInfoService;

    /**
     * 查询房间类型版本列表
     */
    //@RequiresPermissions("storiedBuilding:TypeVersionInfo:list")
    @GetMapping("/list")
    public TableDataInfo list(TypeVersionInfo TypeVersionInfo) {
        startPage();
        List<TypeVersionInfo> list = TypeVersionInfoService.selectTypeVersionInfoList(TypeVersionInfo);
        return getDataTable(list);
    }

    /**
     * 导出房间类型版本列表
     */
    //@RequiresPermissions("storiedBuilding:TypeVersionInfo:export")
    @Log(platform = "1", title = "房间类型版本", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TypeVersionInfo TypeVersionInfo) {
        List<TypeVersionInfo> list = TypeVersionInfoService.selectTypeVersionInfoList(TypeVersionInfo);
        ExcelUtil<TypeVersionInfo> util = new ExcelUtil<TypeVersionInfo>(TypeVersionInfo.class);
        util.exportExcel(response, list, "房间类型版本数据");
    }

    /**
     * 获取房间类型版本详细信息
     */
    //@RequiresPermissions("storiedBuilding:TypeVersionInfo:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(TypeVersionInfoService.selectTypeVersionInfoById(id));
    }

    /**
     * 新增房间类型版本
     */
    //@RequiresPermissions("storiedBuilding:TypeVersionInfo:add")
    @Log(platform = "1", title = "房间类型版本", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TypeVersionInfo TypeVersionInfo) {
        return toAjax(TypeVersionInfoService.insertTypeVersionInfo(TypeVersionInfo));
    }

    /**
     * 修改房间类型版本
     */
    //@RequiresPermissions("storiedBuilding:TypeVersionInfo:edit")
    @Log(platform = "1", title = "房间类型版本", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TypeVersionInfo TypeVersionInfo) {
        return toAjax(TypeVersionInfoService.updateTypeVersionInfo(TypeVersionInfo));
    }

    /**
     * 删除房间类型版本
     */
    //@RequiresPermissions("storiedBuilding:TypeVersionInfo:remove")
    @Log(platform = "1", title = "房间类型版本", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(TypeVersionInfoService.deleteTypeVersionInfoByIds(ids));
    }
}
