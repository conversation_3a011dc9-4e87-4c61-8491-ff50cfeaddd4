package com.ruoyi.custom.admin.wexinjavapay.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName WxOrderRefund
 * @Description
 * <AUTHOR>
 * @Date 2022/8/18 15:08
 */
@Data
@ApiModel("wx_order_refund")
@TableName("wx_order_refund")
public class WxOrderRefund implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * base订单id
     */
    @ApiModelProperty("base订单id")
    private String outTradeNo;

    /**
     * 微信订单号
     */
    @ApiModelProperty("微信订单号")
    private String transactionId;

    /**
     * 微信退款单号
     */
    @ApiModelProperty("微信退款单号")
    private String refundId;

    /**
     * 商户退款单号
     */
    @ApiModelProperty("商户退款单号")
    private String outRefundNo;

    /**
     * 订单金额(订单总金额，单位为分，只能为整数，详见支付金额)
     */
    @ApiModelProperty("订单金额(订单总金额，单位为分，只能为整数，详见支付金额)")
    private Integer totalFee;

    /**
     * 结订单金额(当该订单有使用非充值券时，返回此字段。应结订单金额=订单金额-非充值代金券金额，应结订单金额<=订单金额。)
     */
    @ApiModelProperty("结订单金额(当该订单有使用非充值券时，返回此字段。应结订单金额=订单金额-非充值代金券金额，应结订单金额<=订单金额。)")
    private Integer settlementTotalFee;

    /**
     * 申请退款金额 退款总金额，单位为分
     */
    @ApiModelProperty("申请退款金额 退款总金额，单位为分")
    private Integer refundFee;

    /**
     * 退款金额=申请退款金额-非充值代金券退款金额，退款金额<=申请退款金额
     */
    @ApiModelProperty("退款金额=申请退款金额-非充值代金券退款金额，退款金额<=申请退款金额")
    private Integer settlementRefundFee;

    /**
     * 退款状态 success-退款成功，change-退款异常，refundclose—退款关闭
     */
    @ApiModelProperty("退款状态 success-退款成功，change-退款异常，refundclose—退款关闭")
    private String refundStatus;

    /**
     * 退款成功时间
     */
    @ApiModelProperty("退款成功时间")
    private String successTime;

    /**
     * 退款入账账户 --取当前退款单的退款入账方，1）退回银行卡：{银行名称}{卡类型}{卡尾号}，2）退回支付用户零钱:支付用户零钱 ，3）退还商户: 商户基本账户，商户结算银行账户，4）退回支付用户零钱通: 支付用户零钱通
     */
    @ApiModelProperty("退款入账账户 --取当前退款单的退款入账方，1）退回银行卡：{银行名称}{卡类型}{卡尾号}，2）退回支付用户零钱:支付用户零钱 ，3）退还商户: 商户基本账户，商户结算银行账户，4）退回支付用户零钱通: 支付用户零钱通")
    private String refundRecvAccout;

    /**
     * 退款资金来源-->refund_source_recharge_funds 可用余额退款/基本账户，refund_source_unsettled_funds 未结算资金退款
     */
    @ApiModelProperty("退款资金来源-->refund_source_recharge_funds 可用余额退款/基本账户，refund_source_unsettled_funds 未结算资金退款")
    private String refundAccount;

    /**
     * 退款发起来源-->api接口，vendor_platform商户平台
     */
    @ApiModelProperty("退款发起来源-->api接口，vendor_platform商户平台")
    private String refundRequestSource;

    /**
     * create_time
     */
    @ApiModelProperty("create_time")
    private Date createTime;

    /**
     * update_time
     */
    @ApiModelProperty("update_time")
    private Date updateTime;


}
