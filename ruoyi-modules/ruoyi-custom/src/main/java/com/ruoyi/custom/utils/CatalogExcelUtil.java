package com.ruoyi.custom.utils;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFSheet;

public class CatalogExcelUtil {

    /**
     * 创建Workbook（仅适用于 .xls 文件）
     * 如果需要自动识别类型建议使用 WorkbookFactory.create(in)
     */
    public static Workbook createWorkBook(InputStream in) throws Exception {
        try {
            return new HSSFWorkbook(in);
        } finally {
            if (in != null) {
                in.close();
            }
        }
    }

    public static void initCell(Cell cell, CellStyle style, String value) {
        cell.setCellStyle(style);
        cell.setCellValue(value);
    }

    public static void initCell(Cell cell, CellStyle style, String value, Comment comment) {
        cell.setCellStyle(style);
        cell.setCellValue(value);
        cell.setCellComment(comment);
    }

    public static Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<>();

        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setFontName("Arial");
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setBold(true);
        style.setFont(titleFont);
        styles.put("title", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        Font dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        styles.put("data", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font headerFont = wb.createFont();
        headerFont.setFontName("Arial");
        headerFont.setFontHeightInPoints((short) 10);
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(headerFont);
        styles.put("header", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font totalFont = wb.createFont();
        totalFont.setFontName("Arial");
        totalFont.setFontHeightInPoints((short) 10);
        style.setFont(totalFont);
        styles.put("total", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.LEFT);
        styles.put("data1", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.CENTER);
        styles.put("data2", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.RIGHT);
        styles.put("data3", style);

        return styles;
    }

    /**
     * 为 .xlsx (XSSFWorkbook) 创建下拉列表
     */
    public static void createDropDownList(Workbook wb, Sheet realSheet, String[] datas,
                                          int firstRow, int lastRow, int firstCol, int lastCol,
                                          String hiddenSheetName) {
        // 创建隐藏Sheet
        Sheet hiddenSheet = wb.createSheet(hiddenSheetName);
        for (int i = 0; i < datas.length; i++) {
            Row row = hiddenSheet.createRow(i);
            row.createCell(0).setCellValue(datas[i]);
        }

        // 修复1：设置列宽防止数据截断 [2](@ref)
        hiddenSheet.setColumnWidth(0, 6000); // 重要修复！

        // 修复2：使用简单命名规则（避免特殊字符）
        String namedRangeName = "NR_" + hiddenSheetName;
        Name namedRange = wb.createName();
        namedRange.setNameName(namedRangeName);

        // 修复3：正确构建引用公式 [3,6](@ref)
        String refersTo = String.format("'%s'!$A$1:$A$%d",
                hiddenSheetName,
                datas.length);
        namedRange.setRefersToFormula(refersTo);

        // 兼容XSSF和HSSF [1,5](@ref)
        DataValidationHelper dvHelper = realSheet.getDataValidationHelper();
        DataValidationConstraint dvConstraint = dvHelper.createFormulaListConstraint(namedRangeName);

        // 设置验证范围
        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
        DataValidation dataValidation = dvHelper.createValidation(dvConstraint, addressList);

        // 启用下拉箭头
        dataValidation.setSuppressDropDownArrow(false);
        realSheet.addValidationData(dataValidation);

        // 隐藏Sheet
        wb.setSheetHidden(wb.getSheetIndex(hiddenSheet), true);
    }

    /**
     * @deprecated 请使用 createDropDownList 方法替代，仅适用于 .xls (HSSFWorkbook)
     */
    @Deprecated
    public static HSSFWorkbook dropDownList2003(Workbook wb, Sheet realSheet, String[] datas,
                                                int startRow, int endRow, int startCol, int endCol,
                                                String hiddenSheetName, int hiddenSheetIndex) {
        if (!(wb instanceof HSSFWorkbook)) {
            throw new IllegalArgumentException("This method is for HSSFWorkbook only.");
        }

        HSSFWorkbook workbook = (HSSFWorkbook) wb;
        org.apache.poi.hssf.usermodel.HSSFSheet hidden = workbook.createSheet(hiddenSheetName);
        workbook.setSheetHidden(workbook.getSheetIndex(hidden.getSheetName()), true);

        for (int i = 0; i < datas.length; i++) {
            org.apache.poi.hssf.usermodel.HSSFRow row = hidden.createRow(i);
            row.createCell(0).setCellValue(datas[i]);
        }

        org.apache.poi.hssf.usermodel.HSSFName namedCell = workbook.createName();
        namedCell.setNameName(hiddenSheetName + "_ref");
        namedCell.setRefersToFormula(hiddenSheetName + "!$A$1:$A$" + datas.length);

        DVConstraint constraint = DVConstraint.createFormulaListConstraint(hiddenSheetName + "_ref");
        CellRangeAddressList addressList = new CellRangeAddressList(startRow, endRow, startCol, endCol);
        DataValidation dataValidation = new org.apache.poi.hssf.usermodel.HSSFDataValidation(addressList, constraint);
        realSheet.addValidationData(dataValidation);

        return workbook;
    }
}
