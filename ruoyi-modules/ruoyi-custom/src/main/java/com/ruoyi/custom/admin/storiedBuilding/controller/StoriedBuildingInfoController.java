package com.ruoyi.custom.admin.storiedBuilding.controller;

import cn.hutool.json.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.google.common.collect.Lists;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.liveManage.domain.LiveBedRecords;
import com.ruoyi.custom.admin.liveManage.service.ILiveBedRecordsService;
import com.ruoyi.custom.admin.storiedBuilding.domain.StoriedBuildingInfo;
import com.ruoyi.custom.admin.storiedBuilding.domain.TreeSelect;
import com.ruoyi.custom.admin.storiedBuilding.service.IBedBaseInfoService;
import com.ruoyi.custom.admin.storiedBuilding.service.IRoomTypeBaseInfoService;
import com.ruoyi.custom.admin.storiedBuilding.service.IRoomTypeIndexInfoService;
import com.ruoyi.custom.admin.storiedBuilding.service.IStoriedBuildingInfoService;
import com.ruoyi.custom.utils.CatalogExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.ruoyi.custom.utils.CatalogExcelUtil.dropDownList2003;

/**
 * 楼栋信息Controller
 *
 * <AUTHOR>
 * @date 2022-03-29
 */
@RestController
@RequestMapping("/storiedBuildingInfo")
@Api(value = "楼栋信息Controller", tags = {"楼栋信息"})
public class StoriedBuildingInfoController extends BaseController {
    @Autowired
    private IStoriedBuildingInfoService storiedBuildingInfoService;

    @Autowired
    private IRoomTypeBaseInfoService roomTypeBaseInfoService;

    @Autowired
    private IBedBaseInfoService bedBaseInfoService;

    @Autowired
    private IRoomTypeIndexInfoService roomTypeIndexInfoService;

    @Autowired
    private ILiveBedRecordsService liveBedRecordsService;

    public static File MultipartFileToFile(MultipartFile multiFile) {
        // 获取文件名
        String fileName = multiFile.getOriginalFilename();
        // 获取文件后缀
        String prefix = fileName.substring(fileName.lastIndexOf("."));
        // 若须要防止生成的临时文件重复,能够在文件名后添加随机码

        try {
            File file = File.createTempFile(fileName, prefix);
            multiFile.transferTo(file);
            return file;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static List<Map<String, String>> readWorkbook(Workbook workbook) {
        if (workbook == null || workbook.getNumberOfSheets() == 0) {
            return null;
        }
        List<Map<String, String>> result = Lists.newLinkedList();
        Map<String, String> map;
        Sheet sheet;
        Cell cell;
        Row head, row;
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            sheet = workbook.getSheetAt(i);
            if (sheet == null || sheet.getPhysicalNumberOfRows() == 0) {
                continue;
            }
            head = sheet.getRow(0);
            if (head == null || head.getPhysicalNumberOfCells() == 0) {
                continue;
            }
            int rows = sheet.getPhysicalNumberOfRows(), cols = head.getPhysicalNumberOfCells();
            for (int j = 1; j < rows; j++) {
                row = sheet.getRow(j);
                map = Maps.newLinkedHashMap();
                for (int k = 0; k < cols; k++) {
                    cell = row.getCell(k);
                    if (null != cell) {
                        if (cell.getCellType().equals(CellType.BOOLEAN)) {
                            map.put(head.getCell(k).getStringCellValue(), Boolean.valueOf(cell.getBooleanCellValue()).toString());
                        } else if (cell.getCellType().equals(CellType.NUMERIC)) {
                            map.put(head.getCell(k).getStringCellValue(), Double.valueOf(cell.getNumericCellValue()).toString());
                        } else if (cell.getCellType().equals(CellType.STRING)) {
                            map.put(head.getCell(k).getStringCellValue(), cell.getStringCellValue());
                        } else {
                            map.put(head.getCell(k).getStringCellValue(), StringUtils.EMPTY);
                        }
                    }
                    result.add(map);
                }
            }
        }
        return result;
    }

    /**
     * 查询楼栋信息列表
     */
    //@RequiresPermissions("storiedBuilding:storiedBuildingInfo:list")
    @ApiOperation(value = "查询楼栋信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "标识类型", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "parentId", value = "父级id", required = false, dataTypeClass = String.class),
    })
    @GetMapping("/list")
    public TableDataInfo<StoriedBuildingInfo> list(@ApiIgnore StoriedBuildingInfo storiedBuildingInfo) {
        startPage();
        List<StoriedBuildingInfo> list = storiedBuildingInfoService.selectStoriedBuildingInfoList(storiedBuildingInfo);
        return getDataTable(list);
    }

    /**
     * 获取全部楼栋楼层房间床位树列表
     */
    @GetMapping("/treeselect")
    @ApiOperation(value = "获取楼栋树列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "name", value = "名称", required = false, dataTypeClass = String.class),
    })
    public AjaxResult treeselect(@ApiIgnore StoriedBuildingInfo storiedBuildingInfo) {
        List<StoriedBuildingInfo> storiedBuildingInfos = storiedBuildingInfoService.selectStoriedBuildingInfoList(storiedBuildingInfo);
        List<TreeSelect> treeSelects = storiedBuildingInfoService.buildBuildingTreeSelect(storiedBuildingInfos);
        return AjaxResult.success(treeSelects);
    }

    /**
     * 获取全部楼栋楼层房间床位树列表
     */
    @GetMapping("/treeBuildingList")
    @ApiOperation(value = "获取全部楼栋楼层房间床位树列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "name", value = "名称", dataTypeClass = String.class),
    })
    public AjaxResult treeBuildingList(@ApiIgnore StoriedBuildingInfo storiedBuildingInfo) {
        List<StoriedBuildingInfo> storiedBuildingInfos = storiedBuildingInfoService.selectStoriedBuildingInfoList(storiedBuildingInfo);
        List<TreeSelect> treeSelects = storiedBuildingInfoService.buildBuildingTreeSelectList(storiedBuildingInfos);
        return AjaxResult.success(treeSelects);
    }

    /**
     * 懒加载楼栋树列表
     */
    @GetMapping("/getTreeData")
    @ApiOperation(value = "懒加载楼栋树列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "id", value = "id", required = false, dataTypeClass = String.class),
    })
    public AjaxResult getTreeData(@RequestParam(value = "type", required = false) String type, @RequestParam(value = "id", required = false) String id) {
        List<JSONObject> treeData = storiedBuildingInfoService.getTreeData(type, id);
        return AjaxResult.success().put("data", treeData);
    }

    /**
     * 导出楼栋信息列表
     */
    //@RequiresPermissions("storiedBuilding:storiedBuildingInfo:export")
    @Log(platform = "1", title = "楼栋信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出楼栋信息列表")
    public void export(HttpServletResponse response, StoriedBuildingInfo storiedBuildingInfo) {
        List<StoriedBuildingInfo> list = storiedBuildingInfoService.selectStoriedBuildingInfoList(storiedBuildingInfo);
        ExcelUtil<StoriedBuildingInfo> util = new ExcelUtil<StoriedBuildingInfo>(StoriedBuildingInfo.class);
        util.exportExcel(response, list, "楼栋信息数据");
    }

    /**
     * 获取楼栋信息详细信息
     */
    //@RequiresPermissions("storiedBuilding:storiedBuildingInfo:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取楼栋信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success().put("data", storiedBuildingInfoService.selectStoriedBuildingInfoById(id));
    }

    /**
     * 新增楼栋信息
     */
    //@RequiresPermissions("storiedBuilding:storiedBuildingInfo:add")
    @Log(platform = "1", title = "楼栋信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增楼栋信息")
    @PostMapping
    public AjaxResult add(@RequestBody StoriedBuildingInfo storiedBuildingInfo) {
        int i = storiedBuildingInfoService.insertStoriedBuildingInfo(storiedBuildingInfo);
        return AjaxResult.success().put("id", storiedBuildingInfo.getId());
    }

    /**
     * 修改楼栋信息
     */
    //@RequiresPermissions("storiedBuilding:storiedBuildingInfo:edit")
    @Log(platform = "1", title = "楼栋信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改楼栋信息")
    @PutMapping
    public AjaxResult edit(@RequestBody StoriedBuildingInfo storiedBuildingInfo) {
        return toAjax(storiedBuildingInfoService.updateStoriedBuildingInfo(storiedBuildingInfo));
    }

    /**
     * 删除楼栋信息
     */
    //@RequiresPermissions("storiedBuilding:storiedBuildingInfo:remove")
    @Log(platform = "1", title = "楼栋信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除楼栋信息")
    @GetMapping("/deleteById")
    public AjaxResult remove(Long[] ids, String type) {
        if ("4".equals(type)) {
            for (int i = 0; i < ids.length; i++) {
                LiveBedRecords bedParam = new LiveBedRecords();
                bedParam.setBedId(String.valueOf(ids[i]));
                bedParam.setLiveState("0");
                List<LiveBedRecords> bedRecordsList = liveBedRecordsService.selectLiveBedRecordsList(bedParam);
                if (!bedRecordsList.isEmpty()) {
                    return AjaxResult.error("当前床位有人，不能删除");
                }
            }
            return toAjax(bedBaseInfoService.deleteBedBaseInfoByIds(ids));
        } else if ("3".equals(type)) {
            for (int i = 0; i < ids.length; i++) {
                if (bedBaseInfoService.hasBedByRoomId(ids[i])) {
                    return AjaxResult.error("当前删除项现有下级，不能删除");
                }
            }
            roomTypeIndexInfoService.deleteByRoomId(ids);
        } else {
            for (int i = 0; i < ids.length; i++) {
                if (storiedBuildingInfoService.hasChildById(ids[i])) {
                    return AjaxResult.error("当前删除项现有下级，不能删除");
                }
            }
        }
        return toAjax(storiedBuildingInfoService.deleteStoriedBuildingInfoByIds(ids));
    }

    @PostMapping("/exportTemplate")
    @ApiOperation(value = "导出楼栋信息模板")
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 1. 使用 XSSFWorkbook 对应 .xlsx 格式
        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet("楼栋导入模板");
        Map<String, CellStyle> styles = CatalogExcelUtil.createStyles(wb);
        CellStyle title = styles.get("header");

        // 第一行
        Row row = sheet.createRow(0);
        CatalogExcelUtil.initCell(row.createCell(0), title, "楼区名称");
        CatalogExcelUtil.initCell(row.createCell(1), title, "楼层名称");
        CatalogExcelUtil.initCell(row.createCell(2), title, "房间名称");
        CatalogExcelUtil.initCell(row.createCell(3), title, "房间类型"); // 下拉列表将在此列

        // 获取下拉列表数据
        JSONObject entries = roomTypeBaseInfoService.selectName(); // 假设这个服务返回格式如 {"str":"单人间,双人间,三人间"}
        String str = entries.getStr("str");
        String[] roomTypes = str.split(",");

        if (str.getBytes("UTF-8").length > 250) {
            // 超过250字节时强制使用隐藏Sheet方案
            CatalogExcelUtil.createDropDownList(wb, sheet, roomTypes, 1, 100, 3, 3, "room_types");
        } else {
            // 小数据量可直接创建
            DataValidationHelper dvHelper = sheet.getDataValidationHelper();
            CellRangeAddressList addressList = new CellRangeAddressList(1, 100, 3, 3);
            DataValidation validation = dvHelper.createValidation(
                    dvHelper.createExplicitListConstraint(roomTypes),
                    addressList
            );
            sheet.addValidationData(validation);
        }

        ExcelUtil<StoriedBuildingInfo> util = new ExcelUtil<StoriedBuildingInfo>(StoriedBuildingInfo.class);

        // 获取输出流
        OutputStream os = response.getOutputStream();
        // 重置输出流
        response.reset();

        // 设定输出文件头 - 文件名后缀已是 .xlsx
        String fileName = "楼栋导入模板.xlsx";
        // 对于中文文件名，进行URL编码更标准
        String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment; filename*=UTF-8''" + encodedFileName);

        // 定义输出类型 - 这个是正确的 .xlsx MIME 类型
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        wb.write(os);
        os.close();
        wb.close(); // 关闭 workbook
    }

    @PostMapping("/importData")
    public AjaxResult importData(@RequestPart("file") MultipartFile file) {
        try (InputStream is = file.getInputStream()) {
            Workbook workbook = WorkbookFactory.create(is); // 自动识别格式
            Sheet sheet = workbook.getSheetAt(0);
            DataFormatter formatter = new DataFormatter(); // 安全格式化单元格值

            List<JSONObject> dataList = new ArrayList<>();
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue; // 跳过标题行

                JSONObject obj = new JSONObject();
                obj.set("buildingName", formatter.formatCellValue(row.getCell(0)));
                obj.set("floorName", formatter.formatCellValue(row.getCell(1)));
                obj.set("roomName", formatter.formatCellValue(row.getCell(2)));
                obj.set("roomType", formatter.formatCellValue(row.getCell(3)));

                dataList.add(obj);
            }

            String msg = storiedBuildingInfoService.importData(dataList, false);
            return AjaxResult.success(msg);
        } catch (Exception e) {
            return AjaxResult.error("导入失败: " + e.getMessage());
        }
    }

}
