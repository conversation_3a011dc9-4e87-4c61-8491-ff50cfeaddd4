package com.ruoyi.custom.admin.marketing.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Data
public class LiveInfo2Res {

    @ApiModelProperty(value = "床位", name = "bedName", required = true, example = "一楼栋-一层-101房间-1号床")
    private String bedName;

    @ApiModelProperty(value = "护理级别", name = "careLevel", required = true, example = "1")
    private String careLevel;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    @ApiModelProperty(value = "护理级别名称", name = "careLevelStr", required = true, example = "1")
    private String careLevelStr;

    @ApiModelProperty(value = "居住状态", name = "state", required = true, example = "0,1,2")
    private String state;

    @ApiModelProperty(value = "居住状态str", name = "stateStr", required = true, example = "居住中")
    private String stateStr;

    @ApiModelProperty(value = "离园日期", name = "dischargeDate", required = true, example = "1")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dischargeDate;

}
