package com.ruoyi.custom.admin.liveManage.service.impl;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.mapper.ElderlyPeopleInfoMapper;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.liveManage.domain.LiveLeaveRecords;
import com.ruoyi.custom.admin.liveManage.domain.res.LiveInfoRes;
import com.ruoyi.custom.admin.liveManage.mapper.LiveBaseInfoMapper;
import com.ruoyi.custom.admin.liveManage.mapper.LiveLeaveRecordsMapper;
import com.ruoyi.custom.admin.liveManage.service.ILiveLeaveRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 居住请假Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@Service
public class LiveLeaveRecordsServiceImpl implements ILiveLeaveRecordsService {

    @Autowired
    private LiveLeaveRecordsMapper liveLeaveRecordsMapper;

    @Autowired
    private ElderlyPeopleInfoMapper elderlyPeopleInfoMapper;

    @Autowired
    private LiveBaseInfoMapper liveBaseInfoMapper;

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    /**
     * 查询居住请假
     *
     * @param id 居住请假主键
     * @return 居住请假
     */
    @Override
    public LiveLeaveRecords selectLiveLeaveRecordsById(String id) {
        return liveLeaveRecordsMapper.selectLiveLeaveRecordsById(id);
    }

    /**
     * 查询居住请假列表
     *
     * @param liveLeaveRecords 居住请假
     * @return 居住请假
     */
    @Override
    public List<LiveLeaveRecords> selectLiveLeaveRecordsList(LiveLeaveRecords liveLeaveRecords) {
        return liveLeaveRecordsMapper.selectLiveLeaveRecordsList(liveLeaveRecords);
    }

    /**
     * 新增居住请假
     *
     * @param liveLeaveRecords 居住请假
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertLiveLeaveRecords(LiveLeaveRecords liveLeaveRecords) {
        // 新增请假记录
        liveLeaveRecords.setId(IdUtils.fastSimpleUUID());
        liveLeaveRecords.setState("0"); // 请假
        liveLeaveRecords.setAuditState("0"); // 审核中
        liveLeaveRecords.setCreateBy(SecurityUtils.getUserId().toString());
        liveLeaveRecords.setCreateTime(DateUtils.getNowDate());
        return liveLeaveRecordsMapper.insertLiveLeaveRecords(liveLeaveRecords);
    }

    /**
     * 修改居住请假
     *
     * @param params 居住请假
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int operatorCancelLeave(LiveLeaveRecords params) {
        // 更新请假记录状态为已销假，等待后勤确认
        LiveLeaveRecords liveLeaveRecords = new LiveLeaveRecords();
        liveLeaveRecords.setId(params.getId());
        liveLeaveRecords.setState("1"); // 已销假，等待后勤确认
        liveLeaveRecords.setBackTime(params.getBackTime());
        liveLeaveRecords.setReturningName(params.getReturningName());
        liveLeaveRecords.setReturningType(params.getReturningType());
        liveLeaveRecords.setIsFreeCare(params.getIsFreeCare());
        liveLeaveRecords.setAvoidCareDays(params.getAvoidCareDays());
        liveLeaveRecords.setIsFreeMeal(params.getIsFreeMeal());
        liveLeaveRecords.setAvoidMealDays(params.getAvoidMealDays());
        liveLeaveRecords.setCancelUserId(SecurityUtils.getUserId().toString());
        liveLeaveRecords.setCancelUserName(SecurityUtils.getUsername());
        liveLeaveRecords.setUpdateTime(DateUtils.getNowDate());
        liveLeaveRecords.setUpdateBy(SecurityUtils.getUserId().toString());
        int i = liveLeaveRecordsMapper.updateLiveLeaveRecords(liveLeaveRecords);
        LiveLeaveRecords leaveRecords = liveLeaveRecordsMapper.selectLiveLeaveRecordsById(params.getId());

        // 更改老人基础信息状态为正常
        ElderlyPeopleInfo elderlyPeopleInfo = elderlyPeopleInfoMapper.selectElderlyPeopleInfoById(leaveRecords.getUserId());
        elderlyPeopleInfo.setStatus("0"); // 入住
        if (elderlyPeopleInfoMapper.updateElderlyPeopleInfo(elderlyPeopleInfo) < 1) {
            throw new ServiceException("异常请联系管理员排查");
        }

        // 更改老人居住基础信息状态为正常
        LiveInfoRes liveInfoRes = liveBaseInfoMapper.selectLiveBaseInfoById(leaveRecords.getLiveId());
        liveInfoRes.setState("0"); // 入住
        if (liveBaseInfoMapper.updateLiveInfoResState(liveInfoRes) < 1) {
            throw new ServiceException("异常请联系管理员排查");
        }

        return i;
    }

    /**
     * 批量删除居住请假
     *
     * @param ids 需要删除的居住请假主键
     * @return 结果
     */
    @Override
    public int deleteLiveLeaveRecordsByIds(String[] ids) {
        return liveLeaveRecordsMapper.deleteLiveLeaveRecordsByIds(ids);
    }

    /**
     * 删除居住请假信息
     *
     * @param id 居住请假主键
     * @return 结果
     */
    @Override
    public int deleteLiveLeaveRecordsById(String id) {
        return liveLeaveRecordsMapper.deleteLiveLeaveRecordsById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int audit(LiveLeaveRecords params) {
        String state = "";
        // 审核通过
        if ("1".equals(params.getAuditState())) {
            // 更改老人基础信息状态为请假
            ElderlyPeopleInfo elderlyPeopleInfo = elderlyPeopleInfoMapper.selectElderlyPeopleInfoById(params.getUserId());
            elderlyPeopleInfo.setStatus("2"); // 请假
            if (elderlyPeopleInfoMapper.updateElderlyPeopleInfo(elderlyPeopleInfo) < 1) {
                throw new ServiceException("异常请联系管理员排查");
            }

            // 更改老人居住基础信息状态为请假
            LiveInfoRes liveInfoRes = liveBaseInfoMapper.selectLiveBaseInfoById(params.getLiveId());
            liveInfoRes.setState("2"); // 请假
            if (liveBaseInfoMapper.updateLiveInfoResState(liveInfoRes) < 1) {
                throw new ServiceException("异常请联系管理员排查");
            }

            state = "0";
        }

        // 更新请假记录
        LiveLeaveRecords liveLeaveRecords = new LiveLeaveRecords();
        liveLeaveRecords.setId(params.getId());
        liveLeaveRecords.setAuditState(params.getAuditState());
        liveLeaveRecords.setRejectReason(params.getRejectReason());
        liveLeaveRecords.setState(state);
        liveLeaveRecordsMapper.updateLiveLeaveRecords(liveLeaveRecords);

        return 1;
    }

    @Override
    public int logisticsCancelLeave(LiveLeaveRecords params) {
        // 更新请假记录
        LiveLeaveRecords liveLeaveRecords = new LiveLeaveRecords();
        liveLeaveRecords.setId(params.getId());
        liveLeaveRecords.setState("2"); // 已销假，后勤已确认
        liveLeaveRecords.setAvoidMealDays(params.getAvoidMealDays());
        liveLeaveRecordsMapper.updateLiveLeaveRecords(liveLeaveRecords);

        return 1;
    }

    @Override
    public int applyLeave(LiveLeaveRecords liveLeaveRecords) {
        // 获取当前老人信息
        ElderlyPeopleInfo elderlyPeopleInfo = elderlyPeopleInfoService.selectElderlyPeopleInfoById(liveLeaveRecords.getUserId());
        String status = elderlyPeopleInfo.getStatus();
        if (status == null || !"0".equals(status)) {
            throw new ServiceException("当前老人未入住，无法进行操作");
        }

        // 插入记录
        liveLeaveRecords.setAuditState("0"); // 审核中
        liveLeaveRecords.setCreateTime(DateUtils.getNowDate());
        liveLeaveRecordsMapper.insertLiveLeaveRecords(liveLeaveRecords);

        return 1;
    }
}
