package com.ruoyi.custom.admin.frontPage.controller;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.custom.admin.frontPage.service.IFrontPageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/frontPage")
@Api(value = "系统首页Controller", tags = {"系统首页"})
public class FrontPageController {

    @Autowired
    private IFrontPageService frontPageService;

    @GetMapping("/getUserNumberList")
    @ApiOperation(value = "老人信息")
    public AjaxResult getUserNumberList() {
        JSONObject jsonObject = frontPageService.getUserNumberList();
        return AjaxResult.success().put("data", jsonObject);
    }

    @GetMapping("/getRoomBedNum")
    @ApiOperation(value = "床位信息")
    public AjaxResult getRoomBedNum() {
        JSONObject jsonObject = frontPageService.getRoomBedNum();
        return AjaxResult.success().put("data", jsonObject);
    }

    @GetMapping("/getArrearsNum")
    @ApiOperation(value = "欠费名单")
    public AjaxResult getArrearsNum() {
        JSONObject jsonObject = frontPageService.getArrearsNum();
        return AjaxResult.success().put("data", jsonObject);
    }


}
