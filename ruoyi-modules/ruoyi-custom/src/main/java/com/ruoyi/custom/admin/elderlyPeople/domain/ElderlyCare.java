package com.ruoyi.custom.admin.elderlyPeople.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 长者入住关怀对象 t_elderly_care
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
@ApiModel(value = "长者入住关怀")
public class ElderlyCare extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 老人id，关联t_elderly_people_info
     */
    @ApiModelProperty(value = "老人id，关联t_elderly_people_info")
    private String elderId;

    /**
     * 标题
     */
    @Excel(name = "标题", sort = 2)
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 内容
     */
    @Excel(name = "内容", sort = 4)
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 图片地址，多个逗号隔开
     */
    @ApiModelProperty(value = "图片地址，多个逗号隔开")
    @Excel(name = "图片地址", sort = 5, width = 50, height = 20, isWrapText = true)
    private String imageUrls;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "时间", sort = 3 ,width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "日期")
    private Date careDate;

    /**
     * 老人姓名（非数据库字段）
     */
    @ApiModelProperty(value = "老人姓名")
    @Excel(name = "老人姓名",sort = 1)
    private String elderName;

}
