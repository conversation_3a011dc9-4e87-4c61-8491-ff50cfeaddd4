package com.ruoyi.custom.admin.marketing.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.liveManage.domain.req.LiveQueryVo;
import com.ruoyi.custom.admin.liveManage.domain.res.LiveInfoRes;
import com.ruoyi.custom.admin.liveManage.service.ILiveBaseInfoService;
import com.ruoyi.custom.admin.marketing.domain.PaymentRecord;
import com.ruoyi.custom.admin.marketing.resp.ElderlyPeopleInfo2;
import com.ruoyi.custom.admin.marketing.resp.ElderlyPeopleInfoExport;
import com.ruoyi.custom.admin.marketing.resp.LiveInfo2Res;
import com.ruoyi.custom.admin.marketing.service.IPaymentRecordService;
import com.ruoyi.custom.admin.marketing.service.MarketingReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 营销报表Controller
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@RestController
@RequestMapping("/marketingReport")
@Api(value = "营销报表", tags = "营销报表")
public class MarketingReportController extends BaseController {

    @Autowired
    private MarketingReportService marketingReportService;

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    @Autowired
    private ILiveBaseInfoService liveBaseInfoService;

    @Autowired
    private IPaymentRecordService paymentRecordService;

    /**
     * 查询近半年每月咨询量、签约量、转换率（签单量/咨询量）
     */
    // @RequiresPermissions("custom:info:list")
    @ApiOperation(value = "查询近半年每月咨询量、签约量、转换率（签单量/咨询量）")
    @GetMapping("/conversionRate")
    public AjaxResult conversionRate() {
        return AjaxResult.success(marketingReportService.conversionRate());
    }

    /**
     * 近半个月回访量统计
     */
    @ApiOperation(value = "近半个月回访量统计")
    @GetMapping("/followUpCount")
    public AjaxResult followUpCount() {
        return AjaxResult.success(marketingReportService.followUpCount());
    }

    /**
     * 长者-列表
     */
    @GetMapping("/elderly/list")
    @ApiOperation(value = "查询长者列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "name", value = "长者姓名", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "phone", value = "长者电话", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "contractNumber", value = "合同编号", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "params[beginAge]", value = "开始年龄", required = false, dataTypeClass = Integer.class),
            @ApiImplicitParam(paramType = "query", name = "params[endAge]", value = "结束年龄", required = false, dataTypeClass = Integer.class),
            @ApiImplicitParam(paramType = "query", name = "contractStateStr", value = "签约状态", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "status", value = "入住状态", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "idCardNum", value = "身份证号", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "careLevel", value = "护理级别", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "marketer", value = "养老顾问", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo list(@ApiIgnore ElderlyPeopleInfo2 elderlyPeopleInfo) {
        startPage();
        List<ElderlyPeopleInfo2> list = elderlyPeopleInfoService.selectElderlyPeopleInfoList2(elderlyPeopleInfo);
        return getDataTable(list);
    }

    /**
     * 导出老人基础信息列表
     */
    @Log(platform = "1", title = "老人基础信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出老人基础信息列表")
    @PostMapping("/elderly/export")
    public void export(HttpServletResponse response, ElderlyPeopleInfo2 elderlyPeopleInfo) {
        List<ElderlyPeopleInfo2> list = elderlyPeopleInfoService.selectElderlyPeopleInfoList2(elderlyPeopleInfo);
        List<ElderlyPeopleInfoExport> targetList = BeanUtil.copyToList(list, ElderlyPeopleInfoExport.class);
        ExcelUtil<ElderlyPeopleInfoExport> util = new ExcelUtil<ElderlyPeopleInfoExport>(ElderlyPeopleInfoExport.class);
        util.exportExcel(response, targetList, "老人基础信息数据");
    }
}

