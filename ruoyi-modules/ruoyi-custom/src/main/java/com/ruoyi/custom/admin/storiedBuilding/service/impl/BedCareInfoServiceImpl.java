package com.ruoyi.custom.admin.storiedBuilding.service.impl;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.custom.admin.storiedBuilding.domain.BedBaseInfo;
import com.ruoyi.custom.admin.storiedBuilding.domain.BedCareInfo;
import com.ruoyi.custom.admin.storiedBuilding.mapper.BedCareInfoMapper;
import com.ruoyi.custom.admin.storiedBuilding.service.IBedBaseInfoService;
import com.ruoyi.custom.admin.storiedBuilding.service.IBedCareInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 床位和护工关联信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@Service
public class BedCareInfoServiceImpl implements IBedCareInfoService {
    @Autowired
    private BedCareInfoMapper bedCareInfoMapper;

    @Autowired
    private IBedBaseInfoService bedBaseInfoService;

    /**
     * 73      * 找出arr2中不在arr1中的字符串
     * 74      * @param arr1  key
     * 75      * @param arr2
     * 76      * @return
     * 77
     */
    public static String[] notInArray(String[] arr1, String[] arr2) {
        String[] res = null;
        if (arr1 != arr2 && arr1.length > 0 && arr2.length > 0) {
            Map<String, Object> map = new HashMap<String, Object>();
            List<String> list = new ArrayList<String>();
            for (int i = 0; i < arr1.length; i++) {
                map.put(arr1[i], i);
            }
            for (int i = 0; i < arr2.length; i++) {
                if (!map.containsKey(arr2[i])) {
                    list.add(arr2[i]);
                }
            }
            if (list.size() > 0) {
                res = new String[list.size()];
                list.toArray(res);
                return res;
            }
        }
        return res;
    }

    /**
     * 查询床位和护工关联信息
     *
     * @param id 床位和护工关联信息主键
     * @return 床位和护工关联信息
     */
    @Override
    public BedCareInfo selectBedCareInfoById(Long id) {
        return bedCareInfoMapper.selectBedCareInfoById(id);
    }

    /**
     * 查询床位和护工关联信息列表
     *
     * @param bedCareInfo 床位和护工关联信息
     * @return 床位和护工关联信息
     */
    @Override
    public List<BedCareInfo> selectBedCareInfoList(BedCareInfo bedCareInfo) {
        return bedCareInfoMapper.selectBedCareInfoList(bedCareInfo);
    }

    /**
     * 新增床位和护工关联信息
     *
     * @param bedCareInfo 床位和护工关联信息
     * @return 结果
     */
    @Override
    public int insertBedCareInfo(BedCareInfo bedCareInfo) {
        bedCareInfo.setCreateTime(DateUtils.getNowDate());
        bedCareInfo.setCareState("0");
        return bedCareInfoMapper.insertBedCareInfo(bedCareInfo);
    }

    /**
     * 修改床位和护工关联信息
     *
     * @param bedCareInfo 床位和护工关联信息
     * @return 结果
     */
    @Override
    public int updateBedCareInfo(BedCareInfo bedCareInfo) {
        bedCareInfo.setUpdateTime(DateUtils.getNowDate());
        return bedCareInfoMapper.updateBedCareInfo(bedCareInfo);
    }

    /**
     * 批量删除床位和护工关联信息
     *
     * @param ids 需要删除的床位和护工关联信息主键
     * @return 结果
     */
    @Override
    public int deleteBedCareInfoByIds(String[] ids) {
        return bedCareInfoMapper.deleteBedCareInfoByIds(ids);
    }

    /**
     * 删除床位和护工关联信息信息
     *
     * @param id 床位和护工关联信息主键
     * @return 结果
     */
    @Override
    public int deleteBedCareInfoById(String id) {
        return bedCareInfoMapper.deleteBedCareInfoById(id);
    }

    /**
     * 根据护工id获取床位
     *
     * @param careWorkerId
     * @return
     */
    @Override
    public String getGroupCareBed(String careWorkerId) {
        return bedCareInfoMapper.getGroupCareBed(careWorkerId);
    }

    /**
     * 树状图进行绑定床位
     *
     * @param beds
     * @param careWorkerId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveBedCareInfo(String[] beds, String careWorkerId) {
        // 处理groupCareBed为null或空的情况
        String groupCareBed = bedCareInfoMapper.getGroupCareBed(careWorkerId);
        if (groupCareBed == null) {
            return handleNoExistingBeds(beds, careWorkerId);
        }

        // 已绑定的床位数组
        String[] existingBeds = groupCareBed.split(",");

        // 选中的床位为空时，解绑所有床位
        if (beds == null || beds.length == 0) {
            return unbindBeds(existingBeds, careWorkerId);
        }

        // 选中床位排序后与已有床位排序比较
        Arrays.sort(existingBeds);
        Arrays.sort(beds);

        // 如果两个数组相等，表示没有变化
        if (Arrays.equals(existingBeds, beds)) {
            return 1;
        }

        // 获取需要解绑的旧床位和需要绑定的新床位
        String[] oldBeds = notInArray(beds, existingBeds);
        String[] newBeds = notInArray(existingBeds, beds);

        // 解绑旧床位
        int result = 0;
        if (oldBeds != null && oldBeds.length > 0) {
            result = unbindBeds(oldBeds, careWorkerId);
        }

        // 绑定新床位
        if (newBeds != null && newBeds.length > 0) {
            result = bindBeds(newBeds, careWorkerId);
        }

        return result;
    }

    /**
     * 当没有已绑定床位时，处理绑定新床位的逻辑
     */
    private int handleNoExistingBeds(String[] beds, String careWorkerId) {
        if (beds == null || beds.length == 0) {
            return 1;
        }

        // 检查床位是否已被绑定
        int count = bedCareInfoMapper.persentCheck(beds);
        if (count > 0) {
            throw new ServiceException("该床位已被绑定，请重新选择");
        }

        // 批量绑定床位
        bedCareInfoMapper.batchBed(beds, careWorkerId);

        // 更新床位基础信息
        updateBedBaseInfo(beds, careWorkerId, true);

        return 1;
    }

    /**
     * 解绑床位
     */
    private int unbindBeds(String[] beds, String careWorkerId) {
        // 解绑床位
        bedCareInfoMapper.updateBed(beds, careWorkerId);

        // 更新床位基础信息
        updateBedBaseInfo(beds, careWorkerId, false);

        return 1;
    }

    /**
     * 绑定新床位，并检查是否已被绑定
     */
    private int bindBeds(String[] beds, String careWorkerId) {
        // 检查床位是否已被绑定
        int count = bedCareInfoMapper.persentCheck(beds);
        if (count > 0) {
            throw new ServiceException("该床位已被绑定，请重新选择");
        }

        // 批量绑定床位
        bedCareInfoMapper.batchBed(beds, careWorkerId);

        // 更新床位基础信息
        updateBedBaseInfo(beds, careWorkerId, true);

        return 1;
    }

    /**
     * 更新床位基础信息
     */
    private void updateBedBaseInfo(String[] beds, String careWorkerId, boolean isBind) {
        Arrays.stream(beds).map(Long::valueOf).forEach(bedId -> {
            BedBaseInfo bedBaseInfoParma = new BedBaseInfo();
            bedBaseInfoParma.setCareIndex(isBind ? careWorkerId : "");
            bedBaseInfoParma.setId(bedId);
            bedBaseInfoService.updateBedBaseInfo(bedBaseInfoParma);
        });
    }

}
