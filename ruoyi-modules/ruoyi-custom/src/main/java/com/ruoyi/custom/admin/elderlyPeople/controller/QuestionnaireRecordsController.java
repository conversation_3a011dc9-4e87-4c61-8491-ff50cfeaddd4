package com.ruoyi.custom.admin.elderlyPeople.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.elderlyPeople.domain.QuestionnaireRecords;
import com.ruoyi.custom.admin.elderlyPeople.service.IQuestionnaireRecordsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 调查问卷历史Controller
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@RestController
@RequestMapping("/questionnaireRecords")
@ApiIgnore
public class QuestionnaireRecordsController extends BaseController {
    @Autowired
    private IQuestionnaireRecordsService questionnaireRecordsService;

    /**
     * 查询调查问卷历史列表
     */
    //@RequiresPermissions("elderlyPeople:questionnaireRecords:list")
    @GetMapping("/list")
    public TableDataInfo list(QuestionnaireRecords questionnaireRecords) {
        startPage();
        List<QuestionnaireRecords> list = questionnaireRecordsService.selectQuestionnaireRecordsList(questionnaireRecords);
        return getDataTable(list);
    }

    /**
     * 导出调查问卷历史列表
     */
    //@RequiresPermissions("elderlyPeople:questionnaireRecords:export")
    @Log(platform = "1", title = "调查问卷历史", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QuestionnaireRecords questionnaireRecords) {
        List<QuestionnaireRecords> list = questionnaireRecordsService.selectQuestionnaireRecordsList(questionnaireRecords);
        ExcelUtil<QuestionnaireRecords> util = new ExcelUtil<QuestionnaireRecords>(QuestionnaireRecords.class);
        util.exportExcel(response, list, "调查问卷历史数据");
    }

    /**
     * 获取调查问卷历史详细信息
     */
    //@RequiresPermissions("elderlyPeople:questionnaireRecords:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(questionnaireRecordsService.selectQuestionnaireRecordsById(id));
    }

    /**
     * 新增调查问卷历史
     */
    //@RequiresPermissions("elderlyPeople:questionnaireRecords:add")
    @Log(platform = "1", title = "调查问卷历史", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QuestionnaireRecords questionnaireRecords) {
        return toAjax(questionnaireRecordsService.insertQuestionnaireRecords(questionnaireRecords));
    }

    /**
     * 修改调查问卷历史
     */
    //@RequiresPermissions("elderlyPeople:questionnaireRecords:edit")
    @Log(platform = "1", title = "调查问卷历史", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QuestionnaireRecords questionnaireRecords) {
        return toAjax(questionnaireRecordsService.updateQuestionnaireRecords(questionnaireRecords));
    }

    /**
     * 删除调查问卷历史
     */
    //@RequiresPermissions("elderlyPeople:questionnaireRecords:remove")
    @Log(platform = "1", title = "调查问卷历史", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(questionnaireRecordsService.deleteQuestionnaireRecordsByIds(ids));
    }
}
