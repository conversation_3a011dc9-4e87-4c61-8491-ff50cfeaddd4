package com.ruoyi.custom.admin.serviceOrder.vo;

import com.ruoyi.custom.admin.serviceWorkOrder.domain.OrderServiceWork;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description home_service_work_order
 * @date 2022-07-20
 */
@Data
@ApiModel("服务订单详情")
public class ServiceOrderBaseInfoAdminVo extends OrderServiceWork {

    @ApiModelProperty("实付金额")
    private BigDecimal totalFee;

    @ApiModelProperty("单价")
    private BigDecimal price;

    /**
     * 下单方式 1:用户 2：商家
     */
    @ApiModelProperty("下单方式Label")
    @Setter(AccessLevel.NONE)
    @Getter(AccessLevel.NONE)
    private String placeOrderWayLabel;

    @ApiModelProperty("付款方式 1:余额付款 2：微信在线支付")
    private Integer payWay;

    /**
     * 支付状态 0：支付失败 1：支付成功
     */
    @ApiModelProperty("0：已关闭 1：待支付 2：已付款 3：已接单 4：已完成  5:退款")
    private Integer status;

    @ApiModelProperty("付款方式Label")
    @Setter(AccessLevel.NONE)
    @Getter(AccessLevel.NONE)
    private String payWayLabel;

    public String getPlaceOrderWayLabel() {
        if (null != super.getPlaceOrderWay()) {
            return super.getPlaceOrderWay() == 1 ? "用户" : "自动";
        }
        return "";
    }

    public void setPlaceOrderWayLabel(String placeOrderWayLabel) {
        this.placeOrderWayLabel = placeOrderWayLabel;
    }

    public String getPayWayLabel() {
        if (null != this.getPayWay()) {
            return this.payWay == 1 ? "余额付款" : "微信在线支付";
        }
        return payWayLabel;
    }

    public void setPayWayLabel(String payWayLabel) {
        this.payWayLabel = payWayLabel;
    }
}
