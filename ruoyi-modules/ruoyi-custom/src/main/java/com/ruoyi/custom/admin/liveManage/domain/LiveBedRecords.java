package com.ruoyi.custom.admin.liveManage.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 居住记录对象 t_live_bed_records
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@Data
public class LiveBedRecords extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 关联主表id
     */
    @Excel(name = "关联主表id")
    private String liveId;

    /**
     * 房间id
     */
    @Excel(name = "房间id")
    @ApiModelProperty(value = "房间id", name = "roomId", required = true, example = "196")
    private String roomId;

    /**
     * 房间版本
     */
    @Excel(name = "房间版本")
    @ApiModelProperty(value = "房间版本", name = "roomVersion", required = true, example = "1")
    private String roomVersion;

    /**
     * 入住床位
     */
    @Excel(name = "入住床位")
    @ApiModelProperty(value = "入住床位id", name = "bedId", required = true, example = "1")
    private String bedId;

    /**
     * 入住日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入住日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "入住日期", name = "beginDate", required = true, example = "2022-04-01")
    private Date beginDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 计费日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计费日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "计费日期", name = "billingDate", required = true, example = "2022-04-01")
    private Date billingDate;

    /**
     * 居住状态（在住，变更，退住）
     */
    @Excel(name = "居住状态", readConverterExp = "在=住，变更，退住")
    private String liveState;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

}
