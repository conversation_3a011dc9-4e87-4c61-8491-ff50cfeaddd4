package com.ruoyi.custom.admin.pharmacyWarehouse.service;

import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryIn;

import java.util.List;


/**
 * 药品入库明细Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IPharmacyInventoryInService {
    /**
     * 查询药品入库明细
     *
     * @param id 药品入库明细主键
     * @return 药品入库明细
     */
    public PharmacyInventoryIn selectPharmacyInventoryInById(Long id);

    /**
     * 查询药品入库明细列表
     *
     * @param pharmacyInventoryIn 药品入库明细
     * @return 药品入库明细集合
     */
    public List<PharmacyInventoryIn> selectPharmacyInventoryInList(PharmacyInventoryIn pharmacyInventoryIn);

    /**
     * 新增药品入库明细
     *
     * @param pharmacyInventoryIn 药品入库明细
     * @return 结果
     */
    public int insertPharmacyInventoryIn(List<PharmacyInventoryIn> pharmacyInventoryIn);

    /**
     * 修改药品入库明细
     *
     * @param pharmacyInventoryIn 药品入库明细
     * @return 结果
     */
    public int updatePharmacyInventoryIn(PharmacyInventoryIn pharmacyInventoryIn);

    /**
     * 批量删除药品入库明细
     *
     * @param ids 需要删除的药品入库明细主键集合
     * @return 结果
     */
    public int deletePharmacyInventoryInByIds(Long[] ids);

    /**
     * 删除药品入库明细信息
     *
     * @param id 药品入库明细主键
     * @return 结果
     */
    public int deletePharmacyInventoryInById(Long id);

    /**
     * 查询药品入库明细列表2
     * @param pharmacyInventoryIn
     * @return
     */
    List<PharmacyInventoryIn> selectPharmacyInventoryInList2(PharmacyInventoryIn pharmacyInventoryIn);
}

