package com.ruoyi.custom.admin.marketing.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "PaymentRecord", description = "缴费确认单")
public class PaymentRecord extends BaseEntity {

    /**
     * 主鍵id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 老人id
     */
    @ApiModelProperty(value = "老人id")
    private String elderlyId;

    /**
     * 老人姓名
     */
    @ApiModelProperty(value = "老人姓名")
    private String elderlyName;

    /**
     * 老人电话
     */
    @ApiModelProperty(value = "老人电话")
    private String elderlyPhone;

    /**
     * 合同开始日期
     */
    @ApiModelProperty(value = "合同开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractStartDate;

    /**
     * 合同结束日期
     */
    @ApiModelProperty(value = "合同结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndDate;

    /**
     * 合同周期（月数）
     */
    @ApiModelProperty(value = "合同周期（月数）")
    private Integer contractCycle;

    /**
     * 护理级别
     */
    @ApiModelProperty(value = "护理级别")
    private String careLevel;

    /**
     * 床位号
     */
    @ApiModelProperty(value = "房间号")
    private String bedName;

    /**
     * 请假时长
     */
    @ApiModelProperty(value = "请假时长（天）")
    private Integer leaveDuration;

    /**
     * 请假日期，格式如：2024/11/11-2024/11/12,2024/12/11-025/01/12
     */
    @ApiModelProperty(value = "请假日期，格式如：2024/11/11-2024/11/12,2024/12/11-025/01/12")
    private String leaveDates;

    /**
     * 需缴费用
     */
    @ApiModelProperty(value = "需缴费用")
    private BigDecimal totalCost;

    /**
     * 总计实缴金额
     */
    @ApiModelProperty(value = "总计实缴金额")
    private BigDecimal totalPaidCost;

    /**
     * 实缴详情
     */
    @ApiModelProperty(value = "实缴详情")
    private List<PaidDetail> paidDetails;

    /**
     * 本次账户增加金额
     */
    @ApiModelProperty(value = "本次账户变动金额")
    private BigDecimal accountAddCost;

    /**
     * 退费合计
     */
    @ApiModelProperty(value = "退费合计")
    private BigDecimal refundCost;

    /**
     * 缴费合计
     */
    @ApiModelProperty(value = "缴纳合计")
    private BigDecimal offerCost;

    /**
     * 缴费日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "缴费日期")
    private Date paymentTime;

    /**
     * 费用类型，字典：t_payment_record_fee_type；1：续费，2：结算
     */
    @ApiModelProperty(value = "费用类型")
    private String feeType;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 离院日期
     */
    @ApiModelProperty(value = "离院日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dischargeDate;

    /**
     * 详情
     */
    @ApiModelProperty(value = "详情")
    private List<Detail> details;

    @Data
    @ApiModel(value = "Detail", description = "费用明细")
    public static class Detail {

        /**
         * 类型
         */
        @ApiModelProperty(value = "类型")
        private String type;

        /**
         * 类型名称
         */
        @ApiModelProperty(value = "类型名称")
        private String typeName;

        /**
         * 收费标准
         */
        @ApiModelProperty(value = "收费标准")
        private BigDecimal feeStandard;

        /**
         * 请假时长（月、天）
         */
        @ApiModelProperty(value = "请假时长")
        private PrepaidPeriod leavePeriod;

        /**
         * 退费金额
         */
        @ApiModelProperty(value = "退费金额")
        private BigDecimal refundAmount;

        /**
         * 预付周期（月、天、折扣）
         */
        @ApiModelProperty(value = "预付周期")
        private PrepaidPeriod prepaidPeriod;

        /**
         * 已缴纳金额
         */
        @ApiModelProperty(value = "已缴纳金额")
        private BigDecimal paidAmount;

        /**
         * 缴费金额
         */
        @ApiModelProperty(value = "缴费金额")
        private BigDecimal paymentAmount;

        /**
         * 备注
         */
        @ApiModelProperty(value = "备注")
        private String remarks;

        /**
         * 增值服务专用，本次缴费的增值服务账单ID，多个逗号隔开
         */
        @ApiModelProperty(value = "增值服务专用，本次缴费的增值服务账单ID，多个逗号隔开")
        private String billIds;

        /**
         * 空调费专用， 2025/6/1~2025/9/15
         */
        @ApiModelProperty(value = "空调费专用， 2025/6/1~2025/9/15")
        String dateRange;

        /**
         * 缴费变更单id, 关联表：t_payment_change_record
         */
        @ApiModelProperty(value = "缴费变更单id, 关联表：t_payment_change_record")
        String changePaymentId;

        /**
         * 预计到期日期
         */
        @ApiModelProperty(value = "预计到期日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date estimatedExpiryDate;
    }

    @Data
    @ApiModel(value = "PrepaidPeriod", description = "预付周期")
    public static class PrepaidPeriod {

        @ApiModelProperty(value = "月数")
        private Integer full;

        @ApiModelProperty(value = "天数")
        private Integer partial;

        @ApiModelProperty(value = "折扣比例，如：0.6")
        private BigDecimal discount;

        @ApiModelProperty(value = "一个月的天数")
        private Integer daysInMonth;

    }

    @Data
    @ApiModel(value = "paidDetail", description = "实缴金额 + 缴费方式")
    public static class PaidDetail {

        /**
         * 实缴金额
         */
        @ApiModelProperty(value = "实缴金额")
        private BigDecimal paidCost;

        /**
         * 缴费方式，字典：t_payment_record_payment_method
         */
        @ApiModelProperty(value = "缴费方式")
        private String paymentMethod;

    }

}
