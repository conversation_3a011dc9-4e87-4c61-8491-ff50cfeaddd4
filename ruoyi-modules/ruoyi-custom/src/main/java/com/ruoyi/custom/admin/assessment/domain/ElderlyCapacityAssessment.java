package com.ruoyi.custom.admin.assessment.domain;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 老年人能力评估对象 t_elderly_capacity_assessment
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@ApiModel(value = "老年人能力评估对象")
public class ElderlyCapacityAssessment extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 编号，格式：pgjh-*************
     */
    @ApiModelProperty(value = "编号，自定义")
    private String serialNumber;

    /**
     * 评估对象基本信息
     */
    @ApiModelProperty(value = "评估对象基本信息")
    private JSONObject assessmentObjBasicsInfo;

    /**
     * 信息提供者及联系人信息表
     */
    @ApiModelProperty(value = "信息提供者及联系人信息表")
    private JSONObject msgSupplierContactInfo;

    /**
     * 疾病诊断和用药情况
     */
    @ApiModelProperty(value = "疾病诊断和用药情况")
    private JSONObject diseaseDiagnosisDrugUsage;

    /**
     * 健康相关问题
     */
    @ApiModelProperty(value = "健康相关问题")
    private JSONObject healthRelatedIssues;

    /**
     * 生理及身体评估
     */
    @ApiModelProperty(value = "生理及身体评估")
    private JSONObject physiologyBodyAssessment;

    /**
     * 老年人能力评估表
     */
    @ApiModelProperty(value = "老年人能力评估表")
    private JSONObject oldPeopleAbilityAssessment;

    /**
     * 基础运动能力评估表
     */
    @ApiModelProperty(value = "基础运动能力评估表")
    private JSONObject basicMotorAbilityAssessment;

    /**
     * 精神状态表
     */
    @ApiModelProperty(value = "精神状态表")
    private JSONObject mentalState;

    /**
     * 感知觉与社会参与评估表
     */
    @ApiModelProperty(value = "感知觉与社会参与评估表")
    private JSONObject perceptionSocialParticipation;

    /**
     * 健康问题
     */
    @ApiModelProperty(value = "健康问题")
    private JSONArray healthProblems;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /** 评估id */
    @ApiModelProperty(value = "评估id")
    private Long assessmentId;

    /** 视频url */
    @ApiModelProperty(value = "视频url")
    private JSONArray fileVideoList;

    /** 图片url */
    @ApiModelProperty(value = "图片url")
    private JSONArray fileImgList;
}
