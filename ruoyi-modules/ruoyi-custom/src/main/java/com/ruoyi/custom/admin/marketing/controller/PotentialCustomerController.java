package com.ruoyi.custom.admin.marketing.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.ruoyi.common.core.enums.UserFilterType;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.assessment.domain.AssessmentPlan;
import com.ruoyi.custom.admin.assessment.service.IAssessmentPlanService;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.fee.service.IFeeComboInfoService;
import com.ruoyi.custom.admin.liveManage.domain.req.LiveInfoSaveReqVO;
import com.ruoyi.custom.admin.liveManage.service.ILiveBaseInfoService;
import com.ruoyi.custom.admin.marketing.domain.MarketingCustomerInfo;
import com.ruoyi.custom.admin.marketing.domain.MarketingFollowUp;
import com.ruoyi.custom.admin.marketing.service.MarketingCustomerInfoService;
import com.ruoyi.custom.admin.marketing.service.MarketingFollowUpService;
import com.ruoyi.custom.admin.storiedBuilding.service.IRoomTypeBaseInfoService;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 意向客户信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@RestController
@RequestMapping("/potentialCustomer")
@Api(value = "意向客户", tags = "意向客户")
public class PotentialCustomerController extends BaseController {
    @Autowired
    private MarketingCustomerInfoService marketingCustomerInfoService;

    @Autowired
    private MarketingFollowUpService marketingFollowUpService;

    @Autowired
    private IAssessmentPlanService assessmentPlanService;

    @Autowired
    private ILiveBaseInfoService liveBaseInfoService;

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    @Autowired
    private IFeeComboInfoService feeComboInfoService;

    @Autowired
    private IRoomTypeBaseInfoService roomTypeBaseInfoService;

    /**
     * 查询意向客户信息列表
     */
    // @RequiresPermissions("custom:info:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取意向客户信息")
    public TableDataInfo list(MarketingCustomerInfo marketingCustomerInfo) {
        // 运营人员只能看自己的客户
        SysUser user = SecurityUtils.getLoginUser().getSysUser();
        Optional.ofNullable(user.getRoles())
                .orElseGet(ArrayList::new)
                .stream()
                .filter(Objects::nonNull)
                .map(SysRole::getRoleKey)
                .filter("regular_operator"::equals)
                .findFirst()
                .ifPresent(roleKey -> marketingCustomerInfo.setMarketerId(user.getId()));


        startPage();
        marketingCustomerInfo.setCustomerType("1");
        marketingCustomerInfo.setParams(MapUtil.of("userFilterType", UserFilterType.UN_SIGN_AND_UN_LIVE.getValue())); // 意向客户、未签约、未入住
        return getDataTable(marketingCustomerInfoService.selectMarketingCustomerInfoList(marketingCustomerInfo));
    }

    /**
     * 查询意向客户、未签约信息列表
     */
    // @RequiresPermissions("custom:info:list")
    @GetMapping("/unSignList")
    @ApiOperation(value = "查询意向客户、未签约信息列表")
    public TableDataInfo unSignList(MarketingCustomerInfo marketingCustomerInfo) {
        startPage();
        marketingCustomerInfo.setCustomerType("1");
        marketingCustomerInfo.setParams(MapUtil.of("userFilterType", UserFilterType.UN_SIGN.getValue())); // 意向客户、未签约、未入住
        return getDataTable(marketingCustomerInfoService.selectMarketingCustomerInfoList(marketingCustomerInfo));
    }

    /**
     * 获取所有意向客户信息列表
     */
    @GetMapping("/allList")
    @ApiOperation(value = "获取所有意向客户信息列表")
    public AjaxResult allList() {
        MarketingCustomerInfo marketingCustomerInfo = new MarketingCustomerInfo();
        marketingCustomerInfo.setCustomerType("1");
        return AjaxResult.success(marketingCustomerInfoService.selectMarketingCustomerInfoList(marketingCustomerInfo));
    }

    // /**
    //  * 获取未签约的客户信息列表
    //  */
    // @GetMapping("/unSignedList")
    // @ApiOperation(value = "获取未签约的客户信息列表")
    // public AjaxResult unSignedList() {
    //     MarketingCustomerInfo marketingCustomerInfo = new MarketingCustomerInfo();
    //     marketingCustomerInfo.setParams(MapUtil.of("userFilterType", UserFilterType.NON_REPEATABLE.getValue()));
    //     return AjaxResult.success(marketingCustomerInfoService.selectMarketingCustomerInfoList(marketingCustomerInfo));
    // }

    /**
     * 获取意向客户信息详细信息
     */
    // @RequiresPermissions("custom:info:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取意向客户信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(marketingCustomerInfoService.selectMarketingCustomerInfoById(id));
    }

    // /**
    //  * 新增意向客户信息
    //  */
    // // @RequiresPermissions("custom:info:add")
    // @Log(title = "意向客户信息", businessType = BusinessType.INSERT)
    // @PostMapping
    // @ApiOperation(value = "新增意向客户信息")
    // public AjaxResult add(@RequestBody MarketingCustomerInfo marketingCustomerInfo) {
    //     marketingCustomerInfo.setCustomerType("1");
    //     return toAjax(marketingCustomerInfoService.insertMarketingCustomerInfo(marketingCustomerInfo));
    // }

    /**
     * 修改意向客户信息
     */
    // @RequiresPermissions("custom:info:edit")
    @Log(title = "意向客户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改意向客户信息")
    public AjaxResult edit(@RequestBody MarketingCustomerInfo marketingCustomerInfo) {
        return toAjax(marketingCustomerInfoService.updateMarketingCustomerInfo(marketingCustomerInfo));
    }

    /**
     * 放弃客户
     */
    // @RequiresPermissions("custom:info:remove")
    @Log(title = "意向客户信息", businessType = BusinessType.UPDATE)
    @PutMapping("/giveUp")
    @ApiOperation(value = "放弃客户")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult giveUp(@RequestBody MarketingCustomerInfo marketingCustomerInfo) {
        /**
         * 修改意向客户状态
         */
        MarketingCustomerInfo param = new MarketingCustomerInfo();
        param.setId(marketingCustomerInfo.getId());
        param.setCustomerType("2");
        param.setFailedTime(DateUtil.date());
        int r1 = marketingCustomerInfoService.updateMarketingCustomerInfo(param);
        /**
         * 若有未执行的回访计划，则删除
         */
        marketingFollowUpService.deleteMarketingFollowUpCustomId(marketingCustomerInfo.getId());
        return toAjax(r1);
    }

    /**
     * 批量添加回访计划
     */
    @Log(title = "营销回访", businessType = BusinessType.INSERT)
    @PostMapping("/followUp/batchAdd")
    @ApiOperation(value = "批量添加回访计划")
    public AjaxResult followUpBatchAdd(@RequestBody MarketingFollowUp marketingFollowUp) {
        List<MarketingFollowUp> list = Arrays.stream(marketingFollowUp.getCustomerIds()).map(customerId -> {
            marketingFollowUp.setCustomerId(customerId);
            marketingFollowUp.setFollowUpStatus("0");
            return BeanUtil.toBean(marketingFollowUp, MarketingFollowUp.class);
        }).collect(Collectors.toList());
        return toAjax(marketingFollowUpService.insertBatchMarketingFollowUp(list));
    }

    /**
     * 添加回访计划
     */
    @Log(title = "营销回访", businessType = BusinessType.INSERT)
    @PostMapping("/followUp/add")
    @ApiOperation(value = "添加回访计划")
    public AjaxResult followUpAdd(@RequestBody MarketingFollowUp marketingFollowUp) {
        marketingFollowUp.setFollowUpStatus("0");
        return toAjax(marketingFollowUpService.insertMarketingFollowUp(marketingFollowUp));
    }

    /**
     * 删除营销回访
     */
    // @RequiresPermissions("custom:up:remove")
    @Log(title = "营销回访", businessType = BusinessType.DELETE)
    @DeleteMapping("/followUp/{id}")
    @ApiOperation(value = "删除营销回访")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(marketingFollowUpService.deleteMarketingFollowUpById(id));
    }

    /**
     * 预约处理（新增评估计划）
     */
    @Log(title = "预约处理", businessType = BusinessType.INSERT)
    @PostMapping("/assessmentPlanAdd")
    @ApiOperation(value = "新增评估计划")
    public AjaxResult assessmentPlanAdd(@RequestBody AssessmentPlan assessmentPlan) {
        // 修改“客户评估预约处理状态”
        // MarketingCustomerInfo marketingCustomerInfo = new MarketingCustomerInfo();
        // marketingCustomerInfo.setId(assessmentPlan.getCustomerId());
        // marketingCustomerInfo.setAppointmentStatus("1");
        // marketingCustomerInfoService.updateMarketingCustomerInfo(marketingCustomerInfo);

        // 新增评估计划
        return toAjax(assessmentPlanService.insertAssessmentPlan(assessmentPlan));
    }

    /**
     * 意向客户入住办理
     */
    @ApiOperation(value = "意向客户入住办理")
    //@RequiresPermissions("liveManage:baseinfo:add")
    @Log(platform = "1", title = "意向客户入住办理", businessType = BusinessType.INSERT)
    @PostMapping("checkIn")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult checkIn(@RequestBody LiveInfoSaveReqVO reqVO) {
        Long marketingCustomerId = Long.valueOf(reqVO.getUserId());
        // 查询营销客户信息
        MarketingCustomerInfo marketingCustomerInfo = marketingCustomerInfoService.selectMarketingCustomerInfoById(marketingCustomerId);

        // 修改营销客户“是否直接入住状态”
        MarketingCustomerInfo param = new MarketingCustomerInfo();
        param.setId(marketingCustomerId);
        param.setDirectCheckIn("1");
        marketingCustomerInfoService.updateMarketingCustomerInfo(param);

        // 复制营销老人信息 -》 老人基本信息表
        ElderlyPeopleInfo elderlyPeopleInfo = new ElderlyPeopleInfo();
        elderlyPeopleInfo.setName(marketingCustomerInfo.getElderName());
        elderlyPeopleInfo.setSex(marketingCustomerInfo.getElderGender());
        elderlyPeopleInfo.setIdCardNum(marketingCustomerInfo.getIdCardNumber());
        elderlyPeopleInfo.setPhone(marketingCustomerInfo.getElderPhone());
        elderlyPeopleInfo.setNation(marketingCustomerInfo.getNation());
        elderlyPeopleInfo.setMarriageStatus(marketingCustomerInfo.getMaritalStatus());
        elderlyPeopleInfo.setLivingSituation(marketingCustomerInfo.getResidenceStatus());
        elderlyPeopleInfo.setHomeAddress(marketingCustomerInfo.getHomeAddress());
        elderlyPeopleInfo.setCustomerId(marketingCustomerId);
        elderlyPeopleInfoService.insertElderlyPeopleInfo(elderlyPeopleInfo);

        // 入住
        reqVO.setUserId(elderlyPeopleInfo.getId());
        return toAjax(liveBaseInfoService.add(reqVO));
    }

    /**
     * 获取导入模版
     *
     * @param response
     * @throws IOException
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<MarketingCustomerInfo> util = new ExcelUtil<>(MarketingCustomerInfo.class);
        util.importTemplateExcel(response, "意向客户");
    }

    /**
     * 导入数据
     *
     * @param file
     * @param updateSupport
     * @return AjaxResult
     * @throws Exception
     */
    @PostMapping("/importData")
    public AjaxResult importData(@RequestPart @RequestParam("file") MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<MarketingCustomerInfo> util = new ExcelUtil<>(MarketingCustomerInfo.class);
        List<MarketingCustomerInfo> list = util.importExcel(file.getInputStream());
        list.forEach(item -> item.setCustomerType("1")); // 设置客户类型为意向客户
        String message = marketingCustomerInfoService.importData(list, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导出意向客户列表
     */
    // @RequiresPermissions("custom:plan:export")
    @Log(title = "意向客户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出意向客户列表")
    public void export(HttpServletResponse response, MarketingCustomerInfo param) {
        // 运营人员只能看自己的客户
        SysUser user = SecurityUtils.getLoginUser().getSysUser();
        Optional.ofNullable(user.getRoles())
                .orElseGet(ArrayList::new)
                .stream()
                .filter(Objects::nonNull)
                .map(SysRole::getRoleKey)
                .filter("regular_operator"::equals)
                .findFirst()
                .ifPresent(roleKey -> param.setMarketerId(user.getId()));
        param.setCustomerType("1");
        param.getParams().put("userFilterType",UserFilterType.UN_SIGN_AND_UN_LIVE.getValue());// 未签约、未入住

        List<MarketingCustomerInfo> list = marketingCustomerInfoService.selectMarketingCustomerInfoList(param);
        ExcelUtil<MarketingCustomerInfo> util = new ExcelUtil<MarketingCustomerInfo>(MarketingCustomerInfo.class);
        util.exportExcel(response, list, "预入住客户数据");
    }
}
