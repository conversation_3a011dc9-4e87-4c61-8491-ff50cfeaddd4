package com.ruoyi.custom.admin.fee.service;

import com.ruoyi.custom.admin.fee.domain.FeeComboDTO;
import com.ruoyi.custom.admin.fee.domain.FeeComboInfo;
import com.ruoyi.custom.admin.fee.domain.res.FeeComboVo;

import java.util.List;

/**
 * 套餐费用Service接口
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
public interface IFeeComboInfoService {
    /**
     * 查询套餐费用
     *
     * @param id 套餐费用主键
     * @return 套餐费用
     */
    public FeeComboInfo selectFeeComboInfoById(String id);

    /**
     * 查询套餐费用列表
     *
     * @param feeComboInfo 套餐费用
     * @return 套餐费用集合
     */
    public List<FeeComboInfo> selectFeeComboInfoList(FeeComboInfo feeComboInfo);

    /**
     * 新增套餐费用
     *
     * @param feeComboInfo 套餐费用
     * @return 结果
     */
    public int insertFeeComboInfo(FeeComboVo feeComboVo);

    /**
     * 修改套餐费用
     *
     * @param feeComboInfo 套餐费用
     * @return 结果
     */
    public int updateFeeComboInfo(FeeComboVo feeComboVo);

    /**
     * 批量删除套餐费用
     *
     * @param ids 需要删除的套餐费用主键集合
     * @return 结果
     */
    public int deleteFeeComboInfoByIds(String[] ids);

    /**
     * 删除套餐费用信息
     *
     * @param id 套餐费用主键
     * @return 结果
     */
    public int deleteFeeComboInfoById(String id);

    List<FeeComboDTO> selectComboList(String careLevel, String comboName);

    FeeComboVo selectComboById(String id);
}
