package com.ruoyi.custom.admin.pharmacyWarehouse.service;

import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventoryScrap;

import java.util.List;

/**
 * 药品报废明细Service接口
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
public interface IPharmacyInventoryScrapService {
    /**
     * 查询药品报废明细
     *
     * @param id 药品报废明细主键
     * @return 药品报废明细
     */
    public PharmacyInventoryScrap selectPharmacyInventoryScrapById(Long id);

    /**
     * 查询药品报废明细列表
     *
     * @param pharmacyInventoryScrap 药品报废明细
     * @return 药品报废明细集合
     */
    public List<PharmacyInventoryScrap> selectPharmacyInventoryScrapList(PharmacyInventoryScrap pharmacyInventoryScrap);

    /**
     * 新增药品报废明细
     *
     * @param pharmacyInventoryScrap 药品报废明细
     * @return 结果
     */
    public int insertPharmacyInventoryScrap(List<PharmacyInventoryScrap> pharmacyInventoryScrap);

    /**
     * 修改药品报废明细
     *
     * @param pharmacyInventoryScrap 药品报废明细
     * @return 结果
     */
    // public int updatePharmacyInventoryScrap(PharmacyInventoryScrap pharmacyInventoryScrap);

    /**
     * 批量删除药品报废明细
     *
     * @param ids 需要删除的药品报废明细主键集合
     * @return 结果
     */
    public int deletePharmacyInventoryScrapByIds(Long[] ids);

    /**
     * 删除药品报废明细信息
     *
     * @param id 药品报废明细主键
     * @return 结果
     */
    public int deletePharmacyInventoryScrapById(Long id);

    /**
     * 查询药品报废明细列表2
     * @param pharmacyInventoryScrap
     * @return
     */
    List<PharmacyInventoryScrap> selectPharmacyInventoryScrapList2(PharmacyInventoryScrap pharmacyInventoryScrap);
}

