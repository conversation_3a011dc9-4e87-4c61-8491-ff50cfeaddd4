package com.ruoyi.custom.admin.assessment.mapper;

import java.util.List;
import com.ruoyi.custom.admin.assessment.domain.NursingPlanTemplate;

/**
 * 评估护理计划模版Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-17
 */
public interface NursingPlanTemplateMapper {
    /**
     * 查询评估护理计划模版
     *
     * @param id 评估护理计划模版主键
     * @return 评估护理计划模版
     */
    public NursingPlanTemplate selectNursingPlanTemplateById(String id);

    /**
     * 查询评估护理计划模版列表
     *
     * @param nursingPlanTemplate 评估护理计划模版
     * @return 评估护理计划模版集合
     */
    public List<NursingPlanTemplate> selectNursingPlanTemplateList(NursingPlanTemplate nursingPlanTemplate);

    /**
     * 新增评估护理计划模版
     *
     * @param nursingPlanTemplate 评估护理计划模版
     * @return 结果
     */
    public int insertNursingPlanTemplate(NursingPlanTemplate nursingPlanTemplate);

    /**
     * 修改评估护理计划模版
     *
     * @param nursingPlanTemplate 评估护理计划模版
     * @return 结果
     */
    public int updateNursingPlanTemplate(NursingPlanTemplate nursingPlanTemplate);

    /**
     * 删除评估护理计划模版
     *
     * @param id 评估护理计划模版主键
     * @return 结果
     */
    public int deleteNursingPlanTemplateById(String id);

    /**
     * 批量删除评估护理计划模版
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNursingPlanTemplateByIds(String[] ids);
    
    /**
     * 根据健康问题查询评估护理计划模版
     *
     * @param healthProblem 健康问题
     * @return 评估护理计划模版
     */
    public NursingPlanTemplate selectNursingPlanTemplateByHealthProblem(String healthProblem);
} 