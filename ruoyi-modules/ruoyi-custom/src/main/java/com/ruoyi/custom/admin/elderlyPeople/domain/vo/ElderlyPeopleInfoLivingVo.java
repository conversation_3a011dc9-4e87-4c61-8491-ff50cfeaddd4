package com.ruoyi.custom.admin.elderlyPeople.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "居住老人信息列表")
public class ElderlyPeopleInfoLivingVo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "老人id")
    private String id;

    @ApiModelProperty(value = "老人名称")
    private String userName;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "性别")
    private Integer sex;

    @ApiModelProperty(value = "床位")
    private String bed;

    @ApiModelProperty(value = "服务级别")
    private String serviceLevel;

    @ApiModelProperty(value = "护理套餐名称")
    private String comboName;

    @ApiModelProperty(value = "餐费套餐名称")
    private String mealName;

    @ApiModelProperty(value = "医护人员")
    private String doctor;

    @ApiModelProperty(value = "亲属电话")
    private String familyPhone;

    @ApiModelProperty(value = "护工姓名")
    private String worker;

    @ApiModelProperty(value = "健康状况")
    private String healthStatus;

    @ApiModelProperty(value = "服务注高事质")
    private String serviceHighQuality;

    @ApiModelProperty(value = "入住日期（合同开始日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date liveDate;

    @ApiModelProperty(value = "结束日期（合同结束日期）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expiredDate;

    @ApiModelProperty(value = "照片")
    private String img;

    @ApiModelProperty(value = "签约状态Str")
    private String signStatusStr;
}
