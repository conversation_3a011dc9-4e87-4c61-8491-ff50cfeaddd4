package com.ruoyi.custom.admin.marketing.resp;

import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 缴费明细响应对象
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@ApiModel(value = "缴费明细")
public class PaymentDetailResp {

    /**
     * 缴费金额
     */
    @ApiModelProperty(value = "缴费金额")
    @Excel(name = "缴费金额")
    private BigDecimal paidCost;

    /**
     * 缴费方式
     */
    @ApiModelProperty(value = "缴费方式")
    @Excel(name = "缴费方式")
    private String paymentMethod;
} 