package com.ruoyi.custom.admin.balance.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.balance.domain.SecurityBalanceRecords;
import com.ruoyi.custom.admin.balance.service.ISecurityBalanceRecordsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 保障金账户记录Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Api(value = "保障金账户记录管理", tags = "保障金账户记录管理")
@RestController
@RequestMapping("/securityBalanceRecords")
public class SecurityBalanceRecordsController extends BaseController {
    @Autowired
    private ISecurityBalanceRecordsService securityBalanceRecordsService;

    /**
     * 查询保障金账户记录列表
     */
    // @RequiresPermissions("custom:securityBalanceRecords:list")
    @ApiOperation(value = "查询保障金账户记录列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "elderlyId", value = "老人ID", paramType = "query"),
        @ApiImplicitParam(name = "changedType", value = "变动类型", paramType = "query"),
        @ApiImplicitParam(name = "params.beginTime", value = "开始时间", paramType = "query"),
        @ApiImplicitParam(name = "params.endTime", value = "结束时间", paramType = "query")
    })
    @GetMapping("/list")
    public TableDataInfo list(SecurityBalanceRecords securityBalanceRecords) {
        startPage();
        List<SecurityBalanceRecords> list = securityBalanceRecordsService.selectSecurityBalanceRecordsList(securityBalanceRecords);
        return getDataTable(list);
    }

    /**
     * 导出保障金账户记录列表
     */
    // @RequiresPermissions("custom:securityBalanceRecords:export")
    @ApiOperation(value = "导出保障金账户记录列表")
    @Log(platform = "1", title = "保障金账户记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SecurityBalanceRecords securityBalanceRecords) {
        List<SecurityBalanceRecords> list = securityBalanceRecordsService.selectSecurityBalanceRecordsList(securityBalanceRecords);
        ExcelUtil<SecurityBalanceRecords> util = new ExcelUtil<>(SecurityBalanceRecords.class);
        util.exportExcel(response, list, "保障金账户记录数据");
    }

    /**
     * 获取保障金账户记录详细信息
     */
    // @RequiresPermissions("custom:securityBalanceRecords:query")
    @ApiOperation(value = "获取保障金账户记录详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(securityBalanceRecordsService.selectSecurityBalanceRecordsById(id));
    }

    /**
     * 新增保障金账户记录
     */
    // @RequiresPermissions("custom:securityBalanceRecords:add")
    @ApiOperation(value = "新增保障金账户记录")
    @Log(platform = "1", title = "保障金账户记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SecurityBalanceRecords securityBalanceRecords) {
        return toAjax(securityBalanceRecordsService.insertSecurityBalanceRecords(securityBalanceRecords));
    }

    /**
     * 修改保障金账户记录
     */
    // @RequiresPermissions("custom:securityBalanceRecords:edit")
    @ApiOperation(value = "修改保障金账户记录")
    @Log(platform = "1", title = "保障金账户记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SecurityBalanceRecords securityBalanceRecords) {
        return toAjax(securityBalanceRecordsService.updateSecurityBalanceRecords(securityBalanceRecords));
    }

    /**
     * 删除保障金账户记录
     */
    // @RequiresPermissions("custom:securityBalanceRecords:remove")
    @ApiOperation(value = "删除保障金账户记录")
    @Log(platform = "1", title = "保障金账户记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(securityBalanceRecordsService.deleteSecurityBalanceRecordsByIds(ids));
    }
} 