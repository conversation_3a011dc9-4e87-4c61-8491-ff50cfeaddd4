package com.ruoyi.custom.admin.care.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 护理套餐中的服务项对象 t_care_combo_service_items_base_info
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
@ApiModel(value = "护理套餐中的服务项")
@Data
public class CareComboServiceItemsBaseInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 护理套餐id
     */
    @Excel(name = "护理套餐id")
    @ApiModelProperty(value = "护理套餐id")
    private Long comboId;

    /**
     * 护理项目id
     */
    @Excel(name = "护理项目id")
    @ApiModelProperty(value = "护理项目id")
    private Long careProjectId;

    @ApiModelProperty(value = "护理项目名称")
    private String careProjectName;

    /**
     * 周期
     */
    @Excel(name = "周期")
    @ApiModelProperty(value = "周期(单位：次或天)")
    private String cycle;

    /**
     * 频次
     */
    @Excel(name = "频次")
    @ApiModelProperty(value = "频次")
    private Long frequency;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;


    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

}
