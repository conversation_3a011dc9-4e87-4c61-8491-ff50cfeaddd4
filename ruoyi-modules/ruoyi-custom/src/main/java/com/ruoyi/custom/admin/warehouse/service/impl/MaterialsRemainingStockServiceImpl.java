package com.ruoyi.custom.admin.warehouse.service.impl;

import java.util.List;

import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.admin.warehouse.mapper.MaterialsRemainingStockMapper;
import com.ruoyi.custom.admin.warehouse.domain.MaterialsRemainingStock;
import com.ruoyi.custom.admin.warehouse.service.IMaterialsRemainingStockService;

/**
 * 物资剩余数量Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-09
 */
@Service
public class MaterialsRemainingStockServiceImpl implements IMaterialsRemainingStockService {
    @Autowired
    private MaterialsRemainingStockMapper materialsRemainingStockMapper;

    /**
     * 查询物资剩余数量
     *
     * @param id 物资剩余数量主键
     * @return 物资剩余数量
     */
    @Override
    public MaterialsRemainingStock selectMaterialsRemainingStockById(Long id) {
        return materialsRemainingStockMapper.selectMaterialsRemainingStockById(id);
    }


    /**
     * 根据物资id和仓库获取
     *
     * @param materialId
     * @param warehouseId
     * @return
     */
    @Override
    public MaterialsRemainingStock getByBaseId(Long materialId, Long warehouseId) {
        return materialsRemainingStockMapper.getByBaseId(materialId, warehouseId);
    }

    /**
     * 查询物资剩余数量列表
     *
     * @param materialsRemainingStock 物资剩余数量
     * @return 物资剩余数量
     */
    @Override
    public List<MaterialsRemainingStock> selectMaterialsRemainingStockList(MaterialsRemainingStock materialsRemainingStock) {
        return materialsRemainingStockMapper.selectMaterialsRemainingStockList(materialsRemainingStock);
    }

    @Override
    public int save(MaterialsRemainingStock materialsRemainingStock) {
        if (null == materialsRemainingStock.getId() || materialsRemainingStock.getId() == 0) {
            return insertMaterialsRemainingStock(materialsRemainingStock);
        } else {
            return updateMaterialsRemainingStock(materialsRemainingStock);
        }
    }

    /**
     * 新增物资剩余数量
     *
     * @param materialsRemainingStock 物资剩余数量
     * @return 结果
     */
    @Override
    public int insertMaterialsRemainingStock(MaterialsRemainingStock materialsRemainingStock) {
        materialsRemainingStock.setCreateTime(DateUtils.getNowDate());
        return materialsRemainingStockMapper.insertMaterialsRemainingStock(materialsRemainingStock);
    }

    /**
     * 修改物资剩余数量
     *
     * @param materialsRemainingStock 物资剩余数量
     * @return 结果
     */
    @Override
    public int updateMaterialsRemainingStock(MaterialsRemainingStock materialsRemainingStock) {
        materialsRemainingStock.setUpdateTime(DateUtils.getNowDate());
        return materialsRemainingStockMapper.updateMaterialsRemainingStock(materialsRemainingStock);
    }

    /**
     * 批量删除物资剩余数量
     *
     * @param ids 需要删除的物资剩余数量主键
     * @return 结果
     */
    @Override
    public int deleteMaterialsRemainingStockByIds(Long[] ids) {
        return materialsRemainingStockMapper.deleteMaterialsRemainingStockByIds(ids);
    }

    /**
     * 删除物资剩余数量信息
     *
     * @param id 物资剩余数量主键
     * @return 结果
     */
    @Override
    public int deleteMaterialsRemainingStockById(Long id) {
        return materialsRemainingStockMapper.deleteMaterialsRemainingStockById(id);
    }
}
