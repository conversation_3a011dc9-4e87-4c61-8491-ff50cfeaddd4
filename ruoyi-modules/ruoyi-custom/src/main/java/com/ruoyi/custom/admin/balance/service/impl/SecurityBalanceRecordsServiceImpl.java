package com.ruoyi.custom.admin.balance.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.custom.admin.balance.domain.SecurityBalanceRecords;
import com.ruoyi.custom.admin.balance.mapper.SecurityBalanceRecordsMapper;
import com.ruoyi.custom.admin.balance.service.ISecurityBalanceRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 保障金账户记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
public class SecurityBalanceRecordsServiceImpl implements ISecurityBalanceRecordsService {
    @Autowired
    private SecurityBalanceRecordsMapper securityBalanceRecordsMapper;

    /**
     * 查询保障金账户记录
     *
     * @param id 保障金账户记录主键
     * @return 保障金账户记录
     */
    @Override
    public SecurityBalanceRecords selectSecurityBalanceRecordsById(Integer id) {
        return securityBalanceRecordsMapper.selectSecurityBalanceRecordsById(id);
    }

    /**
     * 查询保障金账户记录列表
     *
     * @param securityBalanceRecords 保障金账户记录
     * @return 保障金账户记录
     */
    @Override
    public List<SecurityBalanceRecords> selectSecurityBalanceRecordsList(SecurityBalanceRecords securityBalanceRecords) {
        return securityBalanceRecordsMapper.selectSecurityBalanceRecordsList(securityBalanceRecords);
    }

    /**
     * 新增保障金账户记录
     *
     * @param securityBalanceRecords 保障金账户记录
     * @return 结果
     */
    @Override
    public int insertSecurityBalanceRecords(SecurityBalanceRecords securityBalanceRecords) {
        securityBalanceRecords.setCreateTime(DateUtils.getNowDate());
        return securityBalanceRecordsMapper.insertSecurityBalanceRecords(securityBalanceRecords);
    }

    /**
     * 修改保障金账户记录
     *
     * @param securityBalanceRecords 保障金账户记录
     * @return 结果
     */
    @Override
    public int updateSecurityBalanceRecords(SecurityBalanceRecords securityBalanceRecords) {
        securityBalanceRecords.setUpdateTime(DateUtils.getNowDate());
        return securityBalanceRecordsMapper.updateSecurityBalanceRecords(securityBalanceRecords);
    }

    /**
     * 批量删除保障金账户记录
     *
     * @param ids 需要删除的保障金账户记录主键
     * @return 结果
     */
    @Override
    public int deleteSecurityBalanceRecordsByIds(Integer[] ids) {
        return securityBalanceRecordsMapper.deleteSecurityBalanceRecordsByIds(ids);
    }

    /**
     * 删除保障金账户记录信息
     *
     * @param id 保障金账户记录主键
     * @return 结果
     */
    @Override
    public int deleteSecurityBalanceRecordsById(Integer id) {
        return securityBalanceRecordsMapper.deleteSecurityBalanceRecordsById(id);
    }
} 