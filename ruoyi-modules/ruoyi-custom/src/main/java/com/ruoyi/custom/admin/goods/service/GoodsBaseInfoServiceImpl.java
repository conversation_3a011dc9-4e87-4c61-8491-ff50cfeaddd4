package com.ruoyi.custom.admin.goods.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.custom.admin.goods.domain.GoodsBaseInfo;
import com.ruoyi.custom.admin.goods.domain.GoodsCategory;
import com.ruoyi.custom.admin.goods.mapper.GoodsBaseInfoMapper;
import com.ruoyi.custom.admin.goods.mapper.GoodsCategoryMapper;
import com.ruoyi.custom.admin.goods.request.UserMobileGoodsAllParam;
import com.ruoyi.custom.admin.goods.vo.GoodsCategoryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName HomeGoodsBaseInfoServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/7/11 16:47
 */
@Service
public class GoodsBaseInfoServiceImpl implements UserGoodsService {

    @Autowired
    private GoodsBaseInfoMapper goodsBaseInfoMapper;
    @Autowired
    private GoodsCategoryMapper goodsCategoryMapper;


    @Override
    public List<GoodsCategoryResult> getAll(UserMobileGoodsAllParam userMobileGoodsAllParam) {
        // 获取餐品|商品的所有分类
        Long type = userMobileGoodsAllParam.getType();
        QueryWrapper<GoodsCategory> query = Wrappers.query();
        query.eq("type", type);
        List<GoodsCategory> goodsCategories = goodsCategoryMapper.selectList(query);// 非重复获取

        List<GoodsCategoryResult> goodsCategoryResults = new ArrayList<>();
        for (GoodsCategory category : goodsCategories) {
            Long categoryId = category.getId();
            GoodsCategory goodsCategory = goodsCategoryMapper.selectById(categoryId);
            if (null == goodsCategory) {
                return null;
            }

            GoodsCategoryResult goodsCategoryResult = new GoodsCategoryResult();
            BeanUtil.copyProperties(goodsCategory, goodsCategoryResult);

            QueryWrapper<GoodsBaseInfo> queryGoods = Wrappers.query();
            queryGoods.eq("category_id", categoryId);
            queryGoods.eq("type", type);
            queryGoods.eq("status", GoodsBaseInfo.GOODS_STATUS_UP);
            if (type.equals(GoodsBaseInfo.GOODS_TYPE_GENERAL)) {// 如果是商品
                queryGoods.gt("inventory", 0);// 库存必须大于0
            }
            queryGoods.eq("del_flag", GoodsBaseInfo.DEL_FLAG_SHOW);
            queryGoods.orderByDesc("sales");

            List<GoodsBaseInfo> goodsBaseInfoList = goodsBaseInfoMapper.selectList(queryGoods);
            if (CollectionUtils.isNotEmpty(goodsBaseInfoList)) {
                goodsCategoryResult.setGoodsBaseInfoList(goodsBaseInfoList);
                goodsCategoryResults.add(goodsCategoryResult);
            }
        }

        return goodsCategoryResults;
    }
}
