package com.ruoyi.custom.admin.marketing.controller;


import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.marketing.domain.ContractInfo;
import com.ruoyi.custom.admin.marketing.service.ContractInfoService;
import com.ruoyi.custom.utils.OrderUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 合同信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@RestController
@RequestMapping("/contractInfo")
@Api(value = "合同信息", tags = "合同信息")
public class ContractInfoController extends BaseController {
    @Autowired
    private ContractInfoService contractInfoService;

    /**
     * 查询合同信息列表
     */
    // @RequiresPermissions("custom:info:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取合同信息")
    public TableDataInfo list(ContractInfo contractInfo) {
        startPage();
        List<ContractInfo> list = contractInfoService.selectContractInfoListNew(contractInfo);
        return getDataTable(list);
    }

    /**
     * 获取合同信息详细信息
     */
    // @RequiresPermissions("custom:info:query")
    @GetMapping(value = "/{contractNumber}")
    @ApiOperation(value = "获取合同信息详细信息")
    public AjaxResult getInfo(@PathVariable("contractNumber") String contractNumber) {
        return AjaxResult.success(contractInfoService.selectContractInfoByContractNumber(contractNumber));
    }

    /**
     * 新增合同信息（签约）
     */
    // @RequiresPermissions("custom:info:add")
    @Log(title = "合同信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增合同信息（签约）")
    public AjaxResult add(@RequestBody ContractInfo contractInfo) {
        return toAjax(contractInfoService.insertContractInfo(contractInfo));
    }

    /**
     * 生成合同编号
     */
    @GetMapping("/generateContractCode")
    @ApiOperation(value = "生成合同编号")
    public AjaxResult generateContractCode() {
        return AjaxResult.success("生成合同编号成功", contractInfoService.generateContractNumber());
    }

    // /**
    //  * 导出合同信息列表
    //  */
    // // @RequiresPermissions("custom:info:export")
    // @Log(title = "合同信息", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, ContractInfo contractInfo) {
    //     List<ContractInfo> list = contractInfoService.selectContractInfoList(contractInfo);
    //     ExcelUtil<ContractInfo> util = new ExcelUtil<ContractInfo>(ContractInfo.class);
    //     util.exportExcel(response, list, "合同信息数据");
    // }
}

