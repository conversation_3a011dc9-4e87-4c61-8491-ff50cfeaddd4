package com.ruoyi.custom.admin.assessment.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 护理计划Service接口
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface INursingPlanService {

    /**
     * 获取护理计划指标列表
     *
     * @param assessmentPlanId 评估计划ID
     * @return 护理计划指标列表
     */
    public JSONArray getNursingPlanIndicators(Long assessmentPlanId);

    /**
     * 新增护理计划指标
     *
     * @param assessmentPlanId 评估计划ID
     * @param indicator 护理计划指标
     * @return 结果
     */
    public int addNursingPlanIndicator(Long assessmentPlanId, JSONObject indicator);

    /**
     * 修改护理计划指标
     *
     * @param assessmentPlanId 评估计划ID
     * @param uuid 指标UUID
     * @param indicator 护理计划指标
     * @return 结果
     */
    public int updateNursingPlanIndicator(Long assessmentPlanId, String uuid, JSONObject indicator);

    /**
     * 删除护理计划指标
     *
     * @param assessmentPlanId 评估计划ID
     * @param uuid 指标UUID
     * @return 结果
     */
    public int deleteNursingPlanIndicator(Long assessmentPlanId, String uuid);

    /**
     * 获取护理计划指标详情
     *
     * @param assessmentPlanId 评估计划ID
     * @param uuid 指标UUID
     * @return 护理计划指标
     */
    public JSONObject getNursingPlanIndicator(Long assessmentPlanId, String uuid);
} 