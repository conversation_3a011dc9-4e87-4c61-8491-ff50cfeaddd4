package com.ruoyi.custom.admin.marketing.mapper;

import com.ruoyi.custom.admin.marketing.domain.PaymentRecord;
import com.ruoyi.custom.admin.marketing.req.FeeStatisticsReq;
import com.ruoyi.custom.admin.marketing.resp.FeeStatisticsResp;
import com.ruoyi.custom.admin.marketing.resp.PaymentRemindResp;

import java.util.List;
import java.util.Map;

/**
 * 缴费确认单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface PaymentRecordMapper {
    /**
     * 查询缴费确认单
     *
     * @param id 缴费确认单主键
     * @return 缴费确认单
     */
    public PaymentRecord selectPaymentRecordById(String id);

    /**
     * 查询缴费确认单列表
     *
     * @param paymentRecord 缴费确认单
     * @return 缴费确认单集合
     */
    public List<PaymentRecord> selectPaymentRecordList(PaymentRecord paymentRecord);

    /**
     * 新增缴费确认单
     *
     * @param paymentRecord 缴费确认单
     * @return 结果
     */
    public int insertPaymentRecord(PaymentRecord paymentRecord);

    /**
     * 修改缴费确认单
     *
     * @param paymentRecord 缴费确认单
     * @return 结果
     */
    public int updatePaymentRecord(PaymentRecord paymentRecord);

    /**
     * 删除缴费确认单
     *
     * @param id 缴费确认单主键
     * @return 结果
     */
    public int deletePaymentRecordById(String id);

    /**
     * 批量删除缴费确认单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePaymentRecordByIds(String[] ids);

    /**
     * 根据合同编号查询最新一条信息
     * @param contractNumber
     */
    PaymentRecord selectLastInfoByContractNumber(String contractNumber);

    /**
     * 查询缴费提醒列表
     *
     * @param paymentRecord
     * @return
     */
    List<PaymentRemindResp> paymentRemindList(PaymentRecord paymentRecord);

    /**
     * 费用统计
     * @param paymentRecord
     * @return
     */
    List<FeeStatisticsResp> feeStatistics(FeeStatisticsReq feeStatisticsReq);

    /**
     * 查询最大id
     * @return
     */
    String selectMaxId();

    /**
     * 获取收入分类统计
     * @return 收入分类统计列表
     */
    List<Map<String, Object>> getIncomeClassification();

    /**
     * 获取各缴费方式累计缴费统计
     * @return 各缴费方式累计缴费统计列表
     */
    List<Map<String, Object>> getPaymentMethodStatistics();
}

