package com.ruoyi.custom.admin.liveManage.mapper;

import java.util.List;

import com.ruoyi.custom.admin.liveManage.domain.LiveBedRecords;

/**
 * 居住记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
public interface LiveBedRecordsMapper {
    /**
     * 查询居住记录
     *
     * @param id 居住记录主键
     * @return 居住记录
     */
    public LiveBedRecords selectLiveBedRecordsById(String id);

    /**
     * 查询居住记录列表
     *
     * @param liveBedRecords 居住记录
     * @return 居住记录集合
     */
    public List<LiveBedRecords> selectLiveBedRecordsList(LiveBedRecords liveBedRecords);

    /**
     * 新增居住记录
     *
     * @param liveBedRecords 居住记录
     * @return 结果
     */
    public int insertLiveBedRecords(LiveBedRecords liveBedRecords);

    /**
     * 修改居住记录
     *
     * @param liveBedRecords 居住记录
     * @return 结果
     */
    public int updateLiveBedRecords(LiveBedRecords liveBedRecords);

    /**
     * 删除居住记录
     *
     * @param id 居住记录主键
     * @return 结果
     */
    public int deleteLiveBedRecordsById(String id);

    /**
     * 批量删除居住记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLiveBedRecordsByIds(String[] ids);

    /**
     * 根据居住id和房间id修改居住记录
     * @param liveBedRecords1
     * @return
     */
    int updateByLiveIdAndBedId(LiveBedRecords liveBedRecords1);
}
