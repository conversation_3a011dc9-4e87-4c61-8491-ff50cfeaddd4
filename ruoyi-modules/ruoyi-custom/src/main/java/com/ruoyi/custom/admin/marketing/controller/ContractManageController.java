package com.ruoyi.custom.admin.marketing.controller;


import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.marketing.domain.ContractInfo;
import com.ruoyi.custom.admin.marketing.service.ContractInfoService;
import com.ruoyi.custom.utils.OrderUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 合同管理Controller
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@RestController
@RequestMapping("/contractManage")
@Api(value = "合同管理", tags = "合同管理")
public class ContractManageController extends BaseController {
    @Autowired
    private ContractInfoService contractInfoService;

    /**
     * 查询合同信息列表
     */
    // @RequiresPermissions("custom:info:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取合同信息")
    public TableDataInfo list(ContractInfo contractInfo) {
        startPage();
        List<ContractInfo> list = contractInfoService.selectContractRenewInfoListNew(contractInfo);
        return getDataTable(list);
    }

    /**
     * 获取合同信息详细信息
     */
    // @RequiresPermissions("custom:info:query")
    @GetMapping(value = "/{contractNumber}")
    @ApiOperation(value = "获取合同信息详细信息")
    public AjaxResult getInfo(@PathVariable("contractNumber") String contractNumber) {
        return AjaxResult.success(contractInfoService.selectContractRenewInfoByContractNumber(contractNumber));
    }

    /**
     * 续签
     */
    // @RequiresPermissions("custom:info:add")
    @Log(title = "合同信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "续签")
    public AjaxResult renew(@RequestBody ContractInfo contractRenewInfo) {
        return toAjax(contractInfoService.insertContractRenewInfo(contractRenewInfo));
    }

    /**
     * 生成续签合同编号
     */
    @GetMapping("/generateRenewContractCode")
    @ApiOperation(value = "生成续签合同编号")
    @ApiImplicitParam(name = "originalContractNumber", value = "原合同编号", required = true, paramType = "query")
    public AjaxResult generateRenewContractCode(@RequestParam("originalContractNumber") String originalContractNumber) {
        return AjaxResult.success("生成续签合同编号成功", contractInfoService.generateRenewContractNumber(originalContractNumber));
    }

    /**
     * 修改合同信息
     */
    // @RequiresPermissions("custom:info:edit")
    @Log(title = "合同信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContractInfo contractInfo) {
        return toAjax(contractInfoService.updateContractInfo(contractInfo));
    }

    /**
     * 删除合同信息
     */
    // @RequiresPermissions("custom:info:remove")
    @Log(title = "合同信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{contractNumbers}")
    public AjaxResult remove(@PathVariable String contractNumbers) {
        return toAjax(contractInfoService.deleteContractInfoByContractNumbers(contractNumbers));
    }


}

