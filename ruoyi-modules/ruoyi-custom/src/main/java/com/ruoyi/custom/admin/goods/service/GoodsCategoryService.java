package com.ruoyi.custom.admin.goods.service;

import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.custom.admin.goods.domain.GoodsCategory;
import com.ruoyi.custom.admin.goods.request.GoodsCategoryAddRequest;

import java.util.List;

/**
 * @ClassName HomeGoodsCategoryService
 * @Description
 * <AUTHOR>
 * @Date 2022/7/11 13:30
 */
public interface GoodsCategoryService {

    /**
     * <AUTHOR>
     * @Description 获取所有商品分类列表
     * @Date 2022/7/11
     **/
    List<GoodsCategory> getAllByType(Long type);

    /**
     * 新增普通商品分类
     *
     * @param goodsCategoryAddRequest
     * @return
     */
    TAjaxResult add(GoodsCategoryAddRequest goodsCategoryAddRequest);
}
