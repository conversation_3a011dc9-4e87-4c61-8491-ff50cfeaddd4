package com.ruoyi.custom.admin.assessment.service.impl;


import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.assessment.domain.AssessmentTemplateType;
import com.ruoyi.custom.admin.assessment.mapper.AssessmentTemplateTypeMapper;
import com.ruoyi.custom.admin.assessment.service.IAssessmentTemplateTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 评估模版类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class AssessmentTemplateTypeServiceImpl implements IAssessmentTemplateTypeService {
    @Autowired
    private AssessmentTemplateTypeMapper assessmentTemplateTypeMapper;

    /**
     * 查询评估模版类型
     *
     * @param id 评估模版类型主键
     * @return 评估模版类型
     */
    @Override
    public AssessmentTemplateType selectAssessmentTemplateTypeById(Long id) {
        return assessmentTemplateTypeMapper.selectAssessmentTemplateTypeById(id);
    }

    /**
     * 查询评估模版类型列表
     *
     * @param assessmentTemplateType 评估模版类型
     * @return 评估模版类型
     */
    @Override
    public List<AssessmentTemplateType> selectAssessmentTemplateTypeList(AssessmentTemplateType assessmentTemplateType) {
        return assessmentTemplateTypeMapper.selectAssessmentTemplateTypeList(assessmentTemplateType);
    }

    /**
     * 新增评估模版类型
     *
     * @param assessmentTemplateType 评估模版类型
     * @return 结果
     */
    @Override
    public int insertAssessmentTemplateType(AssessmentTemplateType assessmentTemplateType) {
        assessmentTemplateType.setCreateTime(DateUtils.getNowDate());
        assessmentTemplateType.setCreateBy(SecurityUtils.getUsername());
        return assessmentTemplateTypeMapper.insertAssessmentTemplateType(assessmentTemplateType);
    }

    /**
     * 修改评估模版类型
     *
     * @param assessmentTemplateType 评估模版类型
     * @return 结果
     */
    @Override
    public int updateAssessmentTemplateType(AssessmentTemplateType assessmentTemplateType) {
        assessmentTemplateType.setUpdateTime(DateUtils.getNowDate());
        assessmentTemplateType.setUpdateBy(SecurityUtils.getUsername());
        return assessmentTemplateTypeMapper.updateAssessmentTemplateType(assessmentTemplateType);
    }

    /**
     * 批量删除评估模版类型
     *
     * @param ids 需要删除的评估模版类型主键
     * @return 结果
     */
    @Override
    public int deleteAssessmentTemplateTypeByIds(Long[] ids) {
        AssessmentTemplateType params = new AssessmentTemplateType();
        params.setDelFlag("1");
        params.setUpdateBy(SecurityUtils.getUsername());
        params.setUpdateTime(DateUtils.getNowDate());
        return assessmentTemplateTypeMapper.deleteAssessmentTemplateTypeByIds(params, ids);
    }

    /**
     * 删除评估模版类型信息
     *
     * @param id 评估模版类型主键
     * @return 结果
     */
    @Override
    public int deleteAssessmentTemplateTypeById(Long id) {
        Long[] ids = {id};
        return this.deleteAssessmentTemplateTypeByIds(ids);
    }
}

