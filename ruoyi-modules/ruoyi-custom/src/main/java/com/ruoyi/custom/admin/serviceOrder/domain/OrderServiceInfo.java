package com.ruoyi.custom.admin.serviceOrder.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description t_order_service_info
 * @date 2022-07-15
 */
@Data
@ApiModel("t_order_service_info")
@TableName("t_order_service_info")
public class OrderServiceInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键id")
    private Integer id;

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private String orderId;

    /**
     * 服务id
     */
    @ApiModelProperty("服务id")
    private Long serviceId;

    /**
     * 服务名称
     */
    @ApiModelProperty("服务名称")
    private String serviceName;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 老人id
     */
    @ApiModelProperty("老人id")
    private String elderlyPeopleId;

    /**
     * 价格
     */
    @ApiModelProperty("价格")
    private BigDecimal price;

    /**
     * 总价
     */
    @ApiModelProperty("总价")
    private BigDecimal totalFee;

    /**
     * 图片
     */
    @ApiModelProperty("图片")
    private String img;

    /**
     * 规格
     */
    @ApiModelProperty("规格")
    private String standard;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private Integer number;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

}
