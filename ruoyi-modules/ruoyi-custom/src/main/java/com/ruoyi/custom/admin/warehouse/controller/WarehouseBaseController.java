package com.ruoyi.custom.admin.warehouse.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.warehouse.domain.WarehouseBase;
import com.ruoyi.custom.admin.warehouse.service.IWarehouseBaseService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 仓库基础信息Controller
 *
 * <AUTHOR>
 * @date 2022-05-06
 */
@RestController
@RequestMapping("/WarehouseBase")
@Api(value = "仓库管理", tags = "库存管理-仓库管理")
public class WarehouseBaseController extends BaseController {
    @Autowired
    private IWarehouseBaseService warehouseBaseService;

    /**
     * 查询仓库基础信息列表
     */
    //@RequiresPermissions("warehouse:WarehouseBase:list")
    @GetMapping("/list")
    @ApiOperation(value = "仓库信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", dataTypeClass = String.class),
            @ApiImplicitParam(value = "仓库名称", name = "name", paramType = "query", dataTypeClass = String.class)
    })
    public TableDataInfo<WarehouseBase> list(@ApiIgnore WarehouseBase warehouseBase) {
        startPage();
        List<WarehouseBase> list = warehouseBaseService.selectWarehouseBaseList(warehouseBase);
        return getDataTable(list);
    }

    /**
     * 导出仓库基础信息列表
     */
    //@RequiresPermissions("warehouse:WarehouseBase:export")
    @Log(platform = "1", title = "仓库基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiIgnore
    public void export(HttpServletResponse response, WarehouseBase warehouseBase) {
        List<WarehouseBase> list = warehouseBaseService.selectWarehouseBaseList(warehouseBase);
        ExcelUtil<WarehouseBase> util = new ExcelUtil<WarehouseBase>(WarehouseBase.class);
        util.exportExcel(response, list, "仓库基础信息数据");
    }

    /**
     * 获取仓库基础信息详细信息
     */
    //@RequiresPermissions("warehouse:WarehouseBase:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取仓库基础信息详细信息")
    public TAjaxResult<WarehouseBase> getInfo(@PathVariable("id") Long id) {
        return new TAjaxResult(warehouseBaseService.selectWarehouseBaseById(id));
    }

    /**
     * 新增仓库基础信息
     */
    //@RequiresPermissions("warehouse:WarehouseBase:add")
    @Log(platform = "1", title = "仓库基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增仓库基础信息")
    public AjaxResult add(@RequestBody WarehouseBase warehouseBase) {
        return toAjax(warehouseBaseService.insertWarehouseBase(warehouseBase));
    }

    /**
     * 修改仓库基础信息
     */
    //@RequiresPermissions("warehouse:WarehouseBase:edit")
    @Log(platform = "1", title = "仓库基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改仓库基础信息")
    public AjaxResult edit(@RequestBody WarehouseBase warehouseBase) {
        return toAjax(warehouseBaseService.updateWarehouseBase(warehouseBase));
    }

    /**
     * 删除仓库基础信息
     */
    //@RequiresPermissions("warehouse:WarehouseBase:remove")
    @Log(platform = "1", title = "仓库基础信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除仓库基础信息")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(warehouseBaseService.deleteWarehouseBaseByIds(ids));
    }

    /**
     * 获取全部label格式的仓库列表
     *
     * @return
     */
    @ApiOperation(value = "获取全部label格式的仓库列表")
    @GetMapping("/getWarehouseLabelList")
    public AjaxResult getWarehouseLabelList() {
        List<JSONObject> labelList = warehouseBaseService.getWarehouseLabelList();
        return AjaxResult.success().put("data", labelList);
    }
}
