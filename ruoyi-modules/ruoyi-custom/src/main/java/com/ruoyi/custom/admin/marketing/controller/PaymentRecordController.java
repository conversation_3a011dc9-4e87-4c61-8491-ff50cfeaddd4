package com.ruoyi.custom.admin.marketing.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.marketing.domain.PaymentRecord;
import com.ruoyi.custom.admin.marketing.req.FeeStatisticsReq;
import com.ruoyi.custom.admin.marketing.resp.FeeStatisticsResp;
import com.ruoyi.custom.admin.marketing.resp.PaymentRemindResp;
import com.ruoyi.custom.admin.marketing.service.IPaymentRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 缴费确认单Controller
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@RestController
@RequestMapping("/paymentRecord")
@Api(value = "缴费确认单", tags = "缴费确认单")
public class PaymentRecordController extends BaseController {
    @Autowired
    private IPaymentRecordService paymentRecordService;

    /**
     * 查询缴费确认单列表
     */
    // @RequiresPermissions("custom:record:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询缴费确认单列表")
    public TableDataInfo list(PaymentRecord paymentRecord) {
        startPage();
        List<PaymentRecord> list = paymentRecordService.selectPaymentRecordList(paymentRecord);
        return getDataTable(list);
    }

    /**
     * 获取缴费确认单详细信息
     */
    // @RequiresPermissions("custom:record:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取缴费确认单详细信息")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(paymentRecordService.selectPaymentRecordById(id));
    }

    /**
     * 获取缴费金额明细列表
     */
    @GetMapping("/paymentDetail")
    @ApiOperation(value = "获取缴费金额明细列表")
    @ApiImplicitParam(name = "recordId", value = "缴费记录ID", required = true, paramType = "query")
    public AjaxResult getPaymentDetail(String recordId) {
        return AjaxResult.success(paymentRecordService.selectPaymentDetailByRecordId(recordId));
    }

    /**
     * 根据合同id，生成账单确认信息
     */
    @GetMapping("/payment/info")
    @ApiOperation(value = "根据合同id，生成账单缴费信息")
    public AjaxResult generatePaymentInfo(String contractNumber) {
        PaymentRecord paymentRecord = paymentRecordService.generatePaymentInfo(contractNumber);
        return AjaxResult.success(paymentRecord);
    }

    /**
     * 缴费单保存
     */
    @PostMapping("/payment")
    @ApiOperation(value = "缴费单保存")
    public AjaxResult paymentConfirm(@RequestBody PaymentRecord paymentRecord) {
        paymentRecord.setFeeType("1"); // 缴费单
        return toAjax(paymentRecordService.payment(paymentRecord));
    }

    /**
     * 根据合同id，生成账单结算信息
     */
    @GetMapping("/settlement/info")
    @ApiOperation(value = "根据合同id，生成账单结算信息")
    public AjaxResult generatePaymentSettlementInfo(String contractNumber) {
        PaymentRecord paymentRecord = paymentRecordService.generatePaymentSettlementInfo(contractNumber);
        return AjaxResult.success(paymentRecord);
    }

    /**
     * 结算单保存
     */
    @PostMapping("/settlement")
    @ApiOperation(value = "结算单保存")
    public AjaxResult paymentSettlementConfirm(@RequestBody PaymentRecord paymentRecord) {
        paymentRecord.setFeeType("2"); // 结算单
        return toAjax(paymentRecordService.settlement(paymentRecord));
    }

    /**
     * 根据合同编号结算单
     */
    @GetMapping("/settlement")
    @ApiOperation(value = "根据合同编号结算单")
    public AjaxResult settlement(String contractNumber) {
        PaymentRecord params = new PaymentRecord();
        params.setContractNumber(contractNumber);
        params.setFeeType("2"); // 结算单
        List<PaymentRecord> records = paymentRecordService.selectPaymentRecordList(params);
        if (records.size() < 1) {
            return AjaxResult.error("暂无数据");
        }

        return AjaxResult.success(records.get(0));
    }

    /**
     * 缴费提醒列表
     */
    @GetMapping("/paymentRemindList")
    @ApiOperation(value = "缴费提醒列表")
    public TableDataInfo paymentRemindList(PaymentRecord paymentRecord) {
        startPage();
        List<PaymentRemindResp> list = paymentRecordService.paymentRemindList(paymentRecord);
        return getDataTable(list);
    }

    /**
     * 费用统计
     */
    @GetMapping("/feeStatistics")
    @ApiOperation(value = "费用统计")
    public TableDataInfo feeStatistics(FeeStatisticsReq feeStatisticsReq) {
        startPage();
        List<FeeStatisticsResp> list = paymentRecordService.feeStatistics(feeStatisticsReq);
        return getDataTable(list);
    }

    /**
     * 导出费用统计
     */
    // @RequiresPermissions("custom:alarm:export")
    @Log(title = "费用统计", businessType = BusinessType.EXPORT)
    @PostMapping("/feeStatisticsExport")
    public void export(HttpServletResponse response, FeeStatisticsReq feeStatisticsReq) {
        List<FeeStatisticsResp> list = paymentRecordService.feeStatistics(feeStatisticsReq);
        ExcelUtil<FeeStatisticsResp> util = new ExcelUtil<FeeStatisticsResp>(FeeStatisticsResp.class);
        util.exportExcel(response, list, "费用统计数据");
    }
}

