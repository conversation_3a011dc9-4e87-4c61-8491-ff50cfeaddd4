package com.ruoyi.custom.admin.liveManage.domain.req;

import com.ruoyi.common.core.domain.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 居住信息基础对象 t_live_base_info
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@Data
public class LiveQueryVo extends Page {
    @ApiModelProperty(value = "老人id", name = "userId", example = "2")
    private String userId;
    @ApiModelProperty(value = "用户名", name = "userName", example = "刘")
    private String userName;
    @ApiModelProperty(value = "居住类型", name = "liveType", example = "1")
    private String liveType;
    @ApiModelProperty(value = "居住状态", name = "state", example = "1")
    private String state;
    @ApiModelProperty(value = "房间类型", name = "roomType", example = "1")
    private String roomType;
}
