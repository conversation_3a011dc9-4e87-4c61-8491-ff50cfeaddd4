package com.ruoyi.custom.utils;

public class GeoDistanceCalculator {

    // 地球平均半径（单位：公里）
    private static final double EARTH_RADIUS = 6371.0;

    /**
     * 计算两个经纬度之间的直线距离
     *
     * @param lat1Str 点1的纬度（字符串）
     * @param lon1Str 点1的经度（字符串）
     * @param lat2Str 点2的纬度（字符串）
     * @param lon2Str 点2的经度（字符串）
     * @return 距离（单位：公里）
     * @throws NumberFormatException 如果输入的字符串不能转换为数字
     */
    public static double calculateDistance(String lat1Str, String lon1Str, String lat2Str, String lon2Str) {
        try {
            // 将字符串解析为 double 类型
            double lat1 = Double.parseDouble(lat1Str);
            double lon1 = Double.parseDouble(lon1Str);
            double lat2 = Double.parseDouble(lat2Str);
            double lon2 = Double.parseDouble(lon2Str);

            // 调用核心计算逻辑
            return calculateDistance(lat1, lon1, lat2, lon2);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("经纬度必须为有效的数字字符串", e);
        }
    }

    /**
     * 内部方法：实际计算逻辑（接收 double 类型）
     */
    private static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        double radLat1 = Math.toRadians(lat1);
        double radLon1 = Math.toRadians(lon1);
        double radLat2 = Math.toRadians(lat2);
        double radLon2 = Math.toRadians(lon2);

        double deltaLat = radLat2 - radLat1;
        double deltaLon = radLon2 - radLon1;

        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2)
                + Math.cos(radLat1) * Math.cos(radLat2)
                * Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
        double c = 2 * Math.asin(Math.sqrt(a));

        return EARTH_RADIUS * c;
    }

    public static void main(String[] args) {
        // 示例经纬度（字符串）
        String userLat = "34.052235";  // 用户的纬度
        String userLon = "-118.243683"; // 用户的经度
        String backendLat = "40.712776"; // 后台设置的纬度
        String backendLon = "-74.005974"; // 后台设置的经度

        // 计算距离
        try {
            double distance = calculateDistance(userLat, userLon, backendLat, backendLon);
            System.out.printf("两点之间的直线距离是：%.2f 公里%n", distance);
        } catch (IllegalArgumentException e) {
            System.out.println("输入错误：" + e.getMessage());
        }
    }
}

