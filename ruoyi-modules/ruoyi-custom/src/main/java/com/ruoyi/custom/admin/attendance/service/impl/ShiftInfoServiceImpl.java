package com.ruoyi.custom.admin.attendance.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.attendance.domain.CycleManagement;
import com.ruoyi.custom.admin.attendance.domain.ShiftInfo;
import com.ruoyi.custom.admin.attendance.mapper.ShiftInfoMapper;
import com.ruoyi.custom.admin.attendance.service.CycleManagementService;
import com.ruoyi.custom.admin.attendance.service.ShiftInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 班次信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@Service
public class ShiftInfoServiceImpl extends MPJBaseServiceImpl<ShiftInfoMapper, ShiftInfo> implements ShiftInfoService {
    @Autowired
    private ShiftInfoMapper shiftInfoMapper;

    @Autowired
    private CycleManagementService cycleManagementService;

    /**
     * 查询班次信息
     *
     * @param id 班次信息主键
     * @return 班次信息
     */
    @Override
    public ShiftInfo selectShiftInfoById(Integer id) {
        return shiftInfoMapper.selectShiftInfoById(id);
    }

    /**
     * 查询班次信息列表
     *
     * @param shiftInfo 班次信息
     * @return 班次信息
     */
    @Override
    public List<ShiftInfo> selectShiftInfoList(ShiftInfo shiftInfo) {
        return shiftInfoMapper.selectShiftInfoList(shiftInfo);
    }

    /**
     * 新增班次信息
     *
     * @param shiftInfo 班次信息
     * @return 结果
     */
    @Override
    public int insertShiftInfo(ShiftInfo shiftInfo) {
        // 插入班次信息
        shiftInfo.setCreateBy(SecurityUtils.getUserId());
        shiftInfo.setCreateByName(SecurityUtils.getUsername());
        shiftInfo.setUpdatedTime(new Date());
        shiftInfoMapper.insertShiftInfo(shiftInfo);
        // 插入地点信息
        shiftInfo.getShiftLocationList().forEach(a -> {
            a.setShiftId(shiftInfo.getId());
            shiftInfoMapper.insertShiftLocation(a);
        });
        return 1;
    }

    /**
     * 修改班次信息
     *
     * @param shiftInfo 班次信息
     * @return 结果
     */
    @Override
    public int updateShiftInfo(ShiftInfo shiftInfo) {
        // 更新班次信息
        shiftInfo.setUpdatedTime(new Date());
        shiftInfoMapper.updateShiftInfo(shiftInfo);

        // 删除旧的地点信息
        shiftInfoMapper.deleteShiftLocationByShiftId(shiftInfo.getId());

        // 插入新的地点信息
        shiftInfo.getShiftLocationList().forEach(a -> {
            a.setShiftId(shiftInfo.getId());
            shiftInfoMapper.insertShiftLocation(a);
        });
        return 1;
    }

    /**
     * 批量删除班次信息
     *
     * @param ids 需要删除的班次信息主键
     * @return 结果
     */
    @Override
    public int deleteShiftInfoByIds(Integer[] ids) {
        return shiftInfoMapper.deleteShiftInfoByIds(ids);
    }

    /**
     * 删除班次信息信息
     *
     * @param id 班次信息主键
     * @return 结果
     */
    @Override
    public int deleteShiftInfoById(Integer id) {
        // 验证是否有周期引用
        QueryWrapper<CycleManagement> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("shift_ids", id);
        long count = cycleManagementService.count(queryWrapper);
        if (count > 0) {
            throw new RuntimeException("该班次已被周期引用，无法删除");
        }

        // 删除地点信息
        shiftInfoMapper.deleteShiftLocationByShiftId(id);
        // 删除班次信息
        return shiftInfoMapper.deleteShiftInfoById(id);
    }
}

