package com.ruoyi.custom.admin.alarm.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 设备报警对象 t_device_alarm
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Data
@ApiModel(value = "设备报警")
public class DeviceAlarm extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 老人id，对应t_elderly_people_info
     */
    @ApiModelProperty(value = "老人id，对应t_elderly_people_info")
    private String elderId;

    /**
     * 老人姓名
     */
    @ApiModelProperty(value = "老人姓名")
    private String elderName;

    /**
     * 报警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "报警时间")
    private Date alarmTime;

    /**
     * 报警类型
     */
    @ApiModelProperty(value = "报警类型")
    private String alarmType;
}
