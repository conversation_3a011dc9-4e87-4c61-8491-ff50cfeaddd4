package com.ruoyi.custom.api.assessmentApp;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.assessment.domain.AssessmentPlan;
import com.ruoyi.custom.admin.assessment.service.IAssessmentPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController("assessmentAppIndexController")
@RequestMapping("/assessmentApp/index")
@Api(tags = "评估端APP-首页")
public class IndexController extends BaseController {

    @Autowired
    private IAssessmentPlanService assessmentPlanService;

    /**
     * 个人今日待评估列表
     */
    @GetMapping("/ownerTodayAssessmentList")
    @ApiOperation(value = "个人今日待评估列表")
    public TableDataInfo ownerTodayAssessmentList() {
        startPage();
        AssessmentPlan params = new AssessmentPlan();
        params.setAssessorId(SecurityUtils.getLoginUser().getUserid());
        params.setPlannedStartDate(DateUtil.date());
        params.setStatus("0"); // 未开始
        return getDataTable(assessmentPlanService.selectAssessmentPlanList(params));
    }

    /**
     * 今日评估数量
     */
    @GetMapping("/todayAssessmentNum")
    @ApiOperation(value = "今日评估数量")
    public AjaxResult todayAssessmentNum() {
        AssessmentPlan params = new AssessmentPlan();
        params.setAssessorId(SecurityUtils.getLoginUser().getUserid());
        params.setPlannedStartDate(DateUtil.date());
        params.setStatus("0"); // 未开始
        return AjaxResult.success(assessmentPlanService.selectAssessmentPlanCount(params));
    }

    /**
     * 提醒通知
     */
    @GetMapping("/remindNotice")
    @ApiOperation(value = "提醒通知")
    public AjaxResult remindNotice() {
        AssessmentPlan params = new AssessmentPlan();
        params.setAssessorId(SecurityUtils.getLoginUser().getUserid());
        return AjaxResult.success(assessmentPlanService.remindNotice(params));
    }

}
