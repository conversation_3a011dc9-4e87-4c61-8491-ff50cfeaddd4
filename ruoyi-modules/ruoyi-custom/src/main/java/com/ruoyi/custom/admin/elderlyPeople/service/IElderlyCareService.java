package com.ruoyi.custom.admin.elderlyPeople.service;

import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyCare;

import java.util.List;

/**
 * 长者入住关怀Service接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface IElderlyCareService {
    /**
     * 查询长者入住关怀
     *
     * @param id 长者入住关怀主键
     * @return 长者入住关怀
     */
    public ElderlyCare selectElderlyCareById(Long id);

    /**
     * 查询长者入住关怀列表
     *
     * @param elderlyCare 长者入住关怀
     * @return 长者入住关怀集合
     */
    public List<ElderlyCare> selectElderlyCareList(ElderlyCare elderlyCare);

    /**
     * 新增长者入住关怀
     *
     * @param elderlyCare 长者入住关怀
     * @return 结果
     */
    public int insertElderlyCare(ElderlyCare elderlyCare);

    /**
     * 修改长者入住关怀
     *
     * @param elderlyCare 长者入住关怀
     * @return 结果
     */
    public int updateElderlyCare(ElderlyCare elderlyCare);

    /**
     * 批量删除长者入住关怀
     *
     * @param ids 需要删除的长者入住关怀主键集合
     * @return 结果
     */
    public int deleteElderlyCareByIds(Long[] ids);

    /**
     * 删除长者入住关怀信息
     *
     * @param id 长者入住关怀主键
     * @return 结果
     */
    public int deleteElderlyCareById(Long id);
} 