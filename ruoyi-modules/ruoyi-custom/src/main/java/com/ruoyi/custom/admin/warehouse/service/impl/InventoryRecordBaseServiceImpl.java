package com.ruoyi.custom.admin.warehouse.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.fee.domain.res.FeePayVo;
import com.ruoyi.custom.utils.OrderUtils;
import com.ruoyi.custom.admin.warehouse.domain.InventoryRecordBase;
import com.ruoyi.custom.admin.warehouse.domain.MaterialsRemainingStock;
import com.ruoyi.custom.admin.warehouse.domain.WarehousingMaterialsLogInfo;
import com.ruoyi.custom.admin.warehouse.mapper.InventoryRecordBaseMapper;
import com.ruoyi.custom.admin.warehouse.mapper.WarehousingMaterialsLogInfoMapper;
import com.ruoyi.custom.admin.warehouse.service.IInventoryRecordBaseService;
import com.ruoyi.custom.admin.warehouse.service.IMaterialsRemainingStockService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 盘点记录信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-09
 */
@Service
public class InventoryRecordBaseServiceImpl implements IInventoryRecordBaseService {

    @Autowired
    private InventoryRecordBaseMapper inventoryRecordBaseMapper;

    @Autowired
    private WarehousingMaterialsLogInfoMapper warehousingMaterialsLogInfoMapper;

    @Autowired
    private IMaterialsRemainingStockService stockService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询盘点记录信息
     *
     * @param id 盘点记录信息主键
     * @return 盘点记录信息
     */
    @Override
    public InventoryRecordBase selectInventoryRecordBaseById(Long id) {
        InventoryRecordBase data = inventoryRecordBaseMapper.selectInventoryRecordBaseById(id);

        if (Objects.nonNull(data) && StrUtil.isNotBlank(data.getInventoryStaff())) {
            R<SysUser> userR = remoteUserService.getInfoByUserId(Long.valueOf(data.getInventoryStaff()));
            if (Objects.nonNull(userR) && userR.getCode() == 200 && Objects.nonNull(userR.getData())) {
                data.setInventoryStaffName(userR.getData().getNickName());
            }
        }

        if (null != data) {
            List<WarehousingMaterialsLogInfo> list = warehousingMaterialsLogInfoMapper.getLogInfoByBaseId(id, "2");
            data.setDataList(list);
        }
        return data;
    }

    /**
     * 查询盘点记录信息列表
     *
     * @param inventoryRecordBase 盘点记录信息
     * @return 盘点记录信息
     */
    @Override
    public List<InventoryRecordBase> selectInventoryRecordBaseList(InventoryRecordBase inventoryRecordBase) {
        List<InventoryRecordBase> list = inventoryRecordBaseMapper.selectInventoryRecordBaseList(inventoryRecordBase);
        List<Long> ids = list.stream()
                .map(a -> a.getInventoryStaff())
                .filter(StrUtil::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        Map<Long, SysUser> userMap = remoteUserService.getUserMapByIds(ids.toArray(new Long[0])); // 用逗号拼接成字符串);

        list.forEach(a -> {
            if (StrUtil.isNotBlank(a.getInventoryStaff())) {
                SysUser sysUser = userMap.get(Long.parseLong(a.getInventoryStaff()));
                if (!Objects.isNull(sysUser)) {
                    a.setInventoryStaffName(sysUser.getNickName());
                }
            }
        });

        return list;
    }

    /**
     * 新增盘点记录信息
     *
     * @param inventoryRecordBase 盘点记录信息
     * @return 结果
     */
    @Override
    public int insertInventoryRecordBase(InventoryRecordBase inventoryRecordBase) {
        inventoryRecordBase.setCreateTime(DateUtils.getNowDate());
        List<WarehousingMaterialsLogInfo> dataList = inventoryRecordBase.getDataList();
        inventoryRecordBase.setNumber(OrderUtils.getInventoryCode());
        inventoryRecordBase.setInventoryStaff(String.valueOf(SecurityUtils.getUserId()));
        int i = inventoryRecordBaseMapper.insertInventoryRecordBase(inventoryRecordBase);
        for (WarehousingMaterialsLogInfo logInfo : dataList) {
            MaterialsRemainingStock stockInfo = stockService.getByBaseId(logInfo.getMaterialsId(), inventoryRecordBase.getWarehouseId());// 获取剩余数量
            logInfo.setBaseId(inventoryRecordBase.getId());
            logInfo.setType("2");
            logInfo.setWarehouseId(inventoryRecordBase.getWarehouseId());
            logInfo.setPreviousQuantity(stockInfo.getQuantity());// 变化之前数量
            logInfo.setCurrentQuantity(logInfo.getQuantity());// 变化之后数量
            warehousingMaterialsLogInfoMapper.insertWarehousingMaterialsLogInfo(logInfo);
            stockInfo.setQuantity(logInfo.getQuantity());// 剩余数量
            stockInfo.setWarehouseId(inventoryRecordBase.getWarehouseId());
            stockService.save(stockInfo);
        }
        return 1;
    }

    /**
     * 修改盘点记录信息
     *
     * @param inventoryRecordBase 盘点记录信息
     * @return 结果
     */
    @Override
    public int updateInventoryRecordBase(InventoryRecordBase inventoryRecordBase) {
        inventoryRecordBase.setUpdateTime(DateUtils.getNowDate());
        return inventoryRecordBaseMapper.updateInventoryRecordBase(inventoryRecordBase);
    }

    /**
     * 批量删除盘点记录信息
     *
     * @param ids 需要删除的盘点记录信息主键
     * @return 结果
     */
    @Override
    public int deleteInventoryRecordBaseByIds(Long[] ids) {
        return inventoryRecordBaseMapper.deleteInventoryRecordBaseByIds(ids);
    }

    /**
     * 删除盘点记录信息信息
     *
     * @param id 盘点记录信息主键
     * @return 结果
     */
    @Override
    public int deleteInventoryRecordBaseById(Long id) {
        return inventoryRecordBaseMapper.deleteInventoryRecordBaseById(id);
    }

}
