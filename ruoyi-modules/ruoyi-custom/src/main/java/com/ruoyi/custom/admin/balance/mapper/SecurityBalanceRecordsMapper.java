package com.ruoyi.custom.admin.balance.mapper;

import com.ruoyi.custom.admin.balance.domain.SecurityBalanceRecords;

import java.util.List;

/**
 * 保障金账户记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface SecurityBalanceRecordsMapper {
    /**
     * 查询保障金账户记录
     *
     * @param id 保障金账户记录主键
     * @return 保障金账户记录
     */
    public SecurityBalanceRecords selectSecurityBalanceRecordsById(Integer id);

    /**
     * 查询保障金账户记录列表
     *
     * @param securityBalanceRecords 保障金账户记录
     * @return 保障金账户记录集合
     */
    public List<SecurityBalanceRecords> selectSecurityBalanceRecordsList(SecurityBalanceRecords securityBalanceRecords);

    /**
     * 新增保障金账户记录
     *
     * @param securityBalanceRecords 保障金账户记录
     * @return 结果
     */
    public int insertSecurityBalanceRecords(SecurityBalanceRecords securityBalanceRecords);

    /**
     * 修改保障金账户记录
     *
     * @param securityBalanceRecords 保障金账户记录
     * @return 结果
     */
    public int updateSecurityBalanceRecords(SecurityBalanceRecords securityBalanceRecords);

    /**
     * 删除保障金账户记录
     *
     * @param id 保障金账户记录主键
     * @return 结果
     */
    public int deleteSecurityBalanceRecordsById(Integer id);

    /**
     * 批量删除保障金账户记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSecurityBalanceRecordsByIds(Integer[] ids);
} 