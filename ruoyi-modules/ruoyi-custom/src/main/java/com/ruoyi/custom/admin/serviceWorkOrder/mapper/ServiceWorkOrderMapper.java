package com.ruoyi.custom.admin.serviceWorkOrder.mapper;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.custom.admin.serviceWorkOrder.domain.OrderServiceWork;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description home_service_work_orderMapper
 * @date 2022-07-18
 */
@Mapper
public interface ServiceWorkOrderMapper extends BaseMapper<OrderServiceWork> {

    // @Select(
    //         "<script>SELECT t0.* FROM t_order_service_work t0 " +
    //                 // add here if need left join
    //                 "WHERE 1=1" +
    //                 "<IF test='id!=null and id!=&apos;&apos; '> AND t0.id=#{id}</IF> " +
    //                 "<IF test='orderId!=null and orderId!=&apos;&apos; '> AND t0.order_id=#{orderId}</IF> " +
    //                 "<IF test='serviceId!=null and serviceId!=&apos;&apos; '> AND t0.service_id=#{serviceId}</IF> " +
    //                 "<IF test='serviceName!=null and serviceName!=&apos;&apos; '> AND t0.service_name=#{serviceName}</IF> " +
    //                 "<IF test='reserveTime!=null and reserveTime!=&apos;&apos; '> AND t0.reserve_time=#{reserveTime}</IF> " +
    //                 "<IF test='elderlyPeopleId!=null and elderlyPeopleId!=&apos;&apos; '> AND t0.elderly_people_id=#{elderlyPeopleId}</IF> " +
    //                 "<IF test='serviceStartTime!=null and serviceStartTime!=&apos;&apos; '> AND t0.service_start_time=#{serviceStartTime}</IF> " +
    //                 "<IF test='serviceEndTime!=null and serviceEndTime!=&apos;&apos; '> AND t0.service_end_time=#{serviceEndTime}</IF> " +
    //                 "<IF test='serviceTime!=null and serviceTime!=&apos;&apos; '> AND t0.service_time=#{serviceTime}</IF> " +
    //                 "<IF test='workerId!=null and workerId!=&apos;&apos; '> AND t0.worker_id=#{workerId}</IF> " +
    //                 "<IF test='status!=null and status!=&apos;&apos; '> AND t0.status=#{status}</IF> " +
    //                 "<IF test='serviceAddress!=null and serviceAddress!=&apos;&apos; '> AND t0.service_address=#{serviceAddress}</IF> " +
    //                 "<IF test='createTime!=null and createTime!=&apos;&apos; '> AND t0.create_time=#{createTime}</IF> " +
    //                 "<IF test='createBy!=null and createBy!=&apos;&apos; '> AND t0.create_by=#{createBy}</IF> " +
    //                 "<IF test='updateTime!=null and updateTime!=&apos;&apos; '> AND t0.update_time=#{updateTime}</IF> " +
    //                 "<IF test='updateBy!=null and updateBy!=&apos;&apos; '> AND t0.update_by=#{updateBy}</IF> " +
    //                 "<IF test='delFlag!=null and delFlag!=&apos;&apos; '> AND t0.del_flag=#{delFlag}</IF> " +
    //                 "<IF test='remark!=null and remark!=&apos;&apos; '> AND t0.remark=#{remark}</IF> " +
    //                 // add here if need page limit
    //                 //" limit ${page},${limit} " +
    //                 " </script>")
    // List<OrderServiceWork> pageAll(OrderServiceWork queryParamDTO, int page, int limit);
    //
    // @Select("<script>SELECT COUNT(1) FROM t_order_service_work t0 " +
    //         // add here if need left join
    //         "WHERE 1=1" +
    //         "<IF test='id!=null and id!=&apos;&apos; '> AND t0.id=#{id}</IF> " +
    //         "<IF test='orderId!=null and orderId!=&apos;&apos; '> AND t0.order_id=#{orderId}</IF> " +
    //         "<IF test='serviceId!=null and serviceId!=&apos;&apos; '> AND t0.service_id=#{serviceId}</IF> " +
    //         "<IF test='serviceName!=null and serviceName!=&apos;&apos; '> AND t0.service_name=#{serviceName}</IF> " +
    //         "<IF test='reserveTime!=null and reserveTime!=&apos;&apos; '> AND t0.reserve_time=#{reserveTime}</IF> " +
    //         "<IF test='elderlyPeopleId!=null and elderlyPeopleId!=&apos;&apos; '> AND t0.elderly_people_id=#{elderlyPeopleId}</IF> " +
    //         "<IF test='serviceStartTime!=null and serviceStartTime!=&apos;&apos; '> AND t0.service_start_time=#{serviceStartTime}</IF> " +
    //         "<IF test='serviceEndTime!=null and serviceEndTime!=&apos;&apos; '> AND t0.service_end_time=#{serviceEndTime}</IF> " +
    //         "<IF test='serviceTime!=null and serviceTime!=&apos;&apos; '> AND t0.service_time=#{serviceTime}</IF> " +
    //         "<IF test='workerId!=null and workerId!=&apos;&apos; '> AND t0.worker_id=#{workerId}</IF> " +
    //         "<IF test='status!=null and status!=&apos;&apos; '> AND t0.status=#{status}</IF> " +
    //         "<IF test='serviceAddress!=null and serviceAddress!=&apos;&apos; '> AND t0.service_address=#{serviceAddress}</IF> " +
    //         "<IF test='createTime!=null and createTime!=&apos;&apos; '> AND t0.create_time=#{createTime}</IF> " +
    //         "<IF test='createBy!=null and createBy!=&apos;&apos; '> AND t0.create_by=#{createBy}</IF> " +
    //         "<IF test='updateTime!=null and updateTime!=&apos;&apos; '> AND t0.update_time=#{updateTime}</IF> " +
    //         "<IF test='updateBy!=null and updateBy!=&apos;&apos; '> AND t0.update_by=#{updateBy}</IF> " +
    //         "<IF test='delFlag!=null and delFlag!=&apos;&apos; '> AND t0.del_flag=#{delFlag}</IF> " +
    //         "<IF test='remark!=null and remark!=&apos;&apos; '> AND t0.remark=#{remark}</IF> " +
    //         " </script>")
    // int countAll(OrderServiceWork queryParamDTO);

    /**
     * app获取待完成护理工单列表
     *
     * @param
     * @return
     */
    List<JSONObject> appCareTaskList(OrderServiceWork orderServiceWork);

    /**
     * 新增护理任务通知记录
     *
     * @param orderServiceWork 护理任务通知记录
     * @return 结果
     */
    public int insertOrderServiceWork(OrderServiceWork orderServiceWork);

    /**
     * 若“预约时间”为昨日以前，则更新工单状态为“已过期”
     */
    void updateWorkOrderState();
}
