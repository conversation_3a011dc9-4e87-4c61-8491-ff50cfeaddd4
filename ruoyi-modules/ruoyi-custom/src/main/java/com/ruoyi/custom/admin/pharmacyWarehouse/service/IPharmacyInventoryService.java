package com.ruoyi.custom.admin.pharmacyWarehouse.service;

import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyInventory;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyManagement;

import java.util.List;


/**
 * 药品库存Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IPharmacyInventoryService {
    /**
     * 查询药品库存
     *
     * @param id 药品库存主键
     * @return 药品库存
     */
    public PharmacyInventory selectPharmacyInventoryById(Long id);

    /**
     * 通过联合唯一索引药品库存
     *
     * @param warehouseId 仓库id
     * @param pharmacyId  药品id
     * @param batch       批次
     * @return 药品库存
     */
    public PharmacyInventory selectPharmacyInventoryByUnionId(Long warehouseId, Long pharmacyId, Integer batch);

    /**
     * 查询药品库存列表
     *
     * @param pharmacyInventory 药品库存
     * @return 药品库存集合
     */
    public List<PharmacyInventory> selectPharmacyInventoryList(PharmacyInventory pharmacyInventory);

    /**
     * 新增药品库存
     *
     * @param pharmacyInventory 药品库存
     * @return 结果
     */
    public int insertPharmacyInventory(PharmacyInventory pharmacyInventory);

    /**
     * 修改药品库存
     *
     * @param pharmacyInventory 药品库存
     * @return 结果
     */
    public int updatePharmacyInventory(PharmacyInventory pharmacyInventory);

    /**
     * 批量删除药品库存
     *
     * @param ids 需要删除的药品库存主键集合
     * @return 结果
     */
    public int deletePharmacyInventoryByIds(Long[] ids);

    /**
     * 删除药品库存信息
     *
     * @param id 药品库存主键
     * @return 结果
     */
    public int deletePharmacyInventoryById(Long id);

    /**
     * 查询药品库存列表, group by 药品名称
     *
     * @param pharmacyInventory
     * @return
     */
    List<PharmacyInventory> selectPharmacyInventoryList2(PharmacyInventory pharmacyInventory);

    /**
     * 更新库存
     *
     * @param id
     * @param quantity
     */
    void updateQuantity(Long id, Integer quantity);

    /**
     * 查询库存中存在的仓库列表
     *
     * @return
     */
    List<PharmacyInventory> getWarehouseListInInventory();

    /**
     * 根据仓库id获取库存中存在的药品列表
     * @param warehouseId
     * @return
     */
    List<PharmacyManagement> getPharmacyListInInventory(Long warehouseId);

    /**
     * 根据仓库id和药品id获取库存中存在的批次列表
     * @param warehouseId
     * @param pharmacyId
     * @return
     */
    List<Integer> getBatchList(Long warehouseId, Long pharmacyId);

    int getCurrQuantityById(Long inventoryId);
}

