package com.ruoyi.custom.utils;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;

/**
 * 使用poi-tl的Word文档导出工具类
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public class PoiTlWordExportUtil {
    
    /**
     * 根据模板导出Word文档
     * 
     * @param templatePath 模板路径
     * @param targetPath 目标文件路径
     * @param data 数据Map
     * @throws IOException IO异常
     */
    public static void exportWord(String templatePath, String targetPath, Map<String, Object> data) throws IOException {
        // 确保目标目录存在
        Path targetDir = Paths.get(targetPath).getParent();
        if (targetDir != null) {
            Files.createDirectories(targetDir);
        }
        
        // 使用poi-tl渲染模板
        XWPFTemplate template = XWPFTemplate.compile(templatePath).render(data);
        
        // 保存文档
        template.writeToFile(targetPath);
        
        // 关闭模板
        template.close();
    }

    /**
     * 根据模板导出Word文档（使用自定义配置）
     * 
     * @param templatePath 模板路径
     * @param targetPath 目标文件路径
     * @param data 数据Map
     * @param configure 自定义配置
     * @throws IOException IO异常
     */
    public static void exportWord(String templatePath, String targetPath, Map<String, Object> data, Configure configure) throws IOException {
        // 确保目标目录存在
        Path targetDir = Paths.get(targetPath).getParent();
        if (targetDir != null) {
            Files.createDirectories(targetDir);
        }
        
        // 使用poi-tl渲染模板（带自定义配置）
        XWPFTemplate template = XWPFTemplate.compile(templatePath, configure).render(data);
        
        // 保存文档
        template.writeToFile(targetPath);
        
        // 关闭模板
        template.close();
    }
} 