package com.ruoyi.custom.admin.fee.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("消费账户明细表")
public class ConsumeAccountDetail extends BaseEntity {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("老人ID")
    private String elderlyId;

    @ApiModelProperty("老人姓名")
    private String elderlyName;

    @ApiModelProperty("老人手机号")
    private String elderlyPhone;

    @ApiModelProperty("类型（1:离院退费，2:变更退费，3:变更支出，4:溢缴收入，5:缴费支出，6:账户充值）")
    private String type;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
