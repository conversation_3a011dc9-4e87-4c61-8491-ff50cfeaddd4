package com.ruoyi.custom.admin.marketing.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.bean.BeanValidators;
import com.ruoyi.custom.admin.marketing.domain.MarketingCustomerInfo;
import com.ruoyi.custom.admin.marketing.mapper.MarketingCustomerInfoMapper;
import com.ruoyi.custom.admin.marketing.service.MarketingCustomerInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 营销客户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@Service
@Slf4j
public class MarketingCustomerInfoServiceImpl implements MarketingCustomerInfoService {
    @Autowired
    private MarketingCustomerInfoMapper marketingCustomerInfoMapper;

    @Autowired
    private Validator validator;

    /**
     * 查询营销客户信息
     *
     * @param id 营销客户信息主键
     * @return 营销客户信息
     */
    @Override
    public MarketingCustomerInfo selectMarketingCustomerInfoById(Long id) {
        MarketingCustomerInfo data = marketingCustomerInfoMapper.selectMarketingCustomerInfoById(id);
        if (Objects.nonNull(data) && CollectionUtil.isNotEmpty(data.getFollowUpList())) {
            data.setFollowUpHistoryList(data.getFollowUpList().stream().filter(item -> "1".equals(item.getFollowUpStatus())).collect(Collectors.toList()));
            data.setFollowUpPlanList(data.getFollowUpList().stream().filter(item -> "0".equals(item.getFollowUpStatus())).collect(Collectors.toList()));
        }
        return data;
    }

    /**
     * 查询营销客户信息列表
     *
     * @param marketingCustomerInfo 营销客户信息
     * @return 营销客户信息
     */
    @Override
    public List<MarketingCustomerInfo> selectMarketingCustomerInfoList(MarketingCustomerInfo marketingCustomerInfo) {
        return marketingCustomerInfoMapper.selectMarketingCustomerInfoList(marketingCustomerInfo);
    }

    @Override
    public List<MarketingCustomerInfo> selectMarketingCustomerInfoList2(MarketingCustomerInfo marketingCustomerInfo) {
        return marketingCustomerInfoMapper.selectMarketingCustomerInfoList2(marketingCustomerInfo);
    }

    /**
     * 新增营销客户信息
     *
     * @param marketingCustomerInfo 营销客户信息
     * @return 结果
     */
    @Override
    public int insertMarketingCustomerInfo(MarketingCustomerInfo marketingCustomerInfo) {
        // 身份证若不为空需校验不能在表中已存在
        if (StrUtil.isNotBlank(marketingCustomerInfo.getIdCardNumber())) {
            MarketingCustomerInfo param = new MarketingCustomerInfo();
            param.setIdCardNumber(marketingCustomerInfo.getIdCardNumber());
            if (this.selectMarketingCustomerInfoList2(param).size() > 0) {
                throw new ServiceException("身份证号已存在");
            }
        }

        return marketingCustomerInfoMapper.insertMarketingCustomerInfo(marketingCustomerInfo);
    }

    /**
     * 修改营销客户信息
     *
     * @param marketingCustomerInfo 营销客户信息
     * @return 结果
     */
    @Override
    public int updateMarketingCustomerInfo(MarketingCustomerInfo marketingCustomerInfo) {
        // 身份证若不为空需校验不能在表中已存在，排除自己
        if (StrUtil.isNotBlank(marketingCustomerInfo.getIdCardNumber())) {
            MarketingCustomerInfo param = new MarketingCustomerInfo();
            param.setIdCardNumber(marketingCustomerInfo.getIdCardNumber());
            List<MarketingCustomerInfo> list = this.selectMarketingCustomerInfoList2(param);
            list.removeIf(item -> item.getId().equals(marketingCustomerInfo.getId()));
            if (list.size() > 0) {
                throw new ServiceException("身份证号码已存在");
            }
        }

        return marketingCustomerInfoMapper.updateMarketingCustomerInfo(marketingCustomerInfo);
    }

    /**
     * 批量删除营销客户信息
     *
     * @param ids 需要删除的营销客户信息主键
     * @return 结果
     */
    @Override
    public int deleteMarketingCustomerInfoByIds(Long[] ids) {
        return marketingCustomerInfoMapper.deleteMarketingCustomerInfoByIds(ids);
    }

    /**
     * 删除营销客户信息信息
     *
     * @param id 营销客户信息主键
     * @return 结果
     */
    @Override
    public int deleteMarketingCustomerInfoById(Long id) {
        return marketingCustomerInfoMapper.deleteMarketingCustomerInfoById(id);
    }

    @Override
    public int recover(Long[] ids) {
        return marketingCustomerInfoMapper.recover(ids);
    }

    @Override
    public List<MarketingCustomerInfo> unSignedList(MarketingCustomerInfo marketingCustomerInfo) {
        return marketingCustomerInfoMapper.selectMarketingCustomerInfoList(marketingCustomerInfo);
    }

    @Override
    public String importData(List<MarketingCustomerInfo> list, boolean updateSupport) {
        if (StringUtils.isNull(list) || list.isEmpty()) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (MarketingCustomerInfo customer : list) {
            try {
                // 通用验证
                BeanValidators.validateWithException(validator, customer);
                this.insertMarketingCustomerInfo(customer);
                successNum++;
                successMsg.append("<br/>" + successNum + "、咨询人：" + customer.getConsultantName() + "；手机号：" + customer.getConsultantPhone() + " 导入成功");
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、咨询人：" + customer.getConsultantName() + "；手机号：" + customer.getConsultantPhone() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public Long findIdByName(String marketerName) {
        return marketingCustomerInfoMapper.findIdByName(marketerName);
    }

    @Override
    public Long selectMarketingCustomerInfoCount(MarketingCustomerInfo consultationCustomer) {
        return marketingCustomerInfoMapper.selectMarketingCustomerInfoCount(consultationCustomer);
    }

    @Override
    public void updateMarketingCustomerInfo2(MarketingCustomerInfo marketingCustomerInfo1) {
        marketingCustomerInfoMapper.updateMarketingCustomerInfo2(marketingCustomerInfo1);
    }
}

