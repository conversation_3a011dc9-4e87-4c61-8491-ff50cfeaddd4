package com.ruoyi.custom.api.adminApp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.liveManage.domain.req.LiveInfoSaveReqVO;
import com.ruoyi.custom.admin.liveManage.service.ILiveBaseInfoService;
import com.ruoyi.custom.admin.marketing.domain.MarketingCustomerInfo;
import com.ruoyi.custom.admin.marketing.service.MarketingCustomerInfoService;
import com.ruoyi.custom.admin.storiedBuilding.service.IBedBaseInfoService;
import com.ruoyi.custom.api.adminApp.rep.BedStateDataResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 管理端APP-居住 Controller
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
@RestController("adminAppLiveController")
@RequestMapping("/adminApp/live")
@Api(tags = "管理端APP-居住")
public class LiveController extends BaseController {

    @Autowired
    private IBedBaseInfoService bedBaseInfoService;

    @Autowired
    private ILiveBaseInfoService liveBaseInfoService;

    @Autowired
    private MarketingCustomerInfoService marketingCustomerInfoService;

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    /**
     * 房态图数据
     */
    @GetMapping("/building/getBedStateData")
    @ApiOperation(value = "房态图数据")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "bedState", value = "occupied: 已入住；unoccupied: 未入住；", required = false, dataTypeClass = String.class),
    })
    public AjaxResult getBedStateData(@ApiIgnore String bedState) {
        // 获取房态图数据
        List<BedStateDataResp> list = bedBaseInfoService.getBedStateData2(bedState);
        return AjaxResult.success(list);
    }

    /**
     * 获取总床位数量、空闲床位数量、入住床位数量
     */
    @GetMapping("/getBedCountSummary")
    @ApiOperation(value = "获取总床位数量、空闲床位数量、入住床位数量")
    public AjaxResult getBedCountSummary() {
        JSONObject data = bedBaseInfoService.getBedCountSummary(MapUtil.empty());
        return AjaxResult.success(data);
    }

    /**
     * 老人入住办理
     */
    @ApiOperation(value = "老人入住办理")
    //@RequiresPermissions("liveManage:baseinfo:add")
    @Log(platform = "1", title = "老人入住办理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LiveInfoSaveReqVO reqVO) {
        return toAjax(liveBaseInfoService.add(reqVO));
    }

    /**
     * 意向客户入住办理
     */
    @ApiOperation(value = "意向客户入住办理")
    @Log(platform = "1", title = "意向客户入住办理", businessType = BusinessType.INSERT)
    @PostMapping("/checkIn")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult checkIn(@RequestBody LiveInfoSaveReqVO reqVO) {
        Long marketingCustomerId = Long.valueOf(reqVO.getUserId());
        // 查询营销客户信息
        MarketingCustomerInfo marketingCustomerInfo = marketingCustomerInfoService.selectMarketingCustomerInfoById(marketingCustomerId);

        // 修改营销客户“是否直接入住状态”
        MarketingCustomerInfo param = new MarketingCustomerInfo();
        param.setId(marketingCustomerId);
        param.setDirectCheckIn("1");
        marketingCustomerInfoService.updateMarketingCustomerInfo(param);

        // 复制营销老人信息 -》 老人基本信息表
        ElderlyPeopleInfo elderlyPeopleInfo = new ElderlyPeopleInfo();
        elderlyPeopleInfo.setName(marketingCustomerInfo.getElderName());
        elderlyPeopleInfo.setSex(marketingCustomerInfo.getElderGender());
        elderlyPeopleInfo.setIdCardNum(marketingCustomerInfo.getIdCardNumber());
        elderlyPeopleInfo.setPhone(marketingCustomerInfo.getElderPhone());
        elderlyPeopleInfo.setNation(marketingCustomerInfo.getNation());
        elderlyPeopleInfo.setMarriageStatus(marketingCustomerInfo.getMaritalStatus());
        elderlyPeopleInfo.setLivingSituation(marketingCustomerInfo.getResidenceStatus());
        elderlyPeopleInfo.setHomeAddress(marketingCustomerInfo.getHomeAddress());
        elderlyPeopleInfo.setCustomerId(marketingCustomerId);
        elderlyPeopleInfoService.insertElderlyPeopleInfo(elderlyPeopleInfo);

        // 入住
        reqVO.setUserId(elderlyPeopleInfo.getId());
        return toAjax(liveBaseInfoService.add(reqVO));
    }
}
