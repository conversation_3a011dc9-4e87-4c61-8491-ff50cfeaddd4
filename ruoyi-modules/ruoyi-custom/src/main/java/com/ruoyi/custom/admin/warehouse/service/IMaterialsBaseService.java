package com.ruoyi.custom.admin.warehouse.service;

import java.util.List;

import cn.hutool.json.JSONObject;
import com.ruoyi.custom.admin.warehouse.domain.MaterialsBase;
import com.ruoyi.custom.admin.warehouse.domain.vo.MaterialsBaseVo;

/**
 * 物资基础信息Service接口
 *
 * <AUTHOR>
 * @date 2022-05-06
 */
public interface IMaterialsBaseService {
    /**
     * 查询物资基础信息
     *
     * @param id 物资基础信息主键
     * @return 物资基础信息
     */
    public MaterialsBase selectMaterialsBaseById(Long id);

    /**
     * 查询物资基础信息列表
     *
     * @param materialsBase 物资基础信息
     * @return 物资基础信息集合
     */
    public List<MaterialsBase> selectMaterialsBaseList(MaterialsBase materialsBase);

    /**
     * 新增物资基础信息
     *
     * @param materialsBase 物资基础信息
     * @return 结果
     */
    public int insertMaterialsBase(MaterialsBase materialsBase);

    /**
     * 修改物资基础信息
     *
     * @param materialsBase 物资基础信息
     * @return 结果
     */
    public int updateMaterialsBase(MaterialsBase materialsBase);

    /**
     * 批量删除物资基础信息
     *
     * @param ids 需要删除的物资基础信息主键集合
     * @return 结果
     */
    public int deleteMaterialsBaseByIds(Long[] ids);

    /**
     * 删除物资基础信息信息
     *
     * @param id 物资基础信息主键
     * @return 结果
     */
    public int deleteMaterialsBaseById(Long id);


    /**
     * 查询物资基础信息列表
     *
     * @param vo
     * @return
     */
    List<MaterialsBaseVo> getMaterialsStockList(MaterialsBaseVo vo);

    /**
     * 获取物资下拉选项
     *
     * @return
     */
    public List<JSONObject> getMaterialsList(Long warehouseId);

    /**
     * 盘点模块-选择仓库之后出现列表
     *
     * @param warehouseId
     * @return
     */
    List<JSONObject> getMaterialsListByWarehouseId(Long warehouseId);
}
