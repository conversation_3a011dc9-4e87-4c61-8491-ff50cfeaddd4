package com.ruoyi.custom.admin.assessment.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.custom.admin.assessment.domain.NursingPlanTemplate;
import com.ruoyi.custom.admin.assessment.service.INursingPlanTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 评估护理计划模版Controller
 *
 * <AUTHOR>
 * @date 2024-07-17
 */
@RestController
@RequestMapping("/assessment/nursingPlanTemplate")
@Api(value = "评估护理计划模版接口", tags = "评估护理计划模版接口")
public class NursingPlanTemplateController extends BaseController {
    @Autowired
    private INursingPlanTemplateService nursingPlanTemplateService;

    /**
     * 查询评估护理计划模版列表
     */
    // @RequiresPermissions("custom:nursingPlanTemplate:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询评估护理计划模版列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "healthProblem", value = "健康问题", paramType = "query"),
        @ApiImplicitParam(name = "templateType", value = "模版类型", paramType = "query"),
    })
    public TableDataInfo list(NursingPlanTemplate nursingPlanTemplate) {
        startPage();
        List<NursingPlanTemplate> list = nursingPlanTemplateService.selectNursingPlanTemplateList(nursingPlanTemplate);
        return getDataTable(list);
    }

    // /**
    //  * 导出评估护理计划模版列表
    //  */
    // // @RequiresPermissions("custom:nursingPlanTemplate:export")
    // @Log(title = "评估护理计划模版", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // @ApiOperation(value = "导出评估护理计划模版列表")
    // public void export(HttpServletResponse response, NursingPlanTemplate nursingPlanTemplate) {
    //     List<NursingPlanTemplate> list = nursingPlanTemplateService.selectNursingPlanTemplateList(nursingPlanTemplate);
    //     ExcelUtil<NursingPlanTemplate> util = new ExcelUtil<NursingPlanTemplate>(NursingPlanTemplate.class);
    //     util.exportExcel(response, list, "评估护理计划模版数据");
    // }

    /**
     * 获取评估护理计划模版详细信息
     */
    // @RequiresPermissions("custom:nursingPlanTemplate:query")
    @GetMapping("/{id}")
    @ApiOperation(value = "获取评估护理计划模版详细信息")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(nursingPlanTemplateService.selectNursingPlanTemplateById(id));
    }

    /**
     * 新增评估护理计划模版
     */
    // @RequiresPermissions("custom:nursingPlanTemplate:add")
    @Log(title = "评估护理计划模版", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增评估护理计划模版")
    public AjaxResult add(@RequestBody NursingPlanTemplate nursingPlanTemplate) {
        return toAjax(nursingPlanTemplateService.insertNursingPlanTemplate(nursingPlanTemplate));
    }

    /**
     * 修改评估护理计划模版
     */
    // @RequiresPermissions("custom:nursingPlanTemplate:edit")
    @Log(title = "评估护理计划模版", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改评估护理计划模版")
    public AjaxResult edit(@RequestBody NursingPlanTemplate nursingPlanTemplate) {
        return toAjax(nursingPlanTemplateService.updateNursingPlanTemplate(nursingPlanTemplate));
    }

    /**
     * 删除评估护理计划模版
     */
    // @RequiresPermissions("custom:nursingPlanTemplate:remove")
    @Log(title = "评估护理计划模版", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除评估护理计划模版")
    public AjaxResult remove(@PathVariable("ids") String[] ids) {
        return toAjax(nursingPlanTemplateService.deleteNursingPlanTemplateByIds(ids));
    }

    /**
     * 更新部件内容
     */
    // @RequiresPermissions("custom:nursingPlanTemplate:edit")
    @Log(title = "评估护理计划模版-更新部件内容", businessType = BusinessType.UPDATE)
    @PutMapping("/section/{templateType}/{section}/{id}")
    @ApiOperation(value = "更新部件内容")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "templateType", value = "模版类型：1-标准护理计划，2-简化护理计划", paramType = "path", required = true),
        @ApiImplicitParam(name = "section", value = "部件名称。类型1支持：cause,feature,goal,measure,evaluation；类型2支持：cause,expectedGoal,nursingDisposition", paramType = "path", required = true),
        @ApiImplicitParam(name = "id", value = "模版ID", paramType = "path", required = true)
    })
    public AjaxResult updateSectionContent(@PathVariable("templateType") Integer templateType, @PathVariable("section") String section, @PathVariable("id") String id, @RequestBody Map<String,String> content) {
        return toAjax(nursingPlanTemplateService.updateSectionContent(id, templateType, section, content));
    }

    /**
     * 查看部件内容
     */
    // @RequiresPermissions("custom:nursingPlanTemplate:query")
    @GetMapping("/section/{templateType}/{section}/{id}")
    @ApiOperation(value = "查看部件内容")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "templateType", value = "模版类型：1-标准护理计划，2-简化护理计划", paramType = "path", required = true),
        @ApiImplicitParam(name = "section", value = "部件名称。类型1支持：cause,feature,goal,measure,evaluation；类型2支持：cause,expectedGoal,nursingDisposition", paramType = "path", required = true),
        @ApiImplicitParam(name = "id", value = "模版ID", paramType = "path", required = true)
    })
    public AjaxResult getSectionContent(@PathVariable("templateType") Integer templateType, @PathVariable("section") String section, @PathVariable("id") String id) {
        return AjaxResult.success(nursingPlanTemplateService.getSectionContent(id, templateType, section));
    }
}
