package com.ruoyi.custom.admin.version.domain;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 版本信息 DO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "版本信息")
public class VersionInfo {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String versionNumber;
    /**
     * 说明
     */
    @ApiModelProperty(value = "说明")
    private String description;
    /**
     * app名称
     */
    @ApiModelProperty(value = "app名称")
    private String appName;
    /**
     * app下载地址
     */
    @ApiModelProperty(value = "app下载地址")
    private String appDownloadUrl;
    /**
     * 发布日期
     */
    @ApiModelProperty(value = "发布日期")
    private Date releaseDate;
    /**
     * 状态， 字典：custom_version_status，0：暂停中，1：使用中
     */
    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    @ApiModelProperty(value = "删除标志")
    private String delFlag;
}

