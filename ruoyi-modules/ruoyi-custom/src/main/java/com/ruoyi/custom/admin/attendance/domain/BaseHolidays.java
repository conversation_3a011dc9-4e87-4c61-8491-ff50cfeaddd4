package com.ruoyi.custom.admin.attendance.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * 法定节假日 base_holidays
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@Data
@TableName("base_holidays")
@ApiModel("法定节假日对象")
public class BaseHolidays {
    private static final long serialVersionUID = 6107378728856609694L;

    /**
     * 法定节假日ID
     */
    @TableId(value = "id", type = com.baomidou.mybatisplus.annotation.IdType.AUTO)
    private Integer id;

    /**
     * 法定节假日
     */
    private Date holidays;

    /**
     * 状态，0：停用，1：启用
     */
    private Integer state;

    /**
     * 备注
     */
    private String remarks;
}
