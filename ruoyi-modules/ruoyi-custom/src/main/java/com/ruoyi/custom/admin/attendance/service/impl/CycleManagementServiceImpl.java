package com.ruoyi.custom.admin.attendance.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.custom.admin.attendance.domain.CycleManagement;
import com.ruoyi.custom.admin.attendance.domain.SchedulingGroup;
import com.ruoyi.custom.admin.attendance.mapper.CycleManagementMapper;
import com.ruoyi.custom.admin.attendance.mapper.SchedulingGroupMapper;
import com.ruoyi.custom.admin.attendance.service.CycleManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 周期管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@Service
public class CycleManagementServiceImpl extends MPJBaseServiceImpl<CycleManagementMapper, CycleManagement> implements CycleManagementService {
    @Autowired
    private CycleManagementMapper cycleManagementMapper;
    @Autowired
    private SchedulingGroupMapper schedulingGroupMapper;

    /**
     * 查询周期管理
     *
     * @param id 周期管理主键
     * @return 周期管理
     */
    @Override
    public CycleManagement selectCycleManagementById(Integer id) {
        CycleManagement cycleManagement = cycleManagementMapper.selectCycleManagementById(id);
        cycleManagement.setShiftIdList(Arrays.stream(cycleManagement.getShiftIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()));
        return cycleManagement;
    }

    /**
     * 查询周期管理列表
     *
     * @param cycleManagement 周期管理
     * @return 周期管理
     */
    @Override
    public List<CycleManagement> selectCycleManagementList(CycleManagement cycleManagement) {
        List<CycleManagement> cycleManagements = cycleManagementMapper.selectCycleManagementList(cycleManagement);
        cycleManagements.stream().forEach(a -> {
            a.setShiftIdList(Arrays.stream(a.getShiftIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()));
        });
        return cycleManagements;
    }

    /**
     * 新增周期管理
     *
     * @param cycleManagement 周期管理
     * @return 结果
     */
    @Override
    public int insertCycleManagement(CycleManagement cycleManagement) {
        cycleManagement.setShiftIds(String.join(",", cycleManagement.getShiftIdList().stream().map(String::valueOf).collect(Collectors.toList())));
        return cycleManagementMapper.insertCycleManagement(cycleManagement);
    }

    /**
     * 修改周期管理
     *
     * @param cycleManagement 周期管理
     * @return 结果
     */
    @Override
    public int updateCycleManagement(CycleManagement cycleManagement) {
        cycleManagement.setShiftIds(String.join(",", cycleManagement.getShiftIdList().stream().map(String::valueOf).collect(Collectors.toList())));
        return cycleManagementMapper.updateCycleManagement(cycleManagement);
    }

    /**
     * 批量删除周期管理
     *
     * @param ids 需要删除的周期管理主键
     * @return 结果
     */
    @Override
    public int deleteCycleManagementByIds(Integer[] ids) {
        // 查询当前周期管理是否被排班分组引用,如果被引用则无法删除
        QueryWrapper<SchedulingGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("cycle_id", ids);
        List<SchedulingGroup> schedulingGroups = schedulingGroupMapper.selectList(queryWrapper);
        if (schedulingGroups.size() > 0) {
            throw new RuntimeException("当前周期管理已被排班分组引用，无法删除");
        }
        return cycleManagementMapper.deleteCycleManagementByIds(ids);
    }

    /**
     * 删除周期管理信息
     *
     * @param id 周期管理主键
     * @return 结果
     */
    @Override
    public int deleteCycleManagementById(Integer id) {
        return cycleManagementMapper.deleteCycleManagementById(id);
    }
}
