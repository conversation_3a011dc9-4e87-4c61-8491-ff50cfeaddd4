package com.ruoyi.custom.admin.liveManage.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 居住套餐记录对象 t_live_combo_records
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@Data
public class LiveComboRecords extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 关联主表id
     */
    @Excel(name = "关联主表id")
    private String liveId;

    /**
     * 护理级别
     */
    @Excel(name = "护理级别")
    @ApiModelProperty(value = "护理级别", name = "careLevel", required = true, example = "111")
    private String careLevel;

    /**
     * 套餐id
     */
    @Excel(name = "套餐id")
    @ApiModelProperty(value = "套餐id", name = "comboId", required = true, example = "222")
    private String comboId;

    /**
     * 套餐版本
     */
    @Excel(name = "套餐版本")
    @ApiModelProperty(value = "套餐版本", name = "comboVersion", required = true, example = "222")
    private String comboVersion;

    /**
     * 状态（在用，已变更，终止）
     */
    @Excel(name = "状态", readConverterExp = "在用，已变更，终止")
    private String state;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

}
