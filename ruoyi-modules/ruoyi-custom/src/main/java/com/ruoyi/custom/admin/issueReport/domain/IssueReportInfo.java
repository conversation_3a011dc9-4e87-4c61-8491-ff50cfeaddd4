package com.ruoyi.custom.admin.issueReport.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 问题上报信息对象 t_issue_report_info
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Data
@ApiModel(value = "问题上报信息")
public class IssueReportInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 所在区域
     */
    @Excel(name = "所在区域")
    @ApiModelProperty(value = "所在区域")
    private String area;

    /**
     * 上报人id，对应t_care_worker_info表
     */
    @ApiModelProperty(value = "上报人id，对应t_care_worker_info表")
    private Long reporterId;

    /**
     * 上报人
     */
    @Excel(name = "上报人")
    @ApiModelProperty(value = "上报人")
    private String reporterName;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上报时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "上报时间")
    private Date reportTime;

    /**
     * 紧急程度，字典：custom_issue_report_info_urgency_level；1：一般，2：重要，3：紧急
     */
    @Excel(name = "紧急程度", readConverterExp = "1=一般,2=重要,3=紧急")
    @ApiModelProperty(value = "紧急程度，字典：custom_issue_report_info_urgency_level；1：一般，2：重要，3：紧急")
    private String urgencyLevel;

    /**
     * 状态，字典：common_deal_status；0：未处理，1：已处理
     */
    @Excel(name = "状态", readConverterExp = "0=未处理,1=已处理")
    @ApiModelProperty(value = "状态，字典：common_deal_status；0：未处理，1：已处理")
    private String status;

    /**
     * 问题描述
     */
    @Excel(name = "问题描述")
    @ApiModelProperty(value = "问题描述")
    private String issueDescription;

    /**
     * 处理结果
     */
    @Excel(name = "处理结果")
    @ApiModelProperty(value = "处理结果")
    private String resolution;
}

