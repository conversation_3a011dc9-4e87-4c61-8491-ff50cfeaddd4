package com.ruoyi.custom.admin.service.mapper;

import com.ruoyi.custom.admin.service.domain.UserValueAddedService;
import com.ruoyi.custom.admin.service.domain.UserValueAddedServiceBill;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * 用户增值服务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface UserValueAddedServiceMapper {
    /**
     * 查询用户增值服务
     *
     * @param id 用户增值服务主键
     * @return 用户增值服务
     */
    public UserValueAddedService selectUserValueAddedServiceById(Long id);

    /**
     * 查询用户增值服务列表
     *
     * @param userValueAddedService 用户增值服务
     * @return 用户增值服务集合
     */
    public List<UserValueAddedService> selectUserValueAddedServiceList(UserValueAddedService userValueAddedService);

    /**
     * 新增用户增值服务
     *
     * @param userValueAddedService 用户增值服务
     * @return 结果
     */
    public int insertUserValueAddedService(UserValueAddedService userValueAddedService);

    /**
     * 修改用户增值服务
     *
     * @param userValueAddedService 用户增值服务
     * @return 结果
     */
    public int updateUserValueAddedService(UserValueAddedService userValueAddedService);

    /**
     * 删除用户增值服务
     *
     * @param id 用户增值服务主键
     * @return 结果
     */
    public int deleteUserValueAddedServiceById(Long id);

    /**
     * 批量删除用户增值服务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserValueAddedServiceByIds(Long[] ids);

    /**
     * 新增用户增值服务账单
     *
     * @param monthlyBill 用户增值服务账单
     * @return 影响行数
     */
    int insertUserValueAddedServiceBill(UserValueAddedServiceBill monthlyBill);

    /**
     * 批量新增用户增值服务账单
     *
     * @param billList 用户增值服务账单
     */
    void batchInsertUserValueAddedServiceBill(@Param("list") List<UserValueAddedServiceBill> billList);

    /**
     * 查询用户增值服务账单
     *
     * @param params 查询参数
     * @return 用户增值服务账单
     */
    List<UserValueAddedServiceBill> selectUserValueAddedServiceBill(Map<String, Object> params);

    /**
     * 批量更新用户增值服务账单
     *
     * @param billList 用户增值服务账单
     */
    void batchUpdateUserValueAddedServiceBill(@Param("list") List<UserValueAddedServiceBill> billList);
}

