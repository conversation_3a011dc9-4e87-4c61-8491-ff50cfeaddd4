package com.ruoyi.custom.admin.storiedBuilding.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeBaseInfo;
import com.ruoyi.custom.admin.storiedBuilding.domain.RoomTypeIndexInfo;
import com.ruoyi.custom.admin.storiedBuilding.domain.StoriedBuildingInfo;
import com.ruoyi.custom.admin.storiedBuilding.domain.TreeSelect;
import com.ruoyi.custom.admin.storiedBuilding.mapper.RoomTypeBaseInfoMapper;
import com.ruoyi.custom.admin.storiedBuilding.mapper.RoomTypeIndexInfoMapper;
import com.ruoyi.custom.admin.storiedBuilding.mapper.StoriedBuildingInfoMapper;
import com.ruoyi.custom.admin.storiedBuilding.service.IBedBaseInfoService;
import com.ruoyi.custom.admin.storiedBuilding.service.IStoriedBuildingInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 楼栋信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-29
 */
@Service
public class StoriedBuildingInfoServiceImpl implements IStoriedBuildingInfoService {
    @Autowired
    private StoriedBuildingInfoMapper storiedBuildingInfoMapper;
    @Autowired
    private RoomTypeBaseInfoMapper roomTypeBaseInfoMapper;
    @Autowired
    private RoomTypeIndexInfoMapper roomTypeIndexInfoMapper;
    @Autowired
    private IBedBaseInfoService bedBaseInfoService;

    /**
     * 查询楼栋信息
     *
     * @param id 楼栋信息主键
     * @return 楼栋信息
     */
    @Override
    public StoriedBuildingInfo selectStoriedBuildingInfoById(Long id) {
        StoriedBuildingInfo info = storiedBuildingInfoMapper.selectStoriedBuildingInfoById(id);
        if (null != info) {
            if (!"3".equals(info.getType())) {
                StoriedBuildingInfo totalNum = getTotalNum(info.getId());
                info.setTotalBedNumber(totalNum.getTotalBedNumber());
                info.setTotalFloorsNumber(totalNum.getTotalFloorsNumber());
                info.setTotalRoomsNumber(totalNum.getTotalRoomsNumber());
            }
        }
        return info;
    }

    @Override
    public List<StoriedBuildingInfo> getInfoByPid(Long pid) {
        List<StoriedBuildingInfo> infoByPid = storiedBuildingInfoMapper.getInfoByPid(pid);
        return infoByPid;
    }

    /**
     * 查询楼栋信息列表
     *
     * @param storiedBuildingInfo 楼栋信息
     * @return 楼栋信息
     */
    @Override
    public List<StoriedBuildingInfo> selectStoriedBuildingInfoList(StoriedBuildingInfo storiedBuildingInfo) {
        List<StoriedBuildingInfo> infoList = storiedBuildingInfoMapper.selectStoriedBuildingInfoList(storiedBuildingInfo);
        if (infoList.size() > 0) {
            for (StoriedBuildingInfo info : infoList) {
                if (!"3".equals(info.getType())) {
                    StoriedBuildingInfo totalNum = getTotalNum(info.getId());
                    info.setTotalBedNumber(totalNum.getTotalBedNumber());
                    info.setTotalFloorsNumber(totalNum.getTotalFloorsNumber());
                    info.setTotalRoomsNumber(totalNum.getTotalRoomsNumber());
                    // info.setOccupancyNumber(totalNum.getOccupancyNumber());
                }
            }
        }

        return infoList;
    }

    /**
     * 新增楼栋信息
     *
     * @param storiedBuildingInfo 楼栋信息
     * @return 结果
     */
    @Override
    public int insertStoriedBuildingInfo(StoriedBuildingInfo storiedBuildingInfo) {
        StoriedBuildingInfo info = storiedBuildingInfoMapper.selectStoriedBuildingInfoById(storiedBuildingInfo.getParentId());
        storiedBuildingInfo.setCreateTime(DateUtils.getNowDate());
        storiedBuildingInfo.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
        // 是否有父级：如果没有直接父级列表为0
        if (null != info) {
            // 看父级是否有父级列表：如果有就前面加上父级的父级列表
            if (!"".equals(info.getAncestors())) {
                storiedBuildingInfo.setAncestors(info.getAncestors() + storiedBuildingInfo.getParentId() + ",");
            } else {
                storiedBuildingInfo.setAncestors(storiedBuildingInfo.getParentId() + ",");
            }

        } else {
            storiedBuildingInfo.setAncestors("0,");
            storiedBuildingInfo.setParentId(Long.parseLong("0"));
        }
        // 类型3为房间：然后根据房间的类型来添加床位和床位数
        if ("3".equals(storiedBuildingInfo.getType())) {
            // 获取房间类型数据
            RoomTypeBaseInfo baseInfo = roomTypeBaseInfoMapper.selectRoomTypeBaseInfoById(Long.parseLong(storiedBuildingInfo.getRoomType()));
            // 保存之后拿到房间id
            storiedBuildingInfoMapper.insertStoriedBuildingInfo(storiedBuildingInfo);
            storiedBuildingInfo.setTotalBedNumber(baseInfo.getBedNumber());
            RoomTypeIndexInfo typeInfo = new RoomTypeIndexInfo();
            typeInfo.setRoomId(storiedBuildingInfo.getId());
            typeInfo.setTypeId(baseInfo.getId());
            typeInfo.setStatus("0");
            typeInfo.setTypeVersion(baseInfo.getVersion());
            typeInfo.setTypeVersionId(baseInfo.getTypeVersionId());
            typeInfo.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
            typeInfo.setCreateTime(DateUtils.getNowDate());
            // 保存房间和类型的关联数据
            return roomTypeIndexInfoMapper.insertRoomTypeIndexInfo(typeInfo);
        } else {
            // 保存楼栋信息
            return storiedBuildingInfoMapper.insertStoriedBuildingInfo(storiedBuildingInfo);
        }

    }

    /**
     * 修改楼栋信息
     *
     * @param storiedBuildingInfo 楼栋信息
     * @return 结果
     */
    @Override
    public int updateStoriedBuildingInfo(StoriedBuildingInfo storiedBuildingInfo) {
        storiedBuildingInfo.setUpdateTime(DateUtils.getNowDate());
        storiedBuildingInfo.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
        StoriedBuildingInfo info = storiedBuildingInfoMapper.selectStoriedBuildingInfoById(storiedBuildingInfo.getParentId());
        // 是否有父级：如果没有直接父级列表为0
        if (null != info) {
            // 看父级是否有父级列表：如果有就前面加上父级的父级列表
            if (!"".equals(info.getAncestors())) {
                storiedBuildingInfo.setAncestors(info.getAncestors() + storiedBuildingInfo.getParentId() + ",");
            } else {
                storiedBuildingInfo.setAncestors(storiedBuildingInfo.getParentId() + ",");
            }

        } else {
            storiedBuildingInfo.setAncestors("0,");
        }
        // 类型3为房间：然后根据房间的类型来添加床位和床位数
        if ("3".equals(storiedBuildingInfo.getType())) {
            // 获取房间类型数据数据
            RoomTypeBaseInfo baseInfo = roomTypeBaseInfoMapper.selectRoomTypeBaseInfoById(Long.parseLong(storiedBuildingInfo.getRoomType()));
            // 根据房间id修改关联数据状态
            RoomTypeIndexInfo roomTypeIndexInfo = roomTypeIndexInfoMapper.selectRoomTypeIndexInfoByRoomId(storiedBuildingInfo.getId());
            if (null != roomTypeIndexInfo) {
                roomTypeIndexInfo.setStatus("1");
                roomTypeIndexInfo.setEndTime(DateUtils.getNowDate());
                roomTypeIndexInfoMapper.updateRoomTypeIndexInfo(roomTypeIndexInfo);
            }
            // 创建房间和类型的实体类进行保存
            RoomTypeIndexInfo typeInfo = new RoomTypeIndexInfo();
            typeInfo.setRoomId(storiedBuildingInfo.getId());
            typeInfo.setTypeId(baseInfo.getId());
            typeInfo.setTypeVersion(baseInfo.getVersion());
            typeInfo.setTypeVersionId(baseInfo.getTypeVersionId());
            typeInfo.setStatus("0");
            typeInfo.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
            typeInfo.setCreateTime(DateUtils.getNowDate());
            // 保存房间和类型的关联数据
            roomTypeIndexInfoMapper.insertRoomTypeIndexInfo(typeInfo);
        }
        // 保存楼栋信息
        return storiedBuildingInfoMapper.updateStoriedBuildingInfo(storiedBuildingInfo);
    }

    @Override
    public int updateStoriedBedNum(Long id, Long totalBedNumber, Long occupancyNumber) {
        return storiedBuildingInfoMapper.updateStoriedBedNum(id, totalBedNumber, occupancyNumber);
    }


    /**
     * 批量删除楼栋信息
     *
     * @param ids 需要删除的楼栋信息主键
     * @return 结果
     */
    @Override
    public int deleteStoriedBuildingInfoByIds(Long[] ids) {
        return storiedBuildingInfoMapper.deleteStoriedBuildingInfoByIds(ids);
    }

    /**
     * 删除楼栋信息信息
     *
     * @param id 楼栋信息主键
     * @return 结果
     */
    @Override
    public int deleteStoriedBuildingInfoById(Long id) {
        return storiedBuildingInfoMapper.deleteStoriedBuildingInfoById(id);
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildBuildingTreeSelect(List<StoriedBuildingInfo> storiedBuildingInfos) {
        List<StoriedBuildingInfo> trees = buildTree(storiedBuildingInfos);
        return trees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildBuildingTreeSelectList(List<StoriedBuildingInfo> storiedBuildingInfos) {
        List<StoriedBuildingInfo> trees = buildTree(storiedBuildingInfos);
        List<TreeSelect> collect = trees.stream().map(TreeSelect::new).collect(Collectors.toList());
        for (int i = 0; i < collect.size(); i++) {
            TreeSelect treeSelect = collect.get(i);
            if (null != treeSelect.getChildren()) {
                // 取出全部楼层
                for (TreeSelect list : treeSelect.getChildren()) {
                    if (null != list.getChildren()) {
                        // 取出全部全部房间
                        for (TreeSelect data : list.getChildren()) {
                            if ("3".equals(data.getType())) {
                                List<JSONObject> bedList = bedBaseInfoService.getBedNameByRoomId(data.getId());
                                if (bedList.size() > 0) {
                                    List<TreeSelect> treeSelects = JSONUtil.toList(bedList.toString(), TreeSelect.class);
                                    data.setChildren(treeSelects);
                                }
                            }
                        }
                    }
                }
            }
        }
        return collect;
    }

    /**
     * 构建前端所需要树结构
     *
     * @param
     * @return 树结构列表
     */
    @Override
    public List<StoriedBuildingInfo> buildTree(List<StoriedBuildingInfo> storiedBuildingInfos) {
        List<StoriedBuildingInfo> returnList = new ArrayList<StoriedBuildingInfo>();
        List<Long> tempList = new ArrayList<Long>();
        for (StoriedBuildingInfo storiedBuildingInfo : storiedBuildingInfos) {
            tempList.add(storiedBuildingInfo.getId());
        }
        for (StoriedBuildingInfo storiedBuildingInfo : storiedBuildingInfos) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(storiedBuildingInfo.getParentId())) {
                recursionFn(storiedBuildingInfos, storiedBuildingInfo);
                returnList.add(storiedBuildingInfo);
            }
        }
        if (returnList.isEmpty()) {
            returnList = storiedBuildingInfos;
        }
        return returnList;
    }


    /**
     * 递归列表
     */
    private void recursionFn(List<StoriedBuildingInfo> list, StoriedBuildingInfo t) {
        // 得到子节点列表
        List<StoriedBuildingInfo> childList = getChildList(list, t);
        t.setChildren(childList);
        for (StoriedBuildingInfo tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<StoriedBuildingInfo> getChildList(List<StoriedBuildingInfo> list, StoriedBuildingInfo t) {
        List<StoriedBuildingInfo> tlist = new ArrayList<StoriedBuildingInfo>();
        Iterator<StoriedBuildingInfo> it = list.iterator();
        while (it.hasNext()) {
            StoriedBuildingInfo n = (StoriedBuildingInfo) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }


    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<StoriedBuildingInfo> list, StoriedBuildingInfo t) {
        return getChildList(list, t).size() > 0 ? true : false;
    }

    /**
     * 是否有子节点
     *
     * @param id
     * @return
     */
    @Override
    public Boolean hasChildById(Long id) {
        int result = storiedBuildingInfoMapper.hasChildById(id);
        return result > 0 ? true : false;
    }

    /**
     * 根据类型和id懒加载
     *
     * @param type
     * @param id
     * @return
     */
    @Override
    public List<JSONObject> getTreeData(String type, String id) {
        List<JSONObject> jsonList = new ArrayList<>();
        if (StringUtils.isBlank(id)) {
            id = "0";
        }
        // 判断是否是床位
        if ("4".equals(type)) {
            jsonList = bedBaseInfoService.getBedInfo(Long.parseLong(id));
        } else {
            jsonList = storiedBuildingInfoMapper.getBuildingInfoList(type, id);
        }
        // 判断是否有下级
        if (jsonList.size() > 0) {
            for (JSONObject data : jsonList) {
                Long pid = data.getLong("id");
                if ("3".equals(type)) {
                    data.set("isLeaf", !bedBaseInfoService.hasBedByRoomId(pid));
                } else if ("4".equals(type)) {
                    data.set("isLeaf", true);
                } else {
                    data.set("isLeaf", !hasChildById(pid));
                }
            }
        }
        return jsonList;
    }

    /**
     * 导入用户数据
     *
     * @param list            用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    @Transactional
    public String importData(List<JSONObject> list, Boolean isUpdateSupport) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        int num = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        List<JSONObject> jsonData = roomTypeBaseInfoMapper.selectData();
        if (jsonData.size() == 0) {
            throw new ServiceException("请检查导入房间类型与系统房间类型是否一致，重新下载导入模板！");
        }
        for (JSONObject data : list) {
            try {
                // 插入楼栋信息
                String buildingName = data.getStr("buildingName");
                long bId = 0;
                long floorId = 0;
                if (buildingName != null) {
                    StoriedBuildingInfo info1 = new StoriedBuildingInfo();
                    info1.setName(buildingName);
                    info1.setType("1");
                    List<StoriedBuildingInfo> infoList = storiedBuildingInfoMapper.selectStoriedBuildingInfoList(info1);
                    if (infoList.size() == 0) {
                        int i = insertStoriedBuildingInfo(info1);
                        bId = info1.getId();
                        successNum++;
                        num++;
                    } else {
                        bId = infoList.get(0).getId();
                        successNum++;
                        num++;
                    }
                }
                // 插入楼层信息
                String floorName = data.getStr("floorName");
                if (floorName != null) {
                    StoriedBuildingInfo info2 = new StoriedBuildingInfo();
                    info2.setName(floorName);
                    info2.setType("2");
                    info2.setParentId(bId);
                    List<StoriedBuildingInfo> infoList2 = storiedBuildingInfoMapper.selectStoriedBuildingInfoList(info2);
                    if (infoList2.size() == 0) {
                        int i = insertStoriedBuildingInfo(info2);
                        floorId = info2.getId();
                        successNum++;
                        num++;
                    } else {
                        floorId = infoList2.get(0).getId();
                        successNum++;
                        num++;
                    }
                }
                // 插入房间信息
                String roomName = data.getStr("roomName");
                String roomType = data.getStr("roomType");
                if (roomName != null) {
                    StoriedBuildingInfo info3 = new StoriedBuildingInfo();
                    info3.setName(roomName);
                    info3.setParentId(floorId);
                    info3.setType("3");
                    List<StoriedBuildingInfo> infoList3 = storiedBuildingInfoMapper.selectStoriedBuildingInfoList(info3);
                    if (infoList3.size() == 0) {
                        for (JSONObject object : jsonData) {
                            String name = object.getStr("name");
                            if (name.equals(roomType)) {
                                info3.setRoomType(object.getStr("id"));
                                //                            info3.setTotalBedNumber(object.getLong("bedNum"));
                            }
                        }
                        int i = insertStoriedBuildingInfo(info3);
                        successNum++;
                        num++;

                    } else {
                        successNum++;
                        num++;
                    }
                }
            } catch (Exception e) {
                failureNum++;
                num++;
                String msg = "<br/>第" + num + "行导入失败：请检查导入房间类型与系统房间类型是否一致或重新下载导入模板！";
                failureMsg.append(msg);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }


    private Long insertDataInfo(String type, String name, String rooType, String parentId) {
        StoriedBuildingInfo info = new StoriedBuildingInfo();
        if ("1".equals(type)) {
            info = storiedBuildingInfoMapper.selectInfoByName(type, name, parentId);
        } else if ("2".equals(type)) {
            info = storiedBuildingInfoMapper.selectInfoByName(type, name, parentId);
        } else if ("3".equals(type)) {
            info = storiedBuildingInfoMapper.selectInfoByName(type, name, parentId);
        }
        if (null == info) {
            if ("3".equals(type)) {
                info.setRoomType(rooType);
            }
            info.setType(type);
            info.setName(name);
            info.setParentId(Long.parseLong(parentId));
        }
        int i = insertStoriedBuildingInfo(info);
        return info.getId();
    }


    // 根据id获取总层数、房间数、床位数
    private StoriedBuildingInfo getTotalNum(Long id) {
        // 查询层数、房间数、床位数
        StoriedBuildingInfo info = new StoriedBuildingInfo();
        Map<String, Object> map = storiedBuildingInfoMapper.selectByNum(String.valueOf(id));
        if (null != map) {
            Object floorNum = map.get("floorNum");
            Object roomNum = map.get("roomNum");
            Object bedNum = map.get("bedNum");
            Object occupancyNumber = map.get("occupancyNumber");
            if (null != floorNum) {
                info.setTotalFloorsNumber(Long.parseLong(floorNum.toString()));
            }
            if (null != roomNum) {
                info.setTotalRoomsNumber(Long.parseLong(roomNum.toString()));
            }
            if (null != bedNum) {
                info.setTotalBedNumber(Long.parseLong(bedNum.toString()));
            }
            if (null != occupancyNumber) {
                info.setOccupancyNumber(Long.parseLong(occupancyNumber.toString()));
            }
        }
        return info;

    }


}
