package com.ruoyi.custom.admin.care.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.care.domain.CareWorkerInfo;
import com.ruoyi.custom.admin.care.service.ICareWorkerInfoService;
import com.ruoyi.custom.utils.ReflectionUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.care.domain.CareRecordInfo;
import com.ruoyi.custom.admin.care.service.ICareRecordInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 护理记录Controller
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
@RestController
@RequestMapping("/careRecordInfo")
@Api(value = "护理记录Controller", tags = "护理记录")
public class CareRecordInfoController extends BaseController {
    @Autowired
    private ICareRecordInfoService careRecordInfoService;

    @Autowired
    private ICareWorkerInfoService careWorkerInfoService;

    /**
     * 查询护理记录列表
     */
    //@RequiresPermissions("care:careRecordInfo:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询护理记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(value = "护理项目名称", name = "careName", dataTypeClass = String.class, paramType = "query"),
            @ApiImplicitParam(value = "护工id", name = "careWorkerId", dataTypeClass = String.class, paramType = "query"),
            @ApiImplicitParam(value = "老人名称", name = "userName", dataTypeClass = String.class, paramType = "query"),
    })
    public TableDataInfo<CareRecordInfo> list(@ApiIgnore CareRecordInfo careRecordInfo) {
        startPage();
        List<CareRecordInfo> list = careRecordInfoService.selectCareRecordInfoList(careRecordInfo);
        return getDataTable(list);
    }

    /**
     * 导出护理记录列表
     */
    //@RequiresPermissions("care:careRecordInfo:export")
    @Log(platform = "1", title = "护理记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiIgnore
    public void export(HttpServletResponse response, CareRecordInfo careRecordInfo) {
        List<CareRecordInfo> list = careRecordInfoService.selectCareRecordInfoList(careRecordInfo);
        ExcelUtil<CareRecordInfo> util = new ExcelUtil<CareRecordInfo>(CareRecordInfo.class);
        util.exportExcel(response, list, "护理记录数据");
    }

    /**
     * 获取护理记录详细信息
     */
    //@RequiresPermissions("care:careRecordInfo:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取护理记录详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        CareRecordInfo careRecordInfo = (CareRecordInfo) ReflectionUtils.nullifyStrings(careRecordInfoService.selectCareRecordInfoById(id));
        return AjaxResult.success(careRecordInfo);
    }

    /**
     * 保存护理记录
     */
    ////@RequiresPermissions("care:careRecordInfo:add")
    @Log(platform = "1", title = "护理记录", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @ApiOperation(value = "保存护理记录")
    public AjaxResult save(@RequestBody CareRecordInfo careRecordInfo) {
        if (null == careRecordInfo.getId() || careRecordInfo.getId() == 0) {
            return toAjax(careRecordInfoService.insertCareRecordInfo(careRecordInfo));
        } else {
            return toAjax(careRecordInfoService.updateCareRecordInfo(careRecordInfo));
        }
    }

    /**
     * 新增护理记录
     */
    ////@RequiresPermissions("care:careRecordInfo:add")
    @Log(platform = "1", title = "护理记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody CareRecordInfo careRecordInfo) {
        return toAjax(careRecordInfoService.insertCareRecordInfo(careRecordInfo));
    }

    /**
     * 修改护理记录
     */
    ////@RequiresPermissions("care:careRecordInfo:edit")
    @Log(platform = "1", title = "护理记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody CareRecordInfo careRecordInfo) {
        return toAjax(careRecordInfoService.updateCareRecordInfo(careRecordInfo));
    }

    /**
     * 删除护理记录
     */
    ////@RequiresPermissions("care:careRecordInfo:remove")
    @Log(platform = "1", title = "护理记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除护理记录")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(careRecordInfoService.deleteCareRecordInfoByIds(ids));
    }
}
