package com.ruoyi.custom.api.assessmentApp;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.assessment.domain.AssessmentPlan;
import com.ruoyi.custom.admin.assessment.domain.AssessmentResult;
import com.ruoyi.custom.admin.assessment.domain.ElderlyCapacityAssessment;
import com.ruoyi.custom.admin.assessment.service.IAssessmentPlanService;
import com.ruoyi.custom.admin.assessment.service.IElderlyCapacityAssessmentService;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.utils.OrderUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 评估端APP-评估管理 Controller
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
@RestController("assessmentAppAssessmentController")
@RequestMapping("/assessmentApp/assessment")
@Api(tags = "评估端APP-评估管理")
public class AssessmentController extends BaseController {

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    @Autowired
    private IAssessmentPlanService assessmentPlanService;

    @Autowired
    private IElderlyCapacityAssessmentService iElderlyCapacityAssessmentService;

    @GetMapping("/assessment/list")
    @ApiOperation(value = "评估计划列表")
    public TableDataInfo assessmentList(AssessmentPlan assessmentPlan) {
        // 获取当前用户id
        Long userId = SecurityUtils.getUserId();
        assessmentPlan.setAssessorId(userId);

        startPage();
        List<AssessmentPlan> list = assessmentPlanService.selectAssessmentPlanList(assessmentPlan);
        return getDataTable(list);
    }

    /**
     * 获取评估计划详细信息
     */
    // @RequiresPermissions("custom:plan:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取评估计划详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(assessmentPlanService.selectAssessmentPlanById(id));
    }

    /**
     * 新增评估计划
     */
    // @RequiresPermissions("custom:plan:add")
    @Log(title = "评估计划", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增评估计划")
    public AjaxResult add(@RequestBody AssessmentPlan assessmentPlan) {
        return toAjax(assessmentPlanService.insertAssessmentPlan(assessmentPlan));
    }

    /**
     * 生成评估编号
     */
    @GetMapping("generateSerialNumber")
    @ApiOperation(value = "生成评估编号")
    public AjaxResult generateSerialNumber() {
        return AjaxResult.success("操作成功", OrderUtils.getEvaluationCode());
    }

    /**
     * 修改评估计划
     */
    // @RequiresPermissions("custom:plan:edit")
    @Log(title = "评估计划", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改评估计划")
    public AjaxResult edit(@RequestBody AssessmentPlan assessmentPlan) {
        return toAjax(assessmentPlanService.updateAssessmentPlan(assessmentPlan));
    }

    /**
     * 删除评估计划
     */
    // @RequiresPermissions("custom:plan:remove")
    @Log(title = "评估计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除评估计划")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(assessmentPlanService.deleteAssessmentPlanByIds(ids));
    }

    /**
     * 开始评估&继续评估&查看评估
     */
    @GetMapping("/startOrViewAnswer/{id}")
    @ApiOperation(value = "开始评估&继续评估&查看评估")
    @ApiImplicitParam(name = "id", value = "评估计划id", required = true, dataType = "Long", paramType = "path")
    public AjaxResult startOrViewAnswer(@PathVariable("id") Long id) {
        return AjaxResult.success(assessmentPlanService.startOrViewAnswer(id));
    }

    /**
     * 保存答案
     */
    @PostMapping("/saveAnswer")
    @ApiOperation(value = "保存答案")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "评估计划id", required = true, dataType = "Long", paramType = "body"),
            @ApiImplicitParam(name = "indicator", value = "答案", required = true, dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "attachmentUrl", value = "附件", required = false, dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "params.finish", value = "完成标记,true:完成答题,false:继续", required = false, dataType = "Boolean", paramType = "body")
    })
    public AjaxResult saveAnswer(@RequestBody AssessmentPlan assessmentPlan) {
        if ((Boolean) assessmentPlan.getParams().get("finish")) {
            assessmentPlan.setStatus("2");// 设置状态为已完成
            // 生成评估结果json
            JSONObject assessmentResultJson = assessmentPlanService.createAssessmentResultJson(assessmentPlan);
            assessmentPlan.setAssessmentResult(assessmentResultJson);
        }

        return toAjax(assessmentPlanService.updateAssessmentPlan(assessmentPlan));
    }

    /**
     * 保存评估报告
     */
    @PostMapping("/editReport")
    @ApiOperation(value = "保存评估报告")
    public AjaxResult editReport(@RequestBody AssessmentResult assessmentResult) {
        return toAjax(assessmentPlanService.saveAssessmentResult(assessmentResult));
    }


    @Log(title = "新增老年人能力评估", businessType = BusinessType.DELETE)
    @PostMapping("/addCapabilityAssessment")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "params.finish", value = "完成标记,true:完成答题,false:继续", required = true, dataType = "Boolean", paramType = "body")
    })
    public AjaxResult addCapabilityAssessment(@RequestBody ElderlyCapacityAssessment elderlyCapacityAssessment){

        return toAjax(iElderlyCapacityAssessmentService.insertElderlyCapacityAssessment(elderlyCapacityAssessment));
    }

    /**
     * 获取老年人能力评估详细信息
     */
    @GetMapping(value = "getElderlyCapacityAssessmentInfo")
    public AjaxResult getElderlyCapacityAssessmentInfo(ElderlyCapacityAssessment elderlyCapacityAssessment) {
        return AjaxResult.success(iElderlyCapacityAssessmentService.selectElderlyCapacityAssessmentBySerialNumber(elderlyCapacityAssessment.getSerialNumber()));
    }
}
