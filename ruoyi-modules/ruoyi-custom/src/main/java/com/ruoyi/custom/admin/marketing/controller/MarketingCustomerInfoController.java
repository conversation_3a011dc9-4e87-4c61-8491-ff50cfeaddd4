package com.ruoyi.custom.admin.marketing.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.marketing.domain.MarketingCustomerInfo;
import com.ruoyi.custom.admin.marketing.service.MarketingCustomerInfoService;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * 咨询记录Controller
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@RestController
@RequestMapping("/marketingCustomerInfo")
@Api(value = "咨询记录", tags = "咨询记录")
public class MarketingCustomerInfoController extends BaseController {
    @Autowired
    private MarketingCustomerInfoService marketingCustomerInfoService;

    /**
     * 查询营销客户信息列表
     */
    // @RequiresPermissions("custom:info:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取咨询记录")
    public TableDataInfo list(MarketingCustomerInfo marketingCustomerInfo) {
        // 运营人员只能看自己的客户
        SysUser user = SecurityUtils.getLoginUser().getSysUser();
        Optional.ofNullable(user.getRoles())
                .orElseGet(ArrayList::new)
                .stream()
                .filter(Objects::nonNull)
                .map(SysRole::getRoleKey)
                .filter("regular_operator"::equals)
                .findFirst()
                .ifPresent(roleKey -> marketingCustomerInfo.setMarketerId(user.getId()));

        startPage();
        List<MarketingCustomerInfo> list = marketingCustomerInfoService.selectMarketingCustomerInfoList(marketingCustomerInfo);
        return getDataTable(list);
    }

    /**
     * 获取营销客户信息详细信息
     */
    // @RequiresPermissions("custom:info:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取营销客户信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(marketingCustomerInfoService.selectMarketingCustomerInfoById(id));
    }

    /**
     * 新增营销客户信息
     */
    // @RequiresPermissions("custom:info:add")
    @Log(title = "营销客户信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增咨询记录")
    public AjaxResult add(@RequestBody MarketingCustomerInfo marketingCustomerInfo) {
        marketingCustomerInfo.setElderBirthday(
                marketingCustomerInfo.getElderBirthday() == null ?
                        (StrUtil.isBlank(marketingCustomerInfo.getIdCardNumber()) ? null : IdcardUtil.getBirthDate(marketingCustomerInfo.getIdCardNumber()))
                        : marketingCustomerInfo.getElderBirthday());
        return toAjax(marketingCustomerInfoService.insertMarketingCustomerInfo(marketingCustomerInfo));
    }

    /**
     * 修改营销客户信息
     */
    // @RequiresPermissions("custom:info:edit")
    @Log(title = "营销客户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改咨询记录")
    public AjaxResult edit(@RequestBody MarketingCustomerInfo marketingCustomerInfo) {
        return toAjax(marketingCustomerInfoService.updateMarketingCustomerInfo(marketingCustomerInfo));
    }

    /**
     * 转为意向客户
     */
    @Log(title = "营销客户信息", businessType = BusinessType.UPDATE)
    @PutMapping("/intention")
    @ApiOperation(value = "转为意向客户")
    public AjaxResult intention(@RequestBody MarketingCustomerInfo marketingCustomerInfo) {
        MarketingCustomerInfo param = new MarketingCustomerInfo();
        param.setId(marketingCustomerInfo.getId());
        param.setCustomerType("1");
        return toAjax(marketingCustomerInfoService.updateMarketingCustomerInfo(param));
    }

    /**
     * 获取导入模版
     *
     * @param response
     * @throws IOException
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment; filename=" +
            java.net.URLEncoder.encode("意向客户导入模板", "UTF-8") + ".xlsx");

        // 使用底层POI来创建Excel，以便更好地控制样式
        org.apache.poi.xssf.usermodel.XSSFWorkbook workbook = new org.apache.poi.xssf.usermodel.XSSFWorkbook();
        org.apache.poi.ss.usermodel.Sheet sheet = workbook.createSheet("意向客户");

        // 创建红色字体样式（用于示例数据和提示文字）
        org.apache.poi.ss.usermodel.CellStyle redStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font redFont = workbook.createFont();
        redFont.setColor(org.apache.poi.ss.usermodel.IndexedColors.RED.getIndex());
        redFont.setFontHeightInPoints((short) 10);
        redStyle.setFont(redFont);
        redStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER);
        redStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);
        redStyle.setBorderTop(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        redStyle.setBorderBottom(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        redStyle.setBorderLeft(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        redStyle.setBorderRight(org.apache.poi.ss.usermodel.BorderStyle.THIN);

        // 创建表头样式（保持原有样式，不是红色）
        org.apache.poi.ss.usermodel.CellStyle headerStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 10);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);
        headerStyle.setBorderTop(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        headerStyle.setBorderBottom(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        headerStyle.setBorderLeft(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        headerStyle.setBorderRight(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        headerStyle.setFillForegroundColor(org.apache.poi.ss.usermodel.IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND);

        // 创建提示文字样式（红色，居中）
        org.apache.poi.ss.usermodel.CellStyle tipStyle = workbook.createCellStyle();
        tipStyle.setFont(redFont);
        tipStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER);
        tipStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);

        // 定义表头
        String[] headers = {
            "*咨询人姓名", "咨询人与老人关系", "咨询人联系电话", "咨询日期", "*咨询方式", "其他咨询方式",
            "*来源渠道", "*来源备注", "*接待人", "自理情况", "咨询内容", "老人姓名", "老人性别",
            "老人年龄", "老人出生日期", "老人电话", "身份证号", "民族", "婚姻状态", "居住情况",
            "家庭住址", "备注"
        };

        // 创建表头行（第一行）
        org.apache.poi.ss.usermodel.Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(20);
        for (int i = 0; i < headers.length; i++) {
            org.apache.poi.ss.usermodel.Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle); // 使用表头样式（不是红色）
        }

        // 创建示例数据行（第二行，红色字体）
        org.apache.poi.ss.usermodel.Row exampleRow = sheet.createRow(1);
        exampleRow.setHeightInPoints(20);

        // 填充示例数据
        String[] exampleData = {
            "张三", "子女", "13812345678", "2025/06/09", "线下", "微信：zs112",
            "地推", "其他渠道：xxxx", "超级管理员", "失能", "咨询养老服务费用及护理服务内容", "李四", "男",
            "75", "1950/01/01", "13812345678", "110101199001011234", "汉族", "已婚", "独居",
            "北京市朝阳区XXX路XX号", "客户有轻度认知障碍，需要专业护理"
        };

        for (int i = 0; i < exampleData.length && i < headers.length; i++) {
            org.apache.poi.ss.usermodel.Cell cell = exampleRow.createCell(i);
            cell.setCellValue(exampleData[i]);
            cell.setCellStyle(redStyle); // 示例数据使用红色样式
        }

        // 创建提示行（第三行，红色字体）
        org.apache.poi.ss.usermodel.Row tipRow = sheet.createRow(2);
        tipRow.setHeightInPoints(20);
        org.apache.poi.ss.usermodel.Cell tipCell = tipRow.createCell(0);
        tipCell.setCellValue("模版示例请不要删除");
        tipCell.setCellStyle(tipStyle); // 使用红色样式

        // 合并第三行的所有列
        org.apache.poi.ss.util.CellRangeAddress tipRange = new org.apache.poi.ss.util.CellRangeAddress(2, 2, 0, headers.length - 1);
        sheet.addMergedRegion(tipRange);

        // 设置列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.setColumnWidth(i, 4000); // 设置合适的列宽
        }

        try {
            workbook.write(response.getOutputStream());
        } finally {
            workbook.close();
        }
    }

    /**
     * 导入数据
     *
     * @param file
     * @param updateSupport
     * @return AjaxResult
     * @throws Exception
     */
    @PostMapping("/importData")
    public AjaxResult importData(@RequestPart @RequestParam("file") MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<MarketingCustomerInfo> util = new ExcelUtil<>(MarketingCustomerInfo.class);
        // 表头在第0行，但需要跳过第1、2行的示例数据和提示
        List<MarketingCustomerInfo> list = util.importExcel(file.getInputStream(), 0);

        // 由于ExcelUtil无法直接跳过中间行，我们需要手动过滤掉示例数据
        // 检查是否包含示例数据（第一条记录的特征字段值）
        if (!list.isEmpty()) {
            MarketingCustomerInfo first = list.get(0);
            // 如果第一条记录是示例数据（通过特征值判断），则移除前2条
            if ("张三".equals(first.getConsultantName()) && "李四".equals(first.getElderName())) {
                if (list.size() >= 2) {
                    list = list.subList(2, list.size());
                } else {
                    list.clear();
                }
            }
        }

        String message = marketingCustomerInfoService.importData(list, updateSupport);
        return AjaxResult.success(message);
    }
}

