package com.ruoyi.custom.admin.balance.service;

import com.ruoyi.custom.admin.balance.domain.SecurityBalanceRecords;

import java.util.List;

/**
 * 保障金账户记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface ISecurityBalanceRecordsService {
    /**
     * 查询保障金账户记录
     *
     * @param id 保障金账户记录主键
     * @return 保障金账户记录
     */
    public SecurityBalanceRecords selectSecurityBalanceRecordsById(Integer id);

    /**
     * 查询保障金账户记录列表
     *
     * @param securityBalanceRecords 保障金账户记录
     * @return 保障金账户记录集合
     */
    public List<SecurityBalanceRecords> selectSecurityBalanceRecordsList(SecurityBalanceRecords securityBalanceRecords);

    /**
     * 新增保障金账户记录
     *
     * @param securityBalanceRecords 保障金账户记录
     * @return 结果
     */
    public int insertSecurityBalanceRecords(SecurityBalanceRecords securityBalanceRecords);

    /**
     * 修改保障金账户记录
     *
     * @param securityBalanceRecords 保障金账户记录
     * @return 结果
     */
    public int updateSecurityBalanceRecords(SecurityBalanceRecords securityBalanceRecords);

    /**
     * 批量删除保障金账户记录
     *
     * @param ids 需要删除的保障金账户记录主键集合
     * @return 结果
     */
    public int deleteSecurityBalanceRecordsByIds(Integer[] ids);

    /**
     * 删除保障金账户记录
     *
     * @param id 保障金账户记录主键
     * @return 结果
     */
    public int deleteSecurityBalanceRecordsById(Integer id);
} 