package com.ruoyi.custom.admin.warehouse.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.utils.OrderUtils;
import com.ruoyi.custom.admin.warehouse.domain.InWarehousingRecordBase;
import com.ruoyi.custom.admin.warehouse.domain.MaterialsRemainingStock;
import com.ruoyi.custom.admin.warehouse.domain.WarehousingMaterialsLogInfo;
import com.ruoyi.custom.admin.warehouse.mapper.InWarehousingRecordBaseMapper;
import com.ruoyi.custom.admin.warehouse.mapper.WarehousingMaterialsLogInfoMapper;
import com.ruoyi.custom.admin.warehouse.service.IInWarehousingRecordBaseService;
import com.ruoyi.custom.admin.warehouse.service.IMaterialsRemainingStockService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 入库记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-06
 */
@Service
public class InWarehousingRecordBaseServiceImpl implements IInWarehousingRecordBaseService {

    @Autowired
    private InWarehousingRecordBaseMapper inWarehousingRecordBaseMapper;

    @Autowired
    private WarehousingMaterialsLogInfoMapper warehousingMaterialsLogInfoMapper;

    @Autowired
    private IMaterialsRemainingStockService stockService;

    @Autowired
    private RemoteUserService remoteUserService;


    /**
     * 查询入库记录
     *
     * @param id 入库记录主键
     * @return 入库记录
     */
    @Override
    public InWarehousingRecordBase selectInWarehousingRecordBaseById(Long id) {
        InWarehousingRecordBase data = inWarehousingRecordBaseMapper.selectInWarehousingRecordBaseById(id);

        if (Objects.nonNull(data) && StrUtil.isNotBlank(data.getRegisterPerson())) {
            R<SysUser> userR = remoteUserService.getInfoByUserId(Long.valueOf(data.getRegisterPerson()));
            if (Objects.nonNull(userR) && userR.getCode() == 200 && Objects.nonNull(userR.getData())) {
                data.setRegisterPersonName(userR.getData().getNickName());
            }
        }

        if (Objects.nonNull(data) && StrUtil.isNotBlank(data.getResponsiblePerson())) {
            R<SysUser> userR = remoteUserService.getInfoByUserId(Long.valueOf(data.getResponsiblePerson()));
            if (Objects.nonNull(userR) && userR.getCode() == 200 && Objects.nonNull(userR.getData())) {
                data.setResponsiblePersonName(userR.getData().getNickName());
            }
        }

        if (null != data) {
            List<WarehousingMaterialsLogInfo> list = warehousingMaterialsLogInfoMapper.getLogInfoByBaseId(id, "0");
            data.setDataList(list);
        }
        return data;
    }

    /**
     * 查询入库记录列表
     *
     * @param inWarehousingRecordBase 入库记录
     * @return 入库记录
     */
    @Override
    public List<InWarehousingRecordBase> selectInWarehousingRecordBaseList(InWarehousingRecordBase inWarehousingRecordBase) {
        List<InWarehousingRecordBase> list = inWarehousingRecordBaseMapper.selectInWarehousingRecordBaseList(inWarehousingRecordBase);
        List<Long> ids = list.stream()
                .map(a -> a.getRegisterPerson())
                .filter(StrUtil::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        Map<Long, SysUser> userMap = remoteUserService.getUserMapByIds(ids.toArray(new Long[0]));
        list.forEach(a -> {
            if (StrUtil.isNotBlank(a.getResponsiblePerson())) {
                SysUser sysUser = userMap.get(Long.parseLong(a.getResponsiblePerson()));
                if (!Objects.isNull(sysUser)) {
                    a.setResponsiblePersonName(sysUser.getNickName());
                }
            }
        });

        List<Long> ids2 = list.stream()
                .map(a -> a.getRegisterPerson())
                .filter(StrUtil::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        Map<Long, SysUser> userMap2 = remoteUserService.getUserMapByIds(ids2.toArray(new Long[0]));
        list.forEach(a -> {
            if (StrUtil.isNotBlank(a.getRegisterPerson())) {
                SysUser sysUser = userMap2.get(Long.parseLong(a.getRegisterPerson()));
                if (!Objects.isNull(sysUser)) {
                    a.setRegisterPersonName(sysUser.getNickName());
                }
            }
        });

        return list;
    }

    /**
     * 新增入库记录
     *
     * @param inWarehousingRecordBase 入库记录
     * @return 结果
     */
    @Override
    public int insertInWarehousingRecordBase(InWarehousingRecordBase inWarehousingRecordBase) {
        inWarehousingRecordBase.setCreateTime(DateUtils.getNowDate());
        inWarehousingRecordBase.setRegisterPerson(String.valueOf(SecurityUtils.getUserId()));
        inWarehousingRecordBase.setNumber(OrderUtils.getInOutCode());
        int i = inWarehousingRecordBaseMapper.insertInWarehousingRecordBase(inWarehousingRecordBase);
        List<WarehousingMaterialsLogInfo> dataList = inWarehousingRecordBase.getDataList();
        for (WarehousingMaterialsLogInfo logInfo : dataList) {
            MaterialsRemainingStock stockInfo = stockService.getByBaseId(logInfo.getMaterialsId(), inWarehousingRecordBase.getWarehouseId());// 获取剩余数量
            logInfo.setBaseId(inWarehousingRecordBase.getId());
            logInfo.setType("0");
            logInfo.setWarehouseId(inWarehousingRecordBase.getWarehouseId());
            logInfo.setPreviousQuantity(stockInfo.getQuantity());// 变化之前数量
            logInfo.setCurrentQuantity(logInfo.getQuantity() + stockInfo.getQuantity());// 变化之后数量
            warehousingMaterialsLogInfoMapper.insertWarehousingMaterialsLogInfo(logInfo);
            stockInfo.setQuantity(logInfo.getQuantity() + stockInfo.getQuantity());// 剩余数量
            stockInfo.setWarehouseId(inWarehousingRecordBase.getWarehouseId());// 剩余数量
            stockService.save(stockInfo);
        }
        return 1;
    }

    /**
     * 修改入库记录
     *
     * @param inWarehousingRecordBase 入库记录
     * @return 结果
     */
    @Override
    public int updateInWarehousingRecordBase(InWarehousingRecordBase inWarehousingRecordBase) {
        inWarehousingRecordBase.setUpdateTime(DateUtils.getNowDate());
        return inWarehousingRecordBaseMapper.updateInWarehousingRecordBase(inWarehousingRecordBase);
    }

    /**
     * 批量删除入库记录
     *
     * @param ids 需要删除的入库记录主键
     * @return 结果
     */
    @Override
    public int deleteInWarehousingRecordBaseByIds(Long[] ids) {
        return inWarehousingRecordBaseMapper.deleteInWarehousingRecordBaseByIds(ids);
    }

    /**
     * 删除入库记录信息
     *
     * @param id 入库记录主键
     * @return 结果
     */
    @Override
    public int deleteInWarehousingRecordBaseById(Long id) {
        return inWarehousingRecordBaseMapper.deleteInWarehousingRecordBaseById(id);
    }

}
