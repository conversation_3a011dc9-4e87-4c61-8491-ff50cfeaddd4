package com.ruoyi.custom.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.ruoyi.custom.admin.assessment.domain.AssessmentPlan;
import org.springframework.util.StringUtils;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 护理计划Word导出工具类(使用poi-tl)
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public class NursingPlanPoiTlUtil {

    private static final Pattern INPUT_PATTERN = Pattern.compile("@input\\s*(.*?)\\s*@/input");

    /**
     * 识别护理计划指标的模版类型
     *
     * @param nursingPlanIndicator 护理计划指标JSON数据
     * @return 模版类型：1-标准护理计划，2-简化护理计划
     */
    public static int identifyTemplateType(JSONObject nursingPlanIndicator) {
        JSONObject details = nursingPlanIndicator.getJSONObject("details");
        if (details == null) {
            return 1; // 默认类型1
        }

        // 如果包含expectedGoal和nursingDisposition字段，则为类型2
        if (details.containsKey("expectedGoal") && details.containsKey("nursingDisposition")) {
            return 2;
        }

        // 否则为类型1
        return 1;
    }

    /**
     * 将护理计划JSON数据转换为Word模板所需的Map数据（类型1）
     *
     * @param nursingPlanIndicator 护理计划指标JSON数据
     * @param assessmentPlan 评估计划对象（包含老人基本信息）
     * @return Word模板数据Map
     */
    public static Map<String, Object> convertNursingPlanToWordDataType1(JSONObject nursingPlanIndicator,
                                                                       AssessmentPlan assessmentPlan) {
        Map<String, Object> data = new HashMap<>();

        // 添加基本信息
        data.put("problem", nursingPlanIndicator.getString("problem"));
        // issueConfirmDate -> icd
        data.put("icd", nursingPlanIndicator.getString("issueConfirmDate"));
        // issueEndDate -> ied
        data.put("ied", nursingPlanIndicator.getString("issueEndDate"));

        // 添加老人基本信息
        if (assessmentPlan != null) {
            // 老人姓名
            data.put("nm", assessmentPlan.getElderlyName() != null ? assessmentPlan.getElderlyName() : "");

            // 老人性别（复选框格式）
            String sex = assessmentPlan.getElderlySex();
            data.put("sex", formatGenderCheckbox(sex));
        } else {
            data.put("nm", "");
            data.put("sex", "☑男 □女");  // 默认格式
        }

        // 获取详情JSON
        JSONObject details = nursingPlanIndicator.getJSONObject("details");
        if (details != null) {
            // 处理导因: cause_content -> cc
            data.put("cc", formatOptions(details.getJSONArray("cause")));

            // 处理鉴定特征: feature_content -> fc
            data.put("fc", formatOptions(details.getJSONArray("feature")));

            // 处理护理目标: goal_content -> gc（根据type字段决定是否显示复选框）
            data.put("gc", formatOptions(details.getJSONArray("goal")));

            // 处理护理措施: measure_content -> mc
            data.put("mc", formatOptions(details.getJSONArray("measure")));

            // 处理评值: evaluation_content -> ec
            data.put("ec", formatOptions(details.getJSONArray("evaluation")));
        }

        return data;
    }

    /**
     * 将护理计划JSON数据转换为Word模板所需的Map数据（类型2）
     *
     * @param nursingPlanIndicator 护理计划指标JSON数据
     * @param assessmentPlan 评估计划对象（包含老人基本信息）
     * @return Word模板数据Map
     */
    public static Map<String, Object> convertNursingPlanToWordDataType2(JSONObject nursingPlanIndicator,
                                                                       AssessmentPlan assessmentPlan) {
        Map<String, Object> data = new HashMap<>();

        // 添加基本信息
        data.put("problem", nursingPlanIndicator.getString("problem"));
        // issueConfirmDate -> icd
        data.put("icd", nursingPlanIndicator.getString("issueConfirmDate"));
        // issueEndDate -> ied
        data.put("ied", nursingPlanIndicator.getString("issueEndDate"));

        // 添加老人基本信息
        if (assessmentPlan != null) {
            // 老人姓名
            data.put("nm", assessmentPlan.getElderlyName() != null ? assessmentPlan.getElderlyName() : "");

            // 老人性别（复选框格式）
            String sex = assessmentPlan.getElderlySex();
            data.put("sex", formatGenderCheckbox(sex));
        } else {
            data.put("nm", "");
            data.put("sex", "☑男 □女");  // 默认格式
        }

        // 获取详情JSON
        JSONObject details = nursingPlanIndicator.getJSONObject("details");
        if (details != null) {
            // 处理导因: cause -> cause（根据type字段决定是否显示复选框）
            data.put("cause", formatOptions(details.getJSONArray("cause")));

            // 处理预期目标: expectedGoal -> eg（根据type字段决定是否显示复选框）
            data.put("eg", formatOptions(details.getJSONArray("expectedGoal")));

            // 处理护理处置: nursingDisposition -> ndRows (使用LoopRowTableRenderPolicy)
            JSONArray nursingDispositions = details.getJSONArray("nursingDisposition");
            List<Map<String, Object>> ndRows = new ArrayList<>();

            if (nursingDispositions != null && !nursingDispositions.isEmpty()) {
                for (int i = 0; i < nursingDispositions.size(); i++) {
                    JSONObject disposition = nursingDispositions.getJSONObject(i);
                    Map<String, Object> row = new HashMap<>();

                    // 处理护理处置内容
                    String dispositionText;
                    if (disposition.containsKey("answer") && StringUtils.hasText(disposition.getString("answer"))) {
                        dispositionText = disposition.getString("answer");
                    } else {
                        String optionText = disposition.getString("option");
                        if (StringUtils.hasText(optionText)) {
                            dispositionText = processInputTags(optionText, disposition.getJSONArray("inputs"));
                        } else {
                            dispositionText = "";
                        }
                    }

                    row.put("icd", nursingPlanIndicator.getString("issueConfirmDate")); // 开始日期
                    row.put("ied", nursingPlanIndicator.getString("issueEndDate"));     // 停止日期
                    row.put("nd", dispositionText);                                      // 护理处置内容
                    row.put("signature", "/");                                           // 签名列，默认为空

                    ndRows.add(row);
                }
            } else {
                // 如果没有护理处置数据，创建一个空行
                Map<String, Object> emptyRow = new HashMap<>();
                emptyRow.put("icd", nursingPlanIndicator.getString("issueConfirmDate"));
                emptyRow.put("ied", nursingPlanIndicator.getString("issueEndDate"));
                emptyRow.put("nd", "");
                emptyRow.put("signature", "/");
                ndRows.add(emptyRow);
            }

            data.put("ndRows", ndRows);
        }

        return data;
    }

    /**
     * 创建poi-tl配置（类型2需要LoopRowTableRenderPolicy）
     *
     * @return Configure配置对象
     */
    public static Configure createConfigureForType2() {
        return Configure.builder()
                .bind("ndRows", new LoopRowTableRenderPolicy())
                .build();
    }

    /**
     * 将护理计划JSON数据转换为Word模板所需的Map数据（兼容方法）
     *
     * @param nursingPlanIndicator 护理计划指标JSON数据
     * @return Word模板数据Map
     */
    public static Map<String, Object> convertNursingPlanToWordData(JSONObject nursingPlanIndicator) {
        int templateType = identifyTemplateType(nursingPlanIndicator);
        if (templateType == 2) {
            return convertNursingPlanToWordDataType2(nursingPlanIndicator, null);
        } else {
            return convertNursingPlanToWordDataType1(nursingPlanIndicator, null);
        }
    }

    /**
     * 处理 @input 标签
     * 将 "@input" 替换为对应的输入值
     *
     * @param text 原始文本
     * @param inputs 输入值数组
     * @return 处理后的文本
     */
    private static String processInputTags(String text, JSONArray inputs) {
        if (text == null) {
            return "";
        }

        // 如果有inputs数组，使用其中的值依次替换@input标签
        if (inputs != null && !inputs.isEmpty()) {
            Matcher matcher = INPUT_PATTERN.matcher(text);
            StringBuffer result = new StringBuffer();
            int inputIndex = 0;

            while (matcher.find() && inputIndex < inputs.size()) {
                String inputValue = inputs.getString(inputIndex++);
                matcher.appendReplacement(result, inputValue + " ");
            }
            matcher.appendTail(result);

            return result.toString();
        } else {
            // 如果没有inputs数组，只移除@input和@/input标签
            Matcher matcher = INPUT_PATTERN.matcher(text);
            StringBuffer result = new StringBuffer();

            while (matcher.find()) {
                matcher.appendReplacement(result, "_________");
            }
            matcher.appendTail(result);

            return result.toString();
        }
    }

    /**
     * 格式化选项为字符串
     *
     * @param options 选项数组
     * @return 格式化后的字符串
     */
    private static String formatOptions(JSONArray options) {
        if (options == null || options.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < options.size(); i++) {
            JSONObject option = options.getJSONObject(i);
            boolean isChecked = option.getBooleanValue("checked");

            // 处理主选项
            String displayText;
            if (option.containsKey("answer") && StringUtils.hasText(option.getString("answer"))) {
                displayText = option.getString("answer");
            } else {
                String optionText = option.getString("option");
                if (!StringUtils.hasText(optionText)) {
                    continue;
                }

                displayText = processInputTags(optionText, option.getJSONArray("inputs"));
            }

            // 根据type字段决定是否添加复选框
            boolean shouldShowCheckbox = false; // 默认不显示复选框
            if (option.containsKey("type")) {
                String type = option.getString("type");
                shouldShowCheckbox = "checkbox".equals(type);
            }

            if (shouldShowCheckbox) {
                sb.append(isChecked ? "☑ " : "□ ");
            }
            sb.append(displayText);

            // 处理子项
            JSONArray subDetails = option.getJSONArray("details");
            if (subDetails != null && !subDetails.isEmpty()) {
                sb.append("\n");
                for (int j = 0; j < subDetails.size(); j++) {
                    JSONObject detail = subDetails.getJSONObject(j);
                    boolean isSubChecked = detail.getBooleanValue("checked");

                    // 处理子选项
                    String subDisplayText;
                    if (detail.containsKey("answer") && StringUtils.hasText(detail.getString("answer"))) {
                        subDisplayText = detail.getString("answer");
                    } else {
                        String subOptionText = detail.getString("option");
                        if (!StringUtils.hasText(subOptionText)) {
                            continue;
                        }

                        subDisplayText = processInputTags(subOptionText, detail.getJSONArray("inputs"));
                    }

                    // 根据子项的type字段决定是否添加复选框
                    boolean subShouldShowCheckbox = false; // 默认不显示复选框
                    if (detail.containsKey("type")) {
                        String subType = detail.getString("type");
                        subShouldShowCheckbox = "checkbox".equals(subType);
                    }

                    if (subShouldShowCheckbox) {
                        sb.append("  ").append(isSubChecked ? "☑ " : "□ ");
                    } else {
                        sb.append("  ");
                    }
                    sb.append(subDisplayText).append("\n");
                }
            } else {
                sb.append("\n");
            }
        }
        return sb.toString();
    }

    /**
     * 格式化性别复选框
     *
     * @param sex 性别值（字典值：0-男，1-女）
     * @return 格式化后的性别复选框字符串
     */
    private static String formatGenderCheckbox(String sex) {
        if ("0".equals(sex)) {
            return "☑男 □女";
        } else if ("1".equals(sex)) {
            return "□男 ☑女";
        } else {
            // 默认或未知性别
            return "□男 □女";
        }
    }

    /**
     * 批量导出护理计划
     *
     * @param indicators 护理计划指标列表
     * @param assessmentPlan 评估计划对象
     * @param templatePath1 类型1模板路径
     * @param templatePath2 类型2模板路径
     * @param outputPath 输出目录路径
     * @param fileNamePrefix 文件名前缀
     * @return 导出的文件路径列表
     * @throws IOException IO异常
     */
    public static List<String> batchExportNursingPlans(JSONArray indicators,
                                                       AssessmentPlan assessmentPlan,
                                                       String templatePath1, String templatePath2,
                                                       String outputPath, String fileNamePrefix) throws IOException {
        List<String> exportedFiles = new ArrayList<>();

        for (int i = 0; i < indicators.size(); i++) {
            JSONObject indicator = indicators.getJSONObject(i);
            String problem = indicator.getString("problem");
            int templateType = identifyTemplateType(indicator);

            // 生成文件名
            String fileName = fileNamePrefix + "_" +
                            (StringUtils.hasText(problem) ? problem : "护理计划") + ".docx";
            String filePath = outputPath + "/" + fileName;

            // 根据模版类型选择模版和转换方法
            String templatePath;
            Map<String, Object> data;

            if (templateType == 2) {
                templatePath = templatePath2;
                data = convertNursingPlanToWordDataType2(indicator, assessmentPlan);
                // 使用LoopRowTableRenderPolicy配置导出
                Configure configure = createConfigureForType2();
                PoiTlWordExportUtil.exportWord(templatePath, filePath, data, configure);
            } else {
                templatePath = templatePath1;
                data = convertNursingPlanToWordDataType1(indicator, assessmentPlan);
                // 使用默认配置导出
                PoiTlWordExportUtil.exportWord(templatePath, filePath, data);
            }

            exportedFiles.add(filePath);
        }

        return exportedFiles;
    }

    /**
     * 批量导出护理计划（向后兼容的方法）
     *
     * @param indicators 护理计划指标列表
     * @param templatePath 模板路径（类型1模版）
     * @param outputPath 输出目录路径
     * @param fileNamePrefix 文件名前缀
     * @return 导出的文件路径列表
     * @throws IOException IO异常
     */
    public static List<String> batchExportNursingPlans(JSONArray indicators, String templatePath,
                                                String outputPath, String fileNamePrefix) throws IOException {
        List<String> exportedFiles = new ArrayList<>();

        for (int i = 0; i < indicators.size(); i++) {
            JSONObject indicator = indicators.getJSONObject(i);
            String problem = indicator.getString("problem");

            // 生成文件名
            String fileName = fileNamePrefix + "_" +
                            (StringUtils.hasText(problem) ? problem : "护理计划") + ".docx";
            String filePath = outputPath + "/" + fileName;

            // 转换数据
            Map<String, Object> data = convertNursingPlanToWordData(indicator);

            // 导出Word文档
            PoiTlWordExportUtil.exportWord(templatePath, filePath, data);
            exportedFiles.add(filePath);
        }

        return exportedFiles;
    }
}
