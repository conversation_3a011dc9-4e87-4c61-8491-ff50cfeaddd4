package com.ruoyi.custom.admin.marketing.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 营销回访对象 t_marketing_follow_up
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
@ApiModel(value = "营销回访信息")
public class MarketingFollowUp extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 客户id，对应t_marketing_customer_info
     */
    @Excel(name = "客户id")
    @ApiModelProperty(value = "客户id，对应t_marketing_customer_info")
    private Long customerId;

    /**
     * 计划时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划时间")
    private Date planTime;

    /**
     * 回访事项
     */
    @Excel(name = "回访事项")
    @ApiModelProperty(value = "回访事项")
    private String followUpItems;

    /**
     * 回访时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "回访时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "回访时间")
    private Date followUpTime;

    /**
     * 回访状态，字典：custom_marketing_follow_up_follow_up_status；0：待回访，1：已回访
     */
    @Excel(name = "回访状态，字典：custom_marketing_follow_up_follow_up_status；0：待回访，1：已回访")
    @ApiModelProperty(value = "回访状态，字典：custom_marketing_follow_up_follow_up_status；0：待回访，1：已回访")
    private String followUpStatus;

    /**
     * 回访情况
     */
    @Excel(name = "回访情况")
    @ApiModelProperty(value = "回访情况")
    private String followUpSituation;

    /**
     * 跟进人id
     */
    @Excel(name = "跟进人id")
    @ApiModelProperty(value = "跟进人id")
    private Long followerId;

    /**
     * 跟进人姓名
     */
    @ApiModelProperty(value = "跟进人姓名")
    private String follower;

    /**
     * 客户ids
     */
    @ApiModelProperty(value = "客户ids")
    private Long[] customerIds;

    // ------------------ 分界线 ------------------

    @ApiModelProperty(value = "客户信息")
    private MarketingCustomerInfo marketingCustomerInfo;
}

