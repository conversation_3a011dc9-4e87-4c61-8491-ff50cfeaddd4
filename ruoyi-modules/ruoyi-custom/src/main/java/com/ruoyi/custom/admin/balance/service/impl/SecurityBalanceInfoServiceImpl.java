package com.ruoyi.custom.admin.balance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.custom.admin.balance.domain.SecurityBalanceInfo;
import com.ruoyi.custom.admin.balance.domain.SecurityBalanceRecords;
import com.ruoyi.custom.admin.balance.mapper.SecurityBalanceInfoMapper;
import com.ruoyi.custom.admin.balance.mapper.SecurityBalanceRecordsMapper;
import com.ruoyi.custom.admin.balance.service.ISecurityBalanceInfoService;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 保障金账户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
public class SecurityBalanceInfoServiceImpl implements ISecurityBalanceInfoService {
    @Autowired
    private SecurityBalanceInfoMapper securityBalanceInfoMapper;

    @Autowired
    private SecurityBalanceRecordsMapper securityBalanceRecordsMapper;

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    /**
     * 查询保障金账户信息
     *
     * @param id 保障金账户信息主键
     * @return 保障金账户信息
     */
    @Override
    public SecurityBalanceInfo selectSecurityBalanceInfoById(Integer id) {
        return securityBalanceInfoMapper.selectSecurityBalanceInfoById(id);
    }

    /**
     * 查询保障金账户信息列表
     *
     * @param securityBalanceInfo 保障金账户信息
     * @return 保障金账户信息
     */
    @Override
    public List<SecurityBalanceInfo> selectSecurityBalanceInfoList(SecurityBalanceInfo securityBalanceInfo) {
        return securityBalanceInfoMapper.selectSecurityBalanceInfoList(securityBalanceInfo);
    }

    /**
     * 新增保障金账户信息
     *
     * @param securityBalanceInfo 保障金账户信息
     * @return 结果
     */
    @Override
    public int insertSecurityBalanceInfo(SecurityBalanceInfo securityBalanceInfo) {
        securityBalanceInfo.setCreateTime(DateUtils.getNowDate());
        return securityBalanceInfoMapper.insertSecurityBalanceInfo(securityBalanceInfo);
    }

    /**
     * 修改保障金账户信息
     *
     * @param securityBalanceInfo 保障金账户信息
     * @return 结果
     */
    @Override
    public int updateSecurityBalanceInfo(SecurityBalanceInfo securityBalanceInfo) {
        securityBalanceInfo.setUpdateTime(DateUtils.getNowDate());
        return securityBalanceInfoMapper.updateSecurityBalanceInfo(securityBalanceInfo);
    }

    /**
     * 批量删除保障金账户信息
     *
     * @param ids 需要删除的保障金账户信息主键
     * @return 结果
     */
    @Override
    public int deleteSecurityBalanceInfoByIds(Integer[] ids) {
        return securityBalanceInfoMapper.deleteSecurityBalanceInfoByIds(ids);
    }

    /**
     * 删除保障金账户信息信息
     *
     * @param id 保障金账户信息主键
     * @return 结果
     */
    @Override
    public int deleteSecurityBalanceInfoById(Integer id) {
        return securityBalanceInfoMapper.deleteSecurityBalanceInfoById(id);
    }
    
    /**
     * 充值保障金
     *
     * @param elderlyId  老人ID
     * @param operatorId 操作人ID
     * @param payAmount  充值金额
     * @return 保障金账户记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecurityBalanceRecords doPay(String elderlyId, String operatorId, BigDecimal payAmount) {
        // 查询老人信息是否存在
        ElderlyPeopleInfo elderlyInfo = elderlyPeopleInfoService.selectElderlyPeopleInfoById(elderlyId);
        if (elderlyInfo == null) {
            throw new ServiceException("老人信息不存在，无法充值保障金");
        }
        
        // 查询保障金账户信息
        SecurityBalanceInfo securityBalanceInfo = securityBalanceInfoMapper.selectSecurityBalanceInfoByElderlyId(elderlyId);
        
        // 如果账户不存在，则创建新账户
        if (securityBalanceInfo == null) {
            securityBalanceInfo = new SecurityBalanceInfo();
            securityBalanceInfo.setElderlyId(elderlyId);
            securityBalanceInfo.setLastAmount(new BigDecimal("0"));
            securityBalanceInfo.setAmount(payAmount);
            securityBalanceInfo.setCreateBy(operatorId);
            securityBalanceInfo.setCreateTime(new Date());
            securityBalanceInfo.setDelFlag("0");
            securityBalanceInfoMapper.insertSecurityBalanceInfo(securityBalanceInfo);
        } else {
            // 如果账户存在，则更新账户金额
            BigDecimal lastAmount = securityBalanceInfo.getAmount();
            if (lastAmount == null) {
                lastAmount = new BigDecimal("0");
            }
            securityBalanceInfo.setLastAmount(lastAmount);
            securityBalanceInfo.setAmount(lastAmount.add(payAmount));
            securityBalanceInfo.setUpdateBy(operatorId);
            securityBalanceInfo.setUpdateTime(new Date());
            securityBalanceInfoMapper.updateSecurityBalanceInfo(securityBalanceInfo);
        }

        // 创建保障金账户记录
        SecurityBalanceRecords securityBalanceRecords = BeanUtil.copyProperties(securityBalanceInfo, SecurityBalanceRecords.class);
        securityBalanceRecords.setCreateBy(operatorId);
        securityBalanceRecords.setCreateTime(new Date());
        securityBalanceRecords.setChangedAmount(payAmount);
        securityBalanceRecords.setChangedType("1"); // 充值
        securityBalanceRecords.setDelFlag("0");
        securityBalanceRecordsMapper.insertSecurityBalanceRecords(securityBalanceRecords);

        return securityBalanceRecords;
    }

    /**
     * 扣款保障金
     *
     * @param elderlyId    老人ID
     * @param operatorId   操作人ID
     * @param deductAmount 扣款金额
     * @return 保障金账户记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecurityBalanceRecords doDeduction(String elderlyId, String operatorId, BigDecimal deductAmount) {
        // 查询老人信息是否存在
        ElderlyPeopleInfo elderlyInfo = elderlyPeopleInfoService.selectElderlyPeopleInfoById(elderlyId);
        if (elderlyInfo == null) {
            throw new ServiceException("老人信息不存在，无法扣款保障金");
        }
        
        // 查询保障金账户信息
        SecurityBalanceInfo securityBalanceInfo = securityBalanceInfoMapper.selectSecurityBalanceInfoByElderlyId(elderlyId);
        
        // 如果账户不存在或余额不足，则抛出异常
        if (securityBalanceInfo == null) {
            throw new ServiceException("保障金账户不存在，无法扣款");
        }
        
        BigDecimal lastAmount = securityBalanceInfo.getAmount();
        if (lastAmount == null) {
            lastAmount = new BigDecimal("0");
        }
        
        // 检查余额是否充足
        if (lastAmount.compareTo(deductAmount) < 0) {
            throw new ServiceException("保障金账户余额不足，无法扣款");
        }
        
        // 更新账户金额
        securityBalanceInfo.setLastAmount(lastAmount);
        securityBalanceInfo.setAmount(lastAmount.subtract(deductAmount));
        securityBalanceInfo.setUpdateBy(operatorId);
        securityBalanceInfo.setUpdateTime(new Date());
        securityBalanceInfoMapper.updateSecurityBalanceInfo(securityBalanceInfo);

        // 创建保障金账户记录
        SecurityBalanceRecords securityBalanceRecords = BeanUtil.copyProperties(securityBalanceInfo, SecurityBalanceRecords.class);
        securityBalanceRecords.setCreateBy(operatorId);
        securityBalanceRecords.setCreateTime(new Date());
        securityBalanceRecords.setChangedAmount(deductAmount);
        securityBalanceRecords.setChangedType("2"); // 扣款
        securityBalanceRecords.setDelFlag("0");
        securityBalanceRecordsMapper.insertSecurityBalanceRecords(securityBalanceRecords);

        return securityBalanceRecords;
    }

    /**
     * 退还保障金
     *
     * @param elderlyId    老人ID
     * @param operatorId   操作人ID
     * @param returnAmount 退款金额
     * @return 保障金账户记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecurityBalanceRecords returnSecurityBalance(String elderlyId, String operatorId, BigDecimal returnAmount) {
        // 查询老人信息是否存在
        ElderlyPeopleInfo elderlyInfo = elderlyPeopleInfoService.selectElderlyPeopleInfoById(elderlyId);
        if (elderlyInfo == null) {
            throw new ServiceException("老人信息不存在，无法退还保障金");
        }
        
        // 查询保障金账户信息
        SecurityBalanceInfo securityBalanceInfo = securityBalanceInfoMapper.selectSecurityBalanceInfoByElderlyId(elderlyId);
        
        // 如果账户不存在或余额不足，则抛出异常
        if (securityBalanceInfo == null) {
            throw new ServiceException("保障金账户不存在，无法退款");
        }
        
        BigDecimal lastAmount = securityBalanceInfo.getAmount();
        if (lastAmount == null) {
            lastAmount = new BigDecimal("0");
        }
        
        // 检查余额是否充足
        if (lastAmount.compareTo(returnAmount) < 0) {
            throw new ServiceException("保障金账户余额不足，无法退款");
        }
        
        // 更新账户金额
        securityBalanceInfo.setLastAmount(lastAmount);
        securityBalanceInfo.setAmount(lastAmount.subtract(returnAmount));
        securityBalanceInfo.setUpdateBy(operatorId);
        securityBalanceInfo.setUpdateTime(new Date());
        securityBalanceInfoMapper.updateSecurityBalanceInfo(securityBalanceInfo);

        // 创建保障金账户记录
        SecurityBalanceRecords securityBalanceRecords = BeanUtil.copyProperties(securityBalanceInfo, SecurityBalanceRecords.class);
        securityBalanceRecords.setCreateBy(operatorId);
        securityBalanceRecords.setCreateTime(new Date());
        securityBalanceRecords.setChangedAmount(returnAmount);
        securityBalanceRecords.setChangedType("3"); // 退款
        securityBalanceRecords.setDelFlag("0");
        securityBalanceRecordsMapper.insertSecurityBalanceRecords(securityBalanceRecords);

        return securityBalanceRecords;
    }
} 