package com.ruoyi.custom.admin.pharmacyWarehouse.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "药品库存")
public class PharmacyInventory {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "仓库id，对应t_pharmacy_warehouse")
    private Long warehouseId;

    @ApiModelProperty(value = "药品id，对应t_pharmacy_management")
    private Long pharmacyId;

    @ApiModelProperty(value = "批次")
    private Integer batch;

    @ApiModelProperty(value = "规格")
    private String specifications;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "余量")
    private Integer quantity;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "市场价")
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "生产时间")
    private Date productionTime;

    @ApiModelProperty(value = "保质期/天")
    private Integer shelfLife;

    @ApiModelProperty(value = "仓库名称")
    @TableField(exist = false)
    private String warehouseName;

    @ApiModelProperty(value = "药品名称")
    @TableField(exist = false)
    private String pharmacyName;

    @ApiModelProperty(value = "药品类型")
    @TableField(exist = false)
    private String pharmacyTypeName;
}

