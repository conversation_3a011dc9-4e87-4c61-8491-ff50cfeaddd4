package com.ruoyi.custom.admin.mealManagement.service;

import java.util.List;

import com.ruoyi.custom.admin.mealManagement.domain.RecipeDetailsInfo;

/**
 * 食谱详情Service接口
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
public interface IRecipeDetailsInfoService {
    /**
     * 查询食谱详情
     *
     * @param id 食谱详情主键
     * @return 食谱详情
     */
    public RecipeDetailsInfo selectRecipeDetailsInfoById(Long id);

    /**
     * 查询食谱详情列表
     *
     * @param recipeDetailsInfo 食谱详情
     * @return 食谱详情集合
     */
    public List<RecipeDetailsInfo> selectRecipeDetailsInfoList(RecipeDetailsInfo recipeDetailsInfo);

    /**
     * 新增食谱详情
     *
     * @param recipeDetailsInfo 食谱详情
     * @return 结果
     */
    public int insertRecipeDetailsInfo(RecipeDetailsInfo recipeDetailsInfo);

    /**
     * 修改食谱详情
     *
     * @param recipeDetailsInfo 食谱详情
     * @return 结果
     */
    public int updateRecipeDetailsInfo(RecipeDetailsInfo recipeDetailsInfo);

    /**
     * 批量删除食谱详情
     *
     * @param ids 需要删除的食谱详情主键集合
     * @return 结果
     */
    public int deleteRecipeDetailsInfoByIds(Long[] ids);

    /**
     * 删除食谱详情信息
     *
     * @param id 食谱详情主键
     * @return 结果
     */
    public int deleteRecipeDetailsInfoById(Long id);
}
