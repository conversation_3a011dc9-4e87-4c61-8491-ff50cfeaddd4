package com.ruoyi.custom.admin.dashboard.service.impl;

import com.ruoyi.custom.admin.dashboard.mapper.DashboardMapper;
import com.ruoyi.custom.admin.dashboard.service.DashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 大屏接口
 */
@Service
public class DashboardServiceimpl implements DashboardService {

    @Autowired
    private DashboardMapper dashboardMapper;


    @Override
    public List<Map<String, Object>> getElderAgeData() {
        return dashboardMapper.getElderAgeData();
    }

    @Override
    public List<Map<String, Object>> getAbilityAssessment() {
        return dashboardMapper.getAbilityAssessment();
    }

    @Override
    public List<Map<String, Object>> getCheckInFlow() {
        return dashboardMapper.getCheckInFlow();
    }

    @Override
    public List<Map<String, Object>> getIncomeTrend() {
        return dashboardMapper.getIncomeTrend();
    }

}
