package com.ruoyi.custom.admin.marketing.domain;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "缴费变更记录", description = "缴费变更记录表")
public class PaymentChangeRecord extends BaseEntity {

    @TableId
    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("合同编号")
    private String contractNumber;

    @ApiModelProperty(value = "居住id")
    private String liveId;

    @ApiModelProperty("老人ID")
    private String elderlyId;

    @ApiModelProperty("老人姓名")
    private String elderlyName;

    @ApiModelProperty("合同开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractStartDate;

    @ApiModelProperty("合同结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndDate;

    @ApiModelProperty("合同周期（月）")
    private Integer contractCycle;

    @ApiModelProperty("护理级别")
    private String careLevel;

    @ApiModelProperty("床位")
    private String bedName;

    @ApiModelProperty(value = "本次账户变动金额")
    private BigDecimal accountAddCost;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("变更详情（JSON）")
    private List<Detail> details;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @Data
    @ApiModel(value = "Detail", description = "费用明细")
    public static class Detail {

        @ApiModelProperty("类型")
        private String type;

        @ApiModelProperty("类型名称")
        private String typeName;

        @ApiModelProperty("变更前床位id")
        private Long oldBedId;

        @ApiModelProperty("变更前收费标准")
        private BigDecimal originalFeeStandard;

        @ApiModelProperty("变更后收费标准")
        private BigDecimal changedFeeStandard;

        @ApiModelProperty("已缴纳金额")
        private BigDecimal paidAmount;

        @ApiModelProperty("变更后条目")
        private JSONObject changedItem;

        @ApiModelProperty("折扣")
        private String discount;

        @ApiModelProperty("调整金额")
        private BigDecimal changedAmount;

        @ApiModelProperty("变更开始日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date startDate;

        @ApiModelProperty("变更结束日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date endDate;

        @ApiModelProperty("备注")
        private String remark;
    }
}

