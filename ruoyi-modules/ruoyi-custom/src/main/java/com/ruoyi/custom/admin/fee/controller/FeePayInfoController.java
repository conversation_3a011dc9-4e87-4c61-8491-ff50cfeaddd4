package com.ruoyi.custom.admin.fee.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.fee.domain.req.FeePayQueryVo;
import com.ruoyi.custom.admin.fee.domain.req.FeePaySaveVo;
import com.ruoyi.custom.admin.fee.domain.res.FeePayVo;
import com.ruoyi.custom.admin.fee.service.IFeePayInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 费用支付记录Controller
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@Api(tags = "费用-费用缴纳")
@RestController
@RequestMapping("/fee_pay")
public class FeePayInfoController extends BaseController {
    @Autowired
    private IFeePayInfoService feePayInfoService;

    /**
     * 查询费用支付记录列表
     */
    @ApiOperation("查询缴费记录列表")
    //@RequiresPermissions("custom:fee_pay:list")
    @GetMapping("/list")
    public TableDataInfo<FeePayVo> list(FeePayQueryVo feePayQueryVo) {
        startPage();
        List<FeePayVo> list = feePayInfoService.selectNewFeePayInfoList(feePayQueryVo);
        return getDataTable(list);
    }

    /**
     * 获取费用支付记录详细信息
     */
    @ApiOperation("获取费用支付记录详细信息")
    //@RequiresPermissions("custom:fee_pay:query")
    @GetMapping(value = "/{id}")
    public TAjaxResult<FeePayVo> getInfo(@PathVariable("id") String id) {
        TAjaxResult<FeePayVo> result = new TAjaxResult<>();
        result.success(feePayInfoService.selectNewFeePayInfoById(id));
        return result;
    }

    /**
     * 充值
     */
    @ApiOperation("充值")
    //@RequiresPermissions("custom:fee_pay:add")
    @Log(platform = "1", title = "费用支付记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FeePaySaveVo feePaySaveVo) {
        feePaySaveVo.setConsumeAccounType("3"); // 3：充值
        return toAjax(feePayInfoService.insertFeePaySaveVo(feePaySaveVo, "6"));
    }

    // /**
    //  * 导出费用支付记录列表
    //  */
    // @ApiIgnore
    // //@RequiresPermissions("custom:fee_pay:export")
    // @Log(platform = "1", title = "费用支付记录", businessType = BusinessType.EXPORT)
    // @PostMapping("/export")
    // public void export(HttpServletResponse response, FeePayInfo feePayInfo) {
    //     List<FeePayInfo> list = feePayInfoService.selectFeePayInfoList(feePayInfo);
    //     ExcelUtil<FeePayInfo> util = new ExcelUtil<FeePayInfo>(FeePayInfo.class);
    //     util.exportExcel(response, list, "费用支付记录数据");

    // }

    // /**
    //  * 修改费用支付记录
    //  */
    // @ApiIgnore
    // //@RequiresPermissions("custom:fee_pay:edit")
    // @Log(platform = "1", title = "费用支付记录", businessType = BusinessType.UPDATE)
    // @PutMapping
    // public AjaxResult edit(@RequestBody FeePayInfo feePayInfo) {
    //     return toAjax(feePayInfoService.updateFeePayInfo(feePayInfo));
    // }

    // /**
    //  * 删除费用支付记录
    //  */
    // @ApiIgnore
    // //@RequiresPermissions("custom:fee_pay:remove")
    // @Log(platform = "1", title = "费用支付记录", businessType = BusinessType.DELETE)
    // @DeleteMapping("/{ids}")
    // public AjaxResult remove(@PathVariable String[] ids) {
    //     return toAjax(feePayInfoService.deleteFeePayInfoByIds(ids));
    // }
}
