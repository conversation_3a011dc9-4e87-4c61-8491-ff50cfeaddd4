package com.ruoyi.custom.admin.elderlyPeople.service;

import java.util.List;

import cn.hutool.json.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.domain.vo.ElderlyPeopleInfoLivingVo;
import com.ruoyi.custom.admin.elderlyPeople.domain.vo.ElderlyPeopleInfoVo;
import com.ruoyi.custom.admin.marketing.domain.ContractInfo;
import com.ruoyi.custom.admin.marketing.resp.ElderlyPeopleInfo2;
import org.apache.ibatis.annotations.Param;

/**
 * 老人基础信息Service接口
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
public interface IElderlyPeopleInfoService {
    /**
     * 查询老人基础信息
     *
     * @param id 老人基础信息主键
     * @return 老人基础信息
     */
    public ElderlyPeopleInfo selectElderlyPeopleInfoById(String id);

    /**
     * 查询老人基础信息列表
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 老人基础信息集合
     */
    public List<ElderlyPeopleInfo> selectElderlyPeopleInfoList(ElderlyPeopleInfo elderlyPeopleInfo);

    /**
     * 健康管理获取老人全量列表
     *
     * @param name
     * @return
     */
    public List<JSONObject> getUserList(@Param("name") String name, @Param("state") String state);

    /**
     * 新增老人基础信息
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 结果
     */
    public String insertElderlyPeopleInfo(ElderlyPeopleInfo elderlyPeopleInfo);

    /**
     * 修改老人基础信息
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 结果
     */
    public int updateElderlyPeopleInfo(ElderlyPeopleInfo elderlyPeopleInfo);

    /**
     * 批量删除老人基础信息
     *
     * @param ids 需要删除的老人基础信息主键集合
     * @return 结果
     */
    public int deleteElderlyPeopleInfoByIds(Long[] ids);


    public int logicalDeleteElderlyPeopleInfoByIds(String[] ids);

    /**
     * 删除老人基础信息信息
     *
     * @param id 老人基础信息主键
     * @return 结果
     */
    public int deleteElderlyPeopleInfoById(Long id);

    /**
     * 老人信息列表
     *
     * @param elderlyPeopleInfoVo
     * @return
     */
    List<ElderlyPeopleInfoVo> getPeopleInfoList(ElderlyPeopleInfoVo elderlyPeopleInfoVo);

    /**
     * 根据用户id查询老人信息
     * @param userid
     * @return
     */
    ElderlyPeopleInfo getElderlyPeopleInfoBySysUserId(Long userid);

    /**
     * 查询护工管理的老人列表
     * @param workId
     * @return
     */
    List<JSONObject> getEldersByWorkerId(Long workId);

    /**
     * 查询护工管理的老人列表
     * @param elderlyPeopleInfoVo
     * @return
     */
    List<ElderlyPeopleInfoLivingVo> getLivingInfoList(ElderlyPeopleInfoLivingVo elderlyPeopleInfoVo);

    /**
     * 老人签约
     * @param contractInfo
     * @return
     */
    int sign(ContractInfo contractInfo);

    /**
     * 获取近一个月每天的入住老人数量
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 近一个月每天入住老人数量统计
     */
    List<JSONObject> getCheckInFlowStatistics(String startDate, String endDate);

    /**
     * 查询老人基础信息列表2
     *
     * @param elderlyPeopleInfo
     * @return
     */
    List<ElderlyPeopleInfo2> selectElderlyPeopleInfoList2(ElderlyPeopleInfo2 elderlyPeopleInfo);
}
