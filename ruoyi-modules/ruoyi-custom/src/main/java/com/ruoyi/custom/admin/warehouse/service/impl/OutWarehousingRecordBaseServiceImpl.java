package com.ruoyi.custom.admin.warehouse.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.warehouse.domain.MaterialsRemainingStock;
import com.ruoyi.custom.admin.warehouse.domain.OutWarehousingRecordBase;
import com.ruoyi.custom.admin.warehouse.domain.WarehousingMaterialsLogInfo;
import com.ruoyi.custom.admin.warehouse.mapper.OutWarehousingRecordBaseMapper;
import com.ruoyi.custom.admin.warehouse.mapper.WarehousingMaterialsLogInfoMapper;
import com.ruoyi.custom.admin.warehouse.service.IMaterialsRemainingStockService;
import com.ruoyi.custom.admin.warehouse.service.IOutWarehousingRecordBaseService;
import com.ruoyi.custom.utils.OrderUtils;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 出库记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-09
 */
@Service
public class OutWarehousingRecordBaseServiceImpl implements IOutWarehousingRecordBaseService {

    @Autowired
    private OutWarehousingRecordBaseMapper outWarehousingRecordBaseMapper;

    @Autowired
    private WarehousingMaterialsLogInfoMapper warehousingMaterialsLogInfoMapper;

    @Autowired
    private IMaterialsRemainingStockService stockService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询出库记录
     *
     * @param id 出库记录主键
     * @return 出库记录
     */
    @Override
    public OutWarehousingRecordBase selectOutWarehousingRecordBaseById(Long id) {
        OutWarehousingRecordBase data = outWarehousingRecordBaseMapper.selectOutWarehousingRecordBaseById(id);

        if (Objects.nonNull(data) && StrUtil.isNotBlank(data.getRegisterPerson())) {
            R<SysUser> userR = remoteUserService.getInfoByUserId(Long.valueOf(data.getRegisterPerson()));
            if (Objects.nonNull(userR) && userR.getCode() == 200 && Objects.nonNull(userR.getData())) {
                data.setRegisterPersonName(userR.getData().getNickName());
            }
        }

        if (Objects.nonNull(data) && StrUtil.isNotBlank(data.getReceiverPerson())) {
            R<SysUser> userR = remoteUserService.getInfoByUserId(Long.valueOf(data.getReceiverPerson()));
            if (Objects.nonNull(userR) && userR.getCode() == 200 && Objects.nonNull(userR.getData())) {
                data.setReceiverPersonName(userR.getData().getNickName());
            }
        }

        if (null != data) {
            List<WarehousingMaterialsLogInfo> list = warehousingMaterialsLogInfoMapper.getLogInfoByBaseId(id, "1");
            data.setDataList(list);
        }
        return data;
    }

    /**
     * 查询出库记录列表
     *
     * @param outWarehousingRecordBase 出库记录
     * @return 出库记录
     */
    @Override
    public List<OutWarehousingRecordBase> selectOutWarehousingRecordBaseList(OutWarehousingRecordBase outWarehousingRecordBase) {
        List<OutWarehousingRecordBase> list = outWarehousingRecordBaseMapper.selectOutWarehousingRecordBaseList(outWarehousingRecordBase);
        List<Long> ids = list.stream()
                .map(a -> a.getRegisterPerson())
                .filter(StrUtil::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toList());

        Map<Long, SysUser> userMap = remoteUserService.getUserMapByIds(ids.toArray(new Long[0]));
        list.forEach(a -> {
            if (StrUtil.isNotBlank(a.getRegisterPerson())) {
                SysUser sysUser = userMap.get(Long.parseLong(a.getRegisterPerson()));
                if (!Objects.isNull(sysUser)) {
                    a.setRegisterPersonName(sysUser.getNickName());
                }
            }
        });

        List<Long> ids2 = list.stream()
                .map(a -> a.getRegisterPerson())
                .filter(StrUtil::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        Map<Long, SysUser> userMap2 = remoteUserService.getUserMapByIds(ids2.toArray(new Long[0]));
        list.forEach(a -> {
            if (StrUtil.isNotBlank(a.getReceiverPerson())) {
                SysUser sysUser = userMap2.get(Long.parseLong(a.getReceiverPerson()));
                if (!Objects.isNull(sysUser)) {
                    a.setReceiverPersonName(sysUser.getNickName());
                }
            }
        });

        return list;
    }

    /**
     * 新增出库记录
     *
     * @param outWarehousingRecordBase 出库记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertOutWarehousingRecordBase(OutWarehousingRecordBase outWarehousingRecordBase) {
        outWarehousingRecordBase.setCreateTime(DateUtils.getNowDate());
        outWarehousingRecordBase.setRegisterPerson(String.valueOf(SecurityUtils.getUserId()));
        outWarehousingRecordBase.setNumber(OrderUtils.getOutCode());
        int i = outWarehousingRecordBaseMapper.insertOutWarehousingRecordBase(outWarehousingRecordBase);
        List<WarehousingMaterialsLogInfo> dataList = outWarehousingRecordBase.getDataList();

        if (CollUtil.isEmpty(dataList)) {
            throw new ServiceException("请选择物资");
        }

        for (WarehousingMaterialsLogInfo logInfo : dataList) {
            MaterialsRemainingStock stockInfo = stockService.getByBaseId(logInfo.getMaterialsId(), outWarehousingRecordBase.getWarehouseId());// 获取剩余数量
            logInfo.setBaseId(outWarehousingRecordBase.getId());
            logInfo.setType("1");
            logInfo.setWarehouseId(outWarehousingRecordBase.getWarehouseId());
            logInfo.setPreviousQuantity(stockInfo.getQuantity());// 变化之前数量
            Long remainQuantity = stockInfo.getQuantity() - logInfo.getQuantity();
            if (remainQuantity < 0) {
                throw new ServiceException("出库数量不能大于剩余数量");
            }
            logInfo.setCurrentQuantity(remainQuantity);// 变化之后数量
            warehousingMaterialsLogInfoMapper.insertWarehousingMaterialsLogInfo(logInfo);
            stockInfo.setQuantity(stockInfo.getQuantity() - logInfo.getQuantity());// 剩余数量
            stockInfo.setWarehouseId(outWarehousingRecordBase.getWarehouseId());
            stockService.save(stockInfo);
        }
        return 1;
    }

    /**
     * 修改出库记录
     *
     * @param outWarehousingRecordBase 出库记录
     * @return 结果
     */
    @Override
    public int updateOutWarehousingRecordBase(OutWarehousingRecordBase outWarehousingRecordBase) {
        outWarehousingRecordBase.setUpdateTime(DateUtils.getNowDate());
        return outWarehousingRecordBaseMapper.updateOutWarehousingRecordBase(outWarehousingRecordBase);
    }

    /**
     * 批量删除出库记录
     *
     * @param ids 需要删除的出库记录主键
     * @return 结果
     */
    @Override
    public int deleteOutWarehousingRecordBaseByIds(Long[] ids) {
        return outWarehousingRecordBaseMapper.deleteOutWarehousingRecordBaseByIds(ids);
    }

    /**
     * 删除出库记录信息
     *
     * @param id 出库记录主键
     * @return 结果
     */
    @Override
    public int deleteOutWarehousingRecordBaseById(Long id) {
        return outWarehousingRecordBaseMapper.deleteOutWarehousingRecordBaseById(id);
    }

}
