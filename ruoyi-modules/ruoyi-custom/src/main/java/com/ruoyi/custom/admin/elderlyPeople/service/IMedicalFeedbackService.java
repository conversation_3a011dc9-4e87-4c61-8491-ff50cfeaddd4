package com.ruoyi.custom.admin.elderlyPeople.service;


import com.ruoyi.custom.admin.elderlyPeople.domain.MedicalFeedback;

import java.util.List;


/**
 * 医护反馈Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IMedicalFeedbackService {
    /**
     * 查询医护反馈
     *
     * @param id 医护反馈主键
     * @return 医护反馈
     */
    public MedicalFeedback selectMedicalFeedbackById(Long id);

    /**
     * 查询医护反馈列表
     *
     * @param medicalFeedback 医护反馈
     * @return 医护反馈集合
     */
    public List<MedicalFeedback> selectMedicalFeedbackList(MedicalFeedback medicalFeedback);

    /**
     * 新增医护反馈
     *
     * @param medicalFeedback 医护反馈
     * @return 结果
     */
    public int insertMedicalFeedback(MedicalFeedback medicalFeedback);

    /**
     * 修改医护反馈
     *
     * @param medicalFeedback 医护反馈
     * @return 结果
     */
    public int updateMedicalFeedback(MedicalFeedback medicalFeedback);

    /**
     * 批量删除医护反馈
     *
     * @param ids 需要删除的医护反馈主键集合
     * @return 结果
     */
    public int deleteMedicalFeedbackByIds(Long[] ids);

    /**
     * 删除医护反馈信息
     *
     * @param id 医护反馈主键
     * @return 结果
     */
    public int deleteMedicalFeedbackById(Long id);
}

