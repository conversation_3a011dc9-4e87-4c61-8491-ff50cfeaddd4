package com.ruoyi.custom.admin.elderlyPeople.domain;

import java.util.Date;
import java.util.List;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 老人体检记录对象 t_elderly_people_physical_examination_records
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@Data
@ApiModel(value = "老人体检记录")
public class ElderlyPeoplePhysicalExaminationRecords extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 老人id
     */
    @Excel(name = "老人id")
    @ApiModelProperty(value = "老人id")
    private String userId;

    /**
     * 体检医院
     */
    @Excel(name = "体检医院")
    @ApiModelProperty(value = "体检医院")
    private String medicalExaminationHospital;

    /**
     * 体检时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "体检时间")
    @Excel(name = "体检时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date physicalExaminationTime;

    /**
     * 体检项目
     */
    @Excel(name = "体检项目")
    @ApiModelProperty(value = "体检项目")
    private String examinationItem;

    /**
     * 体检结果
     */
    @Excel(name = "体检结果")
    @ApiModelProperty(value = "体检结果")
    private String examinationResult;

    /**
     * 体检结果图片
     */
    @Excel(name = "体检结果图片")
    @ApiModelProperty(value = "体检结果图片")
    private String physicalExaminationImg;

    private JSONArray imgArr;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

}
