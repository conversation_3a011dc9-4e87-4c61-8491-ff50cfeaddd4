package com.ruoyi.custom.admin.elderlyPeople.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.custom.admin.marketing.domain.ContractInfo;
import com.ruoyi.custom.utils.DictUtils;
import io.seata.common.util.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 老人基础信息对象 t_elderly_people_info
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
@Data
@ApiModel(value = "老人基础信息")
public class ElderlyPeopleInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    private String id;
    /**
     * 目前老人居住id
     */
    private String liveId;

    /**
     * 床位id
     */
    private Long bedId;

    /**
     * 床位记录id
     */
    private String bedRecordId;

    /**
     * 床位
     */
    private String bedName;

    /**
     * 护理级别
     */
    private String careLevel;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Excel(name = "姓名")
    private String name;

    /**
     * 性别
     */
    @Excel(name = "性别")
    @ApiModelProperty(value = "性别")
    private String sex;
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    @ApiModelProperty(value = "性别label")
    private String sexStr;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "出生日期")
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dateBirth;

    /**
     * 年龄
     */
    @Excel(name = "年龄")
    @ApiModelProperty(value = "年龄")
    private Integer age;

    /**
     * 民族
     */
    @Excel(name = "民族")
    @ApiModelProperty(value = "民族")
    private String nation;
    @ApiModelProperty(value = "民族label")
    private String nationStr;

    /**
     * 婚姻情况
     */
    @Excel(name = "婚姻情况")
    @ApiModelProperty(value = "婚姻情况")
    private String marriageStatus;

    @ApiModelProperty(value = "民族label")
    private String marriageStatusStr;

    /**
     * 居住情况
     */
    @Excel(name = "居住情况")
    @ApiModelProperty(value = "居住情况")
    private String livingSituation;
    @ApiModelProperty(value = "居住情况label")
    private String livingSituationStr;

    /**
     * 家庭住址
     */
    @Excel(name = "家庭住址")
    @ApiModelProperty(value = "家庭住址")
    private String homeAddress;

    /**
     * 紧急联系人姓名
     */
    @Excel(name = "紧急联系人姓名")
    @ApiModelProperty(value = "紧急联系人姓名")
    private String emergencyContactName;

    /**
     * 紧急联系人手机号
     */
    @Excel(name = "紧急联系人手机号")
    @ApiModelProperty(value = "紧急联系人手机号")
    private String emergencyContactPhone;

    /**
     * 紧急联系人工作单位
     */
    @ApiModelProperty(value = "紧急联系人工作单位")
    private String emergencyWorkUnit;

    /**
     * 紧急联系人住址
     */
    @ApiModelProperty(value = "紧急联系人住址")
    private String emergencyAddress;

    /**
     * 关系
     */
    @Excel(name = "关系")
    @ApiModelProperty(value = "关系")
    private String relation;
    @ApiModelProperty(value = "关系label")
    private String relationStr;

    /**
     * 经济来源
     */
    @Excel(name = "经济来源")
    @ApiModelProperty(value = "经济来源")
    private String economicSources;
    @ApiModelProperty(value = "经济来源label")
    private String economicSourcesStr;

    /**
     * 月收入
     */
    @Excel(name = "月收入")
    @ApiModelProperty(value = "月收入")
    private String monthlyIncome;

    /**
     * 社保号
     */
    @Excel(name = "社保号")
    @ApiModelProperty(value = "社保号")
    private String socialSecurityNo;

    /**
     * 入住状态
     */
    @Excel(name = "入住状态")
    @ApiModelProperty(value = "入住状态")
    private String status;
    @ApiModelProperty(value = "入住状态label")
    private String statusStr;

    /**
     * 头像照片
     */
    @Excel(name = "头像照片")
    @ApiModelProperty(value = "头像照片")
    private String img;

    /**
     * 系统用户id
     */
    @Excel(name = "系统用户id")
    @ApiModelProperty(value = "系统用户id")
    private Long sysUserId;

    /**
     * 营销客户id
     */
    @ApiModelProperty(value = "营销客户id")
    private Long customerId;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    /**
     * 签约状态
     */
    private String contractStateStr;

    /**
     * 账户余额
     */
    @ApiModelProperty(value = "账户余额")
    private BigDecimal amount;

    /**
     * 保证金余额
     */
    @ApiModelProperty(value = "保证金余额")
    private BigDecimal securityAmount;

    /**
     * 人员情况，字典：custom_elderly_people_info_staff_status；0：在世，1：离世
     */
    @ApiModelProperty(value = "人员情况，字典：custom_elderly_people_info_staff_status；0：在世，1：离世")
    private String staffStatus;

    /**
     * 合同信息
     */
    @ApiModelProperty(value = "合同信息")
    private ContractInfo contractInfo;

    /**
     * 营销
     */
    private Long marketerId;

    /**
     * 营销人名字
     */
    private String marketer;

    public String getSexStr() {
        if (StringUtils.isBlank(this.sex)) {
            return this.sex;
        }
        return DictUtils.selectDictLabel("sys_user_sex", this.sex);
    }

    public void setSexStr(String sexStr) {
        this.sexStr = sexStr;
    }

    public String getNationStr() {
        if (StringUtils.isBlank(this.nation)) {
            return this.nation;
        }
        return DictUtils.selectDictLabel("nation", this.nation);
    }


    public String getMarriageStatusStr() {
        if (StringUtils.isBlank(this.marriageStatus)) {
            return this.marriageStatus;
        }
        return DictUtils.selectDictLabel("marriage_state", this.marriageStatus);
    }

    public String getLivingSituationStr() {
        if (StringUtils.isBlank(this.livingSituation)) {
            return this.livingSituation;
        }
        return DictUtils.selectDictLabel("living_situation", this.livingSituation);
    }

    public String getRelationStr() {
        if (StringUtils.isBlank(this.relation)) {
            return this.relation;
        }
        return DictUtils.selectDictLabel("relation", this.relation);
    }

    public void setRelationStr(String relationStr) {
        this.relationStr = relationStr;
    }

    public String getEconomicSourcesStr() {
        if (StringUtils.isBlank(this.economicSources)) {
            return this.economicSources;
        }
        return DictUtils.selectDictLabel("economic_sources", this.economicSources);
    }

    public String getStatusStr() {
        if (StringUtils.isBlank(this.status)) {
            return this.status;
        }
        return DictUtils.selectDictLabel("live_state", this.status);
    }

    public void setImg(String img) {
        {
            if ("".equals(img) || img == null) {
                this.img = "";// 去除该属性的前后空格并进行非空非null判断
            } else {
                this.img = img;
            }
        }
    }

    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

}
