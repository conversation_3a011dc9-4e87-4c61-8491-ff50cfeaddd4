package com.ruoyi.custom.admin.serviceWorkOrder.vo;

import com.ruoyi.custom.admin.serviceWorkOrder.domain.OrderServiceWork;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description home_service_work_order
 * @date 2022-07-18
 */
@Data
@ApiModel("service_work_order")
public class AppServiceWorkOrderInfoRequestVo extends OrderServiceWork {

    @ApiModelProperty("支付方式")
    private Integer payType;

    @ApiModelProperty("支付方式")
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private String payTypeLabel;

    @ApiModelProperty("支付金额")
    private BigDecimal payAmount;

    @ApiModelProperty("服务状态")
    private String serviceStatusLabel;

    // public String getPayTypeLabel() {
    //     if (null != this.payType) {
    //         if (super.getServiceType() == 0) {
    //             if (this.payType == 1) {
    //                 return "余额付款";
    //             } else {
    //                 return "微信在线支付";
    //             }
    //         } else {
    //             return "套餐结算";
    //         }
    //
    //     }
    //     return "--";
    // }
    //
    // public void setPayTypeLabel(String payTypeLabel) {
    //     this.payTypeLabel = payTypeLabel;
    // }

    public String getServiceStatusLabel() {
        if (null != super.getStatus()) {
            if (super.getStatus() == 1) {
                return "待指派人员";
            } else if (super.getStatus() == 2) {
                return "未开始";
            } else if (super.getStatus() == 3) {
                return "服务中";
            } else if (super.getStatus() == 3) {
                return "服务中";
            } else if (super.getStatus() == 4) {
                return "已完成";
            }
        }
        return "--";
    }

    public void setServiceStatusLabel(String serviceStatusLabel) {
        this.serviceStatusLabel = serviceStatusLabel;
    }
}
