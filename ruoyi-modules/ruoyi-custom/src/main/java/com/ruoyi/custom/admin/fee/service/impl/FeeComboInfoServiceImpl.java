package com.ruoyi.custom.admin.fee.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.fee.domain.FeeComboDTO;
import com.ruoyi.custom.admin.fee.domain.FeeComboInfo;
import com.ruoyi.custom.admin.fee.domain.res.FeeComboVo;
import com.ruoyi.custom.admin.fee.mapper.FeeComboInfoMapper;
import com.ruoyi.custom.admin.fee.service.IFeeComboInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 套餐费用Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@Service
public class FeeComboInfoServiceImpl implements IFeeComboInfoService {
    @Autowired
    private FeeComboInfoMapper feeComboInfoMapper;

    /**
     * 查询套餐费用
     *
     * @param id 套餐费用主键
     * @return 套餐费用
     */
    @Override
    public FeeComboInfo selectFeeComboInfoById(String id) {
        return feeComboInfoMapper.selectFeeComboInfoById(id);
    }

    /**
     * 查询套餐费用列表
     *
     * @param feeComboInfo 套餐费用
     * @return 套餐费用
     */
    @Override
    public List<FeeComboInfo> selectFeeComboInfoList(FeeComboInfo feeComboInfo) {
        return feeComboInfoMapper.selectFeeComboInfoList(feeComboInfo);
    }

    /**
     * 新增套餐费用
     *
     * @param feeComboVo 套餐费用
     * @return 结果
     */
    @Override
    public int insertFeeComboInfo(FeeComboVo feeComboVo) {
        FeeComboInfo feeComboInfo = BeanUtil.copyProperties(feeComboVo, FeeComboInfo.class);
        feeComboInfo.setId(IdUtil.fastSimpleUUID());
        feeComboInfo.setCreateBy(SecurityUtils.getUserId().toString());
        feeComboInfo.setCreateTime(DateUtils.getNowDate());
        feeComboInfo.setState("0");
        feeComboInfo.setVersion(1L);
        feeComboInfo.setMonthDetails(JSONUtil.toJsonStr(feeComboVo.getMonthDetails()));
        return feeComboInfoMapper.insertFeeComboInfo(feeComboInfo);
    }

    /**
     * 修改套餐费用
     *
     * @param feeComboVo 套餐费用
     * @return 结果
     */
    @Override
    public int updateFeeComboInfo(FeeComboVo feeComboVo) {
        FeeComboInfo param = new FeeComboInfo();
        param.setState("0");
        param.setComboId(feeComboVo.getComboId());
        List<FeeComboInfo> feeComboInfos = feeComboInfoMapper.selectFeeComboInfoList(param);

        Long version = 1L;

        for (FeeComboInfo feeComboInfo : feeComboInfos) {
            if (feeComboInfo.getVersion() >= version) {
                version = feeComboInfo.getVersion() + 1L;
            }
            feeComboInfo.setState("1");
            feeComboInfo.setUpdateTime(new Date());
            feeComboInfo.setUpdateBy(SecurityUtils.getUserId().toString());
            feeComboInfoMapper.updateFeeComboInfo(feeComboInfo);
        }


        FeeComboInfo feeComboInfo = BeanUtil.copyProperties(feeComboVo, FeeComboInfo.class);
        feeComboInfo.setId(IdUtil.fastSimpleUUID());
        feeComboInfo.setCreateBy(SecurityUtils.getUserId().toString());
        feeComboInfo.setCreateTime(DateUtils.getNowDate());
        feeComboInfo.setState("0");
        feeComboInfo.setVersion(version);
        feeComboInfo.setMonthDetails(JSONUtil.toJsonStr(feeComboVo.getMonthDetails()));
//        feeComboInfo.setDayDetails(JSONUtil.toJsonStr(feeComboVo.getDayDetails()));
        return feeComboInfoMapper.insertFeeComboInfo(feeComboInfo);
    }

    /**
     * 批量删除套餐费用
     *
     * @param ids 需要删除的套餐费用主键
     * @return 结果
     */
    @Override
    public int deleteFeeComboInfoByIds(String[] ids) {
        return feeComboInfoMapper.deleteFeeComboInfoByIds(ids);
    }

    /**
     * 删除套餐费用信息
     *
     * @param id 套餐费用主键
     * @return 结果
     */
    @Override
    public int deleteFeeComboInfoById(String id) {
        return feeComboInfoMapper.deleteFeeComboInfoById(id);
    }

    @Override
    public List<FeeComboDTO> selectComboList(String careLevel, String comboName) {
        List<FeeComboDTO> feeComboDTOS = feeComboInfoMapper.selectComboList(careLevel, comboName);
        return feeComboDTOS;
    }

    @Override
    public FeeComboVo selectComboById(String id) {
        FeeComboDTO feeComboDTO = feeComboInfoMapper.selectComboById(id);
        FeeComboVo feeComboVo = BeanUtil.copyProperties(feeComboDTO, FeeComboVo.class);
//        feeComboVo.setDayDetails(JSONUtil.parseArray(feeComboDTO.getDayDetails()));
        feeComboVo.setMonthDetails(JSONUtil.parseArray(feeComboDTO.getMonthDetails()));
        return feeComboVo;
    }
}
