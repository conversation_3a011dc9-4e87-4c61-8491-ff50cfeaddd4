package com.ruoyi.custom.api.olderApplet;

import com.ruoyi.common.core.utils.bean.BeanUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.liveManage.domain.LiveLeaveRecords;
import com.ruoyi.custom.admin.liveManage.domain.req.LiveLeaveSaveReqVO;
import com.ruoyi.custom.admin.liveManage.service.ILiveLeaveRecordsService;
import com.ruoyi.custom.utils.SysUserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 老人端小程序-居住 Controller
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
@RestController
@RequestMapping("/olderApplet/live")
@Api(tags = "老人端小程序-居住")
public class LiveController extends BaseController {

    @Autowired
    private ILiveLeaveRecordsService liveLeaveRecordsService;

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    /**
     * 请假 - 申请请假
     */
    @PostMapping("/liveLeave/apply")
    @ApiOperation(value = "申请请假")
    public AjaxResult applyLeave(LiveLeaveSaveReqVO params) {
        // 获取当前老人信息
        ElderlyPeopleInfo elderlyPeopleInfo = elderlyPeopleInfoService.getElderlyPeopleInfoBySysUserId(SecurityUtils.getLoginUser().getUserid());
        String status = elderlyPeopleInfo.getStatus();
        if (status == null || !"0".equals(status)) {
            return AjaxResult.error("当前老人未入住，无法进行操作");
        }

        // 装填数据
        LiveLeaveRecords liveLeaveRecords = new LiveLeaveRecords();
        BeanUtils.copyProperties(params, liveLeaveRecords);
        liveLeaveRecords.setLiveId(elderlyPeopleInfo.getLiveId());
        liveLeaveRecords.setUserId(elderlyPeopleInfo.getId());

        // 插入&返回
        return toAjax(liveLeaveRecordsService.insertLiveLeaveRecords(liveLeaveRecords));
    }

    /**
     * 请假 -个人请假记录
     */
    @GetMapping("/liveLeave/ownerRecord")
    @ApiOperation(value = "个人请假记录")
    public TableDataInfo<LiveLeaveRecords> ownerRecord() {
        startPage();
        LiveLeaveRecords params = new LiveLeaveRecords();
        params.setUserId(SysUserUtils.getInfoBySysUserId("applet_lr").getUserId());
        return getDataTable(liveLeaveRecordsService.selectLiveLeaveRecordsList(params));
    }
}
