package com.ruoyi.custom.admin.balance.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.balance.domain.SecurityBalanceInfo;
import com.ruoyi.custom.admin.balance.domain.SecurityBalanceRecords;
import com.ruoyi.custom.admin.balance.service.ISecurityBalanceInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 保障金账户信息Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Api(value = "保障金账户信息管理", tags = "保障金账户信息管理")
@RestController
@RequestMapping("/securityBalance")
public class SecurityBalanceInfoController extends BaseController {
    @Autowired
    private ISecurityBalanceInfoService securityBalanceInfoService;

    /**
     * 查询保障金账户信息列表
     */
    // @RequiresPermissions("custom:securityBalance:list")
    @ApiOperation(value = "查询保障金账户信息列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "elderlyId", value = "老人ID", paramType = "query"),
        @ApiImplicitParam(name = "params.beginTime", value = "开始时间", paramType = "query"),
        @ApiImplicitParam(name = "params.endTime", value = "结束时间", paramType = "query")
    })
    @GetMapping("/list")
    public TableDataInfo list(SecurityBalanceInfo securityBalanceInfo) {
        startPage();
        List<SecurityBalanceInfo> list = securityBalanceInfoService.selectSecurityBalanceInfoList(securityBalanceInfo);
        return getDataTable(list);
    }

    /**
     * 导出保障金账户信息列表
     */
    // @RequiresPermissions("custom:securityBalance:export")
    @ApiOperation(value = "导出保障金账户信息列表")
    @Log(platform = "1", title = "保障金账户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SecurityBalanceInfo securityBalanceInfo) {
        List<SecurityBalanceInfo> list = securityBalanceInfoService.selectSecurityBalanceInfoList(securityBalanceInfo);
        ExcelUtil<SecurityBalanceInfo> util = new ExcelUtil<>(SecurityBalanceInfo.class);
        util.exportExcel(response, list, "保障金账户信息数据");
    }

    /**
     * 获取保障金账户信息详细信息
     */
    // @RequiresPermissions("custom:securityBalance:query")
    @ApiOperation(value = "获取保障金账户信息详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(securityBalanceInfoService.selectSecurityBalanceInfoById(id));
    }

    /**
     * 新增保障金账户信息
     */
    // @RequiresPermissions("custom:securityBalance:add")
    @ApiOperation(value = "新增保障金账户信息")
    @Log(platform = "1", title = "保障金账户信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SecurityBalanceInfo securityBalanceInfo) {
        return toAjax(securityBalanceInfoService.insertSecurityBalanceInfo(securityBalanceInfo));
    }

    /**
     * 修改保障金账户信息
     */
    // @RequiresPermissions("custom:securityBalance:edit")
    @ApiOperation(value = "修改保障金账户信息")
    @Log(platform = "1", title = "保障金账户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SecurityBalanceInfo securityBalanceInfo) {
        return toAjax(securityBalanceInfoService.updateSecurityBalanceInfo(securityBalanceInfo));
    }

    /**
     * 删除保障金账户信息
     */
    // @RequiresPermissions("custom:securityBalance:remove")
    @ApiOperation(value = "删除保障金账户信息")
    @Log(platform = "1", title = "保障金账户信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(securityBalanceInfoService.deleteSecurityBalanceInfoByIds(ids));
    }
    
    /**
     * 充值保障金
     */
    // @RequiresPermissions("custom:securityBalance:pay")
    @ApiOperation(value = "充值保障金")
    @Log(platform = "1", title = "充值保障金", businessType = BusinessType.UPDATE)
    @PostMapping("/pay")
    public AjaxResult pay(@RequestBody SecurityBalanceInfo securityBalanceInfo) {
        String elderlyId = securityBalanceInfo.getElderlyId();
        String operatorId = SecurityUtils.getUsername();
        BigDecimal payAmount = securityBalanceInfo.getAmount();
        SecurityBalanceRecords result = securityBalanceInfoService.doPay(elderlyId, operatorId, payAmount);
        return AjaxResult.success(result);
    }
    
    /**
     * 扣款保障金
     */
    // @RequiresPermissions("custom:securityBalance:deduct")
    @ApiOperation(value = "扣款保障金")
    @Log(platform = "1", title = "扣款保障金", businessType = BusinessType.UPDATE)
    @PostMapping("/deduct")
    public AjaxResult deduct(@RequestBody SecurityBalanceInfo securityBalanceInfo) {
        String elderlyId = securityBalanceInfo.getElderlyId();
        String operatorId = SecurityUtils.getUsername();
        BigDecimal deductAmount = securityBalanceInfo.getAmount();
        SecurityBalanceRecords result = securityBalanceInfoService.doDeduction(elderlyId, operatorId, deductAmount);
        return AjaxResult.success(result);
    }
    
    /**
     * 退还保障金
     */
    // @RequiresPermissions("custom:securityBalance:return")
    @ApiOperation(value = "退还保障金")
    @Log(platform = "1", title = "退还保障金", businessType = BusinessType.UPDATE)
    @PostMapping("/return")
    public AjaxResult returnBalance(@RequestBody SecurityBalanceInfo securityBalanceInfo) {
        String elderlyId = securityBalanceInfo.getElderlyId();
        String operatorId = SecurityUtils.getUsername();
        BigDecimal returnAmount = securityBalanceInfo.getAmount();
        SecurityBalanceRecords result = securityBalanceInfoService.returnSecurityBalance(elderlyId, operatorId, returnAmount);
        return AjaxResult.success(result);
    }
} 