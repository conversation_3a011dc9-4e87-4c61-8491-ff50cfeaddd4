package com.ruoyi.custom.admin.liveManage.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.balance.service.IBalanceInfoService;
import com.ruoyi.custom.admin.liveManage.domain.req.LiveInfoSaveReqVO;
import com.ruoyi.custom.admin.liveManage.domain.req.LiveQueryVo;
import com.ruoyi.custom.admin.liveManage.domain.res.LiveHistoryInfoRes;
import com.ruoyi.custom.admin.liveManage.domain.res.LiveInfoRes;
import com.ruoyi.custom.admin.liveManage.service.ILiveBaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 居住信息基础Controller
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@Api(tags = "居住管理")
@RestController
@RequestMapping("/liveManage/baseinfo")
public class LiveBaseInfoController extends BaseController {
    @Autowired
    private ILiveBaseInfoService liveBaseInfoService;

    @Autowired
    private IBalanceInfoService balanceInfoService;

    /**
     * 查询居住信息基础列表
     */
    @ApiOperation(value = "查询居住信息列表")
    //@RequiresPermissions("liveManage:baseinfo:list")
    @GetMapping("/list")
    public TableDataInfo<LiveInfoRes> list(LiveQueryVo queryVo) {
        startPage();
        List<LiveInfoRes> list = liveBaseInfoService.selectLiveBaseInfoList(queryVo);
        return getDataTable(list);
    }

    /**
     * 查询历史信息列表
     */
    @ApiOperation(value = "查询历史信息列表")
    @GetMapping("/historyList")
    public TableDataInfo<LiveHistoryInfoRes> historyList(String id, String type) {
        startPage();
        List<LiveHistoryInfoRes> list = liveBaseInfoService.selectHistoryList(id, type);
        return getDataTable(list);
    }

    /**
     * 获取居住信息基础详细信息
     */
    @ApiOperation(value = "获取居住信息详细信息")
    //@RequiresPermissions("liveManage:baseinfo:query")
    @GetMapping(value = "/{id}")
    public TAjaxResult<LiveInfoRes> getInfo(@PathVariable("id") String id) {
        return new TAjaxResult(liveBaseInfoService.selectLiveBaseInfoById(id));
    }

    /**
     * 新增居住信息基础（入住办理）
     */
    @ApiOperation(value = "新增居住信息（入住办理）")
    //@RequiresPermissions("liveManage:baseinfo:add")
    @Log(platform = "1", title = "居住信息基础（入住办理）", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LiveInfoSaveReqVO reqVO) {
        return toAjax(liveBaseInfoService.add(reqVO));
    }

    /**
     * 更换房间
     */
    @ApiOperation(value = "更换房间")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "liveId", value = "居住id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "oldBedId", value = "原床位id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "newBedId", value = "新床位id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "newRoomId", value = "新房间id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "newRoomVersion", value = "新房间版本", required = true, dataType = "String"),
    })
    @GetMapping("/changeBed")
    public AjaxResult changeRoom(String liveId, Long oldBedId, Long newBedId, String newRoomId, String newRoomVersion) {
        return toAjax(liveBaseInfoService.changeBed(liveId, oldBedId, newBedId, newRoomId, newRoomVersion));
    }

    /**
     * 入住选择长者筛选列表
     */
    @ApiOperation(value = "入住选择长者筛选列表")
    @GetMapping("/getLivePeopleList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "姓名", required = false, dataType = "String"),
    })
    public TableDataInfo<Map> getLivePeopleList(String name) {
        startPage();
        List<Map> list = liveBaseInfoService.getLivePeopleList(name);
        return getDataTable(list);
    }

}
