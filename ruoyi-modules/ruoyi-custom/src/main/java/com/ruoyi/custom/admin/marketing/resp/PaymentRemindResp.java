package com.ruoyi.custom.admin.marketing.resp;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "PaymentRecordDTO", description = "缴费确认单DTO")
public class PaymentRemindResp {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 老人id
     */
    @ApiModelProperty(value = "老人id")
    private String elderlyId;

    /**
     * 老人姓名
     */
    @ApiModelProperty(value = "老人姓名")
    private String elderlyName;

    /**
     * 到期日期
     */
    @ApiModelProperty(value = "到期日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date estimatedExpiryDate;

    /**
     * 合同开始日期
     */
    @ApiModelProperty(value = "合同开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractStartDate;

    /**
     * 合同结束日期
     */
    @ApiModelProperty(value = "合同结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndDate;

}
