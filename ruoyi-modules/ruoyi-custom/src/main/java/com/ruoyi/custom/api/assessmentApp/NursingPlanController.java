package com.ruoyi.custom.api.assessmentApp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.assessment.service.INursingPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 评估端APP-护理计划管理 Controller
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@RestController("assessmentAppNursingPlanController")
@RequestMapping("/assessmentApp/nursingPlan")
@Api(tags = "评估端APP-护理计划管理")
public class NursingPlanController extends BaseController {

    @Autowired
    private INursingPlanService nursingPlanService;

    /**
     * 获取护理计划指标列表
     */
    @GetMapping("/list/{assessmentPlanId}")
    @ApiOperation(value = "获取护理计划指标列表")
    @ApiImplicitParam(name = "assessmentPlanId", value = "评估计划ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getNursingPlanIndicators(@PathVariable("assessmentPlanId") Long assessmentPlanId) {
        JSONArray indicators = nursingPlanService.getNursingPlanIndicators(assessmentPlanId);
        return AjaxResult.success(indicators);
    }

    /**
     * 获取护理计划指标详情
     */
    @GetMapping("/{assessmentPlanId}/{uuid}")
    @ApiOperation(value = "获取护理计划指标详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "assessmentPlanId", value = "评估计划ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "uuid", value = "指标UUID", required = true, dataType = "String", paramType = "path")
    })
    public AjaxResult getNursingPlanIndicator(@PathVariable("assessmentPlanId") Long assessmentPlanId, @PathVariable("uuid") String uuid) {
        JSONObject indicator = nursingPlanService.getNursingPlanIndicator(assessmentPlanId, uuid);
        return AjaxResult.success(indicator);
    }

    /**
     * 修改护理计划指标
     */
    @PutMapping("/{assessmentPlanId}/{uuid}")
    @Log(title = "APP-护理计划", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改护理计划指标")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "assessmentPlanId", value = "评估计划ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "uuid", value = "指标UUID", required = true, dataType = "String", paramType = "path")
    })
    public AjaxResult updateNursingPlanIndicator(@PathVariable("assessmentPlanId") Long assessmentPlanId,
                                              @PathVariable("uuid") String uuid,
                                              @RequestBody JSONObject indicator) {
        int result = nursingPlanService.updateNursingPlanIndicator(assessmentPlanId, uuid, indicator);
        return toAjax(result);
    }

}
