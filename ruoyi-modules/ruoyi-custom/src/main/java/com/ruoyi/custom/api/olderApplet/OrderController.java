package com.ruoyi.custom.api.olderApplet;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.custom.admin.goods.service.UserGoodsService;
import com.ruoyi.custom.admin.goodsOrder.domain.OrderBaseInfo;
import com.ruoyi.custom.admin.goodsOrder.request.UserGoodsOrderAllByIdRequest;
import com.ruoyi.custom.admin.goodsOrder.response.OrderBaseInfoUserVo;
import com.ruoyi.custom.admin.goodsOrder.service.UserGoodsOrderService;
import com.ruoyi.custom.admin.serviceOrder.service.OrderServiceInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 老人端小程序-订单 Controller
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
@RestController("olderAppletOrderController")
@RequestMapping("/olderApplet/order")
@Api(tags = "老人端小程序-订单")
public class OrderController extends BaseController {

    @Autowired
    private UserGoodsOrderService userGoodsOrderService;

    @Autowired
    private OrderServiceInfoService orderServiceInfoService;

    @Autowired
    private UserGoodsService userGoodsService;


    @GetMapping("/ownerRecord")
    @ApiOperation(value = "个人消费记录")
    public TableDataInfo<OrderBaseInfoUserVo> ownerList(@Valid UserGoodsOrderAllByIdRequest userGoodsOrderAllByIdRequest) {

        TableDataInfo<OrderBaseInfoUserVo> tableDataInfo = userGoodsOrderService.getAllByUserId(userGoodsOrderAllByIdRequest);

        return tableDataInfo;
    }

    @GetMapping("/info")
    @ApiOperation(value = "根据订单id查询消费记录详情")
    public TAjaxResult<OrderBaseInfo> getOneById(
            @RequestParam(value = "orderId", required = true)
            @ApiParam(name = "orderId", value = "订单id")
            @NotBlank(message = "订单id必填！")
            String orderId
    ) {

        OrderBaseInfo orderBaseInfo = userGoodsOrderService.selectById(orderId);
        // 商品类型 1:商品 2：餐品 3：服务
        Integer type = orderBaseInfo.getType();

        TAjaxResult<OrderBaseInfo> tAjaxResult = null;
        if (OrderBaseInfo.TYPE_GOODS.equals(type) || OrderBaseInfo.TYPE_FOOD.equals(type)) {
            tAjaxResult = userGoodsOrderService.getOneById(orderId, orderBaseInfo);
        } else if (OrderBaseInfo.TYPE_SERVICE.equals(type)) {
            tAjaxResult = new TAjaxResult<>();
            OrderBaseInfo baseInfo = orderServiceInfoService.getOrderServiceInfo(orderId);
            tAjaxResult.success(baseInfo);
        }

        return tAjaxResult;
    }

}
