package com.ruoyi.custom.admin.assessment.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.custom.admin.assessment.constant.AssessmentItemEnum;
import com.ruoyi.custom.admin.assessment.constant.CapabilityLevelEnum;
import com.ruoyi.custom.admin.assessment.constant.CareRiskEnum;
import com.ruoyi.custom.admin.assessment.domain.AssessmentPlan;
import com.ruoyi.custom.admin.assessment.domain.ElderlyCapacityAssessment;
import com.ruoyi.custom.admin.assessment.domain.NursingPlanTemplate;
import com.ruoyi.custom.admin.assessment.mapper.AssessmentPlanMapper;
import com.ruoyi.custom.admin.assessment.mapper.ElderlyCapacityAssessmentMapper;
import com.ruoyi.custom.admin.assessment.mapper.NursingPlanTemplateMapper;
import com.ruoyi.custom.admin.assessment.service.IElderlyCapacityAssessmentService;
import com.ruoyi.custom.admin.marketing.domain.MarketingCustomerInfo;
import com.ruoyi.custom.admin.marketing.service.MarketingCustomerInfoService;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;


/**
 * 老年人能力评估Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Service
public class ElderlyCapacityAssessmentServiceImpl implements IElderlyCapacityAssessmentService {
    @Autowired
    private ElderlyCapacityAssessmentMapper elderlyCapacityAssessmentMapper;

    @Autowired
    private AssessmentPlanMapper assessmentPlanMapper;

    @Autowired
    private NursingPlanTemplateMapper nursingPlanTemplateMapper;

    @Autowired
    private MarketingCustomerInfoService marketingCustomerInfoService;

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;


    /**
     * 查询老年人能力评估
     *
     * @param id 老年人能力评估主键
     * @return 老年人能力评估
     */
    @Override
    public ElderlyCapacityAssessment selectElderlyCapacityAssessmentById(Long id) {
        return elderlyCapacityAssessmentMapper.selectElderlyCapacityAssessmentById(id);
    }

    /**
     * 查询老年人能力评估列表
     *
     * @param elderlyCapacityAssessment 老年人能力评估
     * @return 老年人能力评估
     */
    @Override
    public List<ElderlyCapacityAssessment> selectElderlyCapacityAssessmentList(ElderlyCapacityAssessment elderlyCapacityAssessment) {

        return elderlyCapacityAssessmentMapper.selectElderlyCapacityAssessmentList(elderlyCapacityAssessment);
    }

    @Override
    public ElderlyCapacityAssessment selectElderlyCapacityAssessmentBySerialNumber(String serialNumber) {
        return elderlyCapacityAssessmentMapper.selectElderlyCapacityAssessmentBySerialNumber(serialNumber);
    }

    /**
     * 新增老年人能力评估
     *
     * @param elderlyCapacityAssessment 老年人能力评估
     * @return 结果
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertElderlyCapacityAssessment(ElderlyCapacityAssessment elderlyCapacityAssessment) {
        DateTime now = DateTime.now();
        if ((boolean) elderlyCapacityAssessment.getParams().get("finish")) {
            long assessmentID = elderlyCapacityAssessment.getAssessmentId();
            // 如果状态 传过来为2    生成报告  插入到计划表
            JSONObject assessmentResultJson = createAssessmentResultJson(elderlyCapacityAssessment);
            AssessmentPlan assessmentPlan = assessmentPlanMapper.selectAssessmentPlanById(assessmentID);
            if (Objects.isNull(assessmentPlan)) {
                throw new ServiceException("评估计划不存在");
            }
            assessmentPlan.setAssessmentResult(assessmentResultJson);
            assessmentPlan.setActualEndTime(now);
            assessmentPlan.setStatus("2");

            // 更新营销客户基础信息和老人基础信息
            MarketingCustomerInfo marketingCustomerInfo = marketingCustomerInfoService.selectMarketingCustomerInfoById(assessmentPlan.getCustomerId());
            if (marketingCustomerInfo != null) {
                JSONObject assessmentObjBasicsInfo = elderlyCapacityAssessment.getAssessmentObjBasicsInfo();

                // 更新营销客户信息
                updateMarketingCustomerInfo(marketingCustomerInfo, assessmentObjBasicsInfo);
                marketingCustomerInfoService.updateMarketingCustomerInfo(marketingCustomerInfo);

                // 更新老人基础信息（如果存在）
                if (marketingCustomerInfo.getElderlyId() != null) {
                    ElderlyPeopleInfo elderlyPeopleInfo = elderlyPeopleInfoService.selectElderlyPeopleInfoById(marketingCustomerInfo.getElderlyId());
                    if (elderlyPeopleInfo != null) {
                        updateElderlyPeopleInfo(elderlyPeopleInfo, assessmentObjBasicsInfo);
                        elderlyPeopleInfoService.updateElderlyPeopleInfo(elderlyPeopleInfo);
                    }
                }
            }

            // 生成护理计划指标
            JSONArray healthProblems = elderlyCapacityAssessment.getHealthProblems();
            if (healthProblems != null && !healthProblems.isEmpty()) {
                JSONArray nursingPlanIndicators = new JSONArray();

                // 遍历健康问题，匹配护理计划模板
                for (int i = 0; i < healthProblems.size(); i++) {
                    String healthProblem = healthProblems.getString(i);

                    // 根据健康问题查询匹配的护理计划模板
                    NursingPlanTemplate template = nursingPlanTemplateMapper.selectNursingPlanTemplateByHealthProblem(healthProblem);
                    if (template != null) {
                        // 构建护理计划指标项
                        JSONObject indicator = new JSONObject();
                        indicator.put("uuid", UUID.randomUUID().toString());
                        indicator.put("issueConfirmDate", DateUtil.format(now, "yyyy年MM月dd日")); // 格式：2025年6月29日
                        indicator.put("issueEndDate", ""); // 格式：2025年6月29日
                        indicator.put("problem", healthProblem);
                        indicator.put("details", template.getDetails());

                        nursingPlanIndicators.add(indicator);
                    }
                }

                // 设置护理计划指标
                if (!nursingPlanIndicators.isEmpty()) {
                    assessmentPlan.setNursingPlanIndicators(nursingPlanIndicators);
                }
            }

            assessmentPlanMapper.updateAssessmentPlan(assessmentPlan);
        }

        ElderlyCapacityAssessment elderlyCapacityAssessmentBySerialNumber = elderlyCapacityAssessmentMapper.selectElderlyCapacityAssessmentBySerialNumber(elderlyCapacityAssessment.getSerialNumber());
        if (elderlyCapacityAssessmentBySerialNumber != null) {
            elderlyCapacityAssessment.setId(elderlyCapacityAssessmentBySerialNumber.getId());
            return elderlyCapacityAssessmentMapper.updateElderlyCapacityAssessment(elderlyCapacityAssessment);
        }


        return elderlyCapacityAssessmentMapper.insertElderlyCapacityAssessment(elderlyCapacityAssessment);
    }

    /**
     * 修改老年人能力评估
     *
     * @param elderlyCapacityAssessment 老年人能力评估
     * @return 结果
     */
    @Override
    public int updateElderlyCapacityAssessment(ElderlyCapacityAssessment elderlyCapacityAssessment) {
        return elderlyCapacityAssessmentMapper.updateElderlyCapacityAssessment(elderlyCapacityAssessment);
    }

    /**
     * 批量删除老年人能力评估
     *
     * @param ids 需要删除的老年人能力评估主键
     * @return 结果
     */
    @Override
    public int deleteElderlyCapacityAssessmentByIds(Long[] ids) {
        return elderlyCapacityAssessmentMapper.deleteElderlyCapacityAssessmentByIds(ids);
    }

    /**
     * 删除老年人能力评估信息
     *
     * @param id 老年人能力评估主键
     * @return 结果
     */
    @Override
    public int deleteElderlyCapacityAssessmentById(Long id) {
        return elderlyCapacityAssessmentMapper.deleteElderlyCapacityAssessmentById(id);
    }


    /**
     * 获取评估报告
     *
     * @param elderlyCapacityAssessment
     * @return 结果
     */
    @SneakyThrows
    @Override
    public void getAssessmentReport(ElderlyCapacityAssessment elderlyCapacityAssessment, HttpServletResponse response) {
        /**
         * 获取数据
         */
        Map<String, Object> evaluationReportData = getEvaluationReportData(elderlyCapacityAssessment);

        /**
         * 配置插件，渲染模板
         */
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        Configure config = Configure.builder()
                .bind("medicationSituation", policy)
                .build();

        XWPFTemplate template = XWPFTemplate
                .compile(ResourceUtil.getStream("templates/AssessmentTemplate.docx"), config)
                .render(evaluationReportData);

        /**
         * 设置响应头，触发浏览器下载
         */
        String fileName = "能力评估报告.docx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());

        response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        response.setContentType("application/octet-stream");
        // 使用 'filename*' 提供 utf-8 编码支持
        response.setHeader("Content-disposition", "attachment; filename=" + encodedFileName + ";filename*=utf-8''" + encodedFileName);

        /**
         * 输出到响应流
         */
        try (OutputStream out = response.getOutputStream();
             BufferedOutputStream bos = new BufferedOutputStream(out)) {
            template.write(bos);
            bos.flush();
        }

        // 关闭模板资源
        template.close();
    }


    public List<Map<String, String>> getListMap(JSONArray jsonArray) {
        List<Map<String, String>> list = new ArrayList<>();

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Map<String, String> map = new HashMap<>();

            for (String key : jsonObject.keySet()) {
                map.put(key, jsonObject.getString(key));
            }
            list.add(map);
        }
        return list;
    }

    public String getStringValue(JSONArray jsonArray) {
        // 转换为逗号分隔的字符串
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < jsonArray.size(); i++) {
            if (i > 0) {
                result.append(",");
            }
            result.append(jsonArray.getString(i));
        }
        return result.toString();
    }


    /**
     * 生成报告
     *
     * @param elderlyCapacityAssessment
     * @return
     */
    public JSONObject createAssessmentResultJson(ElderlyCapacityAssessment elderlyCapacityAssessment) {
        JSONObject result = new JSONObject();
        JSONObject firstLevelIndicator = new JSONObject();

        int selfCareAbility = Integer.parseInt(elderlyCapacityAssessment.getOldPeopleAbilityAssessment().get("countScore").toString());
        int basicMotorAbility = Integer.parseInt(elderlyCapacityAssessment.getBasicMotorAbilityAssessment().get("countScore").toString());
        int mentalState = Integer.parseInt(elderlyCapacityAssessment.getMentalState().get("countScore").toString());
        int socialParticipation = Integer.parseInt(elderlyCapacityAssessment.getPerceptionSocialParticipation().get("countScore").toString());
        firstLevelIndicator.put("selfCareAbility", selfCareAbility);
        firstLevelIndicator.put("basicMotorAbility", basicMotorAbility);
        firstLevelIndicator.put("mentalState", mentalState);
        firstLevelIndicator.put("socialParticipation", socialParticipation);
        result.put("firstLevelIndicator", firstLevelIndicator);

        int[] numbers = {selfCareAbility, basicMotorAbility, mentalState, socialParticipation};

        result.put("preliminaryGradeScore", Arrays.stream(numbers).sum());
        List initialLevelAbilityElderlyList = new ArrayList();
        if (Arrays.stream(numbers).sum() == 90) {
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.INTACT.getCode());
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.INTACT.getMsg());
            initialLevelAbilityElderlyList.add(Arrays.stream(numbers).sum());
        } else if (Arrays.stream(numbers).sum() >= 66 && Arrays.stream(numbers).sum() <= 89) {
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.MILD.getCode());
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.MILD.getMsg());
            initialLevelAbilityElderlyList.add(Arrays.stream(numbers).sum());
        } else if (Arrays.stream(numbers).sum() >= 46 && Arrays.stream(numbers).sum() <= 65) {
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.MODERATE.getCode());
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.MODERATE.getMsg());
            initialLevelAbilityElderlyList.add(Arrays.stream(numbers).sum());
        } else if (Arrays.stream(numbers).sum() >= 30 && Arrays.stream(numbers).sum() <= 45) {
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.SEVERE.getCode());
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.SEVERE.getMsg());
            initialLevelAbilityElderlyList.add(Arrays.stream(numbers).sum());
        } else if (Arrays.stream(numbers).sum() >= 0 && Arrays.stream(numbers).sum() <= 29) {
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.LOSE.getCode());
            initialLevelAbilityElderlyList.add(CapabilityLevelEnum.LOSE.getMsg());
            initialLevelAbilityElderlyList.add(Arrays.stream(numbers).sum());
        }
        result.put("initialLevelAbilityElderly", initialLevelAbilityElderlyList.toArray());

        JSONObject healthRelatedIssues = elderlyCapacityAssessment.getHealthRelatedIssues();
        JSONObject diseaseDiagnosis = elderlyCapacityAssessment.getDiseaseDiagnosisDrugUsage();
        JSONObject assessmentObjBasicsInfo = elderlyCapacityAssessment.getAssessmentObjBasicsInfo();

        JSONArray jsonArray = new JSONArray();


        List<String> ultimately = new ArrayList<>();

        String yes = "有";
        if (yes.equals(healthRelatedIssues.get("coma").toString())) {
            JSONObject basisAbilityLevelChange = new JSONObject();
            ultimately.add("a");
            basisAbilityLevelChange.put("option", "a");
            basisAbilityLevelChange.put("title", "处于昏迷状态者，直接评定为能力完全丧失");
            jsonArray.add(basisAbilityLevelChange);
        }

        JSONObject diseaseDiagnosis1 = diseaseDiagnosis.getJSONObject("diseaseDiagnosis");
        JSONArray fixedArray = diseaseDiagnosis1.getJSONArray("fixed");

        ObjectMapper mapper = new ObjectMapper();
        // 转换为 int 数组
        String[] fixedNumbers = mapper.convertValue(fixedArray, String[].class);
        String fixed = "痴呆F00～F03";
        if (Arrays.stream(fixedNumbers).anyMatch(value -> fixed.equals(value))) {
            JSONObject basisAbilityLevelChange = new JSONObject();
            ultimately.add("b");
            basisAbilityLevelChange.put("option", "b");
            basisAbilityLevelChange.put("title", "确诊为痴呆(F00～F03)、精神科专科医生诊断的其他精神和行为障碍疾病(F04～ F99），在原有能力级别上提高一个等级;");
            jsonArray.add(basisAbilityLevelChange);
        }

        JSONObject careRisksWithinThePast30Days = assessmentObjBasicsInfo.getJSONObject("CareRisksWithinThePast30Days");
        int tumble = Integer.parseInt(careRisksWithinThePast30Days.get("tumble").toString());
        int beLost = Integer.parseInt(careRisksWithinThePast30Days.get("beLost").toString());
        int chokingOnFood = Integer.parseInt(careRisksWithinThePast30Days.get("ChokingOnFood").toString());
        int suicideAndSelfHarm = Integer.parseInt(careRisksWithinThePast30Days.get("SuicideAndSelfHarm").toString());
        int other = Integer.parseInt(careRisksWithinThePast30Days.get("other").toString());

        if (tumble == 2 || tumble == 3 ||
                beLost == 2 || beLost == 3 ||
                chokingOnFood == 2 || chokingOnFood == 3 ||
                suicideAndSelfHarm == 2 || suicideAndSelfHarm == 3 ||
                other == 2 || other == 3
        ) {
            ultimately.add("c");
            JSONObject basisAbilityLevelChange = new JSONObject();
            basisAbilityLevelChange.put("option", "c");
            basisAbilityLevelChange.put("title", "近30天内发生过2次及以上照护风险事件（如跌倒、噎食、自杀、自伤、走失等），在原有能力级别上提高一个等级");
            jsonArray.add(basisAbilityLevelChange);
        }
        result.put("basisAbilityLevelChange", jsonArray);

        List finalLevel = new ArrayList();
        int i = Integer.parseInt(initialLevelAbilityElderlyList.get(0).toString());
        if (ultimately.stream().anyMatch(value -> "a".equals(value))) {
            finalLevel.add(CapabilityLevelEnum.LOSE5.getCode());
            finalLevel.add(CapabilityLevelEnum.LOSE5.getMsg());
            result.put("finalLevel", finalLevel);
        } else if (ultimately.stream().anyMatch(value -> "c".equals(value)) && ultimately.stream().anyMatch(value1 -> "b".equals(value1))) {
            if (i <= 3) {
                CapabilityLevelEnum requiredPoints = CapabilityLevelEnum.getRequiredPoints(i + 2);
                finalLevel.add(requiredPoints.getCode());
                finalLevel.add(requiredPoints.getMsg());
                result.put("finalLevel", finalLevel);
            } else {
                CapabilityLevelEnum requiredPoints = CapabilityLevelEnum.getRequiredPoints(i + 1);
                finalLevel.add(requiredPoints.getCode());
                finalLevel.add(requiredPoints.getMsg());
                result.put("finalLevel", finalLevel);
            }
        } else if (ultimately.stream().anyMatch(value -> "b".equals(value))) {

            if (i != 5) {
                CapabilityLevelEnum requiredPoints = CapabilityLevelEnum.getRequiredPoints(i + 1);
                finalLevel.add(requiredPoints.getCode());
                finalLevel.add(requiredPoints.getMsg());
                result.put("finalLevel", finalLevel);
            }
        } else if (ultimately.stream().anyMatch(value -> "c".equals(value))) {
            if (i != 5) {
                CapabilityLevelEnum requiredPoints = CapabilityLevelEnum.getRequiredPoints(i + 1);
                finalLevel.add(requiredPoints.getCode());
                finalLevel.add(requiredPoints.getMsg());
                result.put("finalLevel", finalLevel);
            }
        }

        return result;
    }

    /**
     * 组装数据
     *
     * @param elderlyCapacityAssessment
     * @return
     */
    public Map<String, Object> getEvaluationReportData(ElderlyCapacityAssessment elderlyCapacityAssessment) {
        Map<String, Object> data = new HashMap<>();
        // 评估信息表
        ElderlyCapacityAssessment assessment = elderlyCapacityAssessmentMapper.selectElderlyCapacityAssessmentBySerialNumber(elderlyCapacityAssessment.getSerialNumber());
        JSONObject assessmentObjBasicsInfo = assessment.getAssessmentObjBasicsInfo();


        data.put("serialNumber", assessment.getSerialNumber());
        data.put("actualStartTime", Optional.ofNullable(assessmentObjBasicsInfo.get("actualStartTime")).orElse(" ").toString());
        data.put("assessmentReason", Optional.ofNullable(assessmentObjBasicsInfo.get("assessmentReason")).orElse(" ").toString());


// 评估对象基本信息表
        data.put("elderlyName", assessmentObjBasicsInfo.get("elderlyName").toString());
        data.put("sex", assessmentObjBasicsInfo.get("sex").toString());
        data.put("elderlyDateBirth", assessmentObjBasicsInfo.get("elderlyDateBirth").toString());
        data.put("Height", assessmentObjBasicsInfo.get("height").toString());
        data.put("weight", assessmentObjBasicsInfo.get("weight").toString());
        data.put("nationality", assessmentObjBasicsInfo.get("nationality").toString());
        data.put("religion", assessmentObjBasicsInfo.get("religion").toString());
        data.put("ID", assessmentObjBasicsInfo.get("ID").toString());
        data.put("cultureLevel", assessmentObjBasicsInfo.get("cultureLevel").toString());
        data.put("LivingSituation", getStringValue(assessmentObjBasicsInfo.getJSONArray("LivingSituation")));
        data.put("maritalStatus", assessmentObjBasicsInfo.get("maritalStatus").toString());


        data.put("MedicalPayment", getStringValue(assessmentObjBasicsInfo.getJSONArray("MedicalPayment")));
        data.put("EconomicSource", getStringValue(assessmentObjBasicsInfo.getJSONArray("EconomicSource")));
        JSONObject careRisksWithinThePast30Days = assessmentObjBasicsInfo.getJSONObject("CareRisksWithinThePast30Days");

        data.put("CareRisksWithinThePastTumble", CareRiskEnum.getRequiredPoints(Integer.parseInt(careRisksWithinThePast30Days.get("tumble").toString())).getMsg());
        data.put("CareRisksWithinThePastBeLost", CareRiskEnum.getRequiredPoints(Integer.parseInt(careRisksWithinThePast30Days.get("beLost").toString())).getMsg());
        data.put("CareRisksWithinThePastChokingOnFood", CareRiskEnum.getRequiredPoints(Integer.parseInt(careRisksWithinThePast30Days.get("ChokingOnFood").toString())).getMsg());
        data.put("CareRisksWithinThePastSuicideAndSelfHarm", CareRiskEnum.getRequiredPoints(Integer.parseInt(careRisksWithinThePast30Days.get("SuicideAndSelfHarm").toString())).getMsg());
        data.put("CareRisksWithinThePastOther", CareRiskEnum.getRequiredPoints(Integer.parseInt(careRisksWithinThePast30Days.get("other").toString())).getMsg());


        // 信息提供者及联系人信息表
        JSONObject msgSupplierContactInfo = assessment.getMsgSupplierContactInfo();
        data.put("providerName", msgSupplierContactInfo.get("providerName").toString());
        data.put("providerRelation", msgSupplierContactInfo.get("providerRelation").toString());
        data.put("ContactPersonName", msgSupplierContactInfo.get("ContactPersonName").toString());
        data.put("ContactPersonTel", msgSupplierContactInfo.get("ContactPersonTel").toString());

        // 疾病诊断和用药情况
        JSONObject diseaseDiagnosisDrugUsage = assessment.getDiseaseDiagnosisDrugUsage();
        data.put("diseaseDiagnosis", getStringValue(diseaseDiagnosisDrugUsage.getJSONObject("diseaseDiagnosis").getJSONArray("fixed")));
        data.put("medicationSituation", getListMap(diseaseDiagnosisDrugUsage.getJSONArray("medicationSituation")));


//        健康相关问题
        JSONObject healthRelatedIssues = assessment.getHealthRelatedIssues();
        data.put("pressureInjury", healthRelatedIssues.get("pressureInjury").toString());
        data.put("motionOfJoint", healthRelatedIssues.get("motionOfJoint").toString());
        data.put("woundCondition", getStringValue(healthRelatedIssues.getJSONArray("woundCondition")));
        data.put("specialCareSituations", getStringValue(healthRelatedIssues.getJSONArray("specialCareSituations")));
        data.put("painSensation", healthRelatedIssues.get("painSensation").toString());
        data.put("toothLossSituation", getStringValue(healthRelatedIssues.getJSONArray("toothLossSituation")));
        data.put("wearingConditionDentures", getStringValue(healthRelatedIssues.getJSONArray("wearingConditionDentures")));
        data.put("situationSymptomsDysphagia", getStringValue(healthRelatedIssues.getJSONArray("situationSymptomsDysphagia")));
        data.put("bodyMassIndex", healthRelatedIssues.get("bodyMassIndex").toString());
        data.put("clearingRespiratoryTractIneffective", healthRelatedIssues.get("clearingRespiratoryTractIneffective").toString());
        data.put("coma", healthRelatedIssues.get("coma").toString());
        data.put("healthRelatedIssuesOther", healthRelatedIssues.get("other").toString());


//        生理及身体评估
        JSONObject physiologyBodyAssessment = assessment.getPhysiologyBodyAssessment();

        data.put("cardiacFunction", physiologyBodyAssessment.get("cardiacFunction").toString());
        data.put("respiratoryFunction", physiologyBodyAssessment.getJSONObject("respiratoryFunction").get("all").toString());

        JSONObject consciousState = physiologyBodyAssessment.getJSONObject("consciousState");


        String sober = Optional.ofNullable(consciousState.get("sober")).orElse(" ").toString();
        String soberRests = Optional.ofNullable(consciousState.get("soberRests")).orElse(" ").toString();

        if ("清醒".equals(sober)) {
            data.put("GCSTable", false);
        } else {
            data.put("GCSTable", true);
        }
        data.put("eyesOpen", Optional.ofNullable(consciousState.get("eyesOpen")).orElse(" ").toString());
        data.put("bestMotor", Optional.ofNullable(consciousState.get("bestMotor")).orElse(" ").toString());
        data.put("bestVerbalResponse", Optional.ofNullable(consciousState.get("bestVerbalResponse")).orElse(" ").toString());
        data.put("consciousState", sober + soberRests);

        // 泌尿系统
        JSONObject urinarySystem = physiologyBodyAssessment.getJSONObject("urinarySystem");
        String option = Optional.ofNullable(urinarySystem.get("option")).orElse(" ").toString();
        String night = Optional.ofNullable(urinarySystem.get("night")).orElse(" ").toString();
        String daytime = Optional.ofNullable(urinarySystem.get("daytime")).orElse(" ").toString();
        String prostatomegaly = Optional.ofNullable(urinarySystem.get("prostatomegaly")).orElse(" ").toString();
        String optionRests = Optional.ofNullable(urinarySystem.get("optionRests")).orElse(" ").toString() + "\n";
        String urinarySystemAssist = Optional.ofNullable(urinarySystem.get("urinarySystemAssist")).orElse(" ").toString();
        String changeTheDate = Optional.ofNullable(urinarySystem.get("changeTheDate")).orElse(" ").toString();
        String currentUrologicalUsage = Optional.ofNullable(urinarySystem.get("currentUrologicalUsage")).orElse(" ").toString();
        String currentUrologicalUsageRests = Optional.ofNullable(urinarySystem.get("currentUrologicalUsageRests")).orElse(" ").toString();
        StringBuffer urinarySystemBuffer = new StringBuffer();
        urinarySystemBuffer.append(option);
        urinarySystemBuffer.append("\n");
        if (Optional.ofNullable(urinarySystem.get("night")).isPresent() && Optional.ofNullable(urinarySystem.get("daytime")).isPresent()) {
            String frequentUurination = "白天" + daytime + "次   夜晚" + night + "次   \n";
            urinarySystemBuffer.append(frequentUurination);
        }
        urinarySystemBuffer.append(prostatomegaly);
        urinarySystemBuffer.append(optionRests);
        urinarySystemBuffer.append("辅助用物：" + urinarySystemAssist + "Fr.（近期更换日期 :" + changeTheDate + "）\n");
        urinarySystemBuffer.append("目前使用：" + currentUrologicalUsage + currentUrologicalUsageRests + "\n");


        String isHighRiskUrinaryTractInfection = Optional.ofNullable(urinarySystem.get("isHighRiskUrinaryTractInfection")).orElse(" ").toString();
        if ("是".equals(isHighRiskUrinaryTractInfection)) {
            String indwellingCatheter = Optional.ofNullable(urinarySystem.get("indwellingCatheter")).orElse(" ").toString();
            urinarySystemBuffer.append("高危险性泌尿道感染：○性别：女性   ○疾病:糖尿病、肾脏病、尿失禁及反复泌尿道感染   ○留置导尿管 " + indwellingCatheter + "月 ○解尿疼痛或腰背酸痛症状");
        }
        data.put("urinarySystem", urinarySystemBuffer);


//        排尿习惯
        JSONObject defecationHdabit = physiologyBodyAssessment.getJSONObject("defecationHdabit");

        StringBuffer defecationHdabitBuffer = new StringBuffer();
        String bowelHabits = Optional.ofNullable(defecationHdabit.get("bowelHabits")).orElse(" ").toString();

        if ("失禁".equals(bowelHabits)) {
            String incontinenceDays = Optional.ofNullable(defecationHdabit.get("incontinenceDays")).orElse(" ").toString();
            defecationHdabitBuffer.append(bowelHabits + ":" + incontinenceDays + "天");
        } else {
            String bowelHabitsDays = Optional.ofNullable(defecationHdabit.get("bowelHabitsDays")).orElse(" ").toString();
            String bowelHabitsFrequency = Optional.ofNullable(defecationHdabit.get("bowelHabitsFrequency")).orElse(" ").toString();
            defecationHdabitBuffer.append(bowelHabits + bowelHabitsDays + "天 " + bowelHabitsFrequency + "   次");
        }
        if (!"正常".equals(bowelHabits)) {
            String bowelHabitsAssistanceMethod = Optional.ofNullable(defecationHdabit.get("bowelHabitsAssistanceMethod")).orElse(" ").toString();
            defecationHdabitBuffer.append("辅助方式：" + bowelHabitsAssistanceMethod);
        }
        data.put("defecationHdabit", defecationHdabitBuffer);

        // 睡眠形态
        data.put("sleepPattern", Optional.ofNullable(physiologyBodyAssessment.get("sleepPattern")).orElse(" ").toString());


        // 皮肤状态
        JSONObject skinCondition = physiologyBodyAssessment.getJSONObject("skinCondition");

        String isSkinCondition = Optional.ofNullable(skinCondition.get("isSkinCondition")).orElse(" ").toString();
        if ("正常".equals(isSkinCondition)) {
            data.put("skinConditionTable", false);
        } else {
            data.put("skinConditionTable", true);
            JSONObject bradenScale = skinCondition.getJSONObject("bradenScale");

            data.put("pressureMobility", Optional.ofNullable(AssessmentItemEnum.PRESSURE_MOBILITY.getScoreDescription(Integer.parseInt(bradenScale.get("pressureMobility").toString()))).orElse(" "));
            data.put("pressureActivity", Optional.ofNullable(AssessmentItemEnum.PRESSURE_ACTIVITY.getScoreDescription(Integer.parseInt(bradenScale.get("pressureActivity").toString()))).orElse(" "));
            data.put("pressureSkinMoisture", Optional.ofNullable(AssessmentItemEnum.PRESSURE_SKIN_MOISTURE.getScoreDescription(Integer.parseInt(bradenScale.get("pressureSkinMoisture").toString()))).orElse(" "));
            data.put("pressureSoreSensation", Optional.ofNullable(AssessmentItemEnum.PRESSURE_SORE_SENSATION.getScoreDescription(Integer.parseInt(bradenScale.get("pressureSoreSensation").toString()))).orElse(" "));
            data.put("pressureNutritionStatus", Optional.ofNullable(AssessmentItemEnum.PRESSURE_NUTRITION_STATUS.getScoreDescription(Integer.parseInt(bradenScale.get("pressureNutritionStatus").toString()))).orElse(" "));
            data.put("pressureFrictionAndShear", Optional.ofNullable(AssessmentItemEnum.PRESSURE_FRICTION_AND_SHEAR.getScoreDescription(Integer.parseInt(bradenScale.get("pressureFrictionAndShear").toString()))).orElse(" "));
            data.put("bradenScaleCountScore", Optional.ofNullable(bradenScale.get("bradenScaleCountScore")).orElse(" ").toString() + "分");
        }

        StringBuffer skinConditionBuffer = new StringBuffer();
        skinConditionBuffer.append(Optional.ofNullable(skinCondition.get("isSkinCondition")).orElse(" ").toString());
        skinConditionBuffer.append(Optional.ofNullable(skinCondition.get("scarCause")).orElse(" ").toString());
        if ("疥疮".equals(isSkinCondition)) {
            skinConditionBuffer.append("曾经感染日期:" + Optional.ofNullable(skinCondition.get("scabiesInfectionDate")).orElse(" ").toString()
                    + "\t部位：  " + Optional.ofNullable(skinCondition.get("scarLocation")).orElse(" ").toString() +
                    " \t程度：  " + Optional.ofNullable(skinCondition.get("scarSeverity")).orElse(" ").toString() +
                    "  类别：    " + Optional.ofNullable(skinCondition.get("scarType")).orElse(" ").toString() +
                    "\n");
        }
        skinConditionBuffer.append("\n发量:" + Optional.ofNullable(skinCondition.get("hairVolume")).orElse(" ").toString());
        skinConditionBuffer.append("\n指/趾甲:" + Optional.ofNullable(skinCondition.get("nails")).orElse(" ").toString());
        skinConditionBuffer.append("\n疑似受虐：" + Optional.ofNullable(skinCondition.get("suspectedAbuse")).orElse(" ").toString());
        skinConditionBuffer.append("(\n皮肤外观有多处疑似被打过的新旧伤淤青疤痕，见到陌生人靠近深情惊恐，甚至拒绝外人碰触,如有上诉情况，通知社工人员处理）");

        skinConditionBuffer.append("伤口类别：" + Optional.ofNullable(skinCondition.get("typeOfWound")).orElse(" ").toString() + "\n");
        String isPressureSore = skinCondition.get("isPressureSore").toString();
        if ("是".equals(isPressureSore)) {
            skinConditionBuffer.append("\n压疮（等级：  " + Optional.ofNullable(skinCondition.get("pressureSoreGrade")).orElse(" ").toString() + "    ）\n");
        }
        String otherChronicWounds = skinCondition.get("otherChronicWounds").toString();
        if ("是".equals(otherChronicWounds)) {
            skinConditionBuffer.append("其他慢性伤口\t" +
                    "部位：   " + Optional.ofNullable(skinCondition.get("otherChronicWoundsLocation")).orElse(" ").toString() + "      大小：" +
                    Optional.ofNullable(skinCondition.get("otherChronicWoundsSize")).orElse(" ").toString() + "\n");
        }
        data.put("skinCondition", skinConditionBuffer);


        // 过敏药物
        JSONObject allergicDrugs = physiologyBodyAssessment.getJSONObject("allergicDrugs");
        String allergyMedication = Optional.ofNullable(allergicDrugs.get("allergyMedication")).orElse(" ").toString();
        if ("有".equals(allergyMedication)) {
            data.put("allergicDrugs", allergyMedication
                    + ",药名：" + Optional.ofNullable(allergicDrugs.get("allergyMedicationName")).orElse(" ").toString()
                    + "\t食物：" + Optional.ofNullable(allergicDrugs.get("allergyFoods")).orElse(" ").toString()
            );
        } else {
            data.put("allergicDrugs", allergyMedication);
        }

        // 目前是否接受特殊照护
        JSONObject whetherReceiveSpecialCare = physiologyBodyAssessment.getJSONObject("whetherReceiveSpecialCare");

        String isSpecialCare = whetherReceiveSpecialCare.get("isSpecialCare").toString();
        data.put("whetherReceiveSpecialCare", isSpecialCare);
        if ("是".equals(isSpecialCare)) {
            data.put("whetherReceiveSpecialCareTable", true);
            data.put("pipelineName", Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineName")).orElse(" ").toString() +
                    Optional.ofNullable(whetherReceiveSpecialCare.get("pressureSoreGrade")).orElse(" ").toString() +
                    Optional.ofNullable(whetherReceiveSpecialCare.get("artificialBloodVessel")).orElse(" ").toString()
            );
            data.put("pipelineNameDate",
                    Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineNameStartDate")).orElse(" ").toString() + "至" +
                            Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineNameEndDate")).orElse(" ").toString()
            );


            StringBuffer pipelineRestsNameBuffer = new StringBuffer();
            pipelineRestsNameBuffer.append(Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineRestsName")).orElse(" ").toString());

            if ("血液透析".equals(Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineRestsName")).orElse(" ").toString())) {
                pipelineRestsNameBuffer.append(Optional.ofNullable(whetherReceiveSpecialCare.get("hematodialysis")).orElse(" ").toString() + "次/星期");
            } else if ("氧气治疗".equals(Optional.ofNullable(whetherReceiveSpecialCare.get("oxygenTherapy")).orElse(" ").toString())) {
                pipelineRestsNameBuffer.append(Optional.ofNullable(whetherReceiveSpecialCare.get("oxygenTherapy")).orElse(" ").toString() + "L/min");

            }
            data.put("pipelineRestsName", pipelineRestsNameBuffer);
            data.put("pipelineRestsNameDate", Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineRestsNameStartDate")).orElse(" ").toString() + "至" +
                    Optional.ofNullable(whetherReceiveSpecialCare.get("pipelineRestsNameEndDate")).orElse(" ").toString());
        } else {
            data.put("whetherReceiveSpecialCareTable", false);
            data.put("whetherReceiveSpecialCare", "否");
        }


        JSONObject harmfulHabits = physiologyBodyAssessment.getJSONObject("harmfulHabits");
        String isHarmfulHabits = harmfulHabits.get("isHarmfulHabits").toString();

        if ("有".equals(isHarmfulHabits)) {
            StringBuffer harmfulHabitsBuffer = new StringBuffer();
            harmfulHabitsBuffer.append(getStringValue(harmfulHabits.getJSONArray("harmfulHabitsList")) + "\n");
            harmfulHabitsBuffer.append(Optional.ofNullable(harmfulHabits.get("dailyCigaretteAmount")).orElse(" ").toString() + "包/天,已吸"
                    + Optional.ofNullable(harmfulHabits.get("yearsOfSmoking")).orElse(" ").toString() + "年\n");
            harmfulHabitsBuffer.append(Optional.ofNullable(harmfulHabits.get("dailyAlcoholIntake")).orElse(" ").toString() + " /天，已喝"
                    + Optional.ofNullable(harmfulHabits.get("yearsOfDrinking")).orElse(" ").toString() + "年\n");
            harmfulHabitsBuffer.append(Optional.ofNullable(harmfulHabits.get("otherHarmfulHabits")).orElse(" ").toString());
            data.put("harmfulHabits", harmfulHabitsBuffer);
        } else {
            data.put("harmfulHabits", isHarmfulHabits);
        }

        // 肌肉、关节型态及肌力
        JSONObject jointType = physiologyBodyAssessment.getJSONObject("jointType");
        data.put("leftRightJointType", "左：" + Optional.ofNullable(jointType.get("leftJointType")).orElse(" ").toString() + "\n右：" +
                Optional.ofNullable(jointType.get("rightJointType")).orElse(" ").toString());
        data.put("leftMuscleStrength", "左：" + Optional.ofNullable(jointType.get("leftMuscleStrength")).orElse(" ").toString() + "\n右：" +
                Optional.ofNullable(jointType.get("rightMuscleStrength")).orElse(" ").toString());
        data.put("MuscleAppearanceType", "左：" + Optional.ofNullable(jointType.get("leftMuscleAppearanceType")).orElse(" ").toString() + "\n右：" +
                Optional.ofNullable(jointType.get("rightMuscleAppearanceType")).orElse(" ").toString());
        String paralysis = Optional.ofNullable(jointType.get("paralysis")).orElse(" ").toString();
        if ("有".equals(paralysis)) {

            data.put("jointType", paralysis + "\t" + Optional.ofNullable(jointType.get("paralysisType")).orElse(" ").toString());
        } else {
            data.put("jointType", paralysis);
        }

        // 行动力
        data.put("actionAbility", Optional.ofNullable(physiologyBodyAssessment.get("actionAbility")).orElse(" ").toString());

        // 是否使用辅具
        JSONObject whetherAssistiveDevices = physiologyBodyAssessment.getJSONObject("whetherAssistiveDevices");


        String isUsingAssistiveDevices = Optional.ofNullable(whetherAssistiveDevices.get("isUsingAssistiveDevices")).orElse(" ").toString();

        if ("1".equals(isUsingAssistiveDevices)) {
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append(getStringValue(whetherAssistiveDevices.getJSONArray("mainAids")) + "\n");
            String isUsingVisionAssistiveDevices = Optional.ofNullable(whetherAssistiveDevices.get("isUsingVisionAssistiveDevices")).orElse(" ").toString();
            String needOtherAssistiveDevices = Optional.ofNullable(whetherAssistiveDevices.get("needOtherAssistiveDevices")).orElse(" ").toString();
            if ("1".equals(isUsingVisionAssistiveDevices)) {
                stringBuffer.append("视力辅具：" + Optional.ofNullable(whetherAssistiveDevices.get("visionAids")).orElse(" ").toString() + "\n");
            }
            if ("1".equals(needOtherAssistiveDevices)) {
                stringBuffer.append("其他:" + Optional.ofNullable(whetherAssistiveDevices.get("otherAssistiveDevices")).orElse(" ").toString());
            }
            data.put("whetherAssistiveDevices", stringBuffer);
        } else {
            data.put("whetherAssistiveDevices", isUsingAssistiveDevices);
        }


//        fallRecord 跌倒记录
        JSONObject fallRecord = physiologyBodyAssessment.getJSONObject("fallRecord");
        String fallRecordList = getStringValue(fallRecord.getJSONArray("fallRecordList"));
        if (Optional.ofNullable(fallRecord.get("fallHistory")).isPresent() && Optional.ofNullable(fallRecord.get("fallFrequency")).isPresent()) {
            String fallHistory = Optional.ofNullable(fallRecord.get("fallHistory")).orElse("").toString();
            if ("1".equals(fallHistory)) {
                fallHistory = "是";
            } else {
                fallHistory = "否";
            }
            String fallFrequency = Optional.ofNullable(fallRecord.get("fallFrequency")).orElse("").toString();
            fallRecordList = fallRecordList + "\n近三个月内个案是否有跌倒纪录:" + fallHistory + "\n跌倒发生次数：" + fallFrequency;
        }
        data.put("fallRecord", fallRecordList);

        // 疫苗记录
        JSONObject vaccinationRecord = physiologyBodyAssessment.getJSONObject("vaccinationRecord");
        String fluVaccine = Optional.ofNullable(vaccinationRecord.get("fluVaccine")).orElse(" ").toString();
        String fluVaccineYear = Optional.ofNullable(vaccinationRecord.get("fluVaccineYear")).orElse(" ").toString();
        String pneumococcalVaccine = Optional.ofNullable(vaccinationRecord.get("pneumococcalVaccine")).orElse(" ").toString();
        String pneumococcalVaccineYear = Optional.ofNullable(vaccinationRecord.get("pneumococcalVaccineYear")).orElse(" ").toString();
        StringBuffer vaccinationRecordBuffer = new StringBuffer();
        if ("有".equals(fluVaccine)) {
            vaccinationRecordBuffer.append("流感疫苗：" + fluVaccine + "施打年度：" + fluVaccineYear + "\n");
        } else {
            vaccinationRecordBuffer.append("流感疫苗：" + fluVaccine + "\n");
        }
        if ("有".equals(pneumococcalVaccine)) {
            vaccinationRecordBuffer.append("肺炎链球菌多糖体疫苗：" + pneumococcalVaccine + "施打年度：" + pneumococcalVaccineYear + "\n");
        } else {
            vaccinationRecordBuffer.append("肺炎链球菌多糖体疫苗：" + pneumococcalVaccine + "\n");
        }
        data.put("vaccinationRecord", vaccinationRecordBuffer);


        //- 疼痛筛查      最近疼痛
        JSONObject painScreening = physiologyBodyAssessment.getJSONObject("painScreening");

        String recentPain = painScreening.get("recentPain").toString();
        if ("1".equals(recentPain)) {
            data.put("painScreeningTable", true);

            data.put("comforting", AssessmentItemEnum.COMFORTING.getScoreDescription(Integer.parseInt(painScreening.get("comforting").toString())));
            data.put("crying", AssessmentItemEnum.CRYING.getScoreDescription(Integer.parseInt(painScreening.get("crying").toString())));
            data.put("painDuringActivity", AssessmentItemEnum.PAINDURINGACTIVITY.getScoreDescription(Integer.parseInt(painScreening.get("painDuringActivity").toString())));
            data.put("footPain", AssessmentItemEnum.FOOTPAIN.getScoreDescription(Integer.parseInt(painScreening.get("footPain").toString())));
            data.put("facePain", AssessmentItemEnum.FACEPAIN.getScoreDescription(Integer.parseInt(painScreening.get("facePain").toString())));
            data.put("painScreeningTotal", painScreening.get("painScreeningTotal").toString());


            StringBuffer painScreeningBuffer = new StringBuffer();

            painScreeningBuffer.append("引发因素：" + Optional.ofNullable(painScreening.get("triggerFactors")).orElse(" ").toString() + Optional.ofNullable(painScreening.get("otherPainTriggerCauses")).orElse(" ").toString());
            painScreeningBuffer.append("\n性质:" + Optional.ofNullable(painScreening.get("nature")).orElse(" ").toString());
            painScreeningBuffer.append("\n部位:" + Optional.ofNullable(painScreening.get("locationOfPain")).orElse(" ").toString() + "(" + Optional.ofNullable(painScreening.get("referredToOtherLocations")).orElse(" ").toString() + ")");
            painScreeningBuffer.append("\n程度：" + Optional.ofNullable(painScreening.get("painDegree")).orElse(" ").toString());
            painScreeningBuffer.append("\n持续时间：" + Optional.ofNullable(painScreening.get("painDuration")).orElse(" ").toString() + "\t" + "(" + Optional.ofNullable(painScreening.get("painDurationRest")).orElse(" ").toString() + ")");

            String takingPainkillers = Optional.ofNullable(painScreening.get("takingPainkillers")).orElse(" ").toString();
            if ("1".equals(takingPainkillers)) {
                takingPainkillers = "有";
                painScreeningBuffer.append("\n使用止疼药：：" + Optional.ofNullable(painScreening.get("takingPainkillers")).orElse(" ").toString() + ",药名：" + Optional.ofNullable(painScreening.get("usingPainkillerName")).orElse(" "));

            } else {
                takingPainkillers = "无";
                painScreeningBuffer.append("\n使用止疼药：：" + Optional.ofNullable(painScreening.get("takingPainkillers")).orElse(" ").toString());

            }

            if (Optional.ofNullable(painScreening.get("currentPain")).isPresent()) {
                painScreeningBuffer.append("\n目前疼痛：" + Optional.ofNullable(painScreening.get("currentPain")).orElse(" ").toString());
            }
            data.put("painScreening", painScreeningBuffer);
        } else {
            data.put("painScreeningTable", false);
            data.put("painScreening", "否");
        }


//        老年人能力评估表
        JSONObject oldPeopleAbilityAssessment = assessment.getOldPeopleAbilityAssessment();

        data.put("eating", AssessmentItemEnum.EATING.getScoreDescription(Integer.parseInt(oldPeopleAbilityAssessment.get("eating").toString())));
        data.put("modification", AssessmentItemEnum.GROOMING.getScoreDescription(Integer.parseInt(oldPeopleAbilityAssessment.get("modification").toString())));
        data.put("bath", AssessmentItemEnum.BATHING.getScoreDescription(Integer.parseInt(oldPeopleAbilityAssessment.get("bath").toString())));
        data.put("takeOffCoat", AssessmentItemEnum.UPPER_BODY_DRESSING.getScoreDescription(Integer.parseInt(oldPeopleAbilityAssessment.get("takeOffCoat").toString())));
        data.put("shoesSocks", AssessmentItemEnum.LOWER_BODY_DRESSING.getScoreDescription(Integer.parseInt(oldPeopleAbilityAssessment.get("shoesSocks").toString())));
        data.put("urinaryControl", AssessmentItemEnum.URINARY_CONTROL.getScoreDescription(Integer.parseInt(oldPeopleAbilityAssessment.get("urinaryControl").toString())));
        data.put("stoolControl", AssessmentItemEnum.BOWEL_CONTROL.getScoreDescription(Integer.parseInt(oldPeopleAbilityAssessment.get("stoolControl").toString())));
        data.put("toTheToilet", AssessmentItemEnum.TOILETING.getScoreDescription(Integer.parseInt(oldPeopleAbilityAssessment.get("toTheToilet").toString())));


//        基础运动能力评估表
        JSONObject basicMotorAbilityAssessment = assessment.getBasicMotorAbilityAssessment();
        data.put("bedChairTransfer", AssessmentItemEnum.BED_TRANSFER.getScoreDescription(Integer.parseInt(basicMotorAbilityAssessment.get("bedChairTransfer").toString())));
        data.put("bedPositionTransfer", AssessmentItemEnum.CHAIR_TRANSFER.getScoreDescription(Integer.parseInt(basicMotorAbilityAssessment.get("bedPositionTransfer").toString())));
        data.put("walkingGround", AssessmentItemEnum.WALKING.getScoreDescription(Integer.parseInt(basicMotorAbilityAssessment.get("walkingGround").toString())));
        data.put("upDownStairs", AssessmentItemEnum.STAIRS.getScoreDescription(Integer.parseInt(basicMotorAbilityAssessment.get("upDownStairs").toString())));


//        精神状态表
        JSONObject mentalState = assessment.getMentalState();
        data.put("timeOrientation", AssessmentItemEnum.TIME_ORIENTATION.getScoreDescription(Integer.parseInt(mentalState.get("timeOrientation").toString())));
        data.put("spatialOrientation", AssessmentItemEnum.SPACE_ORIENTATION.getScoreDescription(Integer.parseInt(mentalState.get("spatialOrientation").toString())));
        data.put("personalOrientation", AssessmentItemEnum.PERSON_ORIENTATION.getScoreDescription(Integer.parseInt(mentalState.get("personalOrientation").toString())));
        data.put("memory", AssessmentItemEnum.MEMORY.getScoreDescription(Integer.parseInt(mentalState.get("memory").toString())));
        data.put("savvy", AssessmentItemEnum.COMPREHENSION.getScoreDescription(Integer.parseInt(mentalState.get("savvy").toString())));
        data.put("expressionAbility", AssessmentItemEnum.EXPRESSION.getScoreDescription(Integer.parseInt(mentalState.get("expressionAbility").toString())));
        data.put("aggressiveBehaviour", AssessmentItemEnum.AGGRESSIVE_BEHAVIOR.getScoreDescription(Integer.parseInt(mentalState.get("aggressiveBehaviour").toString())));
        data.put("depressed", AssessmentItemEnum.DEPRESSION.getScoreDescription(Integer.parseInt(mentalState.get("depressed").toString())));
        data.put("levelConsciousness", AssessmentItemEnum.CONSCIOUSNESS.getScoreDescription(Integer.parseInt(mentalState.get("levelConsciousness").toString())));

//        感知觉与社会参与评估表
        JSONObject perceptionSocialParticipation = assessment.getPerceptionSocialParticipation();
        data.put("vision", AssessmentItemEnum.VISION.getScoreDescription(Integer.parseInt(perceptionSocialParticipation.get("vision").toString())));
        data.put("hearing", AssessmentItemEnum.HEARING.getScoreDescription(Integer.parseInt(perceptionSocialParticipation.get("hearing").toString())));
        data.put("dailyAffairs", AssessmentItemEnum.DAILY_AFFAIRS.getScoreDescription(Integer.parseInt(perceptionSocialParticipation.get("dailyAffairs").toString())));
        data.put("vehicle", AssessmentItemEnum.TRANSPORTATION.getScoreDescription(Integer.parseInt(perceptionSocialParticipation.get("vehicle").toString())));
        data.put("interactionAbility", AssessmentItemEnum.SOCIAL_INTERACTION.getScoreDescription(Integer.parseInt(perceptionSocialParticipation.get("interactionAbility").toString())));

//        健康问题
        JSONArray healthProblems = assessment.getHealthProblems();
        data.put("healthProblems", getStringValue(healthProblems));

        AssessmentPlan assessmentPlan = assessmentPlanMapper.selectAssessmentPlanById(assessment.getAssessmentId());
        JSONObject assessmentResult = assessmentPlan.getAssessmentResult();
        data.put("selfCareAbility", assessmentResult.getJSONObject("firstLevelIndicator").get("selfCareAbility").toString());
        data.put("basicMotorAbility", assessmentResult.getJSONObject("firstLevelIndicator").get("basicMotorAbility").toString());
        data.put("mentalState", assessmentResult.getJSONObject("firstLevelIndicator").get("mentalState").toString());
        data.put("socialParticipation", assessmentResult.getJSONObject("firstLevelIndicator").get("socialParticipation").toString());


        data.put("preliminaryGradeScore", assessmentResult.get("preliminaryGradeScore").toString());
        data.put("initialLevelAbilityElderly", assessmentResult.getJSONArray("initialLevelAbilityElderly").get(1).toString());

        JSONArray basisAbilityLevelChange = assessmentResult.getJSONArray("basisAbilityLevelChange");

        List<String> titles = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        JsonNode nodes = null;
        try {
            nodes = mapper.readTree(basisAbilityLevelChange.toJSONString());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        for (JsonNode node : nodes) {
            titles.add(node.get("title").asText());
        }
        data.put("basisAbilityLevelChange", String.join("，", titles));
        data.put("finalLevel", assessmentResult.getJSONArray("finalLevel").get(1).toString());

        return data;
    }

    private void updateMarketingCustomerInfo(MarketingCustomerInfo marketingCustomerInfo, JSONObject assessmentObjBasicsInfo) {
        // 更新存在于营销客户表中的字段
        if (assessmentObjBasicsInfo.containsKey("elderlyName")) {
            marketingCustomerInfo.setElderName(assessmentObjBasicsInfo.getString("elderlyName"));
        }
        if (assessmentObjBasicsInfo.containsKey("sex")) {
            marketingCustomerInfo.setElderGender(assessmentObjBasicsInfo.getString("sex").equals("男") ? "0" : "1");
        }
        if (assessmentObjBasicsInfo.containsKey("age")) {
            marketingCustomerInfo.setElderAge(assessmentObjBasicsInfo.getInteger("age"));
        }
        if (assessmentObjBasicsInfo.containsKey("ID")) {
            marketingCustomerInfo.setIdCardNumber(assessmentObjBasicsInfo.getString("ID"));
        }
        if (assessmentObjBasicsInfo.containsKey("elderlyDateBirth")) {
            try {
                String dateStr = assessmentObjBasicsInfo.getString("elderlyDateBirth");
                if (dateStr != null && !dateStr.isEmpty()) {
                    marketingCustomerInfo.setElderBirthday(DateUtil.parseDate(dateStr));
                }
            } catch (Exception e) {
                // 日期解析失败时忽略
            }
        }
        if (assessmentObjBasicsInfo.containsKey("LivingSituation")) {
            JSONArray livingSituation = assessmentObjBasicsInfo.getJSONArray("LivingSituation");
            if (livingSituation != null && !livingSituation.isEmpty()) {
                // 根据居住情况数组设置居住状态（需要根据实际业务逻辑映射）
                String residenceStatus = getStringValue(livingSituation);
                if (residenceStatus.contains("独居")) {
                    marketingCustomerInfo.setResidenceStatus("0");
                } else if (residenceStatus.contains("子女")) {
                    marketingCustomerInfo.setResidenceStatus("1");
                } else if (residenceStatus.contains("夫妻") || residenceStatus.contains("配偶")) {
                    marketingCustomerInfo.setResidenceStatus("2");
                }
            }
        }
    }

    private void updateElderlyPeopleInfo(ElderlyPeopleInfo elderlyPeopleInfo, JSONObject assessmentObjBasicsInfo) {
        // 更新存在于老人信息表中的字段
        if (assessmentObjBasicsInfo.containsKey("elderlyName")) {
            elderlyPeopleInfo.setName(assessmentObjBasicsInfo.getString("elderlyName"));
        }
        if (assessmentObjBasicsInfo.containsKey("sex")) {
            elderlyPeopleInfo.setSex(assessmentObjBasicsInfo.getString("sex").equals("男") ? "0" : "1");
        }
        if (assessmentObjBasicsInfo.containsKey("age")) {
            elderlyPeopleInfo.setAge(assessmentObjBasicsInfo.getInteger("age"));
        }
        if (assessmentObjBasicsInfo.containsKey("elderlyDateBirth")) {
            try {
                String dateStr = assessmentObjBasicsInfo.getString("elderlyDateBirth");
                if (dateStr != null && !dateStr.isEmpty()) {
                    elderlyPeopleInfo.setDateBirth(DateUtil.parseDate(dateStr));
                }
            } catch (Exception e) {
                // 日期解析失败时忽略
            }
        }
        if (assessmentObjBasicsInfo.containsKey("ID")) {
            elderlyPeopleInfo.setIdCardNum(assessmentObjBasicsInfo.getString("ID"));
        }
        if (assessmentObjBasicsInfo.containsKey("nationality")) {
            elderlyPeopleInfo.setNation(assessmentObjBasicsInfo.getString("nationality"));
        }
        if (assessmentObjBasicsInfo.containsKey("maritalStatus")) {
            elderlyPeopleInfo.setMarriageStatus(assessmentObjBasicsInfo.getString("maritalStatus"));
        }
        if (assessmentObjBasicsInfo.containsKey("LivingSituation")) {
            JSONArray livingSituation = assessmentObjBasicsInfo.getJSONArray("LivingSituation");
            if (livingSituation != null && !livingSituation.isEmpty()) {
                elderlyPeopleInfo.setLivingSituation(getStringValue(livingSituation));
            }
        }
        if (assessmentObjBasicsInfo.containsKey("EconomicSource")) {
            JSONArray economicSource = assessmentObjBasicsInfo.getJSONArray("EconomicSource");
            if (economicSource != null && !economicSource.isEmpty()) {
                elderlyPeopleInfo.setEconomicSources(getStringValue(economicSource));
            }
        }
    }

}
