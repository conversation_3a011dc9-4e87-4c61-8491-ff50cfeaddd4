package com.ruoyi.custom.admin.fee.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.balance.domain.BalanceRecords;
import com.ruoyi.custom.admin.balance.service.IBalanceInfoService;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.fee.domain.ConsumeAccountDetail;
import com.ruoyi.custom.admin.fee.domain.FeePayInfo;
import com.ruoyi.custom.admin.fee.domain.req.FeePayQueryVo;
import com.ruoyi.custom.admin.fee.domain.req.FeePaySaveVo;
import com.ruoyi.custom.admin.fee.domain.res.FeePayVo;
import com.ruoyi.custom.admin.fee.mapper.FeePayInfoMapper;
import com.ruoyi.custom.admin.fee.service.IConsumeAccountDetailService;
import com.ruoyi.custom.admin.fee.service.IFeePayInfoService;
import com.ruoyi.custom.utils.OrderUtils;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 费用支付记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@Service
public class FeePayInfoServiceImpl implements IFeePayInfoService {
    @Autowired
    private FeePayInfoMapper feePayInfoMapper;

    @Autowired
    private IBalanceInfoService balanceInfoService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private IConsumeAccountDetailService consumeAccountDetailService;

    @Autowired
    private IElderlyPeopleInfoService elderlyPeopleInfoService;

    /**
     * 查询费用支付记录
     *
     * @param id 费用支付记录主键
     * @return 费用支付记录
     */
    @Override
    public FeePayInfo selectFeePayInfoById(String id) {
        return feePayInfoMapper.selectFeePayInfoById(id);
    }

    @Override
    public FeePayVo selectNewFeePayInfoById(String id) {
        FeePayVo data = feePayInfoMapper.selectNewFeePayInfoById(id);
        if (Objects.nonNull(data) && StrUtil.isNotBlank(data.getOperatorId())) {
            R<SysUser> userR = remoteUserService.getInfoByUserId(Long.valueOf(data.getOperatorId()));
            if (Objects.nonNull(userR) && userR.getCode() == 200 && Objects.nonNull(userR.getData())) {
                data.setOperatorName(userR.getData().getNickName());
            }
        }
        return data;
    }

    /**
     * 查询费用支付记录列表
     *
     * @param feePayInfo 费用支付记录
     * @return 费用支付记录
     */
    @Override
    public List<FeePayInfo> selectFeePayInfoList(FeePayInfo feePayInfo) {
        return feePayInfoMapper.selectFeePayInfoList(feePayInfo);
    }

    /**
     * 新增费用支付记录
     *
     * @param feePayInfo 费用支付记录
     * @return 结果
     */
    @Override
    public int insertFeePayInfo(FeePayInfo feePayInfo) {
        feePayInfo.setCreateTime(DateUtils.getNowDate());
        return feePayInfoMapper.insertFeePayInfo(feePayInfo);
    }

    /**
     * 修改费用支付记录
     *
     * @param feePayInfo 费用支付记录
     * @return 结果
     */
    @Override
    public int updateFeePayInfo(FeePayInfo feePayInfo) {
        feePayInfo.setUpdateTime(DateUtils.getNowDate());
        return feePayInfoMapper.updateFeePayInfo(feePayInfo);
    }

    /**
     * 批量删除费用支付记录
     *
     * @param ids 需要删除的费用支付记录主键
     * @return 结果
     */
    @Override
    public int deleteFeePayInfoByIds(String[] ids) {
        return feePayInfoMapper.deleteFeePayInfoByIds(ids);
    }

    /**
     * 删除费用支付记录信息
     *
     * @param id 费用支付记录主键
     * @return 结果
     */
    @Override
    public int deleteFeePayInfoById(String id) {
        return feePayInfoMapper.deleteFeePayInfoById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertFeePaySaveVo(FeePaySaveVo feePaySaveVo, String consumeType) {
        feePaySaveVo.setOperatorId(String.valueOf(SecurityUtils.getUserId()));

        // 保存费用缴费明细
        FeePayInfo feePayInfo = BeanUtil.copyProperties(feePaySaveVo, FeePayInfo.class);
        feePayInfo.setPayNumber(OrderUtils.getPayCode(SecurityUtils.getUserId()));
        feePayInfo.setId(IdUtils.fastSimpleUUID());
        feePayInfo.setCreateTime(new Date());
        feePayInfo.setCreateBy(feePaySaveVo.getOperatorId());
        BalanceRecords balanceRecords = balanceInfoService.doPay(feePaySaveVo.getUserId(), feePaySaveVo.getOperatorId(), feePaySaveVo.getPayAmount());
        feePayInfo.setBeginPaidAmount(balanceRecords.getLastAmount());
        feePayInfo.setAfterPaidAmount(balanceRecords.getAmount());
        feePayInfoMapper.insertFeePayInfo(feePayInfo);

        // 保存消费账户明细
        ElderlyPeopleInfo elderlyPeopleInfo = elderlyPeopleInfoService.selectElderlyPeopleInfoById(feePaySaveVo.getUserId());
        ConsumeAccountDetail consumeAccountDetail = new ConsumeAccountDetail();
        consumeAccountDetail.setElderlyId(elderlyPeopleInfo.getId());
        consumeAccountDetail.setElderlyName(elderlyPeopleInfo.getName());
        consumeAccountDetail.setElderlyPhone(elderlyPeopleInfo.getPhone());
        consumeAccountDetail.setType(consumeType);
        consumeAccountDetail.setAmount(feePaySaveVo.getPayAmount());
        elderlyPeopleInfo.setCreateTime(new Date());
        consumeAccountDetailService.insertConsumeAccountDetail(consumeAccountDetail);

        return 1;
    }

    @Override
    public List<FeePayVo> selectNewFeePayInfoList(FeePayQueryVo feePayQueryVo) {
        List<FeePayVo> list = feePayInfoMapper.selectNewFeePayInfoList(feePayQueryVo);
        List<Long> ids = list.stream()
                .map(FeePayVo::getOperatorId)
                .filter(StrUtil::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        Map<Long, SysUser> userMap = remoteUserService.getUserMapByIds(ids.toArray(new Long[0]));
        list.forEach(a -> {
            if (StrUtil.isNotBlank(a.getOperatorId())) {
                SysUser sysUser = userMap.get(Long.parseLong(a.getOperatorId()));
                if (!Objects.isNull(sysUser)) {
                    a.setOperatorName(sysUser.getNickName());
                }
            }
        });

        return list;
    }
}
