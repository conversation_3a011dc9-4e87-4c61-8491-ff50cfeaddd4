package com.ruoyi.custom.admin.assessment.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.assessment.domain.AssessmentPlan;
import com.ruoyi.custom.admin.assessment.mapper.AssessmentPlanMapper;
import com.ruoyi.custom.admin.assessment.service.INursingPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.UUID;

/**
 * 护理计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Service
public class NursingPlanServiceImpl implements INursingPlanService {

    @Autowired
    private AssessmentPlanMapper assessmentPlanMapper;

    /**
     * 获取护理计划指标列表
     *
     * @param assessmentPlanId 评估计划ID
     * @return 护理计划指标列表
     */
    @Override
    public JSONArray getNursingPlanIndicators(Long assessmentPlanId) {
        AssessmentPlan assessmentPlan = assessmentPlanMapper.selectAssessmentPlanById(assessmentPlanId);
        if (assessmentPlan == null) {
            throw new ServiceException("找不到指定的评估计划");
        }

        // 如果护理计划指标为空，则返回空数组
        if (assessmentPlan.getNursingPlanIndicators() == null) {
            return new JSONArray();
        }

        return assessmentPlan.getNursingPlanIndicators();
    }

    /**
     * 新增护理计划指标
     *
     * @param assessmentPlanId 评估计划ID
     * @param indicator        护理计划指标
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addNursingPlanIndicator(Long assessmentPlanId, JSONObject indicator) {
        AssessmentPlan assessmentPlan = assessmentPlanMapper.selectAssessmentPlanById(assessmentPlanId);
        if (assessmentPlan == null) {
            throw new ServiceException("找不到指定的评估计划");
        }

        // 生成UUID
        indicator.put("uuid", UUID.randomUUID().toString());

        // 如果护理计划指标为空，则初始化为空数组
        JSONArray nursingPlanIndicators = assessmentPlan.getNursingPlanIndicators();
        if (nursingPlanIndicators == null) {
            nursingPlanIndicators = new JSONArray();
            assessmentPlan.setNursingPlanIndicators(nursingPlanIndicators);
        }

        // 添加指标
        nursingPlanIndicators.add(indicator);

        // 更新评估计划
        assessmentPlan.setUpdateTime(new Date());
        assessmentPlan.setUpdateBy(SecurityUtils.getUsername());

        return assessmentPlanMapper.updateAssessmentPlan(assessmentPlan);
    }

    /**
     * 修改护理计划指标
     *
     * @param assessmentPlanId 评估计划ID
     * @param uuid             指标UUID
     * @param indicator        护理计划指标
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateNursingPlanIndicator(Long assessmentPlanId, String uuid, JSONObject indicator) {
        AssessmentPlan assessmentPlan = assessmentPlanMapper.selectAssessmentPlanById(assessmentPlanId);
        if (assessmentPlan == null) {
            throw new ServiceException("找不到指定的评估计划");
        }

        JSONArray nursingPlanIndicators = assessmentPlan.getNursingPlanIndicators();
        if (nursingPlanIndicators == null || nursingPlanIndicators.isEmpty()) {
            throw new ServiceException("评估计划没有护理计划指标");
        }

        // 保留原有的UUID
        indicator.put("uuid", uuid);

        // 查找并替换指定UUID的指标
        boolean found = false;
        for (int i = 0; i < nursingPlanIndicators.size(); i++) {
            JSONObject existingIndicator = nursingPlanIndicators.getJSONObject(i);
            if (uuid.equals(existingIndicator.getString("uuid"))) {
                nursingPlanIndicators.set(i, indicator);
                found = true;
                break;
            }
        }

        if (!found) {
            throw new ServiceException("找不到指定UUID的护理计划指标");
        }

        // 更新评估计划
        assessmentPlan.setUpdateTime(new Date());
        assessmentPlan.setUpdateBy(SecurityUtils.getUsername());

        return assessmentPlanMapper.updateAssessmentPlan(assessmentPlan);
    }

    /**
     * 删除护理计划指标
     *
     * @param assessmentPlanId 评估计划ID
     * @param uuid             指标UUID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteNursingPlanIndicator(Long assessmentPlanId, String uuid) {
        AssessmentPlan assessmentPlan = assessmentPlanMapper.selectAssessmentPlanById(assessmentPlanId);
        if (assessmentPlan == null) {
            throw new ServiceException("找不到指定的评估计划");
        }

        JSONArray nursingPlanIndicators = assessmentPlan.getNursingPlanIndicators();
        if (nursingPlanIndicators == null || nursingPlanIndicators.isEmpty()) {
            throw new ServiceException("评估计划没有护理计划指标");
        }

        // 查找并删除指定UUID的指标
        boolean found = false;
        for (int i = 0; i < nursingPlanIndicators.size(); i++) {
            JSONObject existingIndicator = nursingPlanIndicators.getJSONObject(i);
            if (uuid.equals(existingIndicator.getString("uuid"))) {
                nursingPlanIndicators.remove(i);
                found = true;
                break;
            }
        }

        if (!found) {
            throw new ServiceException("找不到指定UUID的护理计划指标");
        }

        // 更新评估计划
        assessmentPlan.setUpdateTime(new Date());
        assessmentPlan.setUpdateBy(SecurityUtils.getUsername());

        return assessmentPlanMapper.updateAssessmentPlan(assessmentPlan);
    }

    /**
     * 获取护理计划指标详情
     *
     * @param assessmentPlanId 评估计划ID
     * @param uuid             指标UUID
     * @return 护理计划指标
     */
    @Override
    public JSONObject getNursingPlanIndicator(Long assessmentPlanId, String uuid) {
        AssessmentPlan assessmentPlan = assessmentPlanMapper.selectAssessmentPlanById(assessmentPlanId);
        if (assessmentPlan == null) {
            throw new ServiceException("找不到指定的评估计划");
        }

        JSONArray nursingPlanIndicators = assessmentPlan.getNursingPlanIndicators();
        if (nursingPlanIndicators == null || nursingPlanIndicators.isEmpty()) {
            throw new ServiceException("评估计划没有护理计划指标");
        }

        // 查找指定UUID的指标
        for (int i = 0; i < nursingPlanIndicators.size(); i++) {
            JSONObject existingIndicator = nursingPlanIndicators.getJSONObject(i);
            if (uuid.equals(existingIndicator.getString("uuid"))) {
                return existingIndicator;
            }
        }

        throw new ServiceException("找不到指定UUID的护理计划指标");
    }
} 