package com.ruoyi.custom.admin.fee.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.domain.Page;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.fee.domain.FeeComboDTO;
import com.ruoyi.custom.admin.fee.domain.res.FeeComboVo;
import com.ruoyi.custom.admin.fee.service.IFeeComboInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.List;

/**
 * 套餐费用Controller
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@Api(tags = "费用-套餐费用管理")
@RestController
@RequestMapping("/fee_combo")
public class FeeComboInfoController extends BaseController {
    @Autowired
    private IFeeComboInfoService feeComboInfoService;

    /**
     * 查询套餐费用列表
     */
    @ApiOperation("查询套餐费用列表")
    //@RequiresPermissions("custom:fee_combo:list")
    @GetMapping("/list")
    public TableDataInfo<FeeComboVo> list(String careLevel, String comboName, Page page) {
        startPage();
        List<FeeComboDTO> list = feeComboInfoService.selectComboList(careLevel, comboName);
        TableDataInfo info = getDataTable(list);

        List<FeeComboVo> result = new ArrayList<>();
        for (FeeComboDTO feeComboDTO : list) {
            FeeComboVo feeComboVo = BeanUtil.copyProperties(feeComboDTO, FeeComboVo.class);
//            feeComboVo.setDayDetails(JSONUtil.parseArray(feeComboDTO.getDayDetails()));
            feeComboVo.setMonthDetails(JSONUtil.parseArray(feeComboDTO.getMonthDetails()));
            result.add(feeComboVo);
        }
        info.setRows(result);
        return info;
    }

    /**
     * 导出套餐费用列表
     */
/*    //@RequiresPermissions("custom:fee_combo:export")
    @Log(platform = "1",title = "套餐费用", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FeeComboInfo feeComboInfo)
    {
        List<FeeComboInfo> list = feeComboInfoService.selectFeeComboInfoList(feeComboInfo);
        ExcelUtil<FeeComboInfo> util = new ExcelUtil<FeeComboInfo>(FeeComboInfo.class);
        util.exportExcel(response, list, "套餐费用数据");
    }*/

    /**
     * 获取套餐费用详细信息
     */
    @ApiOperation("获取套餐费用详细信息")
    //@RequiresPermissions("custom:fee_combo:query")
    @GetMapping(value = "/{id}")
    public TAjaxResult<FeeComboVo> getInfo(@PathVariable("id") String id) {
        TAjaxResult<FeeComboVo> result = new TAjaxResult<FeeComboVo>();
        result.success(feeComboInfoService.selectComboById(id));
        return result;
    }

    /**
     * 新增套餐费用
     */
    @ApiIgnore
    @ApiOperation("新增套餐费用")
    //@RequiresPermissions("custom:fee_combo:add")
    @Log(platform = "1", title = "套餐费用", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FeeComboVo feeComboVo) {
        return toAjax(feeComboInfoService.insertFeeComboInfo(feeComboVo));
    }

    /**
     * 修改套餐费用
     */
    @ApiOperation("保存套餐费用")
    //@RequiresPermissions("custom:fee_combo:edit")
    @Log(platform = "1", title = "保存套餐费用", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FeeComboVo FeeComboVo) {
        return toAjax(feeComboInfoService.updateFeeComboInfo(FeeComboVo));
    }

    /**
     * 删除套餐费用
     */
    @ApiIgnore
    @ApiOperation("删除套餐费用")
    //@RequiresPermissions("custom:fee_combo:remove")
    @Log(platform = "1", title = "套餐费用", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(feeComboInfoService.deleteFeeComboInfoByIds(ids));
    }
}
