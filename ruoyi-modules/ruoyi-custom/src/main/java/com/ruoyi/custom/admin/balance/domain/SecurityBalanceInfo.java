package com.ruoyi.custom.admin.balance.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 保障金账户信息对象 t_security_balance_info
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Data
@ApiModel(value = "保障金账户信息")
public class SecurityBalanceInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Integer id;

    /**
     * 老人id，关联表：t_elderly_people_info
     */
    @Excel(name = "老人id")
    @ApiModelProperty(value = "老人id，关联表：t_elderly_people_info")
    private String elderlyId;

    /**
     * 上次余额
     */
    @Excel(name = "上次余额")
    @ApiModelProperty(value = "上次余额")
    private BigDecimal lastAmount;

    /**
     * 余额
     */
    @Excel(name = "余额")
    @ApiModelProperty(value = "余额")
    private BigDecimal amount;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    @ApiModelProperty(value = "逻辑删除标记（0：显示；1：隐藏")
    private String delFlag;
} 