package com.ruoyi.custom.admin.balance.mapper;

import com.ruoyi.custom.admin.balance.domain.SecurityBalanceInfo;

import java.util.List;

/**
 * 保障金账户信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface SecurityBalanceInfoMapper {
    /**
     * 查询保障金账户信息
     *
     * @param id 保障金账户信息主键
     * @return 保障金账户信息
     */
    public SecurityBalanceInfo selectSecurityBalanceInfoById(Integer id);

    /**
     * 查询保障金账户信息列表
     *
     * @param securityBalanceInfo 保障金账户信息
     * @return 保障金账户信息集合
     */
    public List<SecurityBalanceInfo> selectSecurityBalanceInfoList(SecurityBalanceInfo securityBalanceInfo);

    /**
     * 新增保障金账户信息
     *
     * @param securityBalanceInfo 保障金账户信息
     * @return 结果
     */
    public int insertSecurityBalanceInfo(SecurityBalanceInfo securityBalanceInfo);

    /**
     * 修改保障金账户信息
     *
     * @param securityBalanceInfo 保障金账户信息
     * @return 结果
     */
    public int updateSecurityBalanceInfo(SecurityBalanceInfo securityBalanceInfo);

    /**
     * 删除保障金账户信息
     *
     * @param id 保障金账户信息主键
     * @return 结果
     */
    public int deleteSecurityBalanceInfoById(Integer id);

    /**
     * 批量删除保障金账户信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSecurityBalanceInfoByIds(Integer[] ids);
    
    /**
     * 根据老人ID查询保障金账户信息
     *
     * @param elderlyId 老人ID
     * @return 保障金账户信息
     */
    public SecurityBalanceInfo selectSecurityBalanceInfoByElderlyId(String elderlyId);
} 