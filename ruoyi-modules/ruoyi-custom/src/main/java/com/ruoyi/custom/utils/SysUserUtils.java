package com.ruoyi.custom.utils;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.care.domain.CareWorkerInfo;
import com.ruoyi.custom.admin.care.service.ICareWorkerInfoService;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.elderlyPeople.vo.UserDataInfoResult;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Component
public class SysUserUtils {

    private static SysUserUtils sysUserUtils;

    @Autowired
    protected RemoteUserService remoteUserService;
    @Autowired
    protected IElderlyPeopleInfoService elderlyPeopleInfoService;
    @Autowired
    protected ICareWorkerInfoService careWorkerInfoService;

    public static void main(String[] args) {
        System.out.println(UUID.randomUUID().toString());
    }

    /**
     * 统一新增用户接口
     *
     * @param nickName 用户名称
     * @param userName 登录名称
     * @param password 登录密码
     * @param type     新增用户角色 0老人用户，6护工
     *                 老人用户：角色id=20
     *                 护工：角色id=4
     * @return
     */
    public static R<SysUser> addSysUser(String nickName, String userName, String password, String type, String phone, String imgUrl) {
        SysUser sysUser = new SysUser();
        sysUser.setNickName(nickName);
        // 若没有填用户名，使用昵称的拼音作为用户名
        if (StringUtils.isEmpty(userName)) {
            String uniqueSuffix = UUID.randomUUID().toString().split("-")[1]; // 取UUID的一部分以缩短长度
            userName = ChineseCharacterUtils.chineseConversionPinyin(nickName, true) + "-" + uniqueSuffix;
        }
        sysUser.setPhonenumber(phone);
        sysUser.setUserName(userName);
        sysUser.setAvatar(imgUrl);
        if (StringUtils.isEmpty(password)) {// 如果没有设置密码就给默认密码
            password = "123456";
        }
        sysUser.setPassword(password);
        sysUser.setSourceFrom("elderPeople");
        // 添加角色id
        if ("0".equals(type)) {// 老人用户
            Long[] rolesIds = {20L};
            sysUser.setRoleIds(rolesIds);
            sysUser.setPlatform("100");// 默认为居家系统用户
        } else if ("6".equals(type)) {// 护工
            Long[] rolesIds = {4L};
            sysUser.setRoleIds(rolesIds);
        }
        R<SysUser> loginUserR = sysUserUtils.remoteUserService.addUser(sysUser);
        return loginUserR;
    }

    /**
     * 老人用户：角色id=6  flag = lryh
     * 护工：角色id=4
     *
     * @param flag
     * @return
     */
    public static UserDataInfoResult getInfoBySysUserId(String flag) {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        List<SysRole> roles = sysUser.getRoles();
        List<Long> roleList = new ArrayList<>();
        for (SysRole role : roles) {
            Long roleId = role.getRoleId();
            roleList.add(roleId);
        }
        UserDataInfoResult userDataInfoResult = new UserDataInfoResult();
        if (roleList.contains(20L) && "applet_lr".equals(flag)) {// 老人角色
            ElderlyPeopleInfo elderlyPeopleInfoBySysUserId = sysUserUtils.elderlyPeopleInfoService.getElderlyPeopleInfoBySysUserId(loginUser.getUserid());
            userDataInfoResult.setUserId(elderlyPeopleInfoBySysUserId.getId());
        } else if (roleList.contains(4L) && "app_hg".equals(flag)) {// 护工
            CareWorkerInfo careWorkerInfo = sysUserUtils.careWorkerInfoService.getCareByUserId(loginUser.getUserid());
            userDataInfoResult.setWorkId(careWorkerInfo.getId());
        }
        return userDataInfoResult;
    }

    @PostConstruct // 完成对service的注入
    public void init() {
        sysUserUtils = this;
        sysUserUtils.remoteUserService = this.remoteUserService;
        sysUserUtils.elderlyPeopleInfoService = this.elderlyPeopleInfoService;
        sysUserUtils.careWorkerInfoService = this.careWorkerInfoService;
    }


}
