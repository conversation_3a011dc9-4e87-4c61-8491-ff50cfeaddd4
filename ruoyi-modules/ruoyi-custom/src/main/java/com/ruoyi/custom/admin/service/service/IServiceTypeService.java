package com.ruoyi.custom.admin.service.service;

import com.ruoyi.custom.admin.service.domain.ServiceType;

import java.util.List;


/**
 * 服务分类Service接口
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface IServiceTypeService {
    /**
     * 查询服务分类
     *
     * @param id 服务分类主键
     * @return 服务分类
     */
    public ServiceType selectServiceTypeById(Long id);

    /**
     * 查询服务分类列表
     *
     * @param serviceType 服务分类
     * @return 服务分类集合
     */
    public List<ServiceType> selectServiceTypeList(ServiceType serviceType);

    /**
     * 新增服务分类
     *
     * @param serviceType 服务分类
     * @return 结果
     */
    public int insertServiceType(ServiceType serviceType);

    /**
     * 修改服务分类
     *
     * @param serviceType 服务分类
     * @return 结果
     */
    public int updateServiceType(ServiceType serviceType);

    /**
     * 批量删除服务分类
     *
     * @param ids 需要删除的服务分类主键集合
     * @return 结果
     */
    public int deleteServiceTypeByIds(Long[] ids);

}

