package com.ruoyi.custom.admin.liveManage.domain.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.custom.utils.DictUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class LiveInfoRes {

    private String id;

    @ApiModelProperty(value = "用户id", name = "userId", required = true, example = "07d43fd026944c7892136a25da911c84")
    private String userId;
    @ApiModelProperty(value = "名称", name = "userName", required = true, example = "张三")
    private String userName;
    @ApiModelProperty(value = "年龄", name = "age", required = true, example = "65")
    private String age;
    @ApiModelProperty(value = "手机号", name = "phone", required = true, example = "18568859335")
    private String phone;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "初始入住日期", name = "liveDate", required = true, example = "2022-04-01")
    private Date liveDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "计费日期", name = "billingDate", required = true, example = "2022-04-01")
    private Date billingDate;

    @ApiModelProperty(value = "入住房间", name = "roomId", required = true, example = "d026944c7892136a25da")
    private String roomId;

    @ApiModelProperty(value = "房间名称", name = "roomName", required = true, example = "201房间")
    private String roomName;

    @ApiModelProperty(value = "房间类型", name = "roomType", required = true, example = "201房间")
    private String roomType;

    @ApiModelProperty(value = "房间关联版本", name = "roomVersion", required = true, example = "1.0")
    private String roomVersion;

    @ApiModelProperty(value = "房间类型关联版本id", name = "roomTypeVersionId", required = true, example = "11")
    private Integer roomTypeVersionId;

    @ApiModelProperty(value = "入住床位", name = "bedId", required = true, example = "d026944c7892136a25da")
    private String bedId;

    @ApiModelProperty(value = "床位编号", name = "bedNum", required = true, example = "001")
    private String bedNum;

    @ApiModelProperty(value = "床位", name = "bedName", required = true, example = "一楼栋-一层-101房间-1号床")
    private String bedName;

    @ApiModelProperty(value = "护理级别", name = "careLevel", required = true, example = "1")
    private String careLevel;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    @ApiModelProperty(value = "护理级别名称", name = "careLevelStr", required = true, example = "1")
    private String careLevelStr;

    @ApiModelProperty(value = "套餐id", name = "comboId", required = true, example = "d026944c7892136a25da")
    private String comboId;

    @ApiModelProperty(value = "套餐名称", name = "comboName", required = true, example = "豪华护理套餐")
    private String comboName;

    @ApiModelProperty(value = "费用标准版本", name = "comboVersion", required = true, example = "1.0")
    private String comboVersion;

    @ApiModelProperty(value = "居住状态", name = "state", required = true, example = "0,1,2")
    private String state;

    @ApiModelProperty(value = "账单状态（0结算，1查看）", name = "billState")
    private String billState;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    @ApiModelProperty(value = "居住状态str", name = "stateStr", required = true, example = "居住中")
    private String stateStr;

    @ApiModelProperty(value = "余额", name = "amount", example = "255.22")
    private BigDecimal amount;

    @ApiModelProperty(value = "保障金余额", name = "securityAmount", example = "255.22")
    private BigDecimal securityAmount;

    @ApiModelProperty(value = "合同编号", name = "contractNumber", required = true, example = "20220401001")
    private String contractNumber;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "退住日期", name = "checkOutDate", example = "2022-08-25")
    private Date checkOutDate;

    @ApiModelProperty(value = "合同开始时间", name = "contractStartDate", example = "2022-08-25")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractStartDate;

    @ApiModelProperty(value = "合同结束时间", name = "contractEndDate", example = "2022-08-25")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndDate;

    public String getStateStr() {
        return DictUtils.selectDictLabel("live_state", this.state);
    }

    public void setStateStr(String stateStr) {
        this.stateStr = stateStr;
    }

    public String getCareLevelStr() {
        return DictUtils.selectDictLabel("care_level", this.careLevel);
    }

    public void setCareLevelStr(String careLevelStr) {
        this.careLevelStr = careLevelStr;
    }
}
