package com.ruoyi.custom.admin.marketing.mapper;

import com.ruoyi.custom.admin.marketing.domain.PaymentChangeRecord;

import java.util.List;


/**
 * 缴费确认单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
public interface PaymentChangeRecordMapper {
    /**
     * 查询缴费确认单
     *
     * @param id 缴费确认单主键
     * @return 缴费确认单
     */
    public PaymentChangeRecord selectPaymentChangeRecordById(String id);

    /**
     * 查询缴费确认单列表
     *
     * @param paymentChangeRecord 缴费确认单
     * @return 缴费确认单集合
     */
    public List<PaymentChangeRecord> selectPaymentChangeRecordList(PaymentChangeRecord paymentChangeRecord);

    /**
     * 新增缴费确认单
     *
     * @param paymentChangeRecord 缴费确认单
     * @return 结果
     */
    public int insertPaymentChangeRecord(PaymentChangeRecord paymentChangeRecord);

    /**
     * 修改缴费确认单
     *
     * @param paymentChangeRecord 缴费确认单
     * @return 结果
     */
    public int updatePaymentChangeRecord(PaymentChangeRecord paymentChangeRecord);

    /**
     * 删除缴费确认单
     *
     * @param id 缴费确认单主键
     * @return 结果
     */
    public int deletePaymentChangeRecordById(String id);

    /**
     * 批量删除缴费确认单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePaymentChangeRecordByIds(String[] ids);

    /**
     * 查询最大id
     * @return
     */
    String selectMaxId();

    /**
     * 根据合同编号查询缴费确认单列表，并按ID倒序排序
     *
     * @param contractNumber 合同编号
     * @return 缴费确认单列表
     */
    List<PaymentChangeRecord> selectByContractNumberOrderByIdDesc(String contractNumber);

}

