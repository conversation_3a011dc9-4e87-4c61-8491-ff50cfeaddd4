package com.ruoyi.custom.admin.train.mapper;

import com.ruoyi.custom.admin.train.domain.TrainingInfo;

import java.util.List;


/**
 * 培训信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-29
 */
public interface TrainingInfoMapper {
    /**
     * 查询培训信息
     *
     * @param id 培训信息主键
     * @return 培训信息
     */
    public TrainingInfo selectTrainingInfoById(Long id);

    /**
     * 查询培训信息列表
     *
     * @param trainingInfo 培训信息
     * @return 培训信息集合
     */
    public List<TrainingInfo> selectTrainingInfoList(TrainingInfo trainingInfo);

    /**
     * 新增培训信息
     *
     * @param trainingInfo 培训信息
     * @return 结果
     */
    public int insertTrainingInfo(TrainingInfo trainingInfo);

    /**
     * 修改培训信息
     *
     * @param trainingInfo 培训信息
     * @return 结果
     */
    public int updateTrainingInfo(TrainingInfo trainingInfo);

    /**
     * 删除培训信息
     *
     * @param id 培训信息主键
     * @return 结果
     */
    public int deleteTrainingInfoById(Long id);

    /**
     * 批量删除培训信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTrainingInfoByIds(Long[] ids);

    /**
     * 查询培训记录列表
     * @param trainingInfo
     * @return
     */
    List<TrainingInfo> selectTrainingRecordList(TrainingInfo trainingInfo);

}

