package com.ruoyi.custom.admin.storiedBuilding.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 房间类型对象 t_room_type_base_info
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
@ApiModel(value = "房间类型")
public class RoomTypeBaseInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 房间类型名称
     */
    @Excel(name = "房间类型名称")
    @ApiModelProperty(value = "房间类型名称")
    private String name;

    /**
     * 月费用
     */
    @Excel(name = "月费用")
    @ApiModelProperty(value = "月费用")
    private BigDecimal fees;

    /**
     * 版本号
     */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号")
    private String version;
    @ApiModelProperty(value = "版本号Id")
    private String typeVersionId;

    /**
     * 床位数
     */
    @Excel(name = "床位数")
    @ApiModelProperty(value = "床位数")
    private Long bedNumber;

    /**
     * 状态
     */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getFees() {
        return fees;
    }

    public void setFees(BigDecimal fees) {
        this.fees = fees;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

    public Long getBedNumber() {
        return bedNumber;
    }

    public void setBedNumber(Long bedNumber) {
        this.bedNumber = bedNumber;
    }

    public String getTypeVersionId() {
        return typeVersionId;
    }

    public void setTypeVersionId(String typeVersionId) {
        this.typeVersionId = typeVersionId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("fees", getFees())
                .append("version", getVersion())
                .append("typeVersionId", getTypeVersionId())
                .append("status", getStatus())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }
}
