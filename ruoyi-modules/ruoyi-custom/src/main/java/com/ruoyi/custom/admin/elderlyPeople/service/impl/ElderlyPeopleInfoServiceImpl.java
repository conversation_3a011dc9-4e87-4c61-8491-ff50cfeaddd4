package com.ruoyi.custom.admin.elderlyPeople.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.custom.admin.elderlyPeople.domain.vo.ElderlyPeopleInfoLivingVo;
import com.ruoyi.custom.admin.elderlyPeople.domain.vo.ElderlyPeopleInfoVo;
import com.ruoyi.custom.admin.elderlyPeople.mapper.ElderlyPeopleInfoMapper;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleFamilyInfoService;
import com.ruoyi.custom.admin.elderlyPeople.service.IElderlyPeopleInfoService;
import com.ruoyi.custom.admin.liveManage.domain.LiveBedRecords;
import com.ruoyi.custom.admin.liveManage.service.ILiveBedRecordsService;
import com.ruoyi.custom.admin.marketing.domain.ContractInfo;
import com.ruoyi.custom.admin.marketing.domain.MarketingCustomerInfo;
import com.ruoyi.custom.admin.marketing.mapper.ContractInfoMapper;
import com.ruoyi.custom.admin.marketing.resp.ElderlyPeopleInfo2;
import com.ruoyi.custom.admin.marketing.service.MarketingCustomerInfoService;
import com.ruoyi.custom.utils.Base64ToMultipartUtils;
import com.ruoyi.custom.utils.SysUserUtils;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 老人基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
@Service
public class ElderlyPeopleInfoServiceImpl implements IElderlyPeopleInfoService {
    @Autowired
    private ElderlyPeopleInfoMapper elderlyPeopleInfoMapper;

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private MarketingCustomerInfoService marketingCustomerInfoService;

    @Autowired
    private IElderlyPeopleFamilyInfoService elderlyPeopleFamilyInfoService;

    @Autowired
    private ContractInfoMapper contractInfoMapper;

    @Autowired
    private ILiveBedRecordsService liveBedRecordsService;

    /**
     * 查询老人基础信息
     *
     * @param id 老人基础信息主键
     * @return 老人基础信息
     */
    @Override
    public ElderlyPeopleInfo selectElderlyPeopleInfoById(String id) {
        return elderlyPeopleInfoMapper.selectElderlyPeopleInfoById(id);
    }

    /**
     * 查询老人基础信息列表
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 老人基础信息
     */
    @Override
    public List<ElderlyPeopleInfo> selectElderlyPeopleInfoList(ElderlyPeopleInfo elderlyPeopleInfo) {
        return elderlyPeopleInfoMapper.selectElderlyPeopleInfoList(elderlyPeopleInfo);
    }

    @Override
    public List<JSONObject> getUserList(String name, String state) {
        return elderlyPeopleInfoMapper.getUserList(name, state);
    }

    /**
     * 新增老人基础信息
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 结果
     */
    @Override
    public String insertElderlyPeopleInfo(ElderlyPeopleInfo elderlyPeopleInfo) {
        elderlyPeopleInfo.setCreateTime(DateUtils.getNowDate());
        String id = IdUtils.fastSimpleUUID();
        elderlyPeopleInfo.setId(id);
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleInfo.setCreateBy(String.valueOf(userId));
        if (elderlyPeopleInfo.getImg() != null && elderlyPeopleInfo.getImg().contains(";base64,")) {
            MultipartFile file = Base64ToMultipartUtils.base64ToMultipart(elderlyPeopleInfo.getImg());
            if (!file.isEmpty()) {
                R<SysFile> fileResult = remoteFileService.upload(file);
                if (StringUtils.isNull(fileResult) || StringUtils.isNull(fileResult.getData())) {
                    return "文件服务异常，请联系管理员";
                }
                String url = fileResult.getData().getUrl();
                elderlyPeopleInfo.setImg(url);
            }
        }

        // 处理老人账户信息
        Long sysUserId = getSysUserIdByPhone(elderlyPeopleInfo);
        elderlyPeopleInfo.setSysUserId(sysUserId);
        elderlyPeopleInfoMapper.insertElderlyPeopleInfo(elderlyPeopleInfo);

        return id;
    }

    /**
     * 根据手机号获取系统用户ID，不存在则创建
     *
     * @param elderlyPeopleInfo 老人信息
     * @return 系统用户ID
     */
    private Long getSysUserIdByPhone(ElderlyPeopleInfo elderlyPeopleInfo) {
        // 校验手机号是否为空
        if (StrUtil.isBlank(elderlyPeopleInfo.getPhone())) {
            throw new ServiceException("长者手机号不能为空");
        }

        // 查询老人账户是否已存在
        R<LoginUser> userResult = remoteUserService.getUserInfo(elderlyPeopleInfo.getPhone(), SecurityConstants.INNER);
        if (userResult.getCode() == 200) {
            // 账户已存在，直接返回用户ID
            return userResult.getData().getUserid();
        }

        // 账户不存在，创建新用户
        R<SysUser> sysUserR = SysUserUtils.addSysUser(
                elderlyPeopleInfo.getName(),
                elderlyPeopleInfo.getPhone(),
                null,
                "0",
                elderlyPeopleInfo.getPhone(),
                elderlyPeopleInfo.getImg());

        if (sysUserR.getCode() != 200) {
            throw new ServiceException(sysUserR.getMsg());
        }

        return sysUserR.getData().getUserId();
    }

    /**
     * 修改老人基础信息
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 结果
     */
    @Override
    public int updateElderlyPeopleInfo(ElderlyPeopleInfo elderlyPeopleInfo) {
        // 身份证若不为空需校验不能在表中已存在，排除自己
        if (StringUtils.isNotEmpty(elderlyPeopleInfo.getIdCardNum())) {
            ElderlyPeopleInfo param = new ElderlyPeopleInfo();
            param.setIdCardNum(elderlyPeopleInfo.getIdCardNum());
            List<ElderlyPeopleInfo> list = elderlyPeopleInfoMapper.selectElderlyPeopleInfoList(param);
            list.removeIf(item -> item.getId().equals(elderlyPeopleInfo.getId()));
            if (list.size() > 0) {
                throw new ServiceException("身份证号码已存在");
            }
        }


        elderlyPeopleInfo.setUpdateTime(DateUtils.getNowDate());
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleInfo.setUpdateBy(String.valueOf(userId));
        if (elderlyPeopleInfo.getImg() != null && elderlyPeopleInfo.getImg().contains(";base64,")) {
            MultipartFile file = Base64ToMultipartUtils.base64ToMultipart(elderlyPeopleInfo.getImg());
            if (!file.isEmpty()) {
                R<SysFile> fileResult = remoteFileService.upload(file);
                if (StringUtils.isNull(fileResult) || StringUtils.isNull(fileResult.getData())) {
                    return 2;
                }
                String url = fileResult.getData().getUrl();
                elderlyPeopleInfo.setImg(url);

                // 更新系统老人用户头像
                remoteUserService.updateAvatar(elderlyPeopleInfo.getPhone(), url);
            }
        }
        return elderlyPeopleInfoMapper.updateElderlyPeopleInfo(elderlyPeopleInfo);
    }

    /**
     * 批量删除老人基础信息
     *
     * @param ids 需要删除的老人基础信息主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleInfoByIds(Long[] ids) {
        return elderlyPeopleInfoMapper.deleteElderlyPeopleInfoByIds(ids);
    }

    @Override
    public int logicalDeleteElderlyPeopleInfoByIds(String[] ids) {
        return elderlyPeopleInfoMapper.logicalDeleteElderlyPeopleInfoByIds(ids);
    }

    /**
     * 删除老人基础信息信息
     *
     * @param id 老人基础信息主键
     * @return 结果
     */
    @Override
    public int deleteElderlyPeopleInfoById(Long id) {
        return elderlyPeopleInfoMapper.deleteElderlyPeopleInfoById(id);
    }


    /**
     * 老人信息列表
     *
     * @param elderlyPeopleInfoVo
     * @return
     */
    @Override
    public List<ElderlyPeopleInfoVo> getPeopleInfoList(ElderlyPeopleInfoVo elderlyPeopleInfoVo) {
        List<ElderlyPeopleInfoVo> peopleInfoList = elderlyPeopleInfoMapper.getPeopleInfoList(elderlyPeopleInfoVo);
        return peopleInfoList;
    }

    @Override
    public ElderlyPeopleInfo getElderlyPeopleInfoBySysUserId(Long userid) {
        return elderlyPeopleInfoMapper.getElderlyPeopleInfoBySysUserId(userid);
    }

    @Override
    public List<JSONObject> getEldersByWorkerId(Long workId) {
        return elderlyPeopleInfoMapper.getElderIdsByWorkerId(workId);
    }

    @Override
    public List<ElderlyPeopleInfoLivingVo> getLivingInfoList(ElderlyPeopleInfoLivingVo elderlyPeopleInfoVo) {
        return elderlyPeopleInfoMapper.getLivingInfoList(elderlyPeopleInfoVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int sign(ContractInfo contractInfo) {
        // 校验营销老人是否已有合同
        ContractInfo contractInfoParams = new ContractInfo();
        contractInfoParams.setCustomerId(contractInfo.getCustomerId());
        List<ContractInfo> contractInfoList = contractInfoMapper.selectContractInfoList(contractInfoParams);
        if (CollUtil.isNotEmpty(contractInfoList)) {
            throw new ServiceException("该老人已存在合同，请勿重复添加合同");
        }

        // 校验合同编号是否已存在
        ContractInfo contractInfoParams1 = new ContractInfo();
        contractInfoParams1.setContractNumber(contractInfo.getContractNumber());
        if (CollUtil.isNotEmpty(contractInfoMapper.selectContractInfoList(contractInfoParams1))) {
            throw new ServiceException("该合同编号已存在，请重新输入");
        }

        // 同步老人信息到营销客户
        if (contractInfo.getElderlyPeopleInfo() == null) {
            contractInfo.setElderlyPeopleInfo(new ElderlyPeopleInfo());
        }
        if (contractInfo.getMarketingCustomerInfo() == null) {
            contractInfo.setMarketingCustomerInfo(new MarketingCustomerInfo());
        }
        MarketingCustomerInfo marketingCustomerInfo1 = new MarketingCustomerInfo();
        marketingCustomerInfo1.setId(contractInfo.getCustomerId());
        marketingCustomerInfo1.setElderGender(contractInfo.getElderlyPeopleInfo().getSex());
        marketingCustomerInfo1.setConsultantPhone(contractInfo.getElderlyPeopleInfo().getPhone());
        marketingCustomerInfo1.setHomeAddress(contractInfo.getElderlyPeopleInfo().getHomeAddress());
        marketingCustomerInfo1.setMarketerId(contractInfo.getMarketingCustomerInfo().getMarketerId());
        marketingCustomerInfo1.setMarketer(contractInfo.getMarketingCustomerInfo().getMarketer());
        marketingCustomerInfo1.setSignStatus("1"); // 已签约
        marketingCustomerInfoService.updateMarketingCustomerInfo2(marketingCustomerInfo1);

        // 同步老人信息到 t_elderly_people_info
        MarketingCustomerInfo marketingCustomerInfo2 = marketingCustomerInfoService.selectMarketingCustomerInfoById(contractInfo.getCustomerId());
        ElderlyPeopleInfo elderlyPeopleInfo = contractInfo.getElderlyPeopleInfo();
        elderlyPeopleInfo.setId(contractInfo.getElderlyPeopleId());
        elderlyPeopleInfo.setNation(marketingCustomerInfo2.getNation());
        elderlyPeopleInfo.setMarriageStatus(marketingCustomerInfo2.getMaritalStatus());
        elderlyPeopleInfo.setLivingSituation(marketingCustomerInfo2.getResidenceStatus());
        elderlyPeopleInfo.setHomeAddress(marketingCustomerInfo2.getHomeAddress());
        this.updateElderlyPeopleInfo(elderlyPeopleInfo);

        // 更新床位记录的开始日期（如果已入住）
        ElderlyPeopleInfo baseInfo = elderlyPeopleInfoMapper.selectElderlyPeopleInfoById(contractInfo.getElderlyPeopleId());
        if (baseInfo.getBedId() != null) {
            LiveBedRecords liveBedRecords = liveBedRecordsService.selectLiveBedRecordsById(baseInfo.getBedRecordId());
            if (liveBedRecords.getBeginDate() == null) {
                liveBedRecords.setBeginDate(contractInfo.getContractStartDate());
                liveBedRecordsService.updateLiveBedRecords(liveBedRecords);
            }
        }


        // 保存合同信息
        contractInfo.setIsRenewable("0");
        contractInfo.setElderlyPeopleId(contractInfo.getElderlyPeopleId());
        contractInfoMapper.insertContractInfo(contractInfo);
        return 1;
    }

    /**
     * 获取近一个月每天的入住老人数量
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 近一个月每天入住老人数量统计
     */
    @Override
    public List<JSONObject> getCheckInFlowStatistics(String startDate, String endDate) {
        return elderlyPeopleInfoMapper.getCheckInFlowStatistics(startDate, endDate);
    }

    @Override
    public List<ElderlyPeopleInfo2> selectElderlyPeopleInfoList2(ElderlyPeopleInfo2 elderlyPeopleInfo) {
        return elderlyPeopleInfoMapper.selectElderlyPeopleInfoList2(elderlyPeopleInfo);
    }
}
