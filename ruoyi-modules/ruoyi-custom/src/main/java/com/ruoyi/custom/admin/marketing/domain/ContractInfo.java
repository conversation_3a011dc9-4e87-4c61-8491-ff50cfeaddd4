package com.ruoyi.custom.admin.marketing.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.custom.admin.elderlyPeople.domain.ElderlyPeopleInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 合同信息对象 t_contract_info
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
@ApiModel(value = "合同信息")
public class ContractInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 关联的续签合同编号
     */
    @ApiModelProperty(value = "续签合同编号")
    private String childNumber;

    /**
     * 营销客户id，关联：t_marketing_customer_info
     */
    @Excel(name = "营销客户id")
    @ApiModelProperty(value = "营销客户id，关联t_marketing_customer_info")
    private Long customerId;

    /**
     * 老人id，关联：t_elderly_people_info
     */
    @Excel(name = "老人id")
    @ApiModelProperty(value = "老人id，关联t_elderly_people_info")
    private String elderlyPeopleId;

    /**
     * 合同录入人id，关联sys_user
     */
    @Excel(name = "合同录入人id")
    @ApiModelProperty(value = "合同录入人id，关联sys_user")
    private Long contractEntryUserId;

    /**
     * 合同录入人
     */
    @Excel(name = "合同录入人")
    @ApiModelProperty(value = "合同录入人")
    private String contractEntryUser;

    /**
     * 合同签订日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同签订日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "合同签订日期")
    private Date contractSignDate;

    /**
     * 合同开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "合同开始日期")
    private Date contractStartDate;

    /**
     * 合同结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "合同结束日期")
    private Date contractEndDate;

    /**
     * 床位折扣
     */
    @Excel(name = "床位折扣")
    @ApiModelProperty(value = "床位折扣")
    private String bedDiscount;

    /**
     * 折扣理由
     */
    @Excel(name = "折扣理由")
    @ApiModelProperty(value = "折扣理由")
    private String discountReason;

    /**
     * 附件url
     */
    @Excel(name = "附件url")
    @ApiModelProperty(value = "附件url，多个逗号隔开")
    private String attachmentUrl;

    /**
     * 是否可续签
     */
    @Excel(name = "是否可续签")
    @ApiModelProperty(value = "是否可续签")
    private String isRenewable;

    /**
     * 缴费类型，0：没有交过一次费，1：交过费还没最终结算，2：已结算
     */
    @Excel(name = "缴费类型，0：没有交过一次费，1：交过费还没最终结算，2：已结算")
    private Integer feeType;

    // ------------------ 分界线 ------------------

    /**
     * （原始）合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(exist = false)
    private String oldContractNumber;

    /**
     * 老人信息
     */
    @ApiModelProperty(value = "老人信息")
    private ElderlyPeopleInfo elderlyPeopleInfo;

    /**
     * 营销客户信息
     */
    @ApiModelProperty(value = "营销客户信息")
    private MarketingCustomerInfo marketingCustomerInfo;

    /**
     * 续签合同信息
     */
    @ApiModelProperty(value = "续签合同信息")
    private ContractInfo childContractInfo;

    /**
     * 续签合同信息List
     */
    @ApiModelProperty(value = "续签合同信息List")
    private List<ContractInfo> childContractInfoList;
}

