package com.ruoyi.custom.admin.warehouse.service;

import java.util.List;

import cn.hutool.json.JSONObject;
import com.ruoyi.custom.admin.warehouse.domain.SupplierInfo;

/**
 * 供应商管理Service接口
 *
 * <AUTHOR>
 * @date 2022-05-09
 */
public interface ISupplierInfoService {
    /**
     * 查询供应商管理
     *
     * @param id 供应商管理主键
     * @return 供应商管理
     */
    public SupplierInfo selectSupplierInfoById(Long id);

    /**
     * 查询供应商管理列表
     *
     * @param supplierInfo 供应商管理
     * @return 供应商管理集合
     */
    public List<SupplierInfo> selectSupplierInfoList(SupplierInfo supplierInfo);

    /**
     * 新增供应商管理
     *
     * @param supplierInfo 供应商管理
     * @return 结果
     */
    public int insertSupplierInfo(SupplierInfo supplierInfo);

    /**
     * 修改供应商管理
     *
     * @param supplierInfo 供应商管理
     * @return 结果
     */
    public int updateSupplierInfo(SupplierInfo supplierInfo);

    /**
     * 批量删除供应商管理
     *
     * @param ids 需要删除的供应商管理主键集合
     * @return 结果
     */
    public int deleteSupplierInfoByIds(Long[] ids);

    /**
     * 删除供应商管理信息
     *
     * @param id 供应商管理主键
     * @return 结果
     */
    public int deleteSupplierInfoById(Long id);


    /**
     * 获取全部label格式的供应商列表
     *
     * @return
     */
    List<JSONObject> getSupplierLabelList();
}
