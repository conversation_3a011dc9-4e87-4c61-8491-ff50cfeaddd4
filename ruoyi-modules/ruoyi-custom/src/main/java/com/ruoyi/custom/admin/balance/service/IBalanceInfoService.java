package com.ruoyi.custom.admin.balance.service;

import com.ruoyi.custom.admin.balance.domain.BalanceInfo;
import com.ruoyi.custom.admin.balance.domain.BalanceRecords;

import java.math.BigDecimal;
import java.util.List;

/**
 * 余额信息Service接口
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
public interface IBalanceInfoService {
    /**
     * 查询余额信息
     *
     * @param id 余额信息主键
     * @return 余额信息
     */
    public BalanceInfo selectBalanceInfoById(String id);

    /**
     * 查询余额信息列表
     *
     * @param balanceInfo 余额信息
     * @return 余额信息集合
     */
    public List<BalanceInfo> selectBalanceInfoList(BalanceInfo balanceInfo);

    /**
     * 新增余额信息
     *
     * @param balanceInfo 余额信息
     * @return 结果
     */
    public int insertBalanceInfo(BalanceInfo balanceInfo);

    /**
     * 修改余额信息
     *
     * @param balanceInfo 余额信息
     * @return 结果
     */
    public int updateBalanceInfo(BalanceInfo balanceInfo);

    /**
     * 批量删除余额信息
     *
     * @param ids 需要删除的余额信息主键集合
     * @return 结果
     */
    public int deleteBalanceInfoByIds(String[] ids);

    /**
     * 删除余额信息信息
     *
     * @param id 余额信息主键
     * @return 结果
     */
    public int deleteBalanceInfoById(String id);

    /**
     * 充值
     * @param userId
     * @param operatorId
     * @param pagAmount
     * @return
     */
    BalanceRecords doPay(String userId, String operatorId, BigDecimal pagAmount);

    /**
     * 支出
     *
     * @param userId
     * @param operatorId
     * @param pagAmount
     * @return
     */
    BalanceRecords doDeduction(String userId, String operatorId, BigDecimal pagAmount, String consumeType);

    /**
     * 清空账户余额
     *
     * @param userId
     * @param operatorId
     * @param returnAmount
     * @return
     */
    BalanceRecords cleanBalance(String userId, String operatorId, BigDecimal returnAmount, String consumeType);
}
