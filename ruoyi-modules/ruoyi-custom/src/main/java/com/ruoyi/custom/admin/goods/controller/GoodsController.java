package com.ruoyi.custom.admin.goods.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.custom.admin.goods.domain.GoodsBaseInfo;
import com.ruoyi.custom.admin.goods.request.AddOrUpdateGoodsParam;
import com.ruoyi.custom.admin.goods.request.GoodsDeleteParame;
import com.ruoyi.custom.admin.goods.request.GoodsListParam;
import com.ruoyi.custom.admin.goods.request.GoodsUpOrDownRequest;
import com.ruoyi.custom.admin.goods.service.GoodsService;
import com.ruoyi.custom.admin.goods.vo.GoodsAllVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @ClassName GoodsBaseInfoController
 * @Description 商品-管理
 * <AUTHOR>
 * @Date 2024/11/25
 */
@RestController
@RequestMapping("/goods")
@Api(tags = "商品管理")
public class GoodsController extends BaseController {

    @Autowired
    private GoodsService goodsService;

    /**
     * 查询商品列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页查询商品列表-销量倒序")
    public TableDataInfo<GoodsAllVo> list(@Valid GoodsListParam goodsListParam) {
        List<GoodsAllVo> list = goodsService.getList(goodsListParam);
        return getDataTable(list);
    }

    /**
     * 根据商品id获取商品详情
     */
    @GetMapping("/getByGoodsId")
    @ApiOperation(value = "根据商品id获取商品详情")
    public TAjaxResult<GoodsBaseInfo> getByGoodsId(
            @RequestParam(value = "goodsId", required = true)
            @ApiParam(name = "goodsId", value = "商品id")
            @NotBlank(message = "goodsId is not null！")
            String goodsId
    ) {
        GoodsBaseInfo GoodsBaseInfo = goodsService.getByGoodsId(goodsId);
        return new TAjaxResult().success(GoodsBaseInfo);
    }


    @ApiOperation(httpMethod = "POST", value = "新增(更新)商品/餐品")
    @PostMapping(value = "/addOrUpdate")
    public TAjaxResult addGoods(@RequestBody @Valid AddOrUpdateGoodsParam addOrUpdateGoodsParam) {
        return goodsService.addOrUpdate(addOrUpdateGoodsParam);
    }

    @ApiOperation(httpMethod = "POST", value = "商品上下架")
    @PostMapping(value = "/upOrDown")
    public TAjaxResult upOrDown(@RequestBody @Valid GoodsUpOrDownRequest goodsUpOrDownRequest) {
        return goodsService.upOrDown(goodsUpOrDownRequest);
    }

    @ApiOperation(httpMethod = "POST", value = "删除商品")
    @PostMapping(value = "/delete")
    public TAjaxResult upOrDown(@RequestBody @Valid GoodsDeleteParame goodsDeleteParame) {
        return goodsService.delete(goodsDeleteParame.getGoodsId());
    }


}
