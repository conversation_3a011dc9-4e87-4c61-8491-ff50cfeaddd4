package com.ruoyi.custom.api.dashboard;

import com.ruoyi.common.core.web.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 大屏分析数据大屏人员管理
 */
@RestController("dashboardPeopleManagementController")
@RequestMapping("/dashboard/peopleManagement")
@Api(tags = "大屏-人员管理")
public class PeopleManagementController extends BaseController {
}