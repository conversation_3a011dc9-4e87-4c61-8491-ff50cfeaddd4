package com.ruoyi.custom.admin.marketing.mapper;


import com.ruoyi.custom.admin.marketing.domain.ContractInfo;
import com.ruoyi.custom.admin.marketing.domain.PaymentRecord;
import com.ruoyi.custom.admin.marketing.dto.PaymentRecordDTO;

import java.util.List;

/**
 * 合同信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
public interface ContractInfoMapper {
    /**
     * 查询合同信息
     *
     * @param contractNumber 合同信息主键
     * @return 合同信息
     */
    public ContractInfo selectContractInfoByContractNumber(String contractNumber);

    /**
     * 查询合同信息列表
     *
     * @param contractInfo 合同信息
     * @return 合同信息集合
     */
    public List<ContractInfo> selectContractInfoList(ContractInfo contractInfo);

    /**
     * 新增合同信息
     *
     * @param contractInfo 合同信息
     * @return 结果
     */
    public int insertContractInfo(ContractInfo contractInfo);

    /**
     * 修改合同信息
     *
     * @param contractInfo 合同信息
     * @return 结果
     */
    public int updateContractInfo(ContractInfo contractInfo);

    /**
     * 删除合同信息
     *
     * @param contractNumber 合同信息主键
     * @return 结果
     */
    public int deleteContractInfoByContractNumber(String contractNumber);

    /**
     * 批量删除合同信息
     *
     * @param contractNumbers 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteContractInfoByContractNumbers(String[] contractNumbers);

    /**
     * 根据客户id获取合同信息
     * @param customerId
     * @return
     */
    ContractInfo getContractInfoByCustomerId(Integer customerId);

    /**
     * 查询合同续签信息
     *
     * @param contractInfo
     * @return
     */
    List<ContractInfo> selectContractRenewInfoList(ContractInfo contractInfo);

    /**
     * 根据合同id获取老人入住相关信息
     *
     * @param contractNumber
     * @return
     */
    PaymentRecordDTO selectLiveInfo(String contractNumber);

    /**
     * 根据年份查询最大的合同编号序列
     *
     * @param year 年份
     * @return 最大序列号
     */
    String selectMaxContractNumberByYear(String year);

    /**
     * 查询合同信息列表（new）
     *
     * @param contractInfo
     * @return
     */
    List<ContractInfo> selectContractInfoListNew(ContractInfo contractInfo);

    /**
     * 获取合同信息列表（new）
     *
     * @param contractInfo
     * @return
     */
    List<ContractInfo> selectContractRenewInfoListNew(ContractInfo contractInfo);
}
