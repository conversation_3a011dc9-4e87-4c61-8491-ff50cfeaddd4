package com.ruoyi.custom.api.workerApp;

import cn.hutool.core.map.MapUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.custom.admin.train.domain.TrainingInfo;
import com.ruoyi.custom.admin.train.service.TrainingInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 护工APP-培训 Controller
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
@Slf4j
@Api(tags = "护工APP-培训")
@RestController("workerAppTrainController")
@RequestMapping("/workerApp/train")
public class TrainController extends BaseController {

    @Autowired
    private TrainingInfoService trainingInfoService;

    /**
     * 我的培训
     */
    @RequestMapping("/ownerRecord")
    @ApiOperation(value = "我的培训")
    public TableDataInfo<TrainingInfo> myTrain() {
        startPage();
        TrainingInfo param = new TrainingInfo();
        param.setParams(MapUtil.of("userId", SecurityUtils.getLoginUser().getUserid()));
        return getDataTable(trainingInfoService.selectTrainingRecordList(param));
    }


}
