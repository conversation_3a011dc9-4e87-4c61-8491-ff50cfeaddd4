package com.ruoyi.custom.admin.pharmacyWarehouse.controller;


import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.pharmacyWarehouse.domain.PharmacyManagement;
import com.ruoyi.custom.admin.pharmacyWarehouse.service.IPharmacyManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 药品管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/pharmacy/management")
@Api(tags = "药品管理")
public class PharmacyManagementController extends BaseController {
    @Autowired
    private IPharmacyManagementService pharmacyManagementService;

    /**
     * 查询药品管理列表
     */
    // @RequiresPermissions("custom:management:list")
    @GetMapping("/list")
    @ApiOperation(value = "获取列表")
    public TableDataInfo list(PharmacyManagement pharmacyManagement) {
        startPage();
        List<PharmacyManagement> list = pharmacyManagementService.selectPharmacyManagementList(pharmacyManagement);
        return getDataTable(list);
    }

    /**
     * 导出药品管理列表
     */
    // @RequiresPermissions("custom:management:export")
    @Log(title = "药品管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PharmacyManagement pharmacyManagement) {
        List<PharmacyManagement> list = pharmacyManagementService.selectPharmacyManagementList(pharmacyManagement);
        ExcelUtil<PharmacyManagement> util = new ExcelUtil<PharmacyManagement>(PharmacyManagement.class);
        util.exportExcel(response, list, "药品管理数据");
    }

    /**
     * 获取药品管理详细信息
     */
    // @RequiresPermissions("custom:management:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取详情")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(pharmacyManagementService.selectPharmacyManagementById(id));
    }

    /**
     * 新增药品管理
     */
    // @RequiresPermissions("custom:management:add")
    @Log(title = "药品管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增")
    public AjaxResult add(@RequestBody PharmacyManagement pharmacyManagement) {
        return toAjax(pharmacyManagementService.insertPharmacyManagement(pharmacyManagement));
    }

    /**
     * 修改药品管理
     */
    // @RequiresPermissions("custom:management:edit")
    @Log(title = "药品管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改")
    public AjaxResult edit(@RequestBody PharmacyManagement pharmacyManagement) {
        return toAjax(pharmacyManagementService.updatePharmacyManagement(pharmacyManagement));
    }

    /**
     * 删除药品管理
     */
    // @RequiresPermissions("custom:management:remove")
    @Log(title = "药品管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(pharmacyManagementService.deletePharmacyManagementByIds(ids));
    }
}

