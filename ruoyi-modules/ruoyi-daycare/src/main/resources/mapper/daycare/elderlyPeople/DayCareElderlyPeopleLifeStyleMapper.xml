<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.daycare.elderlyPeople.mapper.DayCareElderlyPeopleLifeStyleMapper">

    <resultMap type="com.ruoyi.daycare.elderlyPeople.domain.DayCareElderlyPeopleLifeStyle"
               id="DayCareElderlyPeopleLifeStyleResult">
        <result property="id" column="id"/>
        <result property="dietaryStatus" column="dietary_status"/>
        <result property="tasteSelection" column="taste_selection"/>
        <result property="eatBreakfast" column="eat_breakfast"/>
        <result property="takeExercise" column="take_exercise"/>
        <result property="exerciseTimesPerWeek" column="exercise_times_per_week"/>
        <result property="exerciseTime" column="exercise_time"/>
        <result property="sleepQuality" column="sleep_quality"/>
        <result property="sleepTime" column="sleep_time"/>
        <result property="stayUpLateStatus" column="stay_up_late_status"/>
        <result property="smokingStatus" column="smoking_status"/>
        <result property="quitSmokingYears" column="quit_smoking_years"/>
        <result property="numberCigarettesSmokedDaily" column="number_cigarettes_smoked_daily"/>
        <result property="smokingTime" column="smoking_time"/>
        <result property="drinkingStatus" column="drinking_status"/>
        <result property="capacityForLiquor" column="capacity_for_liquor"/>
        <result property="drinkingTime" column="drinking_time"/>
        <result property="drinkingTypes" column="drinking_types"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="userId" column="user_id"/>
    </resultMap>

    <sql id="selectDayCareElderlyPeopleLifeStyleVo">
        select id, dietary_status, taste_selection, eat_breakfast, take_exercise, exercise_times_per_week,
        exercise_time, sleep_quality, sleep_time, stay_up_late_status, smoking_status, quit_smoking_years,
        number_cigarettes_smoked_daily, smoking_time, drinking_status, capacity_for_liquor, drinking_time,
        drinking_types, create_time, create_by, update_time, update_by, del_flag, remark, user_id from
        t_daycare_elderly_people_life_style
    </sql>

    <select id="selectDayCareElderlyPeopleLifeStyleList"
            parameterType="com.ruoyi.daycare.elderlyPeople.domain.DayCareElderlyPeopleLifeStyle"
            resultMap="DayCareElderlyPeopleLifeStyleResult">
        <include refid="selectDayCareElderlyPeopleLifeStyleVo"/>
        <where>
            del_flag = '0'
            <if test="dietaryStatus != null  and dietaryStatus != ''">and dietary_status = #{dietaryStatus}</if>
            <if test="tasteSelection != null  and tasteSelection != ''">and taste_selection = #{tasteSelection}</if>
            <if test="eatBreakfast != null  and eatBreakfast != ''">and eat_breakfast = #{eatBreakfast}</if>
            <if test="takeExercise != null  and takeExercise != ''">and take_exercise = #{takeExercise}</if>
            <if test="exerciseTimesPerWeek != null  and exerciseTimesPerWeek != ''">and exercise_times_per_week =
                #{exerciseTimesPerWeek}
            </if>
            <if test="exerciseTime != null  and exerciseTime != ''">and exercise_time = #{exerciseTime}</if>
            <if test="sleepQuality != null  and sleepQuality != ''">and sleep_quality = #{sleepQuality}</if>
            <if test="sleepTime != null  and sleepTime != ''">and sleep_time = #{sleepTime}</if>
            <if test="stayUpLateStatus != null  and stayUpLateStatus != ''">and stay_up_late_status =
                #{stayUpLateStatus}
            </if>
            <if test="smokingStatus != null  and smokingStatus != ''">and smoking_status = #{smokingStatus}</if>
            <if test="quitSmokingYears != null  and quitSmokingYears != ''">and quit_smoking_years =
                #{quitSmokingYears}
            </if>
            <if test="numberCigarettesSmokedDaily != null  and numberCigarettesSmokedDaily != ''">and
                number_cigarettes_smoked_daily = #{numberCigarettesSmokedDaily}
            </if>
            <if test="smokingTime != null  and smokingTime != ''">and smoking_time = #{smokingTime}</if>
            <if test="drinkingStatus != null  and drinkingStatus != ''">and drinking_status = #{drinkingStatus}</if>
            <if test="capacityForLiquor != null  and capacityForLiquor != ''">and capacity_for_liquor =
                #{capacityForLiquor}
            </if>
            <if test="drinkingTime != null  and drinkingTime != ''">and drinking_time = #{drinkingTime}</if>
            <if test="drinkingTypes != null  and drinkingTypes != ''">and drinking_types = #{drinkingTypes}</if>
            <if test="userId != null  and userId != ''">and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectDayCareElderlyPeopleLifeStyleById" parameterType="String"
            resultMap="DayCareElderlyPeopleLifeStyleResult">
        <include refid="selectDayCareElderlyPeopleLifeStyleVo"/>
        where id = #{id}
    </select>

    <insert id="insertDayCareElderlyPeopleLifeStyle"
            parameterType="com.ruoyi.daycare.elderlyPeople.domain.DayCareElderlyPeopleLifeStyle">
        insert into t_daycare_elderly_people_life_style
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="dietaryStatus != null">dietary_status,</if>
            <if test="tasteSelection != null">taste_selection,</if>
            <if test="eatBreakfast != null">eat_breakfast,</if>
            <if test="takeExercise != null">take_exercise,</if>
            <if test="exerciseTimesPerWeek != null">exercise_times_per_week,</if>
            <if test="exerciseTime != null">exercise_time,</if>
            <if test="sleepQuality != null">sleep_quality,</if>
            <if test="sleepTime != null">sleep_time,</if>
            <if test="stayUpLateStatus != null">stay_up_late_status,</if>
            <if test="smokingStatus != null">smoking_status,</if>
            <if test="quitSmokingYears != null">quit_smoking_years,</if>
            <if test="numberCigarettesSmokedDaily != null">number_cigarettes_smoked_daily,</if>
            <if test="smokingTime != null">smoking_time,</if>
            <if test="drinkingStatus != null">drinking_status,</if>
            <if test="capacityForLiquor != null">capacity_for_liquor,</if>
            <if test="drinkingTime != null">drinking_time,</if>
            <if test="drinkingTypes != null">drinking_types,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="dietaryStatus != null">#{dietaryStatus},</if>
            <if test="tasteSelection != null">#{tasteSelection},</if>
            <if test="eatBreakfast != null">#{eatBreakfast},</if>
            <if test="takeExercise != null">#{takeExercise},</if>
            <if test="exerciseTimesPerWeek != null">#{exerciseTimesPerWeek},</if>
            <if test="exerciseTime != null">#{exerciseTime},</if>
            <if test="sleepQuality != null">#{sleepQuality},</if>
            <if test="sleepTime != null">#{sleepTime},</if>
            <if test="stayUpLateStatus != null">#{stayUpLateStatus},</if>
            <if test="smokingStatus != null">#{smokingStatus},</if>
            <if test="quitSmokingYears != null">#{quitSmokingYears},</if>
            <if test="numberCigarettesSmokedDaily != null">#{numberCigarettesSmokedDaily},</if>
            <if test="smokingTime != null">#{smokingTime},</if>
            <if test="drinkingStatus != null">#{drinkingStatus},</if>
            <if test="capacityForLiquor != null">#{capacityForLiquor},</if>
            <if test="drinkingTime != null">#{drinkingTime},</if>
            <if test="drinkingTypes != null">#{drinkingTypes},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
        </trim>
    </insert>

    <update id="updateDayCareElderlyPeopleLifeStyle"
            parameterType="com.ruoyi.daycare.elderlyPeople.domain.DayCareElderlyPeopleLifeStyle">
        update t_daycare_elderly_people_life_style
        <trim prefix="SET" suffixOverrides=",">
            <if test="dietaryStatus != null">dietary_status = #{dietaryStatus},</if>
            <if test="tasteSelection != null">taste_selection = #{tasteSelection},</if>
            <if test="eatBreakfast != null">eat_breakfast = #{eatBreakfast},</if>
            <if test="takeExercise != null">take_exercise = #{takeExercise},</if>
            <if test="exerciseTimesPerWeek != null">exercise_times_per_week = #{exerciseTimesPerWeek},</if>
            <if test="exerciseTime != null">exercise_time = #{exerciseTime},</if>
            <if test="sleepQuality != null">sleep_quality = #{sleepQuality},</if>
            <if test="sleepTime != null">sleep_time = #{sleepTime},</if>
            <if test="stayUpLateStatus != null">stay_up_late_status = #{stayUpLateStatus},</if>
            <if test="smokingStatus != null">smoking_status = #{smokingStatus},</if>
            <if test="quitSmokingYears != null">quit_smoking_years = #{quitSmokingYears},</if>
            <if test="numberCigarettesSmokedDaily != null">number_cigarettes_smoked_daily =
                #{numberCigarettesSmokedDaily},
            </if>
            <if test="smokingTime != null">smoking_time = #{smokingTime},</if>
            <if test="drinkingStatus != null">drinking_status = #{drinkingStatus},</if>
            <if test="capacityForLiquor != null">capacity_for_liquor = #{capacityForLiquor},</if>
            <if test="drinkingTime != null">drinking_time = #{drinkingTime},</if>
            <if test="drinkingTypes != null">drinking_types = #{drinkingTypes},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDayCareElderlyPeopleLifeStyleById" parameterType="String">
        delete from t_daycare_elderly_people_life_style where id = #{id}
    </delete>

    <delete id="deleteDayCareElderlyPeopleLifeStyleByIds" parameterType="String">
        update t_daycare_elderly_people_life_style set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
