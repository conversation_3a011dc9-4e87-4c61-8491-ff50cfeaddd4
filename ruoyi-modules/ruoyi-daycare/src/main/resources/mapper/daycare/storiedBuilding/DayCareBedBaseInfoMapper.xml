<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.daycare.storiedBuilding.mapper.DayCareBedBaseInfoMapper">

    <resultMap type="com.ruoyi.daycare.storiedBuilding.domain.DayCareBedBaseInfo" id="DayCareBedBaseInfoResult">
        <result property="id" column="id"/>
        <result property="bedName" column="bed_name"/>
        <result property="bedNum" column="bed_num"/>
        <result property="bedState" column="bed_state"/>
        <result property="careIndex" column="care_index"/>
        <result property="feeIndex" column="fee_index"/>
        <result property="liveRecordsIndex" column="live_records_index"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="roomId" column="room_id"/>
    </resultMap>

    <sql id="selectDayCareBedBaseInfoVo">
        select id, bed_name, bed_num, bed_state, care_index, fee_index, live_records_index, create_time, create_by,
        update_time, update_by, del_flag, remark, room_id from t_daycare_bed_base_info
    </sql>

    <select id="selectDayCareBedBaseInfoList"
            parameterType="com.ruoyi.daycare.storiedBuilding.domain.DayCareBedBaseInfo"
            resultMap="DayCareBedBaseInfoResult">
        <include refid="selectDayCareBedBaseInfoVo"/>
        <where>
            del_flag = '0'
            <if test="bedName != null  and bedName != ''">and bed_name like concat('%', #{bedName}, '%')</if>
            <if test="bedNum != null  and bedNum != ''">and bed_num like concat('%', #{bedNum}, '%')</if>
            <if test="bedState != null  and bedState != ''">and bed_state = #{bedState}</if>
            <if test="careIndex != null  and careIndex != ''">and care_index = #{careIndex}</if>
            <if test="feeIndex != null  and feeIndex != ''">and fee_index = #{feeIndex}</if>
            <if test="liveRecordsIndex != null  and liveRecordsIndex != ''">and live_records_index =
                #{liveRecordsIndex}
            </if>
            <if test="roomId != null ">and room_id = #{roomId}</if>
        </where>
    </select>

    <select id="selectDayCareBedBaseInfoById" parameterType="Long" resultMap="DayCareBedBaseInfoResult">
        <include refid="selectDayCareBedBaseInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertDayCareBedBaseInfo" parameterType="com.ruoyi.daycare.storiedBuilding.domain.DayCareBedBaseInfo"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_daycare_bed_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bedName != null">bed_name,</if>
            <if test="bedNum != null">bed_num,</if>
            <if test="bedState != null">bed_state,</if>
            <if test="careIndex != null">care_index,</if>
            <if test="feeIndex != null">fee_index,</if>
            <if test="liveRecordsIndex != null">live_records_index,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="roomId != null">room_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bedName != null">#{bedName},</if>
            <if test="bedNum != null">#{bedNum},</if>
            <if test="bedState != null">#{bedState},</if>
            <if test="careIndex != null">#{careIndex},</if>
            <if test="feeIndex != null">#{feeIndex},</if>
            <if test="liveRecordsIndex != null">#{liveRecordsIndex},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="roomId != null">#{roomId},</if>
        </trim>
    </insert>

    <update id="updateDayCareBedBaseInfo" parameterType="com.ruoyi.daycare.storiedBuilding.domain.DayCareBedBaseInfo">
        update t_daycare_bed_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="bedName != null">bed_name = #{bedName},</if>
            <if test="bedNum != null">bed_num = #{bedNum},</if>
            <if test="bedState != null">bed_state = #{bedState},</if>
            <if test="careIndex != null">care_index = #{careIndex},</if>
            <if test="feeIndex != null">fee_index = #{feeIndex},</if>
            <if test="liveRecordsIndex != null">live_records_index = #{liveRecordsIndex},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="roomId != null">room_id = #{roomId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDayCareBedBaseInfoById" parameterType="Long">
        delete from t_daycare_bed_base_info where id = #{id}
    </delete>

    <delete id="deleteDayCareBedBaseInfoByIds" parameterType="String">
        update t_daycare_bed_base_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="hasBedByRoomId" parameterType="Long" resultType="int">
        select count(1) from t_daycare_bed_base_info
        where del_flag = '0' and room_id = #{id} limit 1
    </select>

    <select id="getBedInfo" parameterType="Long" resultType="cn.hutool.json.JSONObject">
        SELECT
        a.id as id,
        CONCAT(bud3.`name`,'-',bud2.`name`,'-',bud1.`name`,'-',a.`bed_name`) as groupName,
        a.bed_name as name,
        a.room_id as roomId,
        r.type_version as roomVersion,
        IFNULL(u.id,'') as userId,
        IFNULL(u.name,'') as userName,
        '4' as type
        FROM
        t_daycare_bed_base_info AS a
        LEFT JOIN t_daycare_room_type_index_info AS r ON a.room_id = r.room_id
        left join t_daycare_live_bed_records br on a.id = br.bed_id and br.live_state=0
        LEFT JOIN t_daycare_live_base_info AS b ON br.live_id = b.id
        LEFT JOIN t_daycare_elderly_people_info AS u ON u.id = b.user_id
        LEFT JOIN t_daycare_storied_building_info bud1 ON a.room_id = bud1.id
        LEFT JOIN t_daycare_storied_building_info bud2 ON bud1.parent_id = bud2.id
        LEFT JOIN t_daycare_storied_building_info bud3 ON bud2.parent_id = bud3.id
        WHERE
        r.STATUS = '0' AND a.del_flag = '0' and a.room_id = #{roomId}

    </select>
    <select id="getRoomBedNum" resultType="cn.hutool.json.JSONObject" parameterType="Long">
        select COUNT(id) as totalBedNumber,SUM(IF(bed_state = '0',1,0) ) as occupancyNumber from t_daycare_bed_base_info
        where room_id = #{id} and del_flag = '0'
    </select>

    <select id="getRoomBedList" resultType="cn.hutool.json.JSONObject" parameterType="Long">


        SELECT
        a.id AS id,
        a.bed_name AS name,
        a.room_id AS roomId,
        r.type_version AS roomVersion,
        IFNULL( u.id, '') AS userId,
        IFNULL( u.name, '') AS userName,
        IFNULL(ve.daily_fee,'') as dailyFee,
        IFNULL(ve.fees,'') as fees,
        IFNULL(u.age,'') as age,
        IFNULL(u.phone,'') as phone,
        -- IFNULL(a.bed_state,1) AS bedState,
        IFNULL(br.live_state,1) as bedState
        FROM
        t_daycare_bed_base_info AS a
        LEFT JOIN t_daycare_room_type_index_info AS r ON a.room_id = r.room_id
        left join t_daycare_live_bed_records as br on a.id=br.bed_id and br.live_state=0
        LEFT JOIN t_daycare_live_base_info AS b ON br.live_id =b.id
        LEFT JOIN t_daycare_elderly_people_info AS u ON u.id = b.user_id
        LEFT JOIN t_daycare_room_type_version_info AS ve ON ve.type_id = r.type_id AND ve.version = r.type_version
        where r.status = '0' and a.del_flag = '0' and r.room_id = #{roomId}


    </select>
    <select id="getBedStateData" resultType="cn.hutool.json.JSONObject" parameterType="Long">
        SELECT
        a.id,
        CONCAT(bud3.`name`,'-',bud2.`name`,'-',bud1.`name`,'-',a.`bed_name`) as groupName,
        CONCAT(bud1.name,'-',a.bed_name) as name,
        a.room_id as roomId,
        r.type_version as roomVersion,
        r.type_version_id as typeVersionId,
        -- a.bed_state as bedState,
        IFNULL(br.live_state,1) as bedState,
        IFNULL(u.name,'') as username,
        IFNULL(u.phone,'') as phone,
        IFNULL(u.age,'') as age,
        IFNULL(ty.name,'') as typeName
        FROM
        t_daycare_bed_base_info AS a
        left join t_daycare_live_bed_records as br on a.id=br.bed_id and br.live_state=0

        -- left join t_daycare_live_base_info as l on a.live_records_index = l.id
        left join t_daycare_live_base_info as l on br.live_id=l.id
        LEFT JOIN t_daycare_storied_building_info bud1 on a.room_id= bud1.id
        LEFT JOIN t_daycare_storied_building_info bud2 on bud1.parent_id= bud2.id
        LEFT JOIN t_daycare_storied_building_info bud3 on bud2.parent_id= bud3.id
        left join t_daycare_elderly_people_info as u on u.id = l.user_id
        left join (select room_id,type_id,type_version,type_version_id from t_daycare_room_type_index_info where status
        = '0') as r on r.room_id = bud1.id
        left join t_daycare_room_type_base_info as ty on r.type_id = ty.id
        <where>
            a.del_flag = '0' and bud1.type = 3
            <if test="id != null and id != ''">
                AND (bud1.ancestors LIKE '%${id},%'
                OR bud1.id = #{id})
            </if>

        </where>
        order by a.create_time desc
    </select>

    <select id="getBedRecordsList" parameterType="String" resultType="DayCareBedRecordsInfoVo">
        SELECT
        IFNULL(u.name,'') as name,
        IFNULL(li.begin_date,'') as beginDate,
        li.end_date as endDate,
        li.live_state as liveState
        FROM
        t_daycare_live_bed_records AS li
        LEFT JOIN t_daycare_live_base_info AS b ON b.id = li.live_id
        LEFT JOIN t_daycare_elderly_people_info AS u ON u.id = b.user_id
        <where>
            li.live_state != '0'
            <if test="bedId != null and bedId != '' ">
                and li.bed_id = #{bedId}
            </if>
            <if test="name != null and name != '' ">
                and u.name like concat('%',#{name},'%')
            </if>
        </where>
    </select>

    <select id="getBedNameByRoomId" parameterType="Long" resultType="cn.hutool.json.JSONObject">
        select id , bed_name as label,'4' as type from t_daycare_bed_base_info where del_flag = '0' and room_id =
        #{roomId}
    </select>
</mapper>
