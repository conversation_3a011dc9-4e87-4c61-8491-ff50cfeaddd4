package com.ruoyi.daycare.mealManagement.controller;

import cn.hutool.json.JSONArray;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.daycare.mealManagement.domain.DayCareRecipeDetailsInfo;
import com.ruoyi.daycare.mealManagement.service.IDayCareRecipeDetailsInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 日间照料-食谱详情Controller
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@RestController
@RequestMapping("/dayCareRecipeDetailsInfo")
@Api(value = "日间照料-食谱详情", tags = "日间照料-食谱详情")
public class DayCareRecipeDetailsInfoController extends BaseController {
    @Autowired
    private IDayCareRecipeDetailsInfoService recipeDetailsInfoService;

    /**
     * 查询食谱详情列表
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:list")
    @GetMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "typeId", value = "食谱类型id", required = true, dataTypeClass = String.class),

    })
    @ApiOperation(value = "食谱详情列表")
    public TableDataInfo<DayCareRecipeDetailsInfo> list(@ApiIgnore DayCareRecipeDetailsInfo recipeDetailsInfo) {
        startPage();
        List<DayCareRecipeDetailsInfo> list = recipeDetailsInfoService.selectDayCareRecipeDetailsInfoList(recipeDetailsInfo);
        return getDataTable(list);
    }

    /**
     * 导出食谱详情列表
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:export")
    @Log(platform = "5", title = "食谱详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiIgnore
    public void export(HttpServletResponse response, DayCareRecipeDetailsInfo recipeDetailsInfo) {
        List<DayCareRecipeDetailsInfo> list = recipeDetailsInfoService.selectDayCareRecipeDetailsInfoList(recipeDetailsInfo);
        ExcelUtil<DayCareRecipeDetailsInfo> util = new ExcelUtil<DayCareRecipeDetailsInfo>(DayCareRecipeDetailsInfo.class);
        util.exportExcel(response, list, "食谱详情数据");
    }

    /**
     * 获取食谱详情详细信息
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取食谱详情详细信息")
    public TAjaxResult<DayCareRecipeDetailsInfo> getInfo(@PathVariable("id") Long id) {
        return new TAjaxResult<DayCareRecipeDetailsInfo>(recipeDetailsInfoService.selectDayCareRecipeDetailsInfoById(id));
    }


    /**
     * 保存食谱
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:add")
    @Log(platform = "5", title = "食谱详情", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @ApiOperation(value = "保存食谱")
    public AjaxResult save(@RequestBody DayCareRecipeDetailsInfo recipeDetailsInfo) {
        JSONArray array = recipeDetailsInfo.getArray();
        recipeDetailsInfo.setData(array.toString());
        if (null == recipeDetailsInfo.getId() || recipeDetailsInfo.getId() == 0) {
            return toAjax(recipeDetailsInfoService.insertDayCareRecipeDetailsInfo(recipeDetailsInfo));
        } else {
            return toAjax(recipeDetailsInfoService.updateDayCareRecipeDetailsInfo(recipeDetailsInfo));
        }
    }


    /**
     * 新增食谱详情
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:add")
    @Log(platform = "5", title = "食谱详情", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody DayCareRecipeDetailsInfo recipeDetailsInfo) {
        return toAjax(recipeDetailsInfoService.insertDayCareRecipeDetailsInfo(recipeDetailsInfo));
    }

    /**
     * 修改食谱详情
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:edit")
    @Log(platform = "5", title = "食谱详情", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody DayCareRecipeDetailsInfo recipeDetailsInfo) {
        return toAjax(recipeDetailsInfoService.updateDayCareRecipeDetailsInfo(recipeDetailsInfo));
    }

    /**
     * 删除食谱详情
     */
    //@RequiresPermissions("mealManagement:recipeDetailsInfo:remove")
    @Log(platform = "5", title = "食谱详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除食谱详情")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(recipeDetailsInfoService.deleteDayCareRecipeDetailsInfoByIds(ids));
    }
}
