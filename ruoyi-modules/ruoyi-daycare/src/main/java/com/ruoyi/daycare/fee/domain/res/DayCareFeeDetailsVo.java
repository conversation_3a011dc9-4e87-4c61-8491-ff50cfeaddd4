package com.ruoyi.daycare.fee.domain.res;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.daycare.fee.domain.DayCareFeeBillInfo;
import com.ruoyi.daycare.utils.DictUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 费用明细对象 t_fee_details
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@Data
public class DayCareFeeDetailsVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "id", name = "id", example = "1231241231")
    private String id;

    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号", name = "invoiceNum", example = "JF023456")
    @Excel(name = "单据编号", sort = 1)
    private String invoiceNum;

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型(0缴费，1退费)", name = "invoiceType", example = "0,1")
    @Excel(name = "费用类型", readConverterExp = "0=缴费,1=退费", sort = 4)
    private String invoiceType;

    @ApiModelProperty(value = "单据类型(0缴费，1退费)", name = "invoiceTypeStr", example = "缴费/退费")
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private String invoiceTypeStr;


    /**
     * 单据明细
     */
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    @ApiModelProperty(value = "单据明细列表", name = "invoiceDetails")
    private List<DayCareFeeBillInfo> invoiceDetailsList;

    @JsonIgnore
    @ApiModelProperty(value = "单据明细", name = "invoiceDetails")
    private String invoiceDetails;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", name = "userId", example = "1")
    private String userId;

    @ApiModelProperty(value = "用户名", name = "userName", example = "fengchenyang")
    @Excel(name = "用户名", sort = 2)
    private String userName;

    @ApiModelProperty(value = "手机号", name = "phone", example = "18568859335")
    private String phone;

    @ApiModelProperty(value = "年龄", name = "age", example = "25")
    private String age;

    /**
     * 入住id
     */
    @ApiModelProperty(value = "入住id", name = "liveId", example = "25")
    private String liveId;

    @ApiModelProperty(value = "入住类型", name = "liveType", example = "0,1")
    @Excel(name = "居住类型", readConverterExp = "0=长住,1=短住", sort = 3)
    private String liveType;

    @ApiModelProperty(value = "入住类型", name = "liveTypeStr", example = "0,1")
    private String liveTypeStr;

    @ApiModelProperty(value = "变更前余额", name = "beforeBalance", example = "345.99")
    private BigDecimal beforeBalance;

    /**
     * 变动金额
     */
    @ApiModelProperty(value = "变动金额", name = "changeAmount", example = "345.99")
    @Excel(name = "变动金额", sort = 5)
    private BigDecimal changeAmount;

    /**
     * 变更后余额
     */
    @ApiModelProperty(value = "变更后余额", name = "afterBalance", example = "345.99")
    private BigDecimal afterBalance;

    /**
     * 扣费类型（足额扣除,部分扣除）
     */
    @ApiModelProperty(value = "扣费类型（足额扣除,部分扣除）", name = "deductionType", example = "0,1")
    private String deductionType;

    @ApiModelProperty(value = "操作人id", name = "operatorId", example = "1")
    private String operatorId;

    @ApiModelProperty(value = "操作人名称", name = "operatorName", example = "1")
    @Excel(name = "操作人名称", sort = 6)
    private String operatorName;

    @ApiModelProperty(value = "操作类型", name = "operatorType", example = "1")
    private String operatorType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    public String getLiveTypeStr() {
        return DictUtils.selectDictLabel("live_type", this.liveType);
    }

    public void setLiveTypeStr(String liveTypeStr) {
        this.liveTypeStr = liveTypeStr;
    }


    public String getInvoiceTypeStr() {
        return DictUtils.selectDictLabel("invoice_type", this.invoiceType);
    }

    public void setInvoiceTypeStr(String invoiceTypeStr) {
        this.invoiceTypeStr = invoiceTypeStr;
    }

    public List<DayCareFeeBillInfo> getInvoiceDetailsList() {
        try {
            JSONArray array = JSONUtil.parseArray(this.invoiceDetails);
            List<DayCareFeeBillInfo> list = array.toList(DayCareFeeBillInfo.class);
            return list;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public void setInvoiceDetailsList(List<DayCareFeeBillInfo> invoiceDetailsList) {
        this.invoiceDetailsList = invoiceDetailsList;
    }
}
