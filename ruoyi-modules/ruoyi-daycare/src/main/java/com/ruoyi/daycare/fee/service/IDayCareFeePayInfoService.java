package com.ruoyi.daycare.fee.service;

import com.ruoyi.daycare.fee.domain.DayCareFeePayInfo;
import com.ruoyi.daycare.fee.domain.req.DayCareFeePayQueryVo;
import com.ruoyi.daycare.fee.domain.req.DayCareFeePaySaveVo;
import com.ruoyi.daycare.fee.domain.res.DayCareFeePayVo;

import java.util.List;

/**
 * 费用支付记录Service接口
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
public interface IDayCareFeePayInfoService {
    /**
     * 查询费用支付记录
     *
     * @param id 费用支付记录主键
     * @return 费用支付记录
     */
    public DayCareFeePayInfo selectDayCareFeePayInfoById(String id);

    public DayCareFeePayVo selectNewDayCareFeePayInfoById(String id);

    /**
     * 查询费用支付记录列表
     *
     * @param feePayInfo 费用支付记录
     * @return 费用支付记录集合
     */
    public List<DayCareFeePayInfo> selectDayCareFeePayInfoList(DayCareFeePayInfo feePayInfo);

    /**
     * 新增费用支付记录
     *
     * @param feePayInfo 费用支付记录
     * @return 结果
     */
    public int insertDayCareFeePayInfo(DayCareFeePayInfo feePayInfo);

    /**
     * 修改费用支付记录
     *
     * @param feePayInfo 费用支付记录
     * @return 结果
     */
    public int updateDayCareFeePayInfo(DayCareFeePayInfo feePayInfo);

    /**
     * 批量删除费用支付记录
     *
     * @param ids 需要删除的费用支付记录主键集合
     * @return 结果
     */
    public int deleteDayCareFeePayInfoByIds(String[] ids);

    /**
     * 删除费用支付记录信息
     *
     * @param id 费用支付记录主键
     * @return 结果
     */
    public int deleteDayCareFeePayInfoById(String id);

    int insertDayCareFeePaySaveVo(DayCareFeePaySaveVo feePaySaveVo);

    List<DayCareFeePayVo> selectNewDayCareFeePayInfoList(DayCareFeePayQueryVo feePayQueryVo);
}
