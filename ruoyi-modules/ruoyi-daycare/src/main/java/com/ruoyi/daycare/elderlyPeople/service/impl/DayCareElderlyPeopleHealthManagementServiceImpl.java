package com.ruoyi.daycare.elderlyPeople.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.daycare.elderlyPeople.domain.DayCareElderlyPeopleHealthManagement;
import com.ruoyi.daycare.elderlyPeople.mapper.DayCareElderlyPeopleHealthManagementMapper;
import com.ruoyi.daycare.elderlyPeople.service.IDayCareElderlyPeopleHealthManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 老人健康管理信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@Service
public class DayCareElderlyPeopleHealthManagementServiceImpl implements IDayCareElderlyPeopleHealthManagementService {
    @Autowired
    private DayCareElderlyPeopleHealthManagementMapper dayCareElderlyPeopleHealthManagementMapper;

    /**
     * 查询老人健康管理信息
     *
     * @param id 老人健康管理信息主键
     * @return 老人健康管理信息
     */
    @Override
    public DayCareElderlyPeopleHealthManagement selectDayCareElderlyPeopleHealthManagementById(String id) {
        return dayCareElderlyPeopleHealthManagementMapper.selectDayCareElderlyPeopleHealthManagementById(id);
    }

    /**
     * 查询老人健康管理信息列表
     *
     * @param elderlyPeopleHealthManagement 老人健康管理信息
     * @return 老人健康管理信息
     */
    @Override
    public List<DayCareElderlyPeopleHealthManagement> selectDayCareElderlyPeopleHealthManagementList(DayCareElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        return dayCareElderlyPeopleHealthManagementMapper.selectDayCareElderlyPeopleHealthManagementList(elderlyPeopleHealthManagement);
    }

    /**
     * 新增老人健康管理信息
     *
     * @param elderlyPeopleHealthManagement 老人健康管理信息
     * @return 结果
     */
    @Override
    public int insertDayCareElderlyPeopleHealthManagement(DayCareElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        elderlyPeopleHealthManagement.setCreateTime(DateUtils.getNowDate());
        String id = IdUtils.fastSimpleUUID();
        elderlyPeopleHealthManagement.setId(id);
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleHealthManagement.setCreateBy(String.valueOf(userId));
        return dayCareElderlyPeopleHealthManagementMapper.insertDayCareElderlyPeopleHealthManagement(elderlyPeopleHealthManagement);
    }

    @Override
    public int save(DayCareElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        if (StringUtils.isBlank(elderlyPeopleHealthManagement.getId())) {
            return insertDayCareElderlyPeopleHealthManagement(elderlyPeopleHealthManagement);
        } else {
            return updateDayCareElderlyPeopleHealthManagement(elderlyPeopleHealthManagement);
        }
    }

    /**
     * 修改老人健康管理信息
     *
     * @param elderlyPeopleHealthManagement 老人健康管理信息
     * @return 结果
     */
    @Override
    public int updateDayCareElderlyPeopleHealthManagement(DayCareElderlyPeopleHealthManagement elderlyPeopleHealthManagement) {
        elderlyPeopleHealthManagement.setUpdateTime(DateUtils.getNowDate());
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleHealthManagement.setUpdateBy(String.valueOf(userId));
        return dayCareElderlyPeopleHealthManagementMapper.updateDayCareElderlyPeopleHealthManagement(elderlyPeopleHealthManagement);
    }

    /**
     * 批量删除老人健康管理信息
     *
     * @param ids 需要删除的老人健康管理信息主键
     * @return 结果
     */
    @Override
    public int deleteDayCareElderlyPeopleHealthManagementByIds(String[] ids) {
        return dayCareElderlyPeopleHealthManagementMapper.deleteDayCareElderlyPeopleHealthManagementByIds(ids);
    }

    /**
     * 删除老人健康管理信息信息
     *
     * @param id 老人健康管理信息主键
     * @return 结果
     */
    @Override
    public int deleteDayCareElderlyPeopleHealthManagementById(String id) {
        return dayCareElderlyPeopleHealthManagementMapper.deleteDayCareElderlyPeopleHealthManagementById(id);
    }
}
