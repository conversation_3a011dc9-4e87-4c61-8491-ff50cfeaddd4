package com.ruoyi.daycare.staffmanagement.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.daycare.staffmanagement.domain.StaffInfo;
import com.ruoyi.daycare.staffmanagement.request.StaffInfoRequest;
import com.ruoyi.daycare.staffmanagement.service.StaffInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 员工信息表
 * @date 2022-09-01
 */
@RestController
@RequestMapping(value = "/staffInfo")
@Api(tags = "员工信息controller")
public class StaffInfoController extends BaseController {

    @Resource
    private StaffInfoService staffInfoService;


    /**
     * 员工信息保存
     */
    @Log(platform = "5", title = "员工信息保存", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "员工信息保存")
    public AjaxResult save(@RequestBody StaffInfo staffInfo) {
        return toAjax(staffInfoService.save(staffInfo));
    }


    @Log(platform = "5", title = "员工信息删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "员工信息删除")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(staffInfoService.deleteByIds(ids));
    }


    /**
     * 查询员工信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询员工信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "sex", value = "性别", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "name", value = "姓名", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo list(@ApiIgnore StaffInfoRequest staffInfo) {
        TableDataInfo list = staffInfoService.selectList(staffInfo);
        return list;
    }

    /**
     * 获取员工信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取员工信息详细信息")
    public TAjaxResult<StaffInfo> getInfo(@PathVariable("id") Long id) {
        return new TAjaxResult<StaffInfo>(staffInfoService.selectStaffInfo(id));
    }

}
