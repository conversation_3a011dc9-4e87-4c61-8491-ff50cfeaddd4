package com.ruoyi.daycare.balance.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.daycare.balance.domain.DayCareBalanceRecords;
import com.ruoyi.daycare.balance.service.IDayCareBalanceRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 余额记录Controller
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
@ApiIgnore
@RestController
@RequestMapping("/dayCareBalanceRecords")
public class DayCareBalanceRecordsController extends BaseController {
    @Autowired
    private IDayCareBalanceRecordsService balanceRecordsService;

    /**
     * 查询余额记录列表
     */
    //@RequiresPermissions("custom:balanceRecords:list")
    @GetMapping("/list")
    public TableDataInfo list(DayCareBalanceRecords balanceRecords) {
        startPage();
        List<DayCareBalanceRecords> list = balanceRecordsService.selectDayCareBalanceRecordsList(balanceRecords);
        return getDataTable(list);
    }

    /**
     * 导出余额记录列表
     */
    //@RequiresPermissions("custom:balanceRecords:export")
    @Log(platform = "5", title = "余额记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DayCareBalanceRecords balanceRecords) {
        List<DayCareBalanceRecords> list = balanceRecordsService.selectDayCareBalanceRecordsList(balanceRecords);
        ExcelUtil<DayCareBalanceRecords> util = new ExcelUtil<DayCareBalanceRecords>(DayCareBalanceRecords.class);
        util.exportExcel(response, list, "余额记录数据");
    }

    /**
     * 获取余额记录详细信息
     */
    //@RequiresPermissions("custom:balanceRecords:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(balanceRecordsService.selectDayCareBalanceRecordsById(id));
    }

    /**
     * 新增余额记录
     */
    //@RequiresPermissions("custom:balanceRecords:add")
    @Log(platform = "5", title = "余额记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DayCareBalanceRecords balanceRecords) {
        return toAjax(balanceRecordsService.insertDayCareBalanceRecords(balanceRecords));
    }

    /**
     * 修改余额记录
     */
    //@RequiresPermissions("custom:balanceRecords:edit")
    @Log(platform = "5", title = "余额记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DayCareBalanceRecords balanceRecords) {
        return toAjax(balanceRecordsService.updateDayCareBalanceRecords(balanceRecords));
    }

    /**
     * 删除余额记录
     */
    //@RequiresPermissions("custom:balanceRecords:remove")
    @Log(platform = "5", title = "余额记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(balanceRecordsService.deleteDayCareBalanceRecordsByIds(ids));
    }
}
