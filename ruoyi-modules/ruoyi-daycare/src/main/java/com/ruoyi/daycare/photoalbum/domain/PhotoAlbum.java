package com.ruoyi.daycare.photoalbum.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 相册信息表
 * @date 2022-09-02
 */
@Data
@ApiModel("相册信息表")
@TableName("t_daycare_photo_album")
public class PhotoAlbum implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 相册名称
     */
    @ApiModelProperty("相册名称")
    private String name;


    /**
     * 封面
     */
    @ApiModelProperty("封面")
    private String cover;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    private Date createTime;

    /**
     * 创建人员
     */
    @ApiModelProperty("创建人员")
    private String createBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /**
     * 修改人员
     */
    @ApiModelProperty("修改人员")
    private String updateBy;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    @ApiModelProperty("逻辑删除标记（0：显示；1：隐藏")
    private String delFlag;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    public PhotoAlbum() {
    }
}
