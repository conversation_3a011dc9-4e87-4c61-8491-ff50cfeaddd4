package com.ruoyi.daycare.elderlyPeople.service;

import com.ruoyi.daycare.elderlyPeople.domain.DayCareElderlyPeopleFamilyInfo;

import java.util.List;

/**
 * 老人家属信息Service接口
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
public interface IDayCareElderlyPeopleFamilyInfoService {
    /**
     * 查询老人家属信息
     *
     * @param id 老人家属信息主键
     * @return 老人家属信息
     */
    public DayCareElderlyPeopleFamilyInfo selectDayCareElderlyPeopleFamilyInfoById(String id);

    /**
     * 查询老人家属信息列表
     *
     * @param elderlyPeopleFamilyInfo 老人家属信息
     * @return 老人家属信息集合
     */
    public List<DayCareElderlyPeopleFamilyInfo> selectDayCareElderlyPeopleFamilyInfoList(DayCareElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo);

    /**
     * 新增老人家属信息
     *
     * @param elderlyPeopleFamilyInfo 老人家属信息
     * @return 结果
     */
    public int insertDayCareElderlyPeopleFamilyInfo(DayCareElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo);

    /**
     * 修改老人家属信息
     *
     * @param elderlyPeopleFamilyInfo 老人家属信息
     * @return 结果
     */
    public int updateDayCareElderlyPeopleFamilyInfo(DayCareElderlyPeopleFamilyInfo elderlyPeopleFamilyInfo);

    /**
     * 批量删除老人家属信息
     *
     * @param ids 需要删除的老人家属信息主键集合
     * @return 结果
     */
    public int deleteDayCareElderlyPeopleFamilyInfoByIds(String[] ids);

    /**
     * 删除老人家属信息信息
     *
     * @param id 老人家属信息主键
     * @return 结果
     */
    public int deleteDayCareElderlyPeopleFamilyInfoById(String id);
}
