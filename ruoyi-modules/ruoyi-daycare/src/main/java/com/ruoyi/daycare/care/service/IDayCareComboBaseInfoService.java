package com.ruoyi.daycare.care.service;

import com.ruoyi.daycare.care.domain.DayCareComboBaseInfo;

import java.util.List;

/**
 * 护理套餐Service接口
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
public interface IDayCareComboBaseInfoService {
    /**
     * 查询护理套餐
     *
     * @param id 护理套餐主键
     * @return 护理套餐
     */
    public DayCareComboBaseInfo selectDayCareComboBaseInfoById(Long id);

    /**
     * 查询护理套餐列表
     *
     * @param comboBaseInfo 护理套餐
     * @return 护理套餐集合
     */
    public List<DayCareComboBaseInfo> selectDayCareComboBaseInfoList(DayCareComboBaseInfo comboBaseInfo);

    /**
     * 新增护理套餐
     *
     * @param comboBaseInfo 护理套餐
     * @return 结果
     */
    public int insertDayCareComboBaseInfo(DayCareComboBaseInfo comboBaseInfo);

    /**
     * 修改护理套餐
     *
     * @param comboBaseInfo 护理套餐
     * @return 结果
     */
    public int updateDayCareComboBaseInfo(DayCareComboBaseInfo comboBaseInfo);

    /**
     * 批量删除护理套餐
     *
     * @param ids 需要删除的护理套餐主键集合
     * @return 结果
     */
    public int deleteDayCareComboBaseInfoByIds(Long[] ids);

    /**
     * 删除护理套餐信息
     *
     * @param id 护理套餐主键
     * @return 结果
     */
    public int deleteDayCareComboBaseInfoById(Long id);
}
