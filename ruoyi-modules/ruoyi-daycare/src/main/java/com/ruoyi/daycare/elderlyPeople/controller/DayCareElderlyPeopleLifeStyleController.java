package com.ruoyi.daycare.elderlyPeople.controller;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.daycare.elderlyPeople.domain.DayCareElderlyPeopleLifeStyle;
import com.ruoyi.daycare.elderlyPeople.service.IDayCareElderlyPeopleLifeStyleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 日间照料-老人生活方式信息Controller
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@RestController
@RequestMapping("/dayCareLifeStyle")
@Api(value = "日间照料-老人生活方式信息Controller", tags = "日间照料-老人生活方式信息")
public class DayCareElderlyPeopleLifeStyleController extends BaseController {
    @Autowired
    private IDayCareElderlyPeopleLifeStyleService dayCareElderlyPeopleLifeStyleService;

    /**
     * 查询日间照料-老人生活方式信息列表
     */
    // //@RequiresPermissions("elderlyPeople:lifeStyle:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询日间照料-老人生活方式信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userId", value = "日间照料-老人基础信息id", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo list(@ApiIgnore DayCareElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        startPage();
        List<DayCareElderlyPeopleLifeStyle> list = dayCareElderlyPeopleLifeStyleService.selectDayCareElderlyPeopleLifeStyleList(elderlyPeopleLifeStyle);
        return getDataTable(list);
    }

    /**
     * 查询日间照料-老人生活方式信息列表
     */
    // //@RequiresPermissions("elderlyPeople:lifeStyle:list")
    @GetMapping("/getLifeStyleByUserId")
    @ApiOperation(value = "根据日间照料-老人id查询日间照料-老人生活方式信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userId", value = "日间照料-老人基础信息id", required = true, dataTypeClass = String.class),
    })
    public AjaxResult getLifeStyleByUserId(@ApiIgnore DayCareElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        List<DayCareElderlyPeopleLifeStyle> list = dayCareElderlyPeopleLifeStyleService.selectDayCareElderlyPeopleLifeStyleList(elderlyPeopleLifeStyle);
        if (list.size() > 0) {
            return AjaxResult.success().put("data", list.get(0));
        } else {
            return AjaxResult.success().put("data", list);
        }
    }

    /**
     * 导出日间照料-老人生活方式信息列表
     */
    // //@RequiresPermissions("elderlyPeople:lifeStyle:export")
    @Log(platform = "5", title = "日间照料-老人生活方式信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出日间照料-老人生活方式信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, DayCareElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        List<DayCareElderlyPeopleLifeStyle> list = dayCareElderlyPeopleLifeStyleService.selectDayCareElderlyPeopleLifeStyleList(elderlyPeopleLifeStyle);
        ExcelUtil<DayCareElderlyPeopleLifeStyle> util = new ExcelUtil<DayCareElderlyPeopleLifeStyle>(DayCareElderlyPeopleLifeStyle.class);
        util.exportExcel(response, list, "日间照料-老人生活方式信息数据");
    }

    /**
     * 获取日间照料-老人生活方式信息详细信息
     */
    // //@RequiresPermissions("elderlyPeople:lifeStyle:query")
    @ApiOperation(value = "获取日间照料-老人生活方式信息详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(dayCareElderlyPeopleLifeStyleService.selectDayCareElderlyPeopleLifeStyleById(id));
    }

    /**
     * 保存日间照料-老人生活方式信息
     */
    @ApiOperation(value = "保存日间照料-老人生活方式信息")
    @PostMapping("/save")
    public AjaxResult save(@RequestBody DayCareElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        if (StringUtils.isBlank(elderlyPeopleLifeStyle.getId())) {
            return toAjax(dayCareElderlyPeopleLifeStyleService.insertDayCareElderlyPeopleLifeStyle(elderlyPeopleLifeStyle));
        } else {
            return toAjax(dayCareElderlyPeopleLifeStyleService.updateDayCareElderlyPeopleLifeStyle(elderlyPeopleLifeStyle));
        }
    }


    /**
     * 新增日间照料-老人生活方式信息
     */
    // //@RequiresPermissions("elderlyPeople:lifeStyle:add")
    @Log(platform = "5", title = "日间照料-老人生活方式信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增日间照料-老人生活方式信息")
    @PostMapping
    public AjaxResult add(@RequestBody DayCareElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        return toAjax(dayCareElderlyPeopleLifeStyleService.insertDayCareElderlyPeopleLifeStyle(elderlyPeopleLifeStyle));
    }

    /**
     * 修改日间照料-老人生活方式信息
     */
    // //@RequiresPermissions("elderlyPeople:lifeStyle:edit")
    @Log(platform = "5", title = "日间照料-老人生活方式信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改日间照料-老人生活方式信息")
    @PutMapping
    public AjaxResult edit(@RequestBody DayCareElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        return toAjax(dayCareElderlyPeopleLifeStyleService.updateDayCareElderlyPeopleLifeStyle(elderlyPeopleLifeStyle));
    }

    /**
     * 删除日间照料-老人生活方式信息
     */
    // //@RequiresPermissions("elderlyPeople:lifeStyle:remove")
    @Log(platform = "5", title = "日间照料-老人生活方式信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除日间照料-老人生活方式信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dayCareElderlyPeopleLifeStyleService.deleteDayCareElderlyPeopleLifeStyleByIds(ids));
    }
}
