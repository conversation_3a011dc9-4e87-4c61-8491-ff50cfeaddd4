package com.ruoyi.daycare.liveManage.mapper;

import com.ruoyi.daycare.liveManage.domain.DayCareLiveComboRecords;

import java.util.List;

/**
 * 居住套餐记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
public interface DayCareLiveComboRecordsMapper {
    /**
     * 查询居住套餐记录
     *
     * @param id 居住套餐记录主键
     * @return 居住套餐记录
     */
    public DayCareLiveComboRecords selectDayCareLiveComboRecordsById(String id);

    /**
     * 查询居住套餐记录列表
     *
     * @param liveComboRecords 居住套餐记录
     * @return 居住套餐记录集合
     */
    public List<DayCareLiveComboRecords> selectDayCareLiveComboRecordsList(DayCareLiveComboRecords liveComboRecords);

    /**
     * 新增居住套餐记录
     *
     * @param liveComboRecords 居住套餐记录
     * @return 结果
     */
    public int insertDayCareLiveComboRecords(DayCareLiveComboRecords liveComboRecords);

    /**
     * 修改居住套餐记录
     *
     * @param liveComboRecords 居住套餐记录
     * @return 结果
     */
    public int updateDayCareLiveComboRecords(DayCareLiveComboRecords liveComboRecords);

    /**
     * 删除居住套餐记录
     *
     * @param id 居住套餐记录主键
     * @return 结果
     */
    public int deleteDayCareLiveComboRecordsById(String id);

    /**
     * 批量删除居住套餐记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDayCareLiveComboRecordsByIds(String[] ids);
}
