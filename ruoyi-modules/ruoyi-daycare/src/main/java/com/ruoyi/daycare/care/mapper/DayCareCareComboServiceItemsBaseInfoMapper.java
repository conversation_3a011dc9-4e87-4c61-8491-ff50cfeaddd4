package com.ruoyi.daycare.care.mapper;

import com.ruoyi.daycare.care.domain.DayCareCareComboServiceItemsBaseInfo;

import java.util.List;

/**
 * 护理套餐中的服务项Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
public interface DayCareCareComboServiceItemsBaseInfoMapper {
    /**
     * 查询护理套餐中的服务项
     *
     * @param id 护理套餐中的服务项主键
     * @return 护理套餐中的服务项
     */
    public DayCareCareComboServiceItemsBaseInfo selectDayCareCareComboServiceItemsBaseInfoById(Long id);

    /**
     * 查询护理套餐中的服务项列表
     *
     * @param careComboServiceItemsBaseInfo 护理套餐中的服务项
     * @return 护理套餐中的服务项集合
     */
    public List<DayCareCareComboServiceItemsBaseInfo> selectDayCareCareComboServiceItemsBaseInfoList(DayCareCareComboServiceItemsBaseInfo careComboServiceItemsBaseInfo);

    /**
     * 新增护理套餐中的服务项
     *
     * @param careComboServiceItemsBaseInfo 护理套餐中的服务项
     * @return 结果
     */
    public int insertDayCareCareComboServiceItemsBaseInfo(DayCareCareComboServiceItemsBaseInfo careComboServiceItemsBaseInfo);

    /**
     * 修改护理套餐中的服务项
     *
     * @param careComboServiceItemsBaseInfo 护理套餐中的服务项
     * @return 结果
     */
    public int updateDayCareCareComboServiceItemsBaseInfo(DayCareCareComboServiceItemsBaseInfo careComboServiceItemsBaseInfo);

    /**
     * 删除护理套餐中的服务项
     *
     * @param id 护理套餐中的服务项主键
     * @return 结果
     */
    public int deleteDayCareCareComboServiceItemsBaseInfoById(Long id);

    /**
     * 批量删除护理套餐中的服务项
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDayCareCareComboServiceItemsBaseInfoByIds(Long[] ids);
}
