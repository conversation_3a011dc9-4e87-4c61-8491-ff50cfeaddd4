package com.ruoyi.daycare.balance.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.daycare.balance.domain.DayCareBalanceInfo;
import com.ruoyi.daycare.balance.service.IDayCareBalanceInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 余额信息Controller
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
@ApiIgnore
@RestController
@RequestMapping("/dayCareBalance")
public class DayCareBalanceInfoController extends BaseController {
    @Autowired
    private IDayCareBalanceInfoService balanceInfoService;

    /**
     * 查询余额信息列表
     */
    //@RequiresPermissions("custom:balance:list")
    @GetMapping("/list")
    public TableDataInfo list(DayCareBalanceInfo balanceInfo) {
        startPage();
        List<DayCareBalanceInfo> list = balanceInfoService.selectDayCareBalanceInfoList(balanceInfo);
        return getDataTable(list);
    }

    /**
     * 导出余额信息列表
     */
    //@RequiresPermissions("custom:balance:export")
    @Log(platform = "5", title = "余额信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DayCareBalanceInfo balanceInfo) {
        List<DayCareBalanceInfo> list = balanceInfoService.selectDayCareBalanceInfoList(balanceInfo);
        ExcelUtil<DayCareBalanceInfo> util = new ExcelUtil<DayCareBalanceInfo>(DayCareBalanceInfo.class);
        util.exportExcel(response, list, "余额信息数据");
    }

    /**
     * 获取余额信息详细信息
     */
    //@RequiresPermissions("custom:balance:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(balanceInfoService.selectDayCareBalanceInfoById(id));
    }

    /**
     * 新增余额信息
     */
    //@RequiresPermissions("custom:balance:add")
    @Log(platform = "5", title = "余额信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DayCareBalanceInfo balanceInfo) {
        return toAjax(balanceInfoService.insertDayCareBalanceInfo(balanceInfo));
    }

    /**
     * 修改余额信息
     */
    //@RequiresPermissions("custom:balance:edit")
    @Log(platform = "5", title = "余额信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DayCareBalanceInfo balanceInfo) {
        return toAjax(balanceInfoService.updateDayCareBalanceInfo(balanceInfo));
    }

    /**
     * 删除余额信息
     */
    //@RequiresPermissions("custom:balance:remove")
    @Log(platform = "5", title = "余额信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(balanceInfoService.deleteDayCareBalanceInfoByIds(ids));
    }
}
