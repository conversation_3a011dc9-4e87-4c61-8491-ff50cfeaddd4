package com.ruoyi.daycare.fee.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.daycare.fee.domain.req.DayCareFeeDetailsQueryVo;
import com.ruoyi.daycare.fee.domain.res.DayCareFeeDetailsVo;
import com.ruoyi.daycare.fee.service.IDayCareFeeDetailsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 费用明细Controller
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@Api(tags = "日间照料-费用-费用明细")
@RestController
@RequestMapping("/day_care_fee_details")
public class DayCareFeeDetailsController extends BaseController {
    @Autowired
    private IDayCareFeeDetailsService feeDetailsService;


    @ApiOperation("获取费用明细列表")
    @GetMapping("/list")
    public TableDataInfo<DayCareFeeDetailsVo> list(DayCareFeeDetailsQueryVo feeDetailsQueryVo) {
        startPage();
        List<DayCareFeeDetailsVo> list = feeDetailsService.selectNewDayCareFeeDetailsList(feeDetailsQueryVo);
        return getDataTable(list);
    }

    /**
     * 获取费用明细详细信息
     */
    @ApiOperation("获取费用明细详细信息")
    @GetMapping(value = "/{id}")
    public TAjaxResult<DayCareFeeDetailsVo> getInfo(@PathVariable("id") String id) {
        TAjaxResult<DayCareFeeDetailsVo> result = new TAjaxResult<>();
        result.success(feeDetailsService.selectNewDayCareFeeDetailsById(id));
        return result;
    }


//    /**
//     * 查询费用明细列表
//     */
//    //@RequiresPermissions("custom:fee_details:list")
//    @GetMapping("/list")
//    public TableDataInfo list(FeeDetails feeDetails)
//    {
//        startPage();
//        List<FeeDetails> list = feeDetailsService.selectFeeDetailsList(feeDetails);
//        return getDataTable(list);
//    }
//

    /**
     * 导出费用明细列表
     */
    @Log(platform = "5", title = "费用明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DayCareFeeDetailsQueryVo feeDetailsQueryVo) {
        List<DayCareFeeDetailsVo> list = feeDetailsService.selectNewDayCareFeeDetailsList(feeDetailsQueryVo);
        ExcelUtil<DayCareFeeDetailsVo> util = new ExcelUtil<DayCareFeeDetailsVo>(DayCareFeeDetailsVo.class);
        util.exportExcel(response, list, "费用明细数据");
    }
//
//    /**
//     * 获取费用明细详细信息
//     */
//    //@RequiresPermissions("custom:fee_details:query")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") String id)
//    {
//        return AjaxResult.success(feeDetailsService.selectFeeDetailsById(id));
//    }
//
//    /**
//     * 新增费用明细
//     */
//    //@RequiresPermissions("custom:fee_details:add")
//    @Log(platform = "5",title = "费用明细", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody FeeDetails feeDetails)
//    {
//        return toAjax(feeDetailsService.insertFeeDetails(feeDetails));
//    }
//
//    /**
//     * 修改费用明细
//     */
//    //@RequiresPermissions("custom:fee_details:edit")
//    @Log(platform = "5",title = "费用明细", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody FeeDetails feeDetails)
//    {
//        return toAjax(feeDetailsService.updateFeeDetails(feeDetails));
//    }
//
//    /**
//     * 删除费用明细
//     */
//    //@RequiresPermissions("custom:fee_details:remove")
//    @Log(platform = "5",title = "费用明细", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable String[] ids)
//    {
//        return toAjax(feeDetailsService.deleteFeeDetailsByIds(ids));
//    }
}
