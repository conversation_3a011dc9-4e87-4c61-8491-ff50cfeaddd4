package com.ruoyi.daycare.care.service;

import com.ruoyi.daycare.care.domain.DayCareCareRecordInfo;

import java.util.List;

/**
 * 护理记录Service接口
 *
 * <AUTHOR>
 * @date 2022-04-09
 */
public interface IDayCareCareRecordInfoService {
    /**
     * 查询护理记录
     *
     * @param id 护理记录主键
     * @return 护理记录
     */
    public DayCareCareRecordInfo selectDayCareCareRecordInfoById(Long id);

    /**
     * 查询护理记录列表
     *
     * @param careRecordInfo 护理记录
     * @return 护理记录集合
     */
    public List<DayCareCareRecordInfo> selectDayCareCareRecordInfoList(DayCareCareRecordInfo careRecordInfo);

    /**
     * 新增护理记录
     *
     * @param careRecordInfo 护理记录
     * @return 结果
     */
    public int insertDayCareCareRecordInfo(DayCareCareRecordInfo careRecordInfo);

    /**
     * 修改护理记录
     *
     * @param careRecordInfo 护理记录
     * @return 结果
     */
    public int updateDayCareCareRecordInfo(DayCareCareRecordInfo careRecordInfo);

    /**
     * 批量删除护理记录
     *
     * @param ids 需要删除的护理记录主键集合
     * @return 结果
     */
    public int deleteDayCareCareRecordInfoByIds(Long[] ids);

    /**
     * 删除护理记录信息
     *
     * @param id 护理记录主键
     * @return 结果
     */
    public int deleteDayCareCareRecordInfoById(Long id);
}
