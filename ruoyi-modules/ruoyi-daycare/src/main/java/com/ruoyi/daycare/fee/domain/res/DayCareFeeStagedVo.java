package com.ruoyi.daycare.fee.domain.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@Data
public class DayCareFeeStagedVo {

    @ApiModelProperty(value = "id", name = "id", example = "1231241231")
    private String id;

    @ApiModelProperty(value = "费用名称", name = "feeName", example = "1231241231")
    private String feeName;

    /**
     * 计费开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "计费开始日期", name = "billingBeginDate", example = "2021-12-10")
    private Date billingBeginDate;

    /**
     * 计费结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "计费结束日期", name = "billingEndDate", example = "2021-12-10")
    private Date billingEndDate;

    /**
     * 计费规则
     */
    @Excel(name = "计费规则")
    @ApiModelProperty(value = "计费结束日期", name = "billingEndDate", example = "0，1")
    private String billingRule;

    /**
     * 费用合计
     */
    @ApiModelProperty(value = "费用合计", name = "feeTotal", example = "200")
    private Long feeTotal;


}
