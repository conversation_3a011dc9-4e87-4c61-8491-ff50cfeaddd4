package com.ruoyi.daycare.storiedBuilding.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.daycare.utils.DictUtils;
import io.seata.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Data
public class DayCareBedRecordsInfoVo {

    @ApiModelProperty(value = "用户名称", name = "name")
    private String name;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "开始时间", name = "beginDate")
    private Date beginDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "结束时间", name = "endDate")
    private Date endDate;

    @ApiModelProperty(value = "搬离原因", name = "stateStr")
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private String stateStr;

    @ApiModelProperty(value = "搬离原因值", name = "liveState")
    private String liveState;

    public String getStateStr() {
        if (StringUtils.isBlank(this.liveState)) {
            return this.liveState;
        }
        return DictUtils.selectDictLabel("live_bed_status", this.liveState);
    }

    public void setStateStr(String stateStr) {
        this.stateStr = stateStr;
    }
}
