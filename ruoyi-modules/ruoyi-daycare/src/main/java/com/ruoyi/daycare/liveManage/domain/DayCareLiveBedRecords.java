package com.ruoyi.daycare.liveManage.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 居住记录对象 t_daycare_live_bed_records
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
public class DayCareLiveBedRecords extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 关联主表id
     */
    @Excel(name = "关联主表id")
    private String liveId;

    /**
     * 房间id
     */
    @Excel(name = "房间id")
    @ApiModelProperty(value = "房间id", name = "roomId", required = true, example = "196")
    private String roomId;

    /**
     * 房间版本
     */
    @Excel(name = "房间版本")
    @ApiModelProperty(value = "房间版本", name = "roomVersion", required = true, example = "1")
    private String roomVersion;

    /**
     * 入住床位
     */
    @Excel(name = "入住床位")
    @ApiModelProperty(value = "入住床位id", name = "bedId", required = true, example = "1")
    private String bedId;

    /**
     * 入住日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入住日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "入住日期", name = "beginDate", required = true, example = "2022-04-01")
    private Date beginDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 计费日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计费日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "计费日期", name = "billingDate", required = true, example = "2022-04-01")
    private Date billingDate;

    /**
     * 居住状态（在住，变更，退住）
     */
    @Excel(name = "居住状态", readConverterExp = "在=住，变更，退住")
    private String liveState;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLiveId() {
        return liveId;
    }

    public void setLiveId(String liveId) {
        this.liveId = liveId;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getRoomVersion() {
        return roomVersion;
    }

    public void setRoomVersion(String roomVersion) {
        this.roomVersion = roomVersion;
    }

    public String getBedId() {
        return bedId;
    }

    public void setBedId(String bedId) {
        this.bedId = bedId;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getBillingDate() {
        return billingDate;
    }

    public void setBillingDate(Date billingDate) {
        this.billingDate = billingDate;
    }

    public String getLiveState() {
        return liveState;
    }

    public void setLiveState(String liveState) {
        this.liveState = liveState;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("liveId", getLiveId())
                .append("roomId", getRoomId())
                .append("roomVersion", getRoomVersion())
                .append("bedId", getBedId())
                .append("beginDate", getBeginDate())
                .append("endDate", getEndDate())
                .append("billingDate", getBillingDate())
                .append("liveState", getLiveState())
                .append("remark", getRemark())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .toString();
    }
}
