package com.ruoyi.daycare.storiedBuilding.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.daycare.storiedBuilding.domain.DayCareTypeVersionInfo;
import com.ruoyi.daycare.storiedBuilding.service.IDayCareTypeVersionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 日间照料-房间类型版本Controller
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@RestController
@RequestMapping("/dayCareTypeVersionInfo")
@ApiIgnore
public class DayCareTypeVersionInfoController extends BaseController {
    @Autowired
    private IDayCareTypeVersionInfoService dayCareTypeVersionInfoService;

    /**
     * 查询房间类型版本列表
     */
    // //@RequiresPermissions("storiedBuilding:TypeVersionInfo:list")
    @GetMapping("/list")
    public TableDataInfo list(DayCareTypeVersionInfo DayCareTypeVersionInfo) {
        startPage();
        List<DayCareTypeVersionInfo> list = dayCareTypeVersionInfoService.selectDayCareTypeVersionInfoList(DayCareTypeVersionInfo);
        return getDataTable(list);
    }

    /**
     * 导出房间类型版本列表
     */
    // //@RequiresPermissions("storiedBuilding:DayCareTypeVersionInfo:export")
    @Log(platform = "5", title = "房间类型版本", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DayCareTypeVersionInfo DayCareTypeVersionInfo) {
        List<DayCareTypeVersionInfo> list = dayCareTypeVersionInfoService.selectDayCareTypeVersionInfoList(DayCareTypeVersionInfo);
        ExcelUtil<DayCareTypeVersionInfo> util = new ExcelUtil<DayCareTypeVersionInfo>(DayCareTypeVersionInfo.class);
        util.exportExcel(response, list, "房间类型版本数据");
    }

    /**
     * 获取房间类型版本详细信息
     */
    // //@RequiresPermissions("storiedBuilding:DayCareTypeVersionInfo:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(dayCareTypeVersionInfoService.selectDayCareTypeVersionInfoById(id));
    }

    /**
     * 新增房间类型版本
     */
    // //@RequiresPermissions("storiedBuilding:DayCareTypeVersionInfo:add")
    @Log(platform = "5", title = "房间类型版本", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DayCareTypeVersionInfo DayCareTypeVersionInfo) {
        return toAjax(dayCareTypeVersionInfoService.insertDayCareTypeVersionInfo(DayCareTypeVersionInfo));
    }

    /**
     * 修改房间类型版本
     */
    // //@RequiresPermissions("storiedBuilding:DayCareTypeVersionInfo:edit")
    @Log(platform = "5", title = "房间类型版本", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DayCareTypeVersionInfo DayCareTypeVersionInfo) {
        return toAjax(dayCareTypeVersionInfoService.updateDayCareTypeVersionInfo(DayCareTypeVersionInfo));
    }

    /**
     * 删除房间类型版本
     */
    // //@RequiresPermissions("storiedBuilding:DayCareTypeVersionInfo:remove")
    @Log(platform = "5", title = "房间类型版本", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dayCareTypeVersionInfoService.deleteDayCareTypeVersionInfoByIds(ids));
    }
}
