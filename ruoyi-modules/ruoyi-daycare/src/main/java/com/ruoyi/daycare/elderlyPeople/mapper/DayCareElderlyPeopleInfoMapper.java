package com.ruoyi.daycare.elderlyPeople.mapper;

import cn.hutool.json.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
import com.ruoyi.daycare.birthday.vo.DayCareElderlyPeopleInfoBirthdayVo;
import com.ruoyi.daycare.elderlyPeople.domain.DayCareElderlyPeopleInfo;
import com.ruoyi.daycare.elderlyPeople.domain.vo.DayCareElderlyPeopleInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 老人基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
public interface DayCareElderlyPeopleInfoMapper {
    /**
     * 查询老人基础信息
     *
     * @param id 老人基础信息主键
     * @return 老人基础信息
     */
    public DayCareElderlyPeopleInfo selectDayCareElderlyPeopleInfoById(String id);

    /**
     * 查询老人基础信息列表
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 老人基础信息集合
     */
    public List<DayCareElderlyPeopleInfo> selectDayCareElderlyPeopleInfoList(DayCareElderlyPeopleInfo elderlyPeopleInfo);

    /**
     * 健康管理获取老人全量列表
     *
     * @param name
     * @return
     */
    public List<JSONObject> getUserList(@Param("name") String name, @Param("state") String state);

    /**
     * 新增老人基础信息
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 结果
     */
    public int insertDayCareElderlyPeopleInfo(DayCareElderlyPeopleInfo elderlyPeopleInfo);

    /**
     * 修改老人基础信息
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 结果
     */
    public int updateDayCareElderlyPeopleInfo(DayCareElderlyPeopleInfo elderlyPeopleInfo);

    /**
     * 删除老人基础信息
     *
     * @param id 老人基础信息主键
     * @return 结果
     */
    public int deleteDayCareElderlyPeopleInfoById(Long id);

    /**
     * 批量删除老人基础信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDayCareElderlyPeopleInfoByIds(Long[] ids);

    int logicalDeleteDayCareElderlyPeopleInfoByIds(String[] ids);

    /**
     * 老人信息列表
     *
     * @param elderlyPeopleInfoVo
     * @return
     */
    List<DayCareElderlyPeopleInfoVo> getPeopleInfoList(DayCareElderlyPeopleInfoVo elderlyPeopleInfoVo);

    /**
     * 系统首页老人数量
     *
     * @return
     */
    JsonObject getUserNumberList();

    List<String> getDayByMonth(String month);

    List<DayCareElderlyPeopleInfo> getListByDay(String day);

    /**
     * 获取当前月份生日的老人信息
     *
     * @param name
     * @return
     */
    List<DayCareElderlyPeopleInfoBirthdayVo> getUserListByMonth(String name);

    /**
     * 老人信息模块详细信息
     *
     * @param id
     * @return
     */
    DayCareElderlyPeopleInfoVo getPeopleInfoById(String id);
}
