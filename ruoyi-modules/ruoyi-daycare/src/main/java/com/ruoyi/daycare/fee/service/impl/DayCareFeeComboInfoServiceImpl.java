package com.ruoyi.daycare.fee.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.daycare.fee.domain.DayCareFeeComboDTO;
import com.ruoyi.daycare.fee.domain.DayCareFeeComboInfo;
import com.ruoyi.daycare.fee.domain.res.DayCareFeeComboVo;
import com.ruoyi.daycare.fee.mapper.DayCareFeeComboInfoMapper;
import com.ruoyi.daycare.fee.service.IDayCareFeeComboInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 套餐费用Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@Service
public class DayCareFeeComboInfoServiceImpl implements IDayCareFeeComboInfoService {
    @Autowired
    private DayCareFeeComboInfoMapper feeComboInfoMapper;

    /**
     * 查询套餐费用
     *
     * @param id 套餐费用主键
     * @return 套餐费用
     */
    @Override
    public DayCareFeeComboInfo selectDayCareFeeComboInfoById(String id) {
        return feeComboInfoMapper.selectDayCareFeeComboInfoById(id);
    }

    /**
     * 查询套餐费用列表
     *
     * @param feeComboInfo 套餐费用
     * @return 套餐费用
     */
    @Override
    public List<DayCareFeeComboInfo> selectDayCareFeeComboInfoList(DayCareFeeComboInfo feeComboInfo) {
        return feeComboInfoMapper.selectDayCareFeeComboInfoList(feeComboInfo);
    }

    /**
     * 新增套餐费用
     *
     * @param feeComboVo 套餐费用
     * @return 结果
     */
    @Override
    public int insertDayCareFeeComboInfo(DayCareFeeComboVo feeComboVo) {
        DayCareFeeComboInfo feeComboInfo = BeanUtil.copyProperties(feeComboVo, DayCareFeeComboInfo.class);
        feeComboInfo.setId(IdUtil.fastSimpleUUID());
        feeComboInfo.setCreateBy(SecurityUtils.getUserId().toString());
        feeComboInfo.setCreateTime(DateUtils.getNowDate());
        feeComboInfo.setState("0");
        feeComboInfo.setVersion(1L);
        feeComboInfo.setMonthDetails(JSONUtil.toJsonStr(feeComboVo.getMonthDetails()));
        feeComboInfo.setDayDetails(JSONUtil.toJsonStr(feeComboVo.getDayDetails()));
        return feeComboInfoMapper.insertDayCareFeeComboInfo(feeComboInfo);
    }

    /**
     * 修改套餐费用
     *
     * @param feeComboVo 套餐费用
     * @return 结果
     */
    @Override
    public int updateDayCareFeeComboInfo(DayCareFeeComboVo feeComboVo) {
        DayCareFeeComboInfo param = new DayCareFeeComboInfo();
        param.setState("0");
        param.setComboId(feeComboVo.getComboId());
        List<DayCareFeeComboInfo> feeComboInfos = feeComboInfoMapper.selectDayCareFeeComboInfoList(param);

        Long version = 1L;

        for (DayCareFeeComboInfo feeComboInfo : feeComboInfos) {
            if (feeComboInfo.getVersion() >= version) {
                version = feeComboInfo.getVersion() + 1L;
            }
            feeComboInfo.setState("1");
            feeComboInfo.setUpdateTime(new Date());
            feeComboInfo.setUpdateBy(SecurityUtils.getUserId().toString());
            feeComboInfoMapper.updateDayCareFeeComboInfo(feeComboInfo);
        }


        DayCareFeeComboInfo feeComboInfo = BeanUtil.copyProperties(feeComboVo, DayCareFeeComboInfo.class);
        feeComboInfo.setId(IdUtil.fastSimpleUUID());
        feeComboInfo.setCreateBy(SecurityUtils.getUserId().toString());
        feeComboInfo.setCreateTime(DateUtils.getNowDate());
        feeComboInfo.setState("0");
        feeComboInfo.setVersion(version);
        feeComboInfo.setMonthDetails(JSONUtil.toJsonStr(feeComboVo.getMonthDetails()));
        feeComboInfo.setDayDetails(JSONUtil.toJsonStr(feeComboVo.getDayDetails()));
        return feeComboInfoMapper.insertDayCareFeeComboInfo(feeComboInfo);
    }

    /**
     * 批量删除套餐费用
     *
     * @param ids 需要删除的套餐费用主键
     * @return 结果
     */
    @Override
    public int deleteDayCareFeeComboInfoByIds(String[] ids) {
        return feeComboInfoMapper.deleteDayCareFeeComboInfoByIds(ids);
    }

    /**
     * 删除套餐费用信息
     *
     * @param id 套餐费用主键
     * @return 结果
     */
    @Override
    public int deleteDayCareFeeComboInfoById(String id) {
        return feeComboInfoMapper.deleteDayCareFeeComboInfoById(id);
    }

    @Override
    public List<DayCareFeeComboVo> selectComboList(String careLevel, String comboName) {
        List<DayCareFeeComboVo> result = new ArrayList<>();
        List<DayCareFeeComboDTO> feeComboDTOS = feeComboInfoMapper.selectComboList(careLevel, comboName);
        for (DayCareFeeComboDTO feeComboDTO : feeComboDTOS) {
            DayCareFeeComboVo feeComboVo = BeanUtil.copyProperties(feeComboDTO, DayCareFeeComboVo.class);
            feeComboVo.setDayDetails(JSONUtil.parseArray(feeComboDTO.getDayDetails()));
            feeComboVo.setMonthDetails(JSONUtil.parseArray(feeComboDTO.getMonthDetails()));
            result.add(feeComboVo);
        }
        return result;
    }

    @Override
    public DayCareFeeComboVo selectComboById(String id) {
        DayCareFeeComboDTO feeComboDTO = feeComboInfoMapper.selectComboById(id);
        DayCareFeeComboVo feeComboVo = BeanUtil.copyProperties(feeComboDTO, DayCareFeeComboVo.class);
        feeComboVo.setDayDetails(JSONUtil.parseArray(feeComboDTO.getDayDetails()));
        feeComboVo.setMonthDetails(JSONUtil.parseArray(feeComboDTO.getMonthDetails()));
        return feeComboVo;
    }
}
