package com.ruoyi.daycare.elderlyPeople.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.daycare.elderlyPeople.domain.DayCareElderlyPeopleLifeStyle;
import com.ruoyi.daycare.elderlyPeople.mapper.DayCareElderlyPeopleLifeStyleMapper;
import com.ruoyi.daycare.elderlyPeople.service.IDayCareElderlyPeopleLifeStyleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 老人生活方式信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@Service
public class DayCareElderlyPeopleLifeStyleServiceImpl implements IDayCareElderlyPeopleLifeStyleService {
    @Autowired
    private DayCareElderlyPeopleLifeStyleMapper dayCareElderlyPeopleLifeStyleMapper;

    /**
     * 查询老人生活方式信息
     *
     * @param id 老人生活方式信息主键
     * @return 老人生活方式信息
     */
    @Override
    public DayCareElderlyPeopleLifeStyle selectDayCareElderlyPeopleLifeStyleById(String id) {
        return dayCareElderlyPeopleLifeStyleMapper.selectDayCareElderlyPeopleLifeStyleById(id);
    }

    /**
     * 查询老人生活方式信息列表
     *
     * @param elderlyPeopleLifeStyle 老人生活方式信息
     * @return 老人生活方式信息
     */
    @Override
    public List<DayCareElderlyPeopleLifeStyle> selectDayCareElderlyPeopleLifeStyleList(DayCareElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        return dayCareElderlyPeopleLifeStyleMapper.selectDayCareElderlyPeopleLifeStyleList(elderlyPeopleLifeStyle);
    }

    /**
     * 新增老人生活方式信息
     *
     * @param elderlyPeopleLifeStyle 老人生活方式信息
     * @return 结果
     */
    @Override
    public int insertDayCareElderlyPeopleLifeStyle(DayCareElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        elderlyPeopleLifeStyle.setCreateTime(DateUtils.getNowDate());
        String id = IdUtils.fastSimpleUUID();
        elderlyPeopleLifeStyle.setId(id);
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleLifeStyle.setCreateBy(String.valueOf(userId));
        return dayCareElderlyPeopleLifeStyleMapper.insertDayCareElderlyPeopleLifeStyle(elderlyPeopleLifeStyle);
    }

    /**
     * 修改老人生活方式信息
     *
     * @param elderlyPeopleLifeStyle 老人生活方式信息
     * @return 结果
     */
    @Override
    public int updateDayCareElderlyPeopleLifeStyle(DayCareElderlyPeopleLifeStyle elderlyPeopleLifeStyle) {
        elderlyPeopleLifeStyle.setUpdateTime(DateUtils.getNowDate());
        Long userId = SecurityUtils.getUserId();
        elderlyPeopleLifeStyle.setUpdateBy(String.valueOf(userId));
        return dayCareElderlyPeopleLifeStyleMapper.updateDayCareElderlyPeopleLifeStyle(elderlyPeopleLifeStyle);
    }

    /**
     * 批量删除老人生活方式信息
     *
     * @param ids 需要删除的老人生活方式信息主键
     * @return 结果
     */
    @Override
    public int deleteDayCareElderlyPeopleLifeStyleByIds(String[] ids) {
        return dayCareElderlyPeopleLifeStyleMapper.deleteDayCareElderlyPeopleLifeStyleByIds(ids);
    }

    /**
     * 删除老人生活方式信息信息
     *
     * @param id 老人生活方式信息主键
     * @return 结果
     */
    @Override
    public int deleteDayCareElderlyPeopleLifeStyleById(String id) {
        return dayCareElderlyPeopleLifeStyleMapper.deleteDayCareElderlyPeopleLifeStyleById(id);
    }
}
