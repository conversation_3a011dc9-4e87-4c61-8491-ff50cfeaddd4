package com.ruoyi.daycare.fee.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class DayCareBillResultVo {

    @ApiModelProperty(value = "应缴金额", name = "amountDue", example = "234523")
    private BigDecimal amountDue;

    /**
     * 实缴金额
     */
    @ApiModelProperty(value = "实缴金额", name = "amountDue", example = "234523")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "欠缴金额", name = "lackAmount", example = "234523")
    private BigDecimal lackAmount;

    /**
     * 账单状态（未结算，未结清，已结清）
     */
    @ApiModelProperty(value = "账单状态", name = "billState", example = "1")
    private String billState;

    @ApiModelProperty(value = "周期列表", name = "rows", example = "1")
    private List<DayCareFeeBillInfo> rows;


    /**
     * 总记录数
     */
    private long total;

}
