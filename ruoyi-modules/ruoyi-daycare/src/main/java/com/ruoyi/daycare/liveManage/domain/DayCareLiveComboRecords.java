package com.ruoyi.daycare.liveManage.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 居住套餐记录对象 t_daycare_live_combo_records
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
public class DayCareLiveComboRecords extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 关联主表id
     */
    @Excel(name = "关联主表id")
    private String liveId;

    /**
     * 入住日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入住日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "入住日期", name = "liveDate", required = true, example = "2021-04-06")
    private Date liveDate;

    /**
     * 到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expiredDate;

    /**
     * 计费日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计费日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "计费日期", name = "billingDate", required = true, example = "2021-04-06")
    private Date billingDate;

    /**
     * 护理级别
     */
    @Excel(name = "护理级别")
    @ApiModelProperty(value = "护理级别", name = "careLevel", required = true, example = "111")
    private String careLevel;

    /**
     * 套餐id
     */
    @Excel(name = "套餐id")
    @ApiModelProperty(value = "套餐id", name = "comboId", required = true, example = "222")
    private String comboId;

    /**
     * 套餐版本
     */
    @Excel(name = "套餐版本")
    @ApiModelProperty(value = "套餐版本", name = "comboVersion", required = true, example = "222")
    private String comboVersion;

    /**
     * 状态（在用，已变更，终止）
     */
    @Excel(name = "状态", readConverterExp = "在=用，已变更，终止")
    private String state;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLiveId() {
        return liveId;
    }

    public void setLiveId(String liveId) {
        this.liveId = liveId;
    }

    public Date getLiveDate() {
        return liveDate;
    }

    public void setLiveDate(Date liveDate) {
        this.liveDate = liveDate;
    }

    public Date getExpiredDate() {
        return expiredDate;
    }

    public void setExpiredDate(Date expiredDate) {
        this.expiredDate = expiredDate;
    }

    public Date getBillingDate() {
        return billingDate;
    }

    public void setBillingDate(Date billingDate) {
        this.billingDate = billingDate;
    }

    public String getCareLevel() {
        return careLevel;
    }

    public void setCareLevel(String careLevel) {
        this.careLevel = careLevel;
    }

    public String getComboId() {
        return comboId;
    }

    public void setComboId(String comboId) {
        this.comboId = comboId;
    }

    public String getComboVersion() {
        return comboVersion;
    }

    public void setComboVersion(String comboVersion) {
        this.comboVersion = comboVersion;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("liveId", getLiveId())
                .append("liveDate", getLiveDate())
                .append("expiredDate", getExpiredDate())
                .append("billingDate", getBillingDate())
                .append("careLevel", getCareLevel())
                .append("comboId", getComboId())
                .append("comboVersion", getComboVersion())
                .append("state", getState())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }
}
