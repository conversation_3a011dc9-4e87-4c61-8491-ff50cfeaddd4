package com.ruoyi.daycare.storiedBuilding.service;

import cn.hutool.json.JSONObject;
import com.ruoyi.daycare.storiedBuilding.domain.DayCareBedBaseInfo;
import com.ruoyi.daycare.storiedBuilding.domain.vo.DayCareBedRecordsInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 床位基础信息Service接口
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
public interface IDayCareBedBaseInfoService {
    /**
     * 查询床位基础信息
     *
     * @param id 床位基础信息主键
     * @return 床位基础信息
     */
    public DayCareBedBaseInfo selectDayCareBedBaseInfoById(Long id);

    /**
     * 查询床位基础信息列表
     *
     * @param bedBaseInfo 床位基础信息
     * @return 床位基础信息集合
     */
    public List<DayCareBedBaseInfo> selectDayCareBedBaseInfoList(DayCareBedBaseInfo bedBaseInfo);

    /**
     * 新增床位基础信息
     *
     * @param bedBaseInfo 床位基础信息
     * @return 结果
     */
    public int insertDayCareBedBaseInfo(DayCareBedBaseInfo bedBaseInfo);

    /**
     * 修改床位基础信息
     *
     * @param bedBaseInfo 床位基础信息
     * @return 结果
     */
    public int updateDayCareBedBaseInfo(DayCareBedBaseInfo bedBaseInfo);

    /**
     * 批量删除床位基础信息
     *
     * @param ids 需要删除的床位基础信息主键集合
     * @return 结果
     */
    public int deleteDayCareBedBaseInfoByIds(Long[] ids);

    /**
     * 删除床位基础信息信息
     *
     * @param id 床位基础信息主键
     * @return 结果
     */
    public int deleteDayCareBedBaseInfoById(Long id);

    /**
     * 通过房间id查询是否有床位
     *
     * @param id
     * @return
     */
    public boolean hasBedByRoomId(Long id);

    /**
     * 通过房间id获取床位信息
     *
     * @param roomId
     * @return
     */
    List<JSONObject> getBedInfo(Long roomId);

    /**
     * 获取房间的所有床位信息
     *
     * @param roomId
     * @return
     */
    public List<JSONObject> getRoomBedList(Long roomId);

    /**
     * 房态图数据
     *
     * @param id
     * @return
     */
    public List<JSONObject> getBedStateData(Long id);

    /**
     * 房态图历史记录
     *
     * @param bedId
     * @param name
     * @return
     */
    public List<DayCareBedRecordsInfoVo> getBedRecordsList(@Param("bedId") String bedId, @Param("name") String name);


    /**
     * 根据房间id获取床位名称和id
     *
     * @param roomId
     * @return
     */
    List<JSONObject> getBedNameByRoomId(Long roomId);
}
