package com.ruoyi.daycare.mealManagement.service.impl;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.daycare.mealManagement.domain.DayCareRecipeMenuTypeBaseInfo;
import com.ruoyi.daycare.mealManagement.mapper.DayCareRecipeMenuTypeBaseInfoMapper;
import com.ruoyi.daycare.mealManagement.service.IDayCareRecipeMenuTypeBaseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 食谱菜单类型基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@Service
public class DayCareRecipeMenuTypeBaseInfoServiceImpl implements IDayCareRecipeMenuTypeBaseInfoService {
    @Autowired
    private DayCareRecipeMenuTypeBaseInfoMapper recipeMenuTypeBaseInfoMapper;

    /**
     * 查询食谱菜单类型基本信息
     *
     * @param id 食谱菜单类型基本信息主键
     * @return 食谱菜单类型基本信息
     */
    @Override
    public DayCareRecipeMenuTypeBaseInfo selectDayCareRecipeMenuTypeBaseInfoById(Long id) {
        return recipeMenuTypeBaseInfoMapper.selectDayCareRecipeMenuTypeBaseInfoById(id);
    }

    /**
     * 查询食谱菜单类型基本信息列表
     *
     * @param recipeMenuTypeBaseInfo 食谱菜单类型基本信息
     * @return 食谱菜单类型基本信息
     */
    @Override
    public List<DayCareRecipeMenuTypeBaseInfo> selectDayCareRecipeMenuTypeBaseInfoList(DayCareRecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo) {
        return recipeMenuTypeBaseInfoMapper.selectDayCareRecipeMenuTypeBaseInfoList(recipeMenuTypeBaseInfo);
    }

    /**
     * 新增食谱菜单类型基本信息
     *
     * @param recipeMenuTypeBaseInfo 食谱菜单类型基本信息
     * @return 结果
     */
    @Override
    public int insertDayCareRecipeMenuTypeBaseInfo(DayCareRecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo) {
        recipeMenuTypeBaseInfo.setCreateTime(DateUtils.getNowDate());
        return recipeMenuTypeBaseInfoMapper.insertDayCareRecipeMenuTypeBaseInfo(recipeMenuTypeBaseInfo);
    }

    /**
     * 修改食谱菜单类型基本信息
     *
     * @param recipeMenuTypeBaseInfo 食谱菜单类型基本信息
     * @return 结果
     */
    @Override
    public int updateDayCareRecipeMenuTypeBaseInfo(DayCareRecipeMenuTypeBaseInfo recipeMenuTypeBaseInfo) {
        recipeMenuTypeBaseInfo.setUpdateTime(DateUtils.getNowDate());
        return recipeMenuTypeBaseInfoMapper.updateDayCareRecipeMenuTypeBaseInfo(recipeMenuTypeBaseInfo);
    }

    /**
     * 批量删除食谱菜单类型基本信息
     *
     * @param ids 需要删除的食谱菜单类型基本信息主键
     * @return 结果
     */
    @Override
    public int deleteDayCareRecipeMenuTypeBaseInfoByIds(Long[] ids) {
        return recipeMenuTypeBaseInfoMapper.deleteDayCareRecipeMenuTypeBaseInfoByIds(ids);
    }

    /**
     * 删除食谱菜单类型基本信息信息
     *
     * @param id 食谱菜单类型基本信息主键
     * @return 结果
     */
    @Override
    public int deleteDayCareRecipeMenuTypeBaseInfoById(Long id) {
        return recipeMenuTypeBaseInfoMapper.deleteDayCareRecipeMenuTypeBaseInfoById(id);
    }

    @Override
    public List<JSONObject> getRecipeMenuTypeList() {
        return recipeMenuTypeBaseInfoMapper.getRecipeMenuTypeList();
    }
}
