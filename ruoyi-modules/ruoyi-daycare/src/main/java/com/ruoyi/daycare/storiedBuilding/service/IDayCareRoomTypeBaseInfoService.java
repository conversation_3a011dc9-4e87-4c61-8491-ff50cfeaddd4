package com.ruoyi.daycare.storiedBuilding.service;

import cn.hutool.json.JSONObject;
import com.ruoyi.daycare.storiedBuilding.domain.DayCareRoomTypeBaseInfo;

import java.util.List;

/**
 * 房间类型Service接口
 *
 * <AUTHOR>
 * @date 2022-03-31
 */
public interface IDayCareRoomTypeBaseInfoService {
    /**
     * 查询房间类型
     *
     * @param id 房间类型主键
     * @return 房间类型
     */
    public DayCareRoomTypeBaseInfo selectDayCareRoomTypeBaseInfoById(Long id);

    /**
     * 查询房间类型列表
     *
     * @param roomTypeBaseInfo 房间类型
     * @return 房间类型集合
     */
    public List<DayCareRoomTypeBaseInfo> selectDayCareRoomTypeBaseInfoList(DayCareRoomTypeBaseInfo roomTypeBaseInfo);

    /**
     * 新增房间类型
     *
     * @param roomTypeBaseInfo 房间类型
     * @return 结果
     */
    public int insertDayCareRoomTypeBaseInfo(DayCareRoomTypeBaseInfo roomTypeBaseInfo);

    /**
     * 修改房间类型
     *
     * @param roomTypeBaseInfo 房间类型
     * @return 结果
     */
    public int updateDayCareRoomTypeBaseInfo(DayCareRoomTypeBaseInfo roomTypeBaseInfo);

    /**
     * 批量删除房间类型
     *
     * @param ids 需要删除的房间类型主键集合
     * @return 结果
     */
    public int deleteDayCareRoomTypeBaseInfoByIds(Long[] ids);

    /**
     * 删除房间类型信息
     *
     * @param id 房间类型主键
     * @return 结果
     */
    public int deleteDayCareRoomTypeBaseInfoById(Long id);


    public JSONObject selectName();

    /**
     * 获取房间类型的key和value值
     *
     * @return
     */
    public List<JSONObject> getRoomTypeLabelAndValue();


    /**
     * 根据房间id获取房间的费用基础信息
     *
     * @return
     */
    public JSONObject getRoomTypeBase(Long roomId);

}
