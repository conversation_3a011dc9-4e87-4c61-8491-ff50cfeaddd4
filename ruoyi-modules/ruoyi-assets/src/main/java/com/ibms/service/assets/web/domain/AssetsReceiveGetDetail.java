package com.ibms.service.assets.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ibms.service.assets.extent.config.mybatisplus.BasePlusEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/3 16:52
 */
@Data
@ApiModel("固定资产领取表明细列表")
@TableName("t_assets_receive_get_detail")
public class AssetsReceiveGetDetail extends BasePlusEntity implements Serializable {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("id")
    private Integer id;

    /**
     * Base表单类型 （字典：assets_base_type）
     */
    @ApiModelProperty("Base表单类型 （字典：assets_base_type）")
    private String baseType;

    /**
     * 基础表id
     */
    @ApiModelProperty("基础表id")
    private String parentBaseId;

    /**
     * 父级详情数据id（例如领用单的详情列）
     */
    @ApiModelProperty("父级详情数据id")
    private Integer parentDetailId;

    /**
     * 资产数据id
     */
    @ApiModelProperty("资产数据id")
    private String stockDataId;

    @ApiModelProperty("仓库管理员id（当前任务是哪个仓管分发的）")
    private Integer keeperId;
    @ApiModelProperty("仓库管理员姓名")
    private String keeperName;

    /**
     * 固定资产id
     */
    @ApiModelProperty("固定资产id")
    private String assetsId;

    /**
     * 接收人id
     */
    @ApiModelProperty("接收人id")
    private String recipientId;

    @ApiModelProperty("接收人姓名")
    private String recipientName;

    /**
     * 接收状态(1未接收，2已接收)
     */
    @ApiModelProperty("(字典：receive_status 1未接收，2已接收)")
    private String receiveStatus;

    @ApiModelProperty("特殊状态（1.已归还）")
    private Integer specialStatus;

    /**
     * 接收类型 1：用户接收 2：仓管接收
     */
    @ApiModelProperty("接收类型 1：用户接收 2：仓管接收")
    private String receiveType;

}
