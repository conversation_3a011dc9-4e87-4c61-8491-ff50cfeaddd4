package com.ibms.service.assets.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ibms.service.assets.web.controller.ciInventoryDetail.param.CiInventoryDetailSaveParam;
import com.ibms.service.assets.web.controller.ciInventoryDetail.param.CiInventoryDetailSelectParam;
import com.ibms.service.assets.web.controller.ciInventoryDetail.vo.CompletedListVo;
import com.ibms.service.assets.web.controller.ciInventoryDetail.vo.WaitingProcessListVo;
import com.ibms.service.assets.web.domain.CiInventoryDetail;
import com.ruoyi.common.core.web.domain.AjaxResult;

import java.util.List;

/**
 * <AUTHOR>
 * @description 耗资盘点列表明细业务层
 * @date 2023/01/03
 */
public interface CiInventoryDetailService extends IService<CiInventoryDetail> {

    AjaxResult saveOrUpdate(CiInventoryDetailSaveParam param);

    List<WaitingProcessListVo> waitingProcessList(CiInventoryDetailSelectParam param);

    List<CompletedListVo> completedList(CiInventoryDetailSelectParam param);
}
