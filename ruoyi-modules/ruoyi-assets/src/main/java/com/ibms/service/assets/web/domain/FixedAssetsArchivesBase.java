package com.ibms.service.assets.web.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ibms.service.assets.extent.config.mybatisplus.BasePlusEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 固定资产档案
 * @date 2022-12-30
 */
@Data
@ApiModel("固定资产档案")
@TableName("t_fixed_assets_archives_base")
public class FixedAssetsArchivesBase extends BasePlusEntity implements Serializable {

    private static final long serialVersionUID = 8737568367001864818L;


    /**
     * id(资产编号)
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("id(资产编号)")
    private String id;

    /**
     * 库存id
     */
    @ApiModelProperty("库存id")
    private String stockDataId;

    /**
     * 使用范围（0办公，1项目）
     */
    @ApiModelProperty("使用范围（0办公，1项目）")
    private String useScope;

    /**
     * 资产名称
     */
    @ApiModelProperty("资产名称")
    private String name;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 品牌
     */
    @ApiModelProperty("品牌")
    private String brand;

    /**
     * 原值金额
     */
    @ApiModelProperty("原值金额")
    private BigDecimal originalValueAmount;

    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String specification;

    /**
     * 资产来源
     */
    @ApiModelProperty("资产来源，字典类型（assets_source）：1.接受捐赠、2.购买")
    private String assetsSource;

    /**
     * 供应商
     */
    @ApiModelProperty("供应商")
    private String supplier;

    /**
     * 预计使用年限
     */
    @ApiModelProperty("预计使用年限")
    private String expectedLife;

    /**
     * 资产类型
     */
    @ApiModelProperty("资产类型，字典类型（assets_type）：1.固定资产、2.无形资产、3.低值易耗资产")
    private String assetsType;

    /**
     * 分类id
     */
    @ApiModelProperty("分类id")
    private String classifyId;

    /**
     * 购入时间
     */
    @ApiModelProperty("购入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date purchaseTime;

    /**
     * 保修情况
     */
    @ApiModelProperty("保修情况")
    private String warrantyCondition;

    /**
     * 保修开始时间
     */
    @ApiModelProperty("保修开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date warrantyStartTime;

    /**
     * 保修结束时间
     */
    @ApiModelProperty("保修结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date warrantyEndTime;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String status;

    /**
     * 状态
     */
    @ApiModelProperty("存放位置")
    private Integer placeId;

    /**
     * 现使用人id
     */
    @ApiModelProperty("现使用人id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)// 加注解（可以设入空值）
    private Integer useUserId;

    /**
     * 现使用人名称
     */
    @ApiModelProperty("现使用人名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)// 加注解（可以设入空值）
    private String useUserName;

    /**
     * 使用部门名称
     */
    @ApiModelProperty("使用部门名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)// 加注解（可以设入空值）
    private String useDeptName;

    /**
     * 使用部门id
     */
    @ApiModelProperty("使用部门id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)// 加注解（可以设入空值）
    private Integer useDeptId;

    /**
     * 使用位置
     */
    @ApiModelProperty("使用位置")
    @TableField(updateStrategy = FieldStrategy.IGNORED)// 加注解（可以设入空值）
    private String usePlace;

    /**
     * 使用时间
     */
    @ApiModelProperty("使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.IGNORED)// 加注解（可以设入空值）
    private Date useTime;

    public FixedAssetsArchivesBase() {
    }
}
