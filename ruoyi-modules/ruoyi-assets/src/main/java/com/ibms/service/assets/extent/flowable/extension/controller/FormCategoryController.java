/**
 * Copyright © 2021-2026 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.ibms.service.assets.extent.flowable.extension.controller;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ibms.service.assets.extent.flowable.extension.domain.FormCategory;
import com.ibms.service.assets.extent.flowable.extension.service.FormCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 流程分类Controller
 *
 * <AUTHOR>
 * @version 2022/12/09 10:24:13
 */
@RestController
@RequestMapping(value = "/extension/formCategory")
public class FormCategoryController {

    @Autowired
    private FormCategoryService formCategoryService;


    /**
     * 流程分类树表数据
     */
    @GetMapping("list")
    public ResponseEntity list(FormCategory formCategory, Page<FormCategory> page) throws Exception {

        QueryWrapper<FormCategory> query = Wrappers.query(formCategory);

        IPage<FormCategory> result = formCategoryService.page(page, query);

        return ResponseEntity.ok(result);
    }


    /**
     * 根据Id获取流程分类数据
     */
//	@GetMapping("queryById")
//	public ResponseEntity<FormCategoryDTO> queryById(String id) {
//		return ResponseEntity.ok ( formCategoryWrapper.toDTO ( formCategoryService.getById ( id ) ));
//	}

    /**
     * 保存流程分类
     */
//	@PostMapping("save")
//	public ResponseEntity save(@RequestBody FormCategoryDTO formCategoryDTO) {
//		//新增或编辑表单保存
//		formCategoryService.saveOrUpdate (formCategoryWrapper.toEntity ( formCategoryDTO ));//保存
//		return ResponseEntity.ok ("保存流程分类成功");
//	}

    /**
     * 删除流程分类
     */
//	@DeleteMapping("delete")
//	public ResponseEntity delete(String id) {
//		formCategoryService.removeWithChildrenById (id );
//		return ResponseEntity.ok ("删除流程分类成功");
//	}

    /**
     * 获取JSON树形数据。
     *
     * @param extId 排除的ID
     * @return
     */
    @GetMapping("treeData")
    public ResponseEntity treeData(@RequestParam(required = false) String extId) {

        List<FormCategory> list = formCategoryService.list();

        // Json树形结构
        TreeNodeConfig config = new TreeNodeConfig();
        // config可以配置属性字段名和排序等等
        // config.setParentIdKey("parentId");
        // config.setDeep(20);//最大递归深度  默认无限制
        List<Tree<String>> treeNodes = TreeUtil.build(list, "0", config, (object, tree) -> {
            tree.setId(object.getId());// 必填属性
            tree.setParentId(object.getParentId());// 必填属性
            tree.setWeight(object.getSort());
            tree.setName(object.getName());
            // 扩展属性 ...
            tree.putExtra("parentIds", object.getParentIds());
        });

        return ResponseEntity.ok(treeNodes);
    }

}
