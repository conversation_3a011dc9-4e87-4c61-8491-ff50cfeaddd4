package com.ibms.service.assets.extent.flowable.flow.common.handler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Lists;
import com.ibms.service.assets.extent.flowable.extension.domain.TaskDefExtension;
import com.ibms.service.assets.extent.flowable.extension.service.TaskDefExtensionService;
import com.ibms.service.assets.extent.flowable.extension.service.dto.FlowAssigneeDTO;
import com.ibms.service.assets.extent.flowable.flow.utils.FlowableUtils;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.param.UserQueryIdParam;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.impl.el.ExpressionManager;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.impl.bpmn.behavior.UserTaskActivityBehavior;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.task.service.TaskService;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

@Slf4j
public class ExtUserTaskActivityBehavior extends UserTaskActivityBehavior {

    private static final long serialVersionUID = 7711531472879418236L;

    public ExtUserTaskActivityBehavior(UserTask userTask) {
        super(userTask);
    }


    /**
     * 分配办理人员
     */
    @Override
    protected void handleAssignments(TaskService taskService, String assignee, String owner, List<String> candidateUsers, List<String> candidateGroups, TaskEntity task, ExpressionManager expressionManager, DelegateExecution execution) {

        Process process = SpringUtil.getBean(RepositoryService.class).getBpmnModel(task.getProcessDefinitionId()).getMainProcess();
        FlowElement flowElement = process.getFlowElement(task.getTaskDefinitionKey());
        Boolean isMultiInstance = FlowableUtils.isFlowElementMultiInstance(flowElement);
        if (isMultiInstance) {
            super.handleAssignments(taskService, assignee, owner, candidateUsers, candidateGroups, task, expressionManager, execution);
            return;
        }
        List<TaskDefExtension> list = SpringUtil.getBean(TaskDefExtensionService.class)
                .lambdaQuery()
                .eq(TaskDefExtension::getProcessDefId, process.getId())
                .eq(TaskDefExtension::getTaskDefId, task.getTaskDefinitionKey()).list();
        HashSet<String> candidateUserIds = new LinkedHashSet<>();
        if (list.size() > 0) {
            TaskDefExtension taskDefExtension = list.get(0);
            List<FlowAssigneeDTO> assigneeList = SpringUtil.getBean(TaskDefExtensionService.class).getById(taskDefExtension.getId()).getFlowAssigneeList();
            for (FlowAssigneeDTO flowAssignee : assigneeList) {
                switch (flowAssignee.getType()) {
                    case "user":
                        candidateUserIds.addAll(Arrays.asList(flowAssignee.getValue().split(",")));
                        break;
                    case "post":
                        if (StrUtil.isNotBlank(flowAssignee.getValue())) {
                            String postId = flowAssignee.getValue();

                            UserQueryIdParam userQueryIdParam = new UserQueryIdParam();
                            userQueryIdParam.setPostId(Long.valueOf(postId));

                            List<SysUser> userList = SpringUtil.getBean(RemoteUserService.class).getUserListByQuery(userQueryIdParam, SecurityConstants.INNER);
                            List userIdList = CollectionUtils.extractToList(userList, "userId");
                            candidateUserIds.addAll(userIdList);
                        }

                        break;
// 公司暂停使用
//                    case "company":
//                        if(StrUtil.isNotBlank (flowAssignee.getValue ())){
//                            String companyId = flowAssignee.getValue ();
//                            List<User> userList = SpringUtil.getBean (UserService.class).lambdaQuery ().eq ( User::getCompanyId, companyId ).list ();
//                            candidateUserIds.addAll (CollectionUtils.extractToList (userList, "id"));
//                        }
//
//                        break;
                    case "depart":
                        if (StrUtil.isNotBlank(flowAssignee.getValue())) {
                            String deptId = flowAssignee.getValue();

                            UserQueryIdParam userQueryIdParam = new UserQueryIdParam();
                            userQueryIdParam.setDeptId(Long.valueOf(deptId));
                            List<SysUser> sysUserList = SpringUtil.getBean(RemoteUserService.class).getUserListByQuery(userQueryIdParam, SecurityConstants.INNER);

                            candidateUserIds.addAll(CollectionUtils.extractToList(sysUserList, "userId"));
                        }

                        break;
                    case "role":
                        if (StrUtil.isNotBlank(flowAssignee.getValue())) {
                            String[] roleIds = flowAssignee.getValue().split(",");
                            for (String roleId : roleIds) {

                                UserQueryIdParam userQueryIdParam = new UserQueryIdParam();
                                userQueryIdParam.setRoleId(Long.valueOf(roleId));
                                List<SysUser> sysUserList = SpringUtil.getBean(RemoteUserService.class).getUserListByQuery(userQueryIdParam, SecurityConstants.INNER);

                                candidateUserIds.addAll(CollectionUtils.extractToList(sysUserList, "userId"));
                            }
                        }
                        break;
                    case "applyUserId":
                        candidateUserIds.add("${applyUserId}");
                        break;
                    case "previousExecutor":
                        HistoryService historyService = SpringUtil.getBean(HistoryService.class);

                        HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery();

                        HistoricTaskInstanceQuery finished = historicTaskInstanceQuery.processInstanceId(task.getProcessInstanceId()).finished();

                        HistoricTaskInstanceQuery historicTaskInstanceQuery1 = finished.includeProcessVariables();

                        List<HistoricTaskInstance> historicTaskInstanceList = historicTaskInstanceQuery1.orderByHistoricTaskInstanceEndTime().desc().list();

                        HistoricTaskInstance historicTaskInstance = historicTaskInstanceList.get(0);

                        candidateUserIds.add(historicTaskInstance.getAssignee());
                        break;
                    case "currentUserId":
                        String userId = SecurityUtils.getUserId().toString();
                        candidateUserIds.add(userId);
                        break;
                    case "sql":
                        Map userMap = SpringUtil.getBean(JdbcTemplate.class).queryForMap(flowAssignee.getValue());
                        candidateUserIds.add(userMap.get("id").toString());
                        break;
                    case "custom":
                        // 根据你的自定义标记，请自行实现
                        break;
                }
            }
        }
        List<String> candidateIds = new ArrayList<>(candidateUserIds);
        // 此处可以根据业务逻辑自定义
        if (candidateIds.size() == 0) {
            super.handleAssignments(taskService, null, owner, Lists.newArrayList(), Lists.newArrayList(), task, expressionManager, execution);
        } else if (candidateIds.size() == 1) {
            String assigneeId = String.valueOf(candidateIds.get(0));
            super.handleAssignments(taskService, assigneeId, owner, Lists.newArrayList(), Lists.newArrayList(), task, expressionManager, execution);
        } else if (candidateIds.size() > 1) {
            super.handleAssignments(taskService, null, owner, candidateIds, null, task, expressionManager, execution);
        }


    }

}

