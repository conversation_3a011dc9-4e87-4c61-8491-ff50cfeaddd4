package com.ibms.service.assets.extent.flowable.extension.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ibms.service.assets.extent.flowable.extension.controller.button.param.*;
import com.ibms.service.assets.extent.flowable.extension.mapper.ButtonMapper;
import com.ibms.service.assets.extent.flowable.extension.domain.Button;
import com.ibms.service.assets.extent.flowable.extension.service.ButtonService;
import com.ruoyi.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/28 16:52
 */
@Service
@Slf4j
public class ButtonServiceImpl extends ServiceImpl<ButtonMapper, Button> implements ButtonService {

    @Autowired
    private ButtonMapper buttonMapper;

    @Override
    public List<Button> list(ButtonListParam param) {

        String code = param.getCode();
        String name = param.getName();

        QueryWrapper query = Wrappers.query();
        if (StrUtil.isNotBlank(code)) {
            query.like("code", code);
        }
        if (StrUtil.isNotBlank(name)) {
            query.like("name", name);
        }

        query.orderByAsc("sort");
        List list = buttonMapper.selectList(query);
        return list;
    }

    @Override
    public Button queryById(ButtonGetByIdParam param) {

        String id = param.getId();

        Button button = buttonMapper.selectById(id);

        return button;
    }

    @Override
    public Integer saveOrUpdate(ButtonSavaOrUpdateParam param) {

        String id = param.getId();
        String code = param.getCode();
        String name = param.getName();

        QueryWrapper query = Wrappers.query();

//        if(StrUtil.isNotBlank(id)){
//            query.ne("id",id);
//        }

        if (StrUtil.isNotBlank(code)) {
            query.eq("code", code);
        }

        if (StrUtil.isNotBlank(name)) {
            query.or();
            query.eq("name", name);
        }
//
        List list = buttonMapper.selectList(query);
        if (CollectionUtils.isNotEmpty(list)) {
            throw new ServiceException().setMessage("code或name 有重复值请修改！");
        }

        Date currentDate = new Date();
        if (StrUtil.isNotBlank(id)) {
            Button button = buttonMapper.selectById(id);
            if (button == null) {
                return 0;
            }

            BeanUtil.copyProperties(param, button);
            button.setUpdateTime(currentDate);
            int update = buttonMapper.updateById(button);
            return update;
        }
        Button button = new Button();
        BeanUtil.copyProperties(param, button);
        button.setId(IdUtil.simpleUUID());
        button.setCreateTime(currentDate);
        button.setUpdateTime(currentDate);
        int insert = buttonMapper.insert(button);

        return insert;

    }

    @Override
    public Integer deleteByIds(ButtonDeleteIdsParam param) {

        String ids = param.getIds();

        String[] idArray = ids.split(",");
        ArrayList<String> strings = Lists.newArrayList(idArray);

        if (CollectionUtils.isEmpty(strings)) {
            return 0;
        }

        int i = buttonMapper.deleteBatchIds(strings);

        return i;
    }

    @Override
    public boolean validateNameNoExist(ButtonValidateNameParam param) {

        String name = param.getName();

        QueryWrapper queryWrapper = Wrappers.query();
        queryWrapper.eq("name", name);

        Long selectCount = buttonMapper.selectCount(queryWrapper);

        return selectCount == 0;
    }

    @Override
    public boolean validateCodeNoExist(ButtonValidateCodeParam param) {

        String code = param.getCode();

        QueryWrapper queryWrapper = Wrappers.query();
        queryWrapper.eq("code", code);

        Long selectCount = buttonMapper.selectCount(queryWrapper);

        return selectCount == 0;

    }

}
