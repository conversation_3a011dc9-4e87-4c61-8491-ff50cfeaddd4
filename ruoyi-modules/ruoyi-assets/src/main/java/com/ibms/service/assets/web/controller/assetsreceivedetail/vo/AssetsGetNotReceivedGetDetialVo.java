package com.ibms.service.assets.web.controller.assetsreceivedetail.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/10 10:44
 */
@Data
@ApiModel(value = "获取未接收的资产详情列表展示数据")
public class AssetsGetNotReceivedGetDetialVo {

    /**
     * 明细列表id
     */
    @ApiModelProperty("明细列表id")
    private Integer detailId;

    /**
     * 资产编号
     */
    @ApiModelProperty("资产编号")
    private String assetsId;

    /**
     * 资产名称
     */
    @ApiModelProperty("资产名称")
    private String name;

    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String specification;

    /**
     * 分类名称
     */
    @ApiModelProperty("分类名称")
    private String classifyName;

    /**
     * 分类id
     */
    @ApiModelProperty("分类id")
    private String classifyId;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 品牌
     */
    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("字典类型（assets_fixed_assets_status）使用状态")
    private String status;

    /**
     * 使用范围（0办公，1项目）
     */
    @ApiModelProperty("使用范围（0办公，1项目）")
    private String useScope;

}
