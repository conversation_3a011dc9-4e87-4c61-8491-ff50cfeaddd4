package com.ibms.service.assets.extent.flowable.extension.controller.actcategory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/28 12:16
 */
@Data
@ApiModel(value = "保存/更新 流程分类参数")
public class ActCategorySavaOrUpdateParam {

    /**
     * 主键
     */
    @ApiModelProperty("主键(不为空则update，反之保存)")
    private String id;

    /**
     * 父级编号(前端需求将 parentId 更改为 parentLevelId)
     */
    @ApiModelProperty("父级编号")
    private String parentLevelId;

    /**
     * 父级编号
     */
    @ApiModelProperty("父级编号")
    private String parentId;
    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;
    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;
    /**
     * 备注信息
     */
    @ApiModelProperty("备注信息")
    private String remarks;

    public String getParentId() {
        return parentId = parentLevelId;
    }


}
