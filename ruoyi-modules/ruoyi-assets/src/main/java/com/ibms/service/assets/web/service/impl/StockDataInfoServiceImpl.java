package com.ibms.service.assets.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ibms.service.assets.web.controller.stockdatainfo.param.StockDataInfoListParam;
import com.ibms.service.assets.web.domain.StockDataInfo;
import com.ibms.service.assets.web.mapper.stockdatainfo.StockDataInfoMapper;
import com.ibms.service.assets.web.service.AssetsClassifyInfoService;
import com.ibms.service.assets.web.service.StockDataInfoService;
import com.ruoyi.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class StockDataInfoServiceImpl extends ServiceImpl<StockDataInfoMapper, StockDataInfo> implements StockDataInfoService {

    @Autowired
    private AssetsClassifyInfoService assetsClassifyInfoService;

    @Override
    public List<StockDataInfo> selectList(StockDataInfoListParam param) {
        List<StockDataInfo> pageList = baseMapper.getPageList(param);
        for (StockDataInfo stockDataInfo : pageList) {
            stockDataInfo.setClassifyName(assetsClassifyInfoService.getFullClassifyByid(stockDataInfo.getClassifyId()));
        }
        return pageList;
    }

    @Override
    public StockDataInfo getStockDataInfoForUpdateById(Integer id) {
        StockDataInfo dataInfo = baseMapper.getStockDataInfoForUpdateById(id);
        return dataInfo;
    }
}
