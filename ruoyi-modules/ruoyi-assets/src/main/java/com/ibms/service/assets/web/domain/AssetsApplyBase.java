package com.ibms.service.assets.web.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ibms.service.assets.extent.config.mybatisplus.BasePlusEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 申请基础信息
 * <AUTHOR>
 * @Date 2022/12/30 11:02
 */
@Data
@ApiModel("申请基础信息")
@TableName("t_assets_apply_base")
public class AssetsApplyBase<T, K> extends BasePlusEntity implements Serializable {

    private static final long serialVersionUID = 6451516069066819164L;


    /**
     * id(单号)
     */
    @TableId
    @ApiModelProperty("id(单号)")
    private String id;

    /**
     * 表单类型
     */
    @ApiModelProperty("表单类型 （字典：assets_base_type）")
    private String baseType;

    /**
     * 申请级别
     */
    @ApiModelProperty("申请级别（字典：assets_apply_level）")
    private String applyLevel;

    /**
     * 流程id
     */
    @ApiModelProperty("流程id")
    private String procInsId;

    /**
     * 使用范围（0办公，1项目）
     */
    @ApiModelProperty("使用范围（0办公，1项目）")
    private String useScope;

    /**
     * 申请人id
     */
    @ApiModelProperty("申请人id")
    private String applyUserId;

    /**
     * 申请人名称
     */
    @ApiModelProperty("申请人名称")
    private String applyUserName;

    /**
     * 申请人部门id
     */
    @ApiModelProperty("申请人部门id")
    private String applyDeptId;

    /**
     * 申请人部门名称
     */
    @ApiModelProperty("申请人部门名称")
    private String applyDeptName;

    /**
     * 时间
     */
    @ApiModelProperty("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date applyTime;

    /**
     * 说明
     */
    @ApiModelProperty("说明")
    private String instructions;

    /**
     * 附件
     */
    @ApiModelProperty("附件")
    private String filesUrl;

    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态,1：审核中、2.审核通过、3.驳回")
    private String auditStatus;

    /**
     * 被选择的原单base表的是否展示状态：如归还时所选择列表，如果选择保存了则就不展示到原单列表中
     */
    @ApiModelProperty("被选择的原单base表的id：如归还时所选择列表，如果选择保存了则就不展示到原单列表中")
    @TableField(updateStrategy = FieldStrategy.IGNORED)// 加注解（可以设入空值，看代码结果）
    private String correlationSelectId;

    /**
     * 本表单的状态：如资产退库状态以及借用状态等
     */
    @ApiModelProperty("本表单的状态：如资产退库状态以及借用状态等")
    private String baseStatus;

    /**
     * 申请总量
     */
    @ApiModelProperty("申请总量")
    private Integer applyTotal;

    /**
     * 已处理数量
     */
    @ApiModelProperty("已处理数量")
    private Integer treatedNumber;

    /**
     * 库管处理数量
     */
    @ApiModelProperty("库管处理数量")
    private Integer keeperTreatedNumber;

    /**
     * 接收状态 1：已完成接收 2：未完成接收
     */
    @ApiModelProperty("接收状态（字典：assets_base_receive_status 1：已完成接收 2：未完成接收")
    private String receiveStatus;

    @ApiModelProperty("细则详情")
    private transient List<T> detailList;


    @ApiModelProperty("已处理明细")
    private transient List<K> dealList;

    /**
     * 验收人id
     */
    @ApiModelProperty("验收人id")
    private transient String acceptorId;

    /**
     * 验收人名称
     */
    @ApiModelProperty("验收人名称")
    private transient String acceptorName;


}
