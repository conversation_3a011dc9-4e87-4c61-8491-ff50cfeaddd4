package com.ibms.service.assets.web.controller.assetspurchaseorderdetail.param;


import com.baomidou.mybatisplus.annotation.TableId;
import com.ibms.service.assets.web.domain.AssetsPurchaseOrderDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "采购订单保存参数")
public class AssetsPurchaseOrderSaveParam {


    /**
     * id(单号)
     */
    @TableId
    @ApiModelProperty("id(单号)")
    private String id;

    /**
     * 申请级别（1：普通，2：重要 3：紧急）
     */
    @ApiModelProperty("申请级别（1：普通，2：重要 3：紧急）")
    private String applyLevel;

    /**
     * 流程id
     */
    @ApiModelProperty("流程id")
    private String flowId;

    /**
     * 申请人id
     */
    @ApiModelProperty("申请人id")
    private String applyUserId;

    /**
     * 申请人名称
     */
    @ApiModelProperty("申请人名称")
    private String applyUserName;

    /**
     * 申请人部门id
     */
    @ApiModelProperty("申请人部门id")
    private String applyDeptId;

    /**
     * 申请人部门名称
     */
    @ApiModelProperty("申请人部门名称")
    private String applyDeptName;

    /**
     * 时间
     */
    @ApiModelProperty("申请时间")
    private Date applyTime;

    /**
     * 说明
     */
    @ApiModelProperty("说明")
    private String instructions;

    /**
     * 附件
     */
    @ApiModelProperty("附件")
    private String filesUrl;

    /**
     * 发布状态
     */
    @ApiModelProperty("发布状态")
    private String releaseStatus;

    /**
     * 流程状态
     */
    @ApiModelProperty("流程状态")
    private String flowStatus;

    /**
     * 被选择的原单base表的是否展示状态：如归还时所选择列表，如果选择保存了则就不展示到原单列表中
     */
    @ApiModelProperty("被选择的原单base表的是否展示状态：如归还时所选择列表，如果选择保存了则就不展示到原单列表中")
    private String correlationSelectStatus;

    /**
     * 本表单的状态：如资产退库状态以及借用状态等
     */
    @ApiModelProperty("本表单的状态：如资产退库状态以及借用状态等")
    private String baseStatus;


    /**
     * 验收人id
     */
    @ApiModelProperty("验收人id")
    private String acceptorId;

    /**
     * 验收人名称
     */
    @ApiModelProperty("验收人名称")
    private String acceptorName;

    @ApiModelProperty(value = "列表明细")
    private List<AssetsPurchaseOrderDetail> detailList;


}
