package com.ibms.service.assets.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ibms.service.assets.extent.config.dict.AsstesDict;
import com.ibms.service.assets.web.controller.assetsborrowdetail.param.*;
import com.ibms.service.assets.web.controller.assetsborrowdetail.vo.AssetsBorrowDetailGetNotGiveBackVo;
import com.ibms.service.assets.web.controller.stockchangerecord.param.StockChangeRecordInsertParam;
import com.ibms.service.assets.web.domain.AssetsApplyBase;
import com.ibms.service.assets.web.domain.AssetsBorrowDetail;
import com.ibms.service.assets.web.domain.AssetsReceiveGetDetail;
import com.ibms.service.assets.web.domain.FixedAssetsArchivesBase;
import com.ibms.service.assets.web.mapper.assetsborrowdetail.AssetsBorrowDetailMapper;
import com.ibms.service.assets.web.mapper.assetsborrowdetail.dto.AssetsBorrowDetailAndArchivesDataDto;
import com.ibms.service.assets.web.mapper.assetsborrowdetail.dto.AssetsBorrowDetailAndStockDataDto;
import com.ibms.service.assets.web.mapper.assetsborrowdetail.dto.AssetsBorrowDetailAndStockDataFinishDto;
import com.ibms.service.assets.web.mapper.fixedAssetsArchivesBase.FixedAssetsArchivesBaseMapper;
import com.ibms.service.assets.web.mapper.fixedAssetsArchivesBase.dto.ArchivesGetByStockDataId;
import com.ibms.service.assets.web.service.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/3 17:33
 */
@Service
@Slf4j
public class AssetsBorrowDetailServiceImpl extends ServiceImpl<AssetsBorrowDetailMapper, AssetsBorrowDetail> implements AssetsBorrowDetailService {

    @Autowired
    private AssetsApplyBaseService assetsApplyBaseService;
    @Autowired
    private FixedAssetsArchivesBaseService fixedAssetsArchivesBaseService;
    @Autowired
    private FixedAssetsArchivesBaseMapper fixedAssetsArchivesBaseMapper;
    @Autowired
    private AssetsBorrowDetailMapper assetsBorrowDetailMapper;
    @Autowired
    private AssetsClassifyInfoService assetsClassifyInfoService;
    @Autowired
    private StockChangeRecordService stockChangeRecordService;

    @Autowired
    private AssetsReceiveGetDetailService assetsReceiveGetDetailService;

    /**
     * @description: TODO 新建借用单base
     * @author: lyc
     * @date: 2023/2/7 16:30:03
     * @param: [param]
     * @return: com.ibms.service.assets.web.domain.AssetsApplyBase
     **/
    public AssetsApplyBase createAssetsApplyBase(AssetsBorrowBaseAndDetailSaveParam param) {
        // 新增
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        // 当前人信息
        AssetsApplyBase assetsApplyBase = new AssetsApplyBase();
        BeanUtil.copyProperties(param, assetsApplyBase);
        assetsApplyBase.setId(null);
        assetsApplyBase.setApplyUserId(sysUser.getUserId().toString());
        assetsApplyBase.setApplyUserName(sysUser.getNickName());
        // 当前人部门信息
        SysDept dept = sysUser.getDept();
        assetsApplyBase.setApplyDeptId(dept.getDeptId().toString());
        assetsApplyBase.setApplyDeptName(dept.getDeptName());
        assetsApplyBase.setApplyTime(new Date());
        // 基础信息
        assetsApplyBase.setBaseType("6");
        assetsApplyBase.setAuditStatus("1");// 审核状态，1：审核中、2.审核通过、3.驳回
        assetsApplyBase.setTreatedNumber(0);// 用户已处理数量0
        assetsApplyBase.setKeeperTreatedNumber(0);// 库管处理数量
        assetsApplyBase.setReceiveStatus("2");// 未完成接收
        AssetsApplyBase base = assetsApplyBaseService.savePlus(assetsApplyBase);

        return base;
    }

    /**
     * @description: TODO 创建借用详情
     * @author: lyc
     * @date: 2023/2/7 16:41:07
     * @param: [baseId, stockDataId, applyNumber]
     * @return: com.ibms.service.assets.web.domain.AssetsBorrowDetail
     **/
    public AssetsBorrowDetail createAssetsBorrowDetail(String baseId, String stockDataId, Integer applyNumber) {
        AssetsBorrowDetail assetsBorrowDetail = new AssetsBorrowDetail();
        assetsBorrowDetail.setParentBaseId(baseId);
        assetsBorrowDetail.setStockDataId(stockDataId);
        assetsBorrowDetail.setBorrowNumber(applyNumber);
        assetsBorrowDetail.setReceivedNumber(0);
        assetsBorrowDetail.setKeeperNumber(0);
        return assetsBorrowDetail;
    }


    @Override
    public AssetsApplyBase saveDetailAndBase(AssetsBorrowBaseAndDetailSaveParam param) {

        String applyBaseId = param.getId();
        // base表维护
        AssetsApplyBase assetsApplyBase = null;
        String auditStatus = "";
        Boolean bohui = null;// 是否是驳回状态
        if (StrUtil.isNotBlank(applyBaseId)) {// 更新

            assetsApplyBase = assetsApplyBaseService.getById(applyBaseId);
            auditStatus = assetsApplyBase.getAuditStatus();
            bohui = AsstesDict.AssetsFlowableStatus.已驳回.getCode().equals(auditStatus);// 驳回状态

            if (bohui) {// 驳回的情况下则新增
                assetsApplyBase = createAssetsApplyBase(param);
            } else {// 更新
                BeanUtil.copyProperties(param, assetsApplyBase);
            }

            assetsApplyBaseService.saveOrUpdate(assetsApplyBase);
        } else {
            assetsApplyBase = createAssetsApplyBase(param);
        }


        List<AssetsBorrowDetailSaveParam> detailSaveParamList = param.getDetailList();
        Integer applyTotal = 0;
        for (AssetsBorrowDetailSaveParam borrowDetailSaveParam : detailSaveParamList) {

            applyTotal += borrowDetailSaveParam.getApplyNumber();
            Integer detialId = borrowDetailSaveParam.getDetialId();

            boolean shenpi = AsstesDict.AssetsFlowableStatus.审批中.getCode().equals(auditStatus);
            AssetsBorrowDetail assetsBorrowDetail = null;
            if (detialId != null) {// 审批中 and 有id-->更新
                // 审批中 可以更新
                if (shenpi) {
                    assetsBorrowDetail = getById(detialId);
                    assetsBorrowDetail.setBorrowNumber(borrowDetailSaveParam.getApplyNumber());// 更新数量
                }
                // 驳回
                if (bohui) {
                    assetsBorrowDetail = createAssetsBorrowDetail(assetsApplyBase.getId(), borrowDetailSaveParam.getStockDataId(), borrowDetailSaveParam.getApplyNumber());
                }
            } else {// 其他新增
                assetsBorrowDetail = createAssetsBorrowDetail(assetsApplyBase.getId(), borrowDetailSaveParam.getStockDataId(), borrowDetailSaveParam.getApplyNumber());
            }

            boolean save = saveOrUpdate(assetsBorrowDetail);
            if (!save) {
                throw new ServiceException("保存借用明细失败，请重试！");
            }
        }

        assetsApplyBase.setApplyTotal(applyTotal);
        assetsApplyBaseService.updateById(assetsApplyBase);
        return assetsApplyBase;
    }

    @Override
    public AssetsApplyBase getByIdInfo(AssetsBorrowDetailGetByIdParam param) {

        String applyBaseId = param.getApplyBaseId();

        AssetsApplyBase assetsApplyBase = assetsApplyBaseService.getById(applyBaseId);
        if (null == assetsApplyBase) {
            throw new ServiceException("未查询到该申请单，请核实id信息！");
        }
        // 获取借用详情
        List<AssetsBorrowDetailAndStockDataDto> list = baseMapper.getDetialList(applyBaseId);
        for (AssetsBorrowDetailAndStockDataDto c : list) {
            String fullClassifyName = assetsClassifyInfoService.getFullClassifyByid(c.getClassifyId());
            c.setClassifyName(fullClassifyName);
        }
        assetsApplyBase.setDetailList(list);

        // 获取已领取
        List<AssetsBorrowDetailAndArchivesDataDto> fixedAssetsList = assetsBorrowDetailMapper.getAlreadyReceived(applyBaseId);

        for (AssetsBorrowDetailAndArchivesDataDto c : fixedAssetsList) {
            String fullClassifyName = assetsClassifyInfoService.getFullClassifyByid(c.getClassifyId());
            c.setClassifyName(fullClassifyName);
        }

        assetsApplyBase.setDealList(fixedAssetsList);

        return assetsApplyBase;

    }

    @Override
    public List<AssetsBorrowDetailGetNotGiveBackVo> getNotGiveBack(AssetsBorrowNotGiveBackParam param) {

        String assetsId = param.getAssetsId();
        String assetsName = param.getAssetsName();
        Long userId = SecurityUtils.getUserId();

        List<AssetsBorrowDetailGetNotGiveBackVo> voList = fixedAssetsArchivesBaseMapper.getNotGiveBack(userId, assetsId, assetsName, param.getUseScope());

        List<AssetsBorrowDetailGetNotGiveBackVo> notGiveBackVoList = voList.stream().map(vo -> {
            String classifyId = vo.getClassifyId();
            String fullClassifyName = assetsClassifyInfoService.getFullClassifyByid(classifyId);
            vo.setClassifyName(fullClassifyName);
            return vo;
        }).collect(Collectors.toList());

        return notGiveBackVoList;
    }

    //================================================================

    /**
     * @description: TODO  仓管-分配借取
     * @author: lyc
     * @date: 2023/2/1 11:44:05
     * @param: [param]
     * @return: boolean
     **/
    @Override
    @Transactional
    public boolean allotBorrow(AllotBorrowOutParam param) {

        List<AllotBorrowInParam> allotBorrowInParamList = param.getAllotBorrowInParamList();

        for (AllotBorrowInParam allotBorrowInParam : allotBorrowInParamList) {
            String detialId = allotBorrowInParam.getDetailId();
            String assetsId = allotBorrowInParam.getAssetsId();

            // 维护t_assets_borrow_detail表信息
            AssetsBorrowDetail assetsBorrowDetail = getById(detialId);
            Integer borrowNumber = assetsBorrowDetail.getBorrowNumber();// 原数量
            Integer newKeeperTreatedNumber = assetsBorrowDetail.getKeeperNumber() + 1;// 新数量
            if (newKeeperTreatedNumber > borrowNumber) {// 超出分发数量
                continue;
            }
            assetsBorrowDetail.setKeeperNumber(newKeeperTreatedNumber);

            // 维护t_assets_apply_base表信息
            String parentBaseId = assetsBorrowDetail.getParentBaseId();
            AssetsApplyBase assetsApplyBase = assetsApplyBaseService.getById(parentBaseId);
            Integer applyTotal = assetsApplyBase.getApplyTotal();// 申请总量
            Integer keeperTreatedNumber = assetsApplyBase.getKeeperTreatedNumber() + 1;
            if (keeperTreatedNumber > applyTotal) {// 超出总分配量
                continue;
            }
            assetsApplyBase.setKeeperTreatedNumber(keeperTreatedNumber);

            // 查询资产档案信息
            FixedAssetsArchivesBase fixedAssetsArchivesBase = fixedAssetsArchivesBaseService.getById(assetsId);

            // 新建t_assets_borrow_get_detail
            AssetsReceiveGetDetail assetsReceiveGetDetail = new AssetsReceiveGetDetail();
            assetsReceiveGetDetail.setBaseType(assetsApplyBase.getBaseType());
            assetsReceiveGetDetail.setParentBaseId(assetsBorrowDetail.getParentBaseId());
            assetsReceiveGetDetail.setParentDetailId(assetsBorrowDetail.getId());
            assetsReceiveGetDetail.setAssetsId(fixedAssetsArchivesBase.getId());
            assetsReceiveGetDetail.setStockDataId(fixedAssetsArchivesBase.getStockDataId());
            assetsReceiveGetDetail.setKeeperId(SecurityUtils.getUserId().intValue());
            assetsReceiveGetDetail.setKeeperName(SecurityUtils.getUsername());
            assetsReceiveGetDetail.setRecipientId(assetsApplyBase.getApplyUserId());
            assetsReceiveGetDetail.setRecipientName(assetsApplyBase.getApplyUserName());
            assetsReceiveGetDetail.setReceiveStatus("1");// 未接收
            assetsReceiveGetDetail.setSpecialStatus(0);// 未处理
            assetsReceiveGetDetail.setReceiveType("1");// 用户接收

            // 减库存
            // 组装变更记录保存信息
            StockChangeRecordInsertParam insertParam = new StockChangeRecordInsertParam();
            insertParam.setNumber(1);
            insertParam.setAssetsId(fixedAssetsArchivesBase.getId());
            insertParam.setOriginalId(assetsApplyBase.getId());
            insertParam.setChangeType("1");
            insertParam.setType("1");
            insertParam.setOriginalState(fixedAssetsArchivesBase.getStatus());// 还原状态
            insertParam.setStockDataId(Integer.parseInt(fixedAssetsArchivesBase.getStockDataId()));
            insertParam.setUsePersonName(assetsApplyBase.getApplyUserName());// 固定资产中的使用人
            insertParam.setUsePersonId(assetsApplyBase.getApplyUserId());// 固定资产中的使用人
            stockChangeRecordService.stockChangeRecordInsert(insertParam);


            // 统一进行更新
            updateById(assetsBorrowDetail);
            assetsApplyBaseService.updateById(assetsApplyBase);
            assetsReceiveGetDetailService.save(assetsReceiveGetDetail);
        }

        return true;
    }

    @Override
    public List<ArchivesGetByStockDataId> getArchivesDataByDetialId(StockDataByDetialIdParam param) {

        List<Integer> detialIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(param.getDetailIdList())) {
            String[] split = param.getDetailIdList().split(",");
            detialIdList = Arrays.asList(split).stream().map(x -> Integer.parseInt(x)).collect(Collectors.toList());// t_assets_borrow_detail的id
        }

        // 根据库存id 获取档案信息
        List<ArchivesGetByStockDataId> archives = fixedAssetsArchivesBaseMapper.getByStockDataId(param.getAssetsName(), param.getAssetsId(), param.getUseScope(), detialIdList);

        List<ArchivesGetByStockDataId> list = archives.stream().map(detial -> {
            String classifyName = assetsClassifyInfoService.getFullClassifyByid(detial.getClassifyId());
            detial.setClassifyName(classifyName);
            return detial;

        }).collect(Collectors.toList());

        return list;
    }

    /**
     * @description: TODO 仓管-获取需要分配的借用申请单
     * @author: lyc
     * @date: 2023/1/31 17:46:30
     * @param: [param]
     * @return: java.util.List
     **/
    @Override
    public List<AssetsApplyBase> getNeedAllotApplyBase(GetNeedAllotApplyBaseParam param) {

        QueryWrapper<AssetsApplyBase> query = Wrappers.query();

        String applyBaseId = param.getApplyBaseId();
        query.like(StrUtil.isNotBlank(applyBaseId), "id", applyBaseId);
        String applyLevel = param.getApplyLevel();
        query.eq(StrUtil.isNotBlank(applyLevel), "apply_level", applyLevel);
        query.eq("base_type", "6");// 默认资产借取
        query.eq("audit_status", "2");// 默认审核通过
        query.eq(com.ruoyi.common.core.utils.StringUtils.isNotEmpty(param.getUseScope()), "use_scope", param.getUseScope());
        Date startApplyTime = param.getStartApplyTime();
        Date endApplyTime = param.getEndApplyTime();
        if (startApplyTime != null && endApplyTime != null) {// 同时存在时间
            query.between("apply_time", startApplyTime, endApplyTime);
        }
        Set<String> roles = SecurityUtils.getLoginUser().getRoles();
        if (roles.contains("ckglx_office") && roles.contains("ckglx_project")) {// 判断是否是双料管理员
            query.in("use_scope", "0", "1");
        } else if (roles.contains("ckglx_office")) {// 判断是否是办公仓库管理员
            query.eq("use_scope", "0");
        } else if (roles.contains("ckglx_project")) {// 判断是否是项目仓库管理员
            query.eq("use_scope", "1");
        } else {// 用户
            return Collections.emptyList();
        }
        query.apply("keeper_treated_number < apply_total");
        query.orderByDesc("create_time");

        List<AssetsApplyBase> list = assetsApplyBaseService.list(query);

        return list;
    }

    @Override
    public List<AssetsBorrowDetailAndStockDataFinishDto> getDetailById(AssetsBorrowGetDetailByIdParam param) {

        String applyBaseId = param.getApplyBaseId();
        String assetsName = param.getAssetsName();

        List<AssetsBorrowDetailAndStockDataFinishDto> finishDto = assetsBorrowDetailMapper.selectNoFinish(applyBaseId, assetsName);
        List<AssetsBorrowDetailAndStockDataFinishDto> list = finishDto.stream().map(info -> {

            String classifyId = info.getClassifyId();
            String classifyName = assetsClassifyInfoService.getFullClassifyByid(classifyId);
            info.setClassifyName(classifyName);
            return info;

        }).collect(Collectors.toList());

        return list;
    }

}
