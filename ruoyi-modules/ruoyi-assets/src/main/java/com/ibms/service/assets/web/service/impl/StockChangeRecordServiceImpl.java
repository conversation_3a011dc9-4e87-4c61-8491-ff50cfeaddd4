package com.ibms.service.assets.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ibms.service.assets.extent.config.dict.AsstesDict;
import com.ibms.service.assets.web.controller.stockchangerecord.param.StockChangeRecordInsertParam;
import com.ibms.service.assets.web.controller.stockchangerecord.param.StockChangeRecordListParam;
import com.ibms.service.assets.web.controller.stockchangerecord.vo.StockChangeRecordListVo;
import com.ibms.service.assets.web.domain.AssetsApplyBase;
import com.ibms.service.assets.web.domain.FixedAssetsArchivesBase;
import com.ibms.service.assets.web.domain.StockChangeRecord;
import com.ibms.service.assets.web.domain.StockDataInfo;
import com.ibms.service.assets.web.mapper.stockchangerecord.StockChangeRecordMapper;
import com.ibms.service.assets.web.service.AssetsApplyBaseService;
import com.ibms.service.assets.web.service.FixedAssetsArchivesBaseService;
import com.ibms.service.assets.web.service.StockChangeRecordService;
import com.ibms.service.assets.web.service.StockDataInfoService;
import com.ruoyi.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class StockChangeRecordServiceImpl extends ServiceImpl<StockChangeRecordMapper, StockChangeRecord> implements StockChangeRecordService {


    @Autowired
    private StockDataInfoService stockDataInfoService;

    @Autowired
    private FixedAssetsArchivesBaseService fixedAssetsArchivesBaseService;

    @Autowired
    private AssetsApplyBaseService assetsApplyBaseService;


    @Override
    public List<StockChangeRecordListVo> selectList(StockChangeRecordListParam param) {
        return baseMapper.getList(param);
    }

    @Override
    @Transactional
    public int stockChangeRecordInsert(StockChangeRecordInsertParam param) {
        if (param.getStockDataId() == 23) {
            System.out.println("555");
        }
        StockDataInfo stockDataInfo = stockDataInfoService.getStockDataInfoForUpdateById(param.getStockDataId());// 获取库存数据
        if (null == stockDataInfo) {
            throw new ServiceException("当前库存数据不存在");
        }
        // todo 缺少固定资产添加到资产档案中
        StockChangeRecord stockChangeRecord = new StockChangeRecord();
        stockChangeRecord.setOriginalId(param.getOriginalId());
        stockChangeRecord.setChangeType(param.getChangeType());
        stockChangeRecord.setLastNumber(stockDataInfo.getStockNumber());
        stockChangeRecord.setChangedNumber(param.getNumber());
        stockChangeRecord.setStockDataId(String.valueOf(stockDataInfo.getId()));
        FixedAssetsArchivesBase archivesBase = new FixedAssetsArchivesBase();
        if ("1".equals(stockDataInfo.getType())) {// 固定资产默认为1
            if (param.getNumber() == null && param.getNumber() < 1) {
                param.setNumber(1);
            }
            archivesBase = fixedAssetsArchivesBaseService.getById(param.getAssetsId()); // 获取固定资产档案
            if (null != archivesBase) {
                stockChangeRecord.setAssetsId(archivesBase.getId());
            }
        }
        String type = param.getType(); // 类型：1使用，2入库，3维修，4领用或借用退库，5维修退库
        if ("1".equals(type)) {// 使用 使用中数量加改动量  库存量减少改动量
            int number = stockDataInfo.getStockNumber() - param.getNumber();
            if (number < 0) {
                throw new ServiceException("库存量不足！");
            }
            if ("1".equals(stockDataInfo.getType())) {// 固定资产
                stockDataInfo.setUseNumber(stockDataInfo.getUseNumber() + param.getNumber());
                archivesBase.setStatus("2");// 资产档案 默认给使用中状态
                AssetsApplyBase assetsApplyBase = assetsApplyBaseService.getById(param.getOriginalId());
                archivesBase.setUseUserId(Integer.parseInt(param.getUsePersonId()));// 使用人
                archivesBase.setUseUserName(param.getUsePersonName());
                archivesBase.setUseDeptId(Integer.parseInt(assetsApplyBase.getApplyDeptId()));// 使用部门id
                archivesBase.setUseDeptName(assetsApplyBase.getApplyDeptName());// 使用部门名称
                archivesBase.setUseTime(new Date());// 使用时间
                archivesBase.setUsePlace(param.getUsePlace());// 使用位置
            }
            stockDataInfo.setStockNumber(number);
        } else if ("2".equals(type)) {// 入库  库存量加改动量
            stockChangeRecord.setAssetsId("-");// 默认为空
            stockDataInfo.setStockNumber(stockDataInfo.getStockNumber() + param.getNumber());
        } else if ("3".equals(type)) {// 维修  未使用状态 库存表中加改动量 维修量加改动量
            stockDataInfo.setStockNumber(stockDataInfo.getStockNumber() - param.getNumber());
            stockDataInfo.setMaintenanceNumber(stockDataInfo.getMaintenanceNumber() + param.getNumber());
            archivesBase.setStatus("3");// 资产档案 默认给维修中状态
        } else if ("4".equals(type)) {// 领用或借用退库  库存表库存量加改动量 使用量减改动量
            stockDataInfo.setStockNumber(stockDataInfo.getStockNumber() + param.getNumber());
            if ("1".equals(stockDataInfo.getType())) {// 固定资产
                archivesBase.setStatus("1");// 资产档案 默认给未使用状态
                stockDataInfo.setUseNumber(stockDataInfo.getUseNumber() - param.getNumber());
                archivesBase.setUseUserId(null);// 使用人为null
                archivesBase.setUseUserName(null);
                archivesBase.setUseDeptId(null);// 使用部门id
                archivesBase.setUseDeptName(null);// 使用部门名称
                archivesBase.setUseTime(null);// 使用时间
                archivesBase.setUsePlace(null);// 使用位置
            }
        } else if ("5".equals(type)) {// 维修退库  库存表中 维修量减改动量  库存量加改动量
            stockDataInfo.setMaintenanceNumber(stockDataInfo.getMaintenanceNumber() - param.getNumber());
            stockDataInfo.setStockNumber(stockDataInfo.getStockNumber() + param.getNumber());
            archivesBase.setStatus(param.getOriginalState());// 资产档案 还原状态
            if (null != param.getChangeCost()) {// 是否改变原值
                archivesBase.setOriginalValueAmount(param.getChangeCost());
            }

        } else if ("6".equals(type)) {// 盘点丢失
            // 固资
            if ("1".equals(stockDataInfo.getType())) {
                String originalState = param.getOriginalState();
                if ("1".equals(originalState))// 未使用
                {
                    stockDataInfo.setStockNumber(stockDataInfo.getStockNumber() - param.getNumber());
                } else if ("2".equals(originalState))// 使用中
                {
                    stockDataInfo.setUseNumber(stockDataInfo.getUseNumber() - param.getNumber());
                } else if ("3".equals(originalState))// 维修中
                {
                    stockDataInfo.setMaintenanceNumber(stockDataInfo.getMaintenanceNumber() - param.getNumber());
                }
                archivesBase.setStatus("6");
            } else {// 耗材
                stockDataInfo.setStockNumber(param.getNumber());
                stockChangeRecord.setChangeAfterNumber(param.getNumber() - stockDataInfo.getStockNumber());
            }

        } else if (type.equals(AsstesDict.AssetsBaseType.资产_处置单.getCode())) {
            stockDataInfo.setStockNumber(stockDataInfo.getStockNumber() - param.getNumber());
            String originalState = param.getOriginalState();
            if ("1".equals(originalState))// 未使用
            {
                stockDataInfo.setStockNumber(stockDataInfo.getStockNumber() - param.getNumber());
            } else if ("2".equals(originalState))// 使用中
            {
                stockDataInfo.setUseNumber(stockDataInfo.getUseNumber() - param.getNumber());
            } else if ("3".equals(originalState))// 维修中
            {
                stockDataInfo.setMaintenanceNumber(stockDataInfo.getMaintenanceNumber() - param.getNumber());
            }
            archivesBase.setStatus("5");
        }
        if ("1".equals(stockDataInfo.getType())) {// 固定资产
            fixedAssetsArchivesBaseService.saveOrUpdate(archivesBase);
        }
        stockDataInfoService.updateById(stockDataInfo);
        if (stockChangeRecord.getChangedNumber() == null) {
            stockChangeRecord.setChangeAfterNumber(stockDataInfo.getStockNumber());
        }
        return baseMapper.insert(stockChangeRecord);
    }
}
