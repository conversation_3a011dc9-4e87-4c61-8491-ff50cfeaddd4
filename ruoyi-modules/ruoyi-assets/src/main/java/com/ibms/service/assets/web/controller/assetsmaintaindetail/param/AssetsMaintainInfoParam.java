package com.ibms.service.assets.web.controller.assetsmaintaindetail.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "维修单详情参数")
public class AssetsMaintainInfoParam {

    /**
     * 主键id
     */
    @NotNull(message = "id必填")
    @ApiModelProperty("基础表id")
    private String applyBaseId;
}
