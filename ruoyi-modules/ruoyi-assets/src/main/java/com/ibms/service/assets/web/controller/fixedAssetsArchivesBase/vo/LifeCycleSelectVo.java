package com.ibms.service.assets.web.controller.fixedAssetsArchivesBase.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 生命周期查询视图
 * @date 2023/01/04
 */
@Data
@ApiModel(value = "生命周期查询视图")
public class LifeCycleSelectVo {


    /**
     * 表单类型
     */
    @ApiModelProperty("表单类型")
    private String baseType;

    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    private String applyUserName;

    /**
     * 申请部门
     */
    @ApiModelProperty("申请部门")
    private String applyDeptName;

    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private String auditStatus;

    /**
     * 通过/驳回时间
     */
    @ApiModelProperty("通过/驳回时间")
    private String updateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String createTime;
//
//    /**
//     * 申请人
//     */
//    @ApiModelProperty("申请人")
//    private String applyUserName;


}
