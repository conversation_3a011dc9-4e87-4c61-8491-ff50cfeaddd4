package com.ibms.service.assets.web.mapper.cireturnstockdetail.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/3 15:16
 */
@ApiModel("退库单详情-耗材库存")
@Data
public class CiReturnStockDetailAndStockDto {

    @ApiModelProperty("库存id")
    private Integer stockDataId;

    /**
     * 使用范围（0办公，1项目）
     */
    @ApiModelProperty("使用范围（0办公，1项目）")
    private String useScope;

    /**
     * 资产名称
     */
    @ApiModelProperty("资产名称")
    private String name;

    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String specification;

    /**
     * 分类id
     */
    @ApiModelProperty("分类id")
    private String classifyId;

    /**
     * 分类名称
     */
    @ApiModelProperty("分类名称")
    private String classifyName;

    /**
     * 品牌
     */
    @ApiModelProperty("品牌")
    private String brand;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("退库数量")
    private String number;


}
