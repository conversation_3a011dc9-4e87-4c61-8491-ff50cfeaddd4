package com.ibms.service.assets.web.controller.assetsreceivedetail;

import com.ibms.service.assets.extent.flowable.extension.response.FlowBusinessForm;
import com.ibms.service.assets.web.controller.assetspurchaseorderdetail.param.PurchaseOrderDetailListByIdsParam;
import com.ibms.service.assets.web.controller.assetsreceivedetail.param.*;
import com.ibms.service.assets.web.controller.assetsreceivedetail.vo.AssetsGetNotReceivedGetDetialVo;
import com.ibms.service.assets.web.controller.assetsreceivedetail.vo.AssetsReceiveDetailListVo;
import com.ibms.service.assets.web.controller.cireceivedetail.param.GetNotReceivedParam;
import com.ibms.service.assets.web.controller.cireceivedetail.param.CiReceiveParam;
import com.ibms.service.assets.web.controller.cireceivedetail.param.KeeperNoAllotDetailParam;
import com.ibms.service.assets.web.controller.cireceivedetail.param.KeeperNoAllotParam;
import com.ibms.service.assets.web.domain.AssetsApplyBase;
import com.ibms.service.assets.web.mapper.assetsreceivedetail.dto.AssetsReceiveDetailAndStockDataDto;
import com.ibms.service.assets.web.mapper.assetsreceivedetail.dto.AssetsReceiveDetailAndStockDataFinishDto;
import com.ibms.service.assets.web.mapper.assetsreceivedetail.dto.FixedAssetsByStockDataIdListDto;
import com.ibms.service.assets.web.mapper.assetsreceivegetdetail.dto.AssetsReceiveGetDetailAlreadyDto;
import com.ibms.service.assets.web.mapper.cireceivedetail.dto.KeeperNoAllotDetailDto;
import com.ibms.service.assets.web.service.AssetsClassifyInfoService;
import com.ibms.service.assets.web.service.AssetsReceiveDetailService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 资产-领用单-列表明细
 * <AUTHOR>
 * @Date 2022/12/30 16:50
 */
@Api(tags = "资产领用单-列表明细信息")
@RestController
@RequestMapping("/assetsReceiveDetail")
public class AssetsReceiveDetailController extends BaseController {

    @Autowired
    private AssetsReceiveDetailService assetsReceiveDetailService;

    @Autowired
    private AssetsClassifyInfoService assetsClassifyInfoService;

    @PostMapping("/save")
    @ApiOperation(value = "保存(更新)-资产领用单")
    public TAjaxResult save(@Validated @RequestBody AssetsReceiveBaseAndDetailSaveParam param) {

        AssetsApplyBase assetsApplyBase = assetsReceiveDetailService.saveDetailAndBase(param);

        FlowBusinessForm flowBusinessForm = new FlowBusinessForm();
        flowBusinessForm.setBusinessId(assetsApplyBase.getId());
        flowBusinessForm.setBusinessTable("t_assets_apply_base");

        return new TAjaxResult<>().success(flowBusinessForm);
    }

    @ApiOperation(value = "根据id获取资产领用单")
    @GetMapping("/getById")
    public TAjaxResult<AssetsApplyBase<AssetsReceiveDetailAndStockDataDto, AssetsReceiveGetDetailAlreadyDto>> getById(@Validated AssetsReceiveDetailGetByIdParam param) {

        AssetsApplyBase assetsApplyBase = assetsReceiveDetailService.getByIdInfo(param);

        return new TAjaxResult().success(assetsApplyBase);

    }

    /**
     * 需要添加仓管权限
     *
     * @param param
     * @return
     */
    @ApiOperation(value = "获取仓管未分配完成的固定资产申请单列表")
    @GetMapping("/getAssetsKeeperNoAllotApply")
    public TableDataInfo<List<AssetsApplyBase>> getAssetsKeeperNoAllotApply(@Validated AssetsKeeperNoAllotParam param) {

        startPage();

        List<AssetsApplyBase> list = assetsReceiveDetailService.getAssetsKeeperNoAllotApply(param);

        return getDataTable(list);

    }


    @ApiOperation(value = "根据BaseId-获取资产领用单详细内容列表（个人-资产领取-领用-第一步）")
    @GetMapping("/getDetailById")
    public TAjaxResult<List<AssetsReceiveDetailAndStockDataFinishDto>> getDetailById(@Validated AssetsReceiveDetailGetByIdParam param) {

        List<AssetsReceiveDetailAndStockDataFinishDto> list = assetsReceiveDetailService.getDetailById(param);
        for (AssetsReceiveDetailAndStockDataFinishDto dto : list) {
            dto.setClassifyName(assetsClassifyInfoService.getFullClassifyByid(dto.getClassifyId()));
        }

        return new TAjaxResult().success(list);

    }

    /**
     * 资产领取下一步获取固定资产的列表
     *
     * @param param
     * @return
     */
    @ApiOperation(value = "资产领取下一步获取固定资产的列表")
    @GetMapping("/getFixedAssetsByStockDataIdList")
    public TableDataInfo<List<FixedAssetsByStockDataIdListDto>> getFixedAssetsByStockDataIdList(@Validated FixedAssetsByStockDataIdParam param) {

        startPage();
        List<FixedAssetsByStockDataIdListDto> list = assetsReceiveDetailService.getFixedAssetsByStockDataIdList(param);
        for (FixedAssetsByStockDataIdListDto dto : list) {
            dto.setClassifyName(assetsClassifyInfoService.getFullClassifyByid(dto.getClassifyId()));
        }
        return getDataTable(list);

    }


    @PostMapping("/keeperAllot")
    @ApiOperation(value = "仓管-分配（个人代办-资产领取-领用）")
    public AjaxResult keeperAllot(@Validated @RequestBody KeeperAllotParan param) {

        boolean b = assetsReceiveDetailService.keeperAllot(param);

        return toAjax(b);
    }

}
