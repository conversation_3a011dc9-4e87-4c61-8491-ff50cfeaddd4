package com.ibms.service.assets.web.mapper.fixedAssetsArchivesBase.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("固资使用率返回值")
@Data
public class AssetsUsePercentDto {

    @ApiModelProperty(value = "资产数量")
    private Integer assetsTotalNumber;

    @ApiModelProperty(value = "库存数量")
    private Integer stockTotalNumber;

    @ApiModelProperty(value = "使用数量")
    private Integer useTotalNumber;

    @ApiModelProperty(value = "使用率")
    private String usePercent;


}
