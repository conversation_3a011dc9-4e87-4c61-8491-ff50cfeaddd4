package com.ibms.service.assets.extent.flowable.flow.mapstruct;

import com.ibms.service.assets.extent.flowable.extension.domain.TaskDefExtension;
import com.ibms.service.assets.extent.flowable.extension.service.dto.TaskDefExtensionDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {})
public interface TaskDefExtensionWrapper extends EntityWrapper<TaskDefExtensionDTO, TaskDefExtension> {

    TaskDefExtensionWrapper INSTANCE = Mappers.getMapper(TaskDefExtensionWrapper.class);

}
