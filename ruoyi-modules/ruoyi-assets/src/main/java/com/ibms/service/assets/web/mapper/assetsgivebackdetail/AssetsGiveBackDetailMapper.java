package com.ibms.service.assets.web.mapper.assetsgivebackdetail;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ibms.service.assets.web.domain.AssetsGiveBackDetail;
import com.ibms.service.assets.web.mapper.assetsgivebackdetail.dto.AssetsGiveBackDetailAndArchivesDataDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 固定资产归还列表明细
 * <AUTHOR>
 * @Date 2023/1/4 11:39
 */
@Mapper
public interface AssetsGiveBackDetailMapper extends BaseMapper<AssetsGiveBackDetail> {

    /**
     * 根据Baseid获取归还单详情
     *
     * @param applyBaseId
     * @return
     */
    List<AssetsGiveBackDetailAndArchivesDataDto> getDetialList(@Param("applyBaseId") String applyBaseId);
}
