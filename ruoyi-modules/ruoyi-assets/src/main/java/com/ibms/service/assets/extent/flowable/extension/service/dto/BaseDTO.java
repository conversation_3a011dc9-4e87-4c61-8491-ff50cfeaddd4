package com.ibms.service.assets.extent.flowable.extension.service.dto;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public abstract class BaseDTO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    protected String id;

    /**
     * 创建日期
     */
    protected Date createTime;

    /**
     * 创建人
     */
    protected String createBy;

    /**
     * 更新日期
     */
    protected Date updateTime;

    /**
     * 更新人
     */
    protected String updateBy;

    /**
     * 逻辑删除标记
     */
    protected Integer delFlag;

    /**
     * 构造函数
     */
    public BaseDTO() {

    }

    /**
     * 构造函数
     *
     * @param id
     */
    public BaseDTO(String id) {
        this.id = id;
    }


}

