package com.ibms.service.assets.web.controller.assetsmaintaindetail.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ibms.service.assets.web.domain.AssetsMaintainDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "资产维修明细列表详情")
public class AssetsMaintainDetailListVo extends AssetsMaintainDetail {


    /**
     * 资产名称
     */
    @ApiModelProperty("资产名称")
    private String name;
    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String specification;

    /**
     * 原值金额
     */
    @ApiModelProperty("原值金额")
    private BigDecimal originalValueAmount;

    /**
     * 品牌
     */
    @ApiModelProperty("品牌")
    private String brand;

    /**
     * 资产来源
     */
    @ApiModelProperty("资产来源，字典类型（assets_source）：1.接受捐赠、2.购买")
    private String assetsSource;

    /**
     * 供应商
     */
    @ApiModelProperty("供应商")
    private String supplier;


    /**
     * 购入时间
     */
    @ApiModelProperty("购入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date purchaseTime;

    /**
     * 保修情况
     */
    @ApiModelProperty("保修情况")
    private String warrantyCondition;


}
