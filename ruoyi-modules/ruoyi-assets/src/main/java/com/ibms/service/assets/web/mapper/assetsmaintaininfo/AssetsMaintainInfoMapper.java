package com.ibms.service.assets.web.mapper.assetsmaintaininfo;

import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.assets.web.controller.assetsmaintaininfo.vo.AssetsMaintainInfoGetByIdVo;
import com.ibms.service.assets.web.domain.AssetsMaintainInfo;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 固定资产保养
 * @date 2023/01/04
 */
@Mapper
public interface AssetsMaintainInfoMapper extends MPJBaseMapper<AssetsMaintainInfo> {
    AssetsMaintainInfoGetByIdVo getInfoById(String id);
}
