package com.ibms.service.assets.web.controller.assetsInventoryDetail.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 固资盘点已办列表
 * @date 2023/01/04
 */
@Data
@ApiModel(value = "固资盘点已办列表")
public class CompletedListVo extends WaitingProcessListVo {

    /**
     * 实际盘点时间
     */
    @ApiModelProperty("实际盘点时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inventoryTime;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String assetsStatus;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 原值金额
     */
    @ApiModelProperty("原值金额")
    private BigDecimal originalValueAmount;

    /**
     * 购入时间
     */
    @ApiModelProperty("购入时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date purchaseTime;

    /**
     * 保修情况
     */
    @ApiModelProperty("保修情况")
    private String warrantyCondition;

    /**
     * 供应商
     */
    @ApiModelProperty("供应商")
    private String supplier;

    /**
     * 资产来源
     */
    @ApiModelProperty("资产来源")
    private String assetsSource;

    /**
     * 基础表id（个人盘点时生成）
     */
    @ApiModelProperty("基础表id（个人盘点时生成）")
    private String dealBaseId;
}
