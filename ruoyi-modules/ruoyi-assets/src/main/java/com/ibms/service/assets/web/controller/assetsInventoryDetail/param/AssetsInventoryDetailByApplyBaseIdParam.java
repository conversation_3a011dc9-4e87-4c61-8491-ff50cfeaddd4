package com.ibms.service.assets.web.controller.assetsInventoryDetail.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @description
 * @date 2022/12/30
 */
@Data
@ApiModel(value = "根据applyBaseId获取数据")
public class AssetsInventoryDetailByApplyBaseIdParam {

    @NotNull(message = "applyBaseId必填")
    @ApiModelProperty("applyBaseId对应base表主键id")
    private Integer applyBaseId;

}
