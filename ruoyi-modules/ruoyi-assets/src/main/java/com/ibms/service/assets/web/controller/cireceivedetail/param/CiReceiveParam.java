package com.ibms.service.assets.web.controller.cireceivedetail.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/10 11:50
 */
@Data
@ApiModel(value = "耗材接收接口参数")
public class CiReceiveParam {

    @ApiModelProperty("申请单BaseId")
    private String applyBaseId;

    @ApiModelProperty("耗材接收Id")
    private List<Integer> ciReceiveGetDetailIdList;


}
