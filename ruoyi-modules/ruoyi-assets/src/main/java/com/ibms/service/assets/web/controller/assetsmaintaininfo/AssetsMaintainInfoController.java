package com.ibms.service.assets.web.controller.assetsmaintaininfo;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.ibms.service.assets.web.controller.assetsmaintaininfo.param.AssetsMaintainInfoByIdParam;
import com.ibms.service.assets.web.controller.assetsmaintaininfo.param.AssetsMaintainInfoListParam;
import com.ibms.service.assets.web.controller.assetsmaintaininfo.vo.AssetsMaintainInfoGetByIdVo;
import com.ibms.service.assets.web.domain.AssetsMaintainInfo;
import com.ibms.service.assets.web.domain.FixedAssetsArchivesBase;
import com.ibms.service.assets.web.service.AssetsMaintainInfoService;
import com.ibms.service.assets.web.service.FixedAssetsArchivesBaseService;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 固定资产保养
 * <AUTHOR> @Date 2023/1/5 17:09
 */
@Api(tags = "固定资产保养")
@RestController
@RequestMapping("/assetsMaintainInfo")
public class AssetsMaintainInfoController extends BaseController {

    @Autowired
    private AssetsMaintainInfoService assetsMaintainInfoService;

    @Autowired
    private FixedAssetsArchivesBaseService fixedAssetsArchivesBaseService;

    @ApiOperation(value = "固定资产保养分页列表")
    @GetMapping("/list")
    public TableDataInfo<AssetsMaintainInfo> list(AssetsMaintainInfoListParam param) {
        startPage();
        List<AssetsMaintainInfo> list = assetsMaintainInfoService.selectList(param);
        list.stream().forEach(a -> {
            FixedAssetsArchivesBase fixedAssetsArchivesBase = fixedAssetsArchivesBaseService.getById(a.getAssetsId());
            a.setUseScope(fixedAssetsArchivesBase.getUseScope());
        });

        return getDataTable(list);
    }


    @ApiOperation(value = "固定资产保养保存")
    @PostMapping("/save")
    public AjaxResult save(@Validated @RequestBody AssetsMaintainInfo assetsMaintainInfo) {
        if (StringUtils.isEmpty(assetsMaintainInfo.getId())) {
            Snowflake snowflake = IdUtil.getSnowflake(1, 1);
            long id = snowflake.nextId();
            assetsMaintainInfo.setId(String.valueOf(id));
        }


        boolean b = assetsMaintainInfoService.saveOrUpdate(assetsMaintainInfo);
        if (b) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error("失败！");
        }
    }


    @ApiOperation(value = "根据id获取详情")
    @GetMapping("/getById")
    public TAjaxResult<AssetsMaintainInfoGetByIdVo> getById(@Validated AssetsMaintainInfoByIdParam param) {

        AssetsMaintainInfoGetByIdVo byId = assetsMaintainInfoService.getInfoById(param.getId());

        return new TAjaxResult().success(byId);

    }

    @ApiOperation(value = "根据id删除详情")
    @DeleteMapping("/deleteById")
    public AjaxResult deleteById(@Validated AssetsMaintainInfoByIdParam param) {
        String id = param.getId();
        boolean b = assetsMaintainInfoService.removeById(id);
        return toAjax(b);

    }


}
