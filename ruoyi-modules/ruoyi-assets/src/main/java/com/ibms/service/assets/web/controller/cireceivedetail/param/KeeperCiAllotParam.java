package com.ibms.service.assets.web.controller.cireceivedetail.param;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/6 10:56
 */
@Data
@ApiModel(value = "仓管分配耗材-参数")
public class KeeperCiAllotParam {

    @ApiModelProperty("耗材领用列表明细id")
    private List<CiReceiveDetailSaveAllotParam> ciReceiveDetailList;

}
