package com.ibms.service.assets.web.controller.assetsreceivedetail.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/6 10:56
 */
@Data
@ApiModel(value = "获取仓管未分配完成的固定资产详情列表")
public class AssetsKeeperNoAllotDetailParam {

    /**
     * 申请表id
     */
    @NotBlank(message = "申请表id必填")
    @ApiModelProperty("申请表id")
    private String applyBaseId;

    /**
     * id(单号)
     */
    @ApiModelProperty("资产名称")
    private String name;

    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String specification;


}
