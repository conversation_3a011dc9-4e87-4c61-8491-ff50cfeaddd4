/**
 * Copyright © 2021-2026 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.ibms.service.assets.extent.flowable.extension.controller.condition;

import com.ibms.service.assets.extent.flowable.extension.domain.Condition;
import com.ibms.service.assets.extent.flowable.extension.service.ConditionService;
import com.ibms.service.assets.extent.flowable.extension.controller.condition.param.ConditionDeleteIdsParam;
import com.ibms.service.assets.extent.flowable.extension.controller.condition.param.ConditionGetByIdParam;
import com.ibms.service.assets.extent.flowable.extension.controller.condition.param.ConditionListParam;
import com.ibms.service.assets.extent.flowable.extension.controller.condition.param.ConditionSavaOrUpdateParam;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 流程表达式Controller
 *
 * <AUTHOR>
 * @version 2021-09-29
 */
@Api(tags = "工作流-流程表达式")
@RestController
@RequestMapping("/extension/condition")
public class ConditionController extends BaseController {

    @Autowired
    private ConditionService conditionService;

    /**
     * 流程表达式列表数据
     */
    @GetMapping("/list")
    @ApiOperation(value = "流程表达式列表")
    public TableDataInfo<Condition> list(@Validated ConditionListParam param) {

        startPage();
        List<Condition> list = conditionService.list(param);

        return getDataTable(list);
    }

    /**
     * 根据id查询数据
     *
     * @return
     */
    @GetMapping("queryById")
    @ApiOperation(value = "根据id获取流程表达式")
    public TAjaxResult<Condition> queryById(@Validated ConditionGetByIdParam param) {

        Condition condition = conditionService.getById(param);

        return new TAjaxResult().success(condition);
    }

    /**
     * 保存流程表达式
     */
    @PostMapping("save")
    @ApiOperation(value = "保存/更新流程表达式")
    public AjaxResult saveOrUpdate(@RequestBody @Validated ConditionSavaOrUpdateParam param) {

        // 新增或编辑表单保存
        Integer num = conditionService.saveOrUpdate(param);// 保存

        return toAjax(num);
    }

    /**
     * 批量删除流程表达式
     */
    @DeleteMapping("delete")
    @ApiOperation(value = "批量删除流程表达式")
    public AjaxResult delete(@Validated ConditionDeleteIdsParam param) {

        Integer num = conditionService.deleteByIds(param);

        return toAjax(num);
    }


}
