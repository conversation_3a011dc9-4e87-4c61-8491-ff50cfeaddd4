package com.ibms.service.assets.web.controller.assetsclassifyinfo;

import cn.hutool.core.lang.tree.Tree;
import com.ibms.service.assets.web.controller.assetsclassifyinfo.param.AssetsClassifyByIdParam;
import com.ibms.service.assets.web.controller.assetsclassifyinfo.param.AssetsClassifyByParentParam;
import com.ibms.service.assets.web.controller.assetsclassifyinfo.param.AssetsClassifyListParam;
import com.ibms.service.assets.web.controller.assetsclassifyinfo.param.AssetsClassifySaveParam;
import com.ibms.service.assets.web.domain.AssetsClassifyInfo;
import com.ibms.service.assets.web.service.AssetsClassifyInfoService;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/assetsClassifyInfo")
@Api(tags = "资产分类")
public class AssetsClassifyInfoController extends BaseController {


    @Autowired
    private AssetsClassifyInfoService assetsClassifyInfoService;


    @ApiOperation(value = "资产分类分页列表")
    @GetMapping("/list")
    public TableDataInfo<AssetsClassifyInfo> list(AssetsClassifyListParam param) {
        startPage();
        List<AssetsClassifyInfo> list = assetsClassifyInfoService.selectList(param);
        return getDataTable(list);
    }

    @ApiOperation(value = "资产分类树形数据")
    @GetMapping("/treeData")
    public TAjaxResult<AssetsClassifyInfo> treeData(AssetsClassifyListParam param) {
        List<Tree<String>> list = assetsClassifyInfoService.treeData(param);
        return new TAjaxResult().success(list);
    }


    @ApiOperation(value = "根据父id获取详情（动态加载）")
    @GetMapping("/getInfoByParentId")
    public TAjaxResult<AssetsClassifyInfo> getInfoByParentId(@Validated AssetsClassifyByParentParam param) {

        List<AssetsClassifyInfo> byId = assetsClassifyInfoService.getInfoByParentId(param.getParentId());

        return new TAjaxResult().success(byId);

    }


    @ApiOperation(value = "资产分类保存")
    @PostMapping("/save")
    public AjaxResult save(@Validated @RequestBody AssetsClassifySaveParam param) {

        AssetsClassifyInfo assetsClassifyInfo = new AssetsClassifyInfo();
        BeanUtils.copyProperties(param, assetsClassifyInfo);
        int i = assetsClassifyInfoService.saveAndUpdate(assetsClassifyInfo);
        return toAjax(i);
    }


    @ApiOperation(value = "根据id获取详情")
    @GetMapping("/getById")
    public TAjaxResult<AssetsClassifyInfo> getById(@Validated AssetsClassifyByIdParam param) {

        AssetsClassifyInfo byId = assetsClassifyInfoService.getById(param.getId());
        if (!"0".equals(byId.getParentId())) {// 不为一级的查询父级名称
            byId.setParentName(assetsClassifyInfoService.getById(byId.getParentId()).getName());
        }
        return new TAjaxResult().success(byId);

    }

    @ApiOperation(value = "根据id删除详情")
    @DeleteMapping("/deleteById")
    public AjaxResult deleteById(@Validated AssetsClassifyByIdParam param) {

        List<AssetsClassifyInfo> infoList = assetsClassifyInfoService.getInfoByParentId(param.getId());
        if (infoList.size() > 0) {
            throw new ServiceException("当前数据有子级，不可删除！");
        }
        boolean b = assetsClassifyInfoService.removeById(param.getId());

        return toAjax(b);

    }


}
