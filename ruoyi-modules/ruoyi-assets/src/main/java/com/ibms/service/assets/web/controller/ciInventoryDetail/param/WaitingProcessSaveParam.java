package com.ibms.service.assets.web.controller.ciInventoryDetail.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 个人盘点登记传参(直接修改库存)
 * @date 2023/01/04
 */
@Data
@ApiModel(value = "个人耗资盘点登记传参")
public class WaitingProcessSaveParam {
    /**
     * 实盘量
     */
    @ApiModelProperty("实盘量")
    @NotNull
    private Integer firmOfferNumber;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 实际盘点时间
     */
    @ApiModelProperty("实际盘点时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull
    private Date inventoryTime;

    /**
     * 耗资盘点列表明细id
     */
    @ApiModelProperty("耗资盘点列表明细id")
    @NotNull
    private Integer ciId;
}
