package com.ibms.service.assets.extent.flowable.extension.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/12/6 16:44
 */
@Data
@ApiModel("流程表单")
@TableName("act_extension_form_def")
public class FormDefinition implements Serializable {

    private static final long serialVersionUID = -1766969318217101696L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 备注信息
     */
    @ApiModelProperty("备注信息")
    private String remarks;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    @ApiModelProperty("逻辑删除标记（0：显示；1：隐藏")
    private String delFlag;

    /**
     * 外键
     */
    @ApiModelProperty("外键")
    private String categoryId;

    /**
     * 表单名称
     */
    @ApiModelProperty("表单名称")
    private String name;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private String version;

}
