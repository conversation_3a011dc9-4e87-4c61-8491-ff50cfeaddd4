package com.ibms.service.assets.web.controller.assetsreturnstockdetail.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("查询参数")
public class SelectParam {
    /**
     * 资产名称
     */
    @ApiModelProperty("资产名称")
    private String assetsName;

    /**
     * id(资产编号)
     */
    @ApiModelProperty("id(资产编号)")
    private String id;

    /**
     * 使用范围（0办公，1项目）
     */
    @ApiModelProperty("使用范围（0办公，1项目）")
    private String useScope;
}
