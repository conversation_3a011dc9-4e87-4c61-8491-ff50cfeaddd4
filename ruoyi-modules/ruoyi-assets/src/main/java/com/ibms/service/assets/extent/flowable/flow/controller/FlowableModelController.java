package com.ibms.service.assets.extent.flowable.flow.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.ibms.service.assets.extent.flowable.flow.controller.param.FlowableModelListParam;
import com.ibms.service.assets.extent.flowable.flow.model.FlowModel;
import com.ibms.service.assets.extent.flowable.flow.service.FlowableModelService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.SysUser;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.RepositoryService;
import org.flowable.idm.engine.impl.persistence.entity.UserEntityImpl;
import org.flowable.ui.common.model.UserRepresentation;
import org.flowable.ui.common.service.exception.BadRequestException;
import org.flowable.ui.common.service.exception.ConflictingRequestException;
import org.flowable.ui.modeler.domain.Model;
import org.flowable.ui.modeler.model.ModelKeyRepresentation;
import org.flowable.ui.modeler.model.ModelRepresentation;
import org.flowable.ui.modeler.serviceapi.ModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.UUID;

/**
 * 流程模型相关Controller
 *
 * <AUTHOR>
 * @version 2021-07-29
 */
@Api(tags = "工作流-流程模型相关")
@RestController
@RequestMapping("/flowable/model")
public class FlowableModelController extends BaseController {

    @Autowired
    protected ModelService modelService;
    ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    private FlowableModelService flowableModelService;
    @Autowired
    private RepositoryService repositoryService;

    @GetMapping(value = "/account", produces = "application/json")
    public UserRepresentation account() {
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        UserEntityImpl fUser = new UserEntityImpl();

        fUser.setId(sysUser.getUserId().toString());
        fUser.setFirstName(sysUser.getNickName());
        fUser.setLastName("");
        fUser.setEmail(sysUser.getEmail());

        UserRepresentation userRepresentation = new UserRepresentation(fUser);

        return userRepresentation;
    }

    /**
     * 流程模型列表
     */
    @GetMapping("/list")
    public TableDataInfo<FlowModel> data(FlowableModelListParam param, HttpServletRequest request) {

        startPage();
        List list = flowableModelService.getModels(param, "modifiedDesc", 0, request);

        return getDataTable(list);
    }

    /**
     * 导出model的xml文件
     */
    @GetMapping("/getBpmnXml")
    public String export(String id, HttpServletResponse response) {

        String export = flowableModelService.export(id, response);

        return export;
    }

    /**
     * 更新Model分类
     */
    @PutMapping(value = "/updateCategory")
    public ResponseEntity updateCategory(String id, String category) {
        repositoryService.setProcessDefinitionCategory(id, category);
        return ResponseEntity.ok("设置成功，模块ID=" + id);
    }

    /**
     * 删除Model
     *
     * @param ids
     * @return
     */
    @DeleteMapping("delete")
    public ResponseEntity deleteAll(String ids) {
        String[] idArray = ids.split(",");
        for (String id : idArray) {
            flowableModelService.delete(id);
        }
        return ResponseEntity.ok("删除成功!");
    }

    /**
     * 根据Model复制流程
     */
    @GetMapping("copy")
    public ResponseEntity copy(String id) throws Exception {

        org.flowable.ui.modeler.domain.Model sourceModel = modelService.getModel(id);
        ModelRepresentation modelRepresentation = new ModelRepresentation();
        modelRepresentation.setKey("Process_" + UUID.randomUUID());
        modelRepresentation.setName(sourceModel.getName() + "_copy");
        modelRepresentation.setModelType(0);
        modelRepresentation.setDescription("");
        modelRepresentation.setKey(modelRepresentation.getKey().replaceAll(" ", ""));
        this.checkForDuplicateKey(modelRepresentation);
        String json = modelService.createModelJson(modelRepresentation);

        // 新版不再使用model对象，使用用户id
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();

        UserEntityImpl fUser = new UserEntityImpl();
        fUser.setId(sysUser.getUserId().toString());
        fUser.setFirstName(sysUser.getNickName());
        fUser.setLastName("");
        fUser.setEmail(sysUser.getEmail());

        org.flowable.ui.modeler.domain.Model newModel = modelService.createModel(modelRepresentation, json, fUser);
        String modelId = newModel.getId();

        ObjectNode sourceObjectNode = (ObjectNode) objectMapper.readTree(sourceModel.getModelEditorJson());
        ObjectNode editorNode = sourceObjectNode.deepCopy();
        ObjectNode properties = objectMapper.createObjectNode();
        properties.put("process_id", newModel.getKey());
        properties.put("name", newModel.getName());
        editorNode.set("properties", properties);

        newModel.setModelEditorJson(editorNode.toString());

        modelService.saveModel(modelId, newModel.getName(), newModel.getKey(), newModel.getDescription(), newModel.getModelEditorJson(), true, "", fUser);

        return ResponseEntity.ok("拷贝成功!");
    }

    /**
     * 根据Model部署流程
     */
    @PutMapping("deploy")
    public ResponseEntity deploy(String id, String category) {
        String result = flowableModelService.deploy(id, category);
        return ResponseEntity.ok(result);
    }

    @PostMapping(value = {"/rest/models"}, produces = {"application/json"})
    public ModelRepresentation createModel(@RequestBody ModelRepresentation modelRepresentation) {
        modelRepresentation.setKey(modelRepresentation.getKey().replaceAll(" ", ""));
        this.checkForDuplicateKey(modelRepresentation);
        String json = this.modelService.createModelJson(modelRepresentation);

        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();

        UserEntityImpl fUser = new UserEntityImpl();
        fUser.setId(sysUser.getUserId().toString());
        fUser.setFirstName(sysUser.getNickName());
        fUser.setLastName("");
        fUser.setEmail(sysUser.getEmail());

        Model newModel = this.modelService.createModel(modelRepresentation, json, fUser);
        return new ModelRepresentation(newModel);
    }


    /**
     * 根据id保存
     *
     * @param modelId 模型id
     * @param values
     * @return
     */
    @PostMapping("/saveModel/{modelId}")
    public ModelRepresentation saveModel(@PathVariable String modelId, @RequestBody MultiValueMap<String, String> values) {

        ModelRepresentation modelRepresentation = flowableModelService.saveModel(modelId, values);

        return modelRepresentation;
    }


    protected ModelRepresentation updateModel(Model model, MultiValueMap<String, String> values, boolean forceNewVersion) {
        String name = values.getFirst("name");
        String key = (values.getFirst("key")).replaceAll(" ", "");
        String description = values.getFirst("description");
        String isNewVersionString = values.getFirst("newversion");
        String newVersionComment = null;
        ModelKeyRepresentation modelKeyInfo = this.modelService.validateModelKey(model, model.getModelType(), key);
        if (modelKeyInfo.isKeyAlreadyExists()) {
            throw new BadRequestException("Model with provided key already exists " + key);
        } else {
            boolean newVersion = false;
            if (forceNewVersion) {
                newVersion = true;
                newVersionComment = values.getFirst("comment");
            } else if (isNewVersionString != null) {
                newVersion = "true".equals(isNewVersionString);
                newVersionComment = values.getFirst("comment");
            }

            String json = values.getFirst("json_xml");
            json = this.flowableModelService.changeXmlToJson(json);

            try {
                ObjectNode editorJsonNode = (ObjectNode) this.objectMapper.readTree(json);
                ObjectNode propertiesNode = (ObjectNode) editorJsonNode.get("properties");
                propertiesNode.put("process_id", key);
                propertiesNode.put("name", name);
                if (StringUtils.isNotEmpty(description)) {
                    propertiesNode.put("documentation", description);
                }

                editorJsonNode.set("properties", propertiesNode);
                model = this.modelService.saveModel(model.getId(), name, key, description, editorJsonNode.toString(), newVersion, newVersionComment, org.flowable.ui.common.security.SecurityUtils.getCurrentUserObject());
                return new ModelRepresentation(model);
            } catch (Exception var15) {
                throw new BadRequestException("Process model could not be saved " + model.getId());
            }
        }
    }

    protected void checkForDuplicateKey(ModelRepresentation modelRepresentation) {
        ModelKeyRepresentation modelKeyInfo = this.modelService.validateModelKey((Model) null, modelRepresentation.getModelType(), modelRepresentation.getKey());
        if (modelKeyInfo.isKeyAlreadyExists()) {
            throw new ConflictingRequestException("Provided model key already exists: " + modelRepresentation.getKey());
        }
    }

    protected ModelRepresentation createNewModel(String name, String description, Integer modelType, String editorJson) {
        ModelRepresentation model = new ModelRepresentation();
        model.setName(name);
        model.setDescription(description);
        model.setModelType(modelType);
        Model newModel = this.modelService.createModel(model, editorJson, org.flowable.ui.common.security.SecurityUtils.getCurrentUserObject());
        return new ModelRepresentation(newModel);
    }

}
