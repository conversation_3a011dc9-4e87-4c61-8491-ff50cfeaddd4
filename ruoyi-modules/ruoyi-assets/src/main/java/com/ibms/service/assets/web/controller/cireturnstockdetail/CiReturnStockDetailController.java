package com.ibms.service.assets.web.controller.cireturnstockdetail;

import com.ibms.service.assets.web.controller.cireturnstockdetail.param.CiAlreadyReceiveParam;
import com.ibms.service.assets.web.controller.cireturnstockdetail.param.CiReturnStockBaseAndDetailSaveParam;
import com.ibms.service.assets.web.controller.cireturnstockdetail.param.CiReturnStockDetailGetByIdParam;
import com.ibms.service.assets.web.domain.AssetsApplyBase;
import com.ibms.service.assets.web.mapper.assetsgivebackdetail.dto.AssetsGiveBackDetailAndArchivesDataDto;
import com.ibms.service.assets.web.mapper.cireceivegetdetail.dto.CiAlreadyReceiveDto;
import com.ibms.service.assets.web.service.CiReturnStockDetailService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/13 17:17
 */
@Api(tags = "耗材退库单")
@RestController
@RequestMapping("/ciReturnStockDetail")
public class CiReturnStockDetailController extends BaseController {

    @Autowired
    private CiReturnStockDetailService ciReturnStockDetailService;

    @PostMapping("/save")
    @ApiOperation(value = "保存(更新)-耗材退库单")
    public AjaxResult save(@Validated @RequestBody CiReturnStockBaseAndDetailSaveParam param) {

        boolean b = ciReturnStockDetailService.saveDetailAndBase(param);

        return toAjax(b);
    }

    @ApiOperation(value = "根据BaseId获取耗材退库单以及详情")
    @GetMapping("/getById")
    public TAjaxResult<AssetsApplyBase<AssetsGiveBackDetailAndArchivesDataDto, String>> getById(@Validated CiReturnStockDetailGetByIdParam param) {

        AssetsApplyBase assetsApplyBase = ciReturnStockDetailService.getByIdInfo(param);

        return new TAjaxResult().success(assetsApplyBase);
    }

    @ApiOperation(value = "获取当前用户领取过的耗材信息列表")
    @GetMapping("/getCiAlreadyReceive")
    public TableDataInfo<List<CiAlreadyReceiveDto>> getCiAlreadyReceive(@Validated CiAlreadyReceiveParam param) {

        startPage();

        List<CiAlreadyReceiveDto> list = ciReturnStockDetailService.getCiAlreadyReceive(param);

        return getDataTable(list);
    }

}
