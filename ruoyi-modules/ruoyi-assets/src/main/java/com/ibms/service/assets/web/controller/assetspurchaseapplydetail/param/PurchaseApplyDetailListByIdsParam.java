package com.ibms.service.assets.web.controller.assetspurchaseapplydetail.param;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "根据选择列表ids获取采购申请单明细列表参数")
public class PurchaseApplyDetailListByIdsParam {


    /**
     * 采购单ids
     */
    @NotNull(message = "ids必填")
    @ApiModelProperty("采购申请单ids")
    private String ids;


}
