package com.ibms.service.assets.web.mapper.assetsrepairdetail;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ibms.service.assets.web.controller.assetsrepairdetail.param.AssetsRepairDetailInfoParam;
import com.ibms.service.assets.web.controller.assetsrepairdetail.vo.AssetsRepairDetailListVo;
import com.ibms.service.assets.web.domain.AssetsPurchaseApplyDetail;
import com.ibms.service.assets.web.domain.AssetsRepairDetail;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AssetsRepairDetailMapper extends BaseMapper<AssetsRepairDetail> {
    List<AssetsRepairDetailListVo> selectDetailList(AssetsRepairDetailInfoParam param);

    List<AssetsRepairDetailListVo> selectDetailListByInIds(List<String> idList);
}
