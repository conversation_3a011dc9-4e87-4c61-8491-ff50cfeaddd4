package com.ibms.service.assets.web.controller.assetsreturnstockdetail;

import cn.hutool.core.bean.BeanUtil;
import com.ibms.service.assets.web.controller.assetsreturnstockdetail.param.AssetsReturnStockBaseAndDetailSaveParam;
import com.ibms.service.assets.web.controller.assetsreturnstockdetail.param.AssetsReturnStockDetailGetByIdParam;
import com.ibms.service.assets.web.controller.assetsreturnstockdetail.param.SelectParam;
import com.ibms.service.assets.web.controller.assetsreturnstockdetail.vo.AlreadyReceiveUseArchivesVo;
import com.ibms.service.assets.web.domain.AssetsApplyBase;
import com.ibms.service.assets.web.domain.FixedAssetsArchivesBase;
import com.ibms.service.assets.web.mapper.assetsgivebackdetail.dto.AssetsGiveBackDetailAndArchivesDataDto;
import com.ibms.service.assets.web.service.AssetsClassifyInfoService;
import com.ibms.service.assets.web.service.AssetsReturnStockDetailService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/4 15:36
 */
@Api(tags = "固定资产退库列表明细")
@RestController
@RequestMapping("/assetsReturnStockDetail")
public class AssetsReturnStockDetailController extends BaseController {

    @Autowired
    private AssetsReturnStockDetailService assetsReturnStockDetailService;

    @Autowired
    private AssetsClassifyInfoService assetsClassifyInfoService;

    @PostMapping("/save")
    @ApiOperation(value = "保存(更新)-资产退库单")
    public AjaxResult save(@Validated @RequestBody AssetsReturnStockBaseAndDetailSaveParam param) {

        boolean b = assetsReturnStockDetailService.saveDetailAndBase(param);

        return toAjax(b);
    }

    @ApiOperation(value = "根据BaseId获取资产退库单以及详情")
    @GetMapping("/getById")
    public TAjaxResult<AssetsApplyBase<AssetsGiveBackDetailAndArchivesDataDto, String>> getById(@Validated AssetsReturnStockDetailGetByIdParam param) {

        AssetsApplyBase assetsApplyBase = assetsReturnStockDetailService.getByIdInfo(param);

        return new TAjaxResult().success(assetsApplyBase);
    }

    @ApiOperation(value = "获取当前用户已领用并正在使用的资产列表")
    @GetMapping("/getAlreadyReceiveUse")
    public TableDataInfo<AlreadyReceiveUseArchivesVo> getAlreadyReceiveUse(@Validated SelectParam selectParam) {

        startPage();

        List<FixedAssetsArchivesBase> list = assetsReturnStockDetailService.getAlreadyReceiveUse(selectParam);

        TableDataInfo tableDataInfo = getDataTable(list);

        /**
         * 循环获取分类名称
         */
        List<AlreadyReceiveUseArchivesVo> archivesVoList = list.stream().map(info -> {
            String fullClassifyName = assetsClassifyInfoService.getFullClassifyByid(info.getClassifyId());
            AlreadyReceiveUseArchivesVo alreadyReceiveUseArchivesVo = new AlreadyReceiveUseArchivesVo();
            BeanUtil.copyProperties(info, alreadyReceiveUseArchivesVo);
            alreadyReceiveUseArchivesVo.setClassifyName(fullClassifyName);
            alreadyReceiveUseArchivesVo.setAssetsId(info.getId());
            return alreadyReceiveUseArchivesVo;
        }).collect(Collectors.toList());

        tableDataInfo.setRows(archivesVoList);
        return tableDataInfo;
    }

}
