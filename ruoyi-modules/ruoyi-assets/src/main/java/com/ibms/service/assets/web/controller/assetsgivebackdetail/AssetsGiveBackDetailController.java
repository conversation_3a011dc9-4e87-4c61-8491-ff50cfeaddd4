package com.ibms.service.assets.web.controller.assetsgivebackdetail;

import com.ibms.service.assets.web.controller.assetsgivebackdetail.param.AssetsGiveBackBaseAndDetailSaveParam;
import com.ibms.service.assets.web.controller.assetsgivebackdetail.param.AssetsGiveBackDetailGetByIdParam;
import com.ibms.service.assets.web.domain.AssetsApplyBase;
import com.ibms.service.assets.web.mapper.assetsgivebackdetail.dto.AssetsGiveBackDetailAndArchivesDataDto;
import com.ibms.service.assets.web.service.AssetsGiveBackDetailService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Description 固定资产归还列表明细
 * <AUTHOR>
 * @Date 2023/1/4 11:46
 */
@Api(tags = "固定资产-归还列表明细")
@RestController
@RequestMapping("/assetsGiveBackDetail")
public class AssetsGiveBackDetailController extends BaseController {

    @Autowired
    private AssetsGiveBackDetailService assetsGiveBackDetailService;

    @PostMapping(value = "/save")
    @ApiOperation(value = "保存(更新)-资产归还单")
    public AjaxResult save(@Validated @RequestBody AssetsGiveBackBaseAndDetailSaveParam param) {

        boolean b = assetsGiveBackDetailService.saveDetailAndBase(param);

        return toAjax(true);
    }

    @ApiOperation(value = "根据BaseId获取资产归还单以及详情")
    @GetMapping("/getById")
    public TAjaxResult<AssetsApplyBase<AssetsGiveBackDetailAndArchivesDataDto, String>> getById(@Validated AssetsGiveBackDetailGetByIdParam param) {

        AssetsApplyBase assetsApplyBase = assetsGiveBackDetailService.getByIdInfo(param);

        return new TAjaxResult().success(assetsApplyBase);
    }


}
