package com.ibms.service.assets.web.controller.ciInventoryDetail.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @description
 * @date 2023/01/03
 */
@Data
@ApiModel(value = "根据applyBaseId获取数据")
public class CiInventoryDetailByApplyBaseIdParam {

    @NotNull(message = "applyBaseId必填")
    @ApiModelProperty("applyBaseId对应base表主键id")
    private String applyBaseId;

}
