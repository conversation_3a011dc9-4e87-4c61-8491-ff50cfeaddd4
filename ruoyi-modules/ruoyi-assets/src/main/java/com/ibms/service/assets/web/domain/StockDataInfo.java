package com.ibms.service.assets.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ibms.service.assets.extent.config.mybatisplus.BasePlusEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 库存数据(资产数据)
 * @date 2022-12-30
 */
@Data
@ApiModel("库存数据(资产数据)")
@TableName("t_stock_data_info")
public class StockDataInfo extends BasePlusEntity implements Serializable {

    private static final long serialVersionUID = 6174910128877582020L;


    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 类型（1固定资产，2耗材）
     */
    @ApiModelProperty("类型（1固定资产，2耗材）")
    private String type;

    /**
     * 资产名称
     */
    @ApiModelProperty("资产名称")
    private String name;

    /**
     * 使用范围（0办公，1项目）
     */
    @ApiModelProperty("使用范围（0办公，1项目）")
    private String useScope;


    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String specification;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;


    /**
     * 分类id
     */
    @ApiModelProperty("分类id")
    private String classifyId;

    /**
     * 分类名称
     */
    @ApiModelProperty("分类名称")
    private transient String classifyName;


    /**
     * 品牌
     */
    @ApiModelProperty("品牌")
    private String brand;

    /**
     * 预计使用年限
     */
    @ApiModelProperty("预计使用年限")
    private String expectedLife;

    /**
     * 所属单位
     */
    @ApiModelProperty("所属单位")
    private String affiliationCompany;

    /**
     * 制造商
     */
    @ApiModelProperty("制造商")
    private String manufacturers;

    /**
     * 库存量
     */
    @ApiModelProperty("库存量")
    private Integer stockNumber;

    /**
     * 维修量
     */
    @ApiModelProperty("维修量")
    private Integer maintenanceNumber;

    /**
     * 在用量
     */
    @ApiModelProperty("在用量")
    private Integer useNumber;

    /**
     * 总量
     */
    @ApiModelProperty("总量")
    private Integer totalNumber;

    /**
     * 是否预警
     */
    @ApiModelProperty("是否预警")
    private String warning;

    /**
     * 预警值
     */
    @ApiModelProperty("预警值")
    private Integer prewarningValue;


    /**
     * 资产图片
     */
    @ApiModelProperty("资产图片")
    private String imgUrl;


    public StockDataInfo() {
    }
}
