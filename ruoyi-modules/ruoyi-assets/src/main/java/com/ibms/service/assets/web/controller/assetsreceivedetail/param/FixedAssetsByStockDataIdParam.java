package com.ibms.service.assets.web.controller.assetsreceivedetail.param;

import com.ruoyi.common.core.web.page.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "根据库存id获取所有未使用的固定资产的列表参数")
public class FixedAssetsByStockDataIdParam extends BasePage {


    @ApiModelProperty(value = "领取列表明细idList")
    private String detailIdList;
    @ApiModelProperty(value = "领取列表明细idLists")
    private List<Integer> detailIdLists;
    @ApiModelProperty(value = "资产id")
    private String assetsId;
    @ApiModelProperty(value = "资产名称")
    private String assetsName;

    /**
     * 使用范围（0办公，1项目）
     */
    @ApiModelProperty("使用范围（0办公，1项目）")
    private String useScope;

}
