package com.ibms.service.assets.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ibms.service.assets.web.controller.cireturnstockdetail.param.CiAlreadyReceiveParam;
import com.ibms.service.assets.web.controller.cireturnstockdetail.param.CiReturnStockBaseAndDetailSaveParam;
import com.ibms.service.assets.web.controller.cireturnstockdetail.param.CiReturnStockDetailGetByIdParam;
import com.ibms.service.assets.web.controller.cireturnstockdetail.param.CiReturnStockDetailSaveParam;
import com.ibms.service.assets.web.domain.*;
import com.ibms.service.assets.web.mapper.cireceivegetdetail.CiReceiveGetDetailMapper;
import com.ibms.service.assets.web.mapper.cireceivegetdetail.dto.CiAlreadyReceiveDto;
import com.ibms.service.assets.web.mapper.cireturnstockdetail.CiReturnStockDetailMapper;
import com.ibms.service.assets.web.mapper.cireturnstockdetail.dto.CiReturnStockDetailAndStockDto;
import com.ibms.service.assets.web.service.AssetsApplyBaseService;
import com.ibms.service.assets.web.service.AssetsClassifyInfoService;
import com.ibms.service.assets.web.service.CiReturnStockDetailService;
import com.ibms.service.assets.web.service.StockDataInfoService;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/13 17:16
 */
@Service
@Slf4j
public class CiReturnStockDetailServiceImpl extends ServiceImpl<CiReturnStockDetailMapper, CiReturnStockDetail> implements CiReturnStockDetailService {

    @Autowired
    private CiReceiveGetDetailMapper ciReceiveGetDetailMapper;
    @Autowired
    private AssetsClassifyInfoService assetsClassifyInfoService;
    @Autowired
    private AssetsApplyBaseService assetsApplyBaseService;
    @Autowired
    private StockDataInfoService stockDataInfoService;

    @Override
    public List<CiAlreadyReceiveDto> getCiAlreadyReceive(CiAlreadyReceiveParam param) {

        Long userId = SecurityUtils.getUserId();
        String assetsName = param.getAssetsName();
        String specification = param.getSpecification();

        List<CiAlreadyReceiveDto> list = ciReceiveGetDetailMapper.getCiAlreadyReceive(userId, assetsName, specification, param.getUseScope());

        List<CiAlreadyReceiveDto> newList = list.stream().map(vo -> {
            String classifyId = vo.getClassifyId();
            String fullClassifyName = assetsClassifyInfoService.getFullClassifyByid(classifyId);
            vo.setClassifyName(fullClassifyName);
            return vo;
        }).collect(Collectors.toList());


        return newList;
    }

    /**
     * 保存(更新)-耗材退库单
     *
     * @param param
     * @return
     */
    @Override
    public boolean saveDetailAndBase(CiReturnStockBaseAndDetailSaveParam param) {

        String applyBaseId = param.getId();
        if (StrUtil.isNotBlank(applyBaseId)) {// 更新
            return true;
        }
        // 新增
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        // 当前人信息
        AssetsApplyBase assetsApplyBase = new AssetsApplyBase();
        BeanUtil.copyProperties(param, assetsApplyBase);
        assetsApplyBase.setApplyUserId(sysUser.getUserId().toString());
        assetsApplyBase.setApplyUserName(sysUser.getNickName());
        // 当前人部门信息
        SysDept dept = sysUser.getDept();
        assetsApplyBase.setApplyDeptId(dept.getDeptId().toString());
        assetsApplyBase.setApplyDeptName(dept.getDeptName());
        assetsApplyBase.setApplyTime(new Date());
        // 基础信息
        assetsApplyBase.setBaseType("11");
        assetsApplyBase.setReceiveStatus("2");// 2：未完成接收


        AssetsApplyBase assetsApplyBaseNew = assetsApplyBaseService.savePlus(assetsApplyBase);

        List<CiReturnStockDetailSaveParam> detailList = param.getDetailList();
        Integer applyTotal = 0;
        for (CiReturnStockDetailSaveParam returnStockDetailSaveParam : detailList) {
            Integer number = returnStockDetailSaveParam.getNumber();
            applyTotal = applyTotal + number;

            // 获取耗材库存数据
            Integer stockDataId = returnStockDetailSaveParam.getStockDataId();
            StockDataInfo stockDataInfo = stockDataInfoService.getById(stockDataId);

            // 更新t_ci_return_stock_detail表
            CiReturnStockDetail ciReturnStockDetail = new CiReturnStockDetail();
            ciReturnStockDetail.setParentBaseId(assetsApplyBaseNew.getId());
            ciReturnStockDetail.setStockDataId(stockDataId);
            ciReturnStockDetail.setNumber(number);
            ciReturnStockDetail.setReceiveStatus("1");// 未接收
            save(ciReturnStockDetail);

        }
        assetsApplyBaseNew.setApplyTotal(applyTotal);
        assetsApplyBaseNew.setTreatedNumber(0);
        assetsApplyBaseNew.setBaseStatus("1");// 未接收
        assetsApplyBaseNew.setKeeperTreatedNumber(applyTotal);// 耗资退库的数量和申请总量一致（仓管接收数量）
        assetsApplyBaseService.updateById(assetsApplyBaseNew);

        return true;
    }

    /**
     * 根据BaseId获取耗材退库单以及详情
     *
     * @param param
     * @return
     */
    @Override
    public AssetsApplyBase getByIdInfo(CiReturnStockDetailGetByIdParam param) {

        String applyBaseId = param.getApplyBaseId();

        AssetsApplyBase assetsApplyBase = assetsApplyBaseService.getById(applyBaseId);
        if (null == assetsApplyBase) {
            throw new ServiceException("未查询到该申请单，请核实id信息！");
        }
        // 获取耗材退库详情
        List<CiReturnStockDetailAndStockDto> list = baseMapper.getDetialList(applyBaseId);
        if (list.size() > 0) {
            for (CiReturnStockDetailAndStockDto ciDto : list) {
                ciDto.setClassifyName(assetsClassifyInfoService.getFullClassifyByid(ciDto.getClassifyId()));
            }
        }
        assetsApplyBase.setDetailList(list);

        return assetsApplyBase;
    }

    @Override
    public List<CiReturnStockDetailAndStockDto> getDetialList(String applyBaseId) {
        List<CiReturnStockDetailAndStockDto> list = baseMapper.getDetialList(applyBaseId);
        if (list.size() > 0) {
            for (CiReturnStockDetailAndStockDto ciDto : list) {
                ciDto.setClassifyName(assetsClassifyInfoService.getFullClassifyByid(ciDto.getClassifyId()));
            }
        }
        return list;
    }


}
