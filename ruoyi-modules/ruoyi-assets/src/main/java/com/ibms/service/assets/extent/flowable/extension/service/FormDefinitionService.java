package com.ibms.service.assets.extent.flowable.extension.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ibms.service.assets.extent.flowable.extension.domain.FormDefinition;
import com.ibms.service.assets.extent.flowable.extension.controller.formdefinition.param.FormDefinitionListParam;
import com.ibms.service.assets.extent.flowable.extension.service.dto.FormDefinitionDto;

/**
 * @Description 流程表单Service
 * <AUTHOR>
 * @Date 2022/12/6 16:32
 */
public interface FormDefinitionService extends IService<FormDefinition> {

    public IPage<FormDefinitionDto> findPage(Page page, FormDefinitionListParam param);

}
