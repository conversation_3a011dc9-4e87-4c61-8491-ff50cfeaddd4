package com.ibms.service.assets.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ibms.service.assets.web.controller.assetspurchaseorderdetail.param.PurchaseOrderDetailListByIdsParam;
import com.ibms.service.assets.web.controller.assetsreceivedetail.param.*;
import com.ibms.service.assets.web.controller.assetsreceivedetail.vo.AssetsGetNotReceivedGetDetialVo;
import com.ibms.service.assets.web.controller.assetsreceivedetail.vo.AssetsReceiveDetailListVo;
import com.ibms.service.assets.web.controller.cireceivedetail.param.GetNotReceivedParam;
import com.ibms.service.assets.web.controller.cireceivedetail.param.KeeperNoAllotParam;
import com.ibms.service.assets.web.domain.AssetsApplyBase;
import com.ibms.service.assets.web.domain.AssetsReceiveDetail;
import com.ibms.service.assets.web.mapper.assetsreceivedetail.dto.AssetsReceiveDetailAndStockDataFinishDto;
import com.ibms.service.assets.web.mapper.assetsreceivedetail.dto.FixedAssetsByStockDataIdListDto;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/12/30 16:49
 */
public interface AssetsReceiveDetailService extends IService<AssetsReceiveDetail> {

    AssetsApplyBase saveDetailAndBase(AssetsReceiveBaseAndDetailSaveParam param);

    AssetsApplyBase getByIdInfo(AssetsReceiveDetailGetByIdParam param);


    /**
     * 根据BsseId获取领用单下的详情列表
     *
     * @param param
     * @return
     */
    List<AssetsReceiveDetailAndStockDataFinishDto> getDetailById(AssetsReceiveDetailGetByIdParam param);

    boolean keeperAllot(KeeperAllotParan param);


    List<AssetsApplyBase> getAssetsKeeperNoAllotApply(AssetsKeeperNoAllotParam param);

    List<FixedAssetsByStockDataIdListDto> getFixedAssetsByStockDataIdList(FixedAssetsByStockDataIdParam param);
}
