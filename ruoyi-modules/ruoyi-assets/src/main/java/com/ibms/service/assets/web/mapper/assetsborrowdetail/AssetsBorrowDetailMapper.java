package com.ibms.service.assets.web.mapper.assetsborrowdetail;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ibms.service.assets.web.domain.AssetsBorrowDetail;
import com.ibms.service.assets.web.mapper.assetsborrowdetail.dto.AssetsBorrowDetailAndStockDataDto;
import com.ibms.service.assets.web.mapper.assetsborrowdetail.dto.AssetsBorrowDetailAndStockDataFinishDto;
import com.ibms.service.assets.web.mapper.assetsborrowdetail.dto.AssetsBorrowDetailAndArchivesDataDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/3 17:32
 */
@Mapper
public interface AssetsBorrowDetailMapper extends BaseMapper<AssetsBorrowDetail> {
    /**
     * 根据applyBaseId获取信息
     *
     * @param applyBaseId
     * @return
     */
    List<AssetsBorrowDetailAndStockDataDto> getDetialList(@Param("applyBaseId") String applyBaseId);

    /**
     * @description: TODO 根据baseid获取没有完成的
     * @author: lyc
     * @date: 2023/2/1 9:35:40
     * @param: [applyBaseId, assetsName]
     * @return: java.util.List<com.ibms.service.assets.web.mapper.assetsborrowgetdetail.dto.AssetsBorrowDetailAndStockDataFinishDto>
     **/
    List<AssetsBorrowDetailAndStockDataFinishDto> selectNoFinish(@Param("applyBaseId") String applyBaseId, @Param("assetsName") String assetsName);

    /**
     * 获取已领取
     *
     * @param applyBaseId
     * @return
     */
    List<AssetsBorrowDetailAndArchivesDataDto> getAlreadyReceived(@Param("applyBaseId") String applyBaseId);
}
