package com.ibms.service.assets.web.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ibms.service.assets.extent.config.dict.AsstesDict;
import com.ibms.service.assets.web.controller.assetsinstoragedetail.param.AssetsInStorageDetailInfoParam;
import com.ibms.service.assets.web.controller.assetsinstoragedetail.param.AssetsInStorageDetailSaveParam;
import com.ibms.service.assets.web.controller.assetsinstoragedetail.vo.AssetsInStorageDetailListVo;
import com.ibms.service.assets.web.controller.stockchangerecord.param.StockChangeRecordInsertParam;
import com.ibms.service.assets.web.domain.*;
import com.ibms.service.assets.web.mapper.assetsinstoragedetail.AssetsInStorageDetailMapper;
import com.ibms.service.assets.web.service.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class AssetsInStorageDetailServiceImpl extends ServiceImpl<AssetsInStorageDetailMapper, AssetsInStorageDetail> implements AssetsInStorageDetailService {


    @Autowired
    private AssetsApplyBaseService assetsApplyBaseService;
    @Autowired
    private StockDataInfoService stockDataInfoService;
    @Autowired
    private FixedAssetsArchivesBaseService fixedAssetsArchivesBaseService;
    @Autowired
    private StockChangeRecordService stockChangeRecordService;
    @Autowired
    private LifeCycleService lifeCycleService;

    @Override
    public AssetsApplyBase getInStorageInfoByParentBaseId(AssetsInStorageDetailInfoParam param) {
        AssetsApplyBase assetsApplyBase = assetsApplyBaseService.getById(param.getApplyBaseId());
        if (null == assetsApplyBase) {
            throw new ServiceException("当前入库单异常！");
        }
        List<AssetsInStorageDetailListVo> list = baseMapper.selectDetailList(param);
        assetsApplyBase.setDetailList(list);
        return assetsApplyBase;
    }

    @Override
    @Transactional
    public String save(AssetsInStorageDetailSaveParam param) {
        String baseId = null;
        if (StringUtils.isNotEmpty(param.getId()) && (param.getAuditStatus().equals(AsstesDict.AssetsFlowableStatus.审批中.getCode()) || param.getAuditStatus().equals(AsstesDict.AssetsFlowableStatus.已通过.getCode()))) {// 审批中直接修改
            AssetsApplyBase assetsApplyBase = new AssetsApplyBase();
            BeanUtils.copyProperties(param, assetsApplyBase);
            boolean b = assetsApplyBaseService.updateById(assetsApplyBase);
            List<AssetsInStorageDetail> list = param.getDetailList();
            boolean i = updateBatchById(list);
            if (!b && !i) {
                throw new ServiceException("当前入库单保存异常！");
            }
            baseId = param.getId();
        } else {
            Integer totalNumber = 0;
            AssetsApplyBase assetsApplyBase = new AssetsApplyBase();
            BeanUtils.copyProperties(param, assetsApplyBase);
            assetsApplyBase.setBaseType(AsstesDict.AssetsBaseType.入库单.getCode());// 默认入库单
            AssetsApplyBase base = assetsApplyBaseService.savePlus(assetsApplyBase);
            List<AssetsInStorageDetail> list = param.getDetailList();
            if (null == list) {
                throw new ServiceException("资产明细异常！");
            }
            for (AssetsInStorageDetail detail : list) {
                detail.setCorrelationApplyId(detail.getParentBaseId());// 这个baseId选择的采购订单id
                detail.setParentBaseId(base.getId());
                detail.setId(null);
                totalNumber = totalNumber + detail.getNumber();
            }
            boolean i = saveBatch(list);
            if (base != null && !i) {
                throw new ServiceException("当前入库单保存异常！");
            }
            base.setApplyTotal(totalNumber);// 入库总量
            assetsApplyBaseService.updateById(base);
            baseId = base.getId();
        }
        return baseId;
    }


    // 入库申请结束并通过操作库存以及资产档案
    @Transactional
    public int addStockAndAssetsPass(String applyBaseId) {
        AssetsApplyBase assetsApplyBase = assetsApplyBaseService.getById(applyBaseId);// 查询base单信息

        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("parent_base_id", applyBaseId);
        List<AssetsInStorageDetail> list = baseMapper.selectList(wrapper);
        if (list.size() > 0) {
            for (AssetsInStorageDetail detail : list) {
                Integer number = detail.getNumber();
                Integer stockDataId = detail.getStockDataId();
                StockDataInfo dataInfo = stockDataInfoService.getStockDataInfoForUpdateById(stockDataId);
                // 组装变更记录
                StockChangeRecordInsertParam insertParam = new StockChangeRecordInsertParam();
                if ("1".equals(dataInfo.getType())) {// 入库为固定资产
                    List<FixedAssetsArchivesBase> archivesBaseList = new ArrayList<>();
                    List<LifeCycle> lifeCycleList = new ArrayList<>();
                    for (int i = 0; i < number; i++) {
                        // 根据入库单和库存数据组装固定资产档案数据
                        FixedAssetsArchivesBase archivesBase = BeanUtil.copyProperties(dataInfo, FixedAssetsArchivesBase.class);
                        archivesBase.setStatus("1");// 默认未使用状态
                        archivesBase.setAssetsType("1");// 默认固定资产
                        archivesBase.setUseScope(assetsApplyBase.getUseScope());
                        archivesBase.setPlaceId(detail.getStoragePlaceId());// 存放位置id
                        archivesBase.setSupplier(detail.getSupplier());// 供应商
                        archivesBase.setAssetsSource(detail.getAssetsSource());// 资产来源
                        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
                        Long id = snowflake.nextId();
                        archivesBase.setId(StrUtil.toString(id));// 资产档案数据id
                        archivesBase.setStockDataId(String.valueOf(dataInfo.getId()));// 库存id
                        archivesBase.setCreateTime(new Date());
                        archivesBase.setOriginalValueAmount(detail.getOriginalValueAmount());// 原值金额
//                        archivesBase.setPurchaseTime();//购入时间
                        archivesBaseList.add(archivesBase);
                        // 组装生命周期实体
                        LifeCycle lifeCycle = new LifeCycle();
                        lifeCycle.setAssetsId(StrUtil.toString(id));
                        lifeCycle.setBaseId(applyBaseId);
                        lifeCycleList.add(lifeCycle);
                    }
                    boolean fixedBatch = fixedAssetsArchivesBaseService.saveBatch(archivesBaseList);
                    boolean cycleBatch = lifeCycleService.saveBatch(lifeCycleList);
                    if (!fixedBatch && !cycleBatch) {
                        throw new ServiceException("资产档案生成失败！");
                    }
                }

                // 无论耗材还是固定资产都新增库存和总量
                // 组装变更记录保存信息
                insertParam.setNumber(number);
                insertParam.setOriginalId(applyBaseId);
                insertParam.setChangeType("2");
                insertParam.setType("2");
                insertParam.setStockDataId(stockDataId);
                stockChangeRecordService.stockChangeRecordInsert(insertParam);
            }
        }
        return 1;
    }

    // 入库申请结束并驳回操作 选中的采购订单的关联id为空
    public int addStockAndAssetsReject(String applyBaseId) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("parent_base_id", applyBaseId);
        List<AssetsInStorageDetail> list = baseMapper.selectList(wrapper);
        if (list.size() > 0) {
            // java8 去重 采购订单调整状态 拿到所有选择采购订单的baseId
            List<AssetsInStorageDetail> newDetail = list.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(AssetsInStorageDetail::getCorrelationApplyId, Comparator.nullsFirst(Comparator.naturalOrder())))), ArrayList::new)
            );
            for (AssetsInStorageDetail detail : newDetail) {
                String correlationApplyId = detail.getCorrelationApplyId();
                AssetsApplyBase newBase = assetsApplyBaseService.getById(correlationApplyId);
                if (null != newBase) {
                    newBase.setCorrelationSelectId("");// 采购订单关联id为空
                    assetsApplyBaseService.updateById(newBase);
                }
            }
        }
        return 1;
    }

}
