package com.ibms.service.assets.web.controller.cireceivedetail.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/3 9:42
 */
@Data
@ApiModel(value = "根据id获取Base申请表及其细则")
public class CiReceiveDetailGetByIdParam {

    /**
     * 申请表id
     */
    @NotBlank(message = "申请表id必填")
    @ApiModelProperty("申请表id")
    private String applyBaseId;


}
