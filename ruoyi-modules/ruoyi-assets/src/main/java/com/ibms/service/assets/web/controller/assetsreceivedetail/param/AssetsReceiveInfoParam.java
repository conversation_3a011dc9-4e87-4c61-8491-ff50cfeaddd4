package com.ibms.service.assets.web.controller.assetsreceivedetail.param;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/12/30 17:58
 */
@Data
@ApiModel(value = "资产领用单细则参数")
public class AssetsReceiveInfoParam {

    /**
     * 基础表id
     */
    private String parentBaseId;

    /**
     * 资产数据id
     */
    private String stockDataId;

    /**
     * 领用量
     */
    private BigDecimal drawingNumber;

    /**
     * 领取量
     */
    private BigDecimal receivedNumber;

    /**
     * 使用位置
     */
    private String usePlace;

    /**
     * 使用人
     */
    private String usePersonId;

    /**
     * 使用人名称
     */
    private String usePersonName;

    /**
     * 领用类型
     */
    private String receiveType;

    /**
     * 创建者
     */
    private String createBy;

}
