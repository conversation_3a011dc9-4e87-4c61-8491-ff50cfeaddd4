package com.ibms.service.assets.extent.flowable.flow.listener;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ibms.service.assets.extent.config.dict.AsstesDict;
import com.ibms.service.assets.extent.flowable.flow.service.FlowProcessService;
import com.ibms.service.assets.extent.flowable.flow.vo.ProcessVo;
import com.ibms.service.assets.web.domain.AssetsApplyBase;
import com.ibms.service.assets.web.service.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 资产执行监听器
 */
@Component("assetsExecutionListener")
public class AssetsExecutionListener implements ExecutionListener {


    /*添加代码 begin*/
    private static AssetsExecutionListener tmapUtil;
    @Autowired
    private FlowProcessService flowProcessService;
    @Autowired
    private AssetsInStorageDetailService assetsInStorageDetailService;
    @Autowired
    private AssetsApplyBaseService assetsApplyBaseService;
    @Autowired
    private AssetsPurchaseApplyDetailService assetsPurchaseApplyDetailService;
    @Autowired
    private AssetsDisposalDetailService assetsDisposalDetailService;

    @PostConstruct
    public void init() {
        tmapUtil = this;
        tmapUtil.flowProcessService = this.flowProcessService;
        tmapUtil.assetsInStorageDetailService = this.assetsInStorageDetailService;
        tmapUtil.assetsApplyBaseService = this.assetsApplyBaseService;
        tmapUtil.assetsPurchaseApplyDetailService = this.assetsPurchaseApplyDetailService;
        tmapUtil.assetsDisposalDetailService = this.assetsDisposalDetailService;
    }


    public void notify(DelegateExecution delegateExecution) {

        String eventName = delegateExecution.getEventName();
        if ("start".equals(eventName)) {// 开始
            System.out.println("start=========");
        } else if ("end".equals(eventName)) {
            System.out.println("end=========");
            FlowProcessService flowProcessService = SpringUtil.getBean("flowProcessService");
            ProcessVo processVo = null; // 获取流程状态信息
            try {
                processVo = flowProcessService.ceshi(delegateExecution.getProcessInstanceId(), delegateExecution.getProcessDefinitionId());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (StringUtils.isEmpty(delegateExecution.getParentId())) {
                throw new ServiceException("流程异常！");
            }
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("proc_ins_id", delegateExecution.getParentId());
            AssetsApplyBase assetsApplyBase = tmapUtil.assetsApplyBaseService.getOne(wrapper);
            if (null == assetsApplyBase) {
                throw new ServiceException("流程业务数据异常！");
            }
            // TODO 业务处理
            String baseType = assetsApplyBase.getBaseType();
            if (!"1".equals(assetsApplyBase.getAuditStatus())) {// 当前申请单如果已审核则不再参加业务
                return;
            }

            if ("success".equals(processVo.getLevel())) {
                // 审批通过
                assetsApplyBase.setAuditStatus("2");// 2.审核通过
                allTypePass(baseType, assetsApplyBase.getId());
            } else {
                // 审批驳回
                assetsApplyBase.setAuditStatus("3");// 3.驳回
                allTypeReject(baseType, assetsApplyBase.getId());
            }
            tmapUtil.assetsApplyBaseService.updateById(assetsApplyBase);

        } else if ("take".equals(eventName)) {// 连线监听
            System.out.println("take=========");
        }
    }

    // 流程通过后操作
    public Boolean allTypePass(String baseType, String assetsApplyId) {
        switch (baseType) {
            case "1":   //    入库单
                tmapUtil.assetsInStorageDetailService.addStockAndAssetsPass(assetsApplyId);
                break;
            case "4":   // 资产_领用单
                // 只改变审核状态暂时未有其他业务
                break;
            case "5":   // 耗材_领用单
                // 只改变审核状态暂时未有其他业务
                break;
            case "6":   // 资产_借用单
                // 只改变审核状态暂时未有其他业务
                break;
            case "9":   // 资产_处置单
                tmapUtil.assetsDisposalDetailService.addStockAndAssetsPass(assetsApplyId);
                break;
        }

        return true;
    }

    // 流程驳回后操作
    public Boolean allTypeReject(String baseType, String assetsApplyId) {

        switch (baseType) {
            case "1"://    入库单
//                tmapUtil.assetsInStorageDetailService.addStockAndAssetsReject(assetsApplyId);
                break;
            case "12"://    采购申请单
//                tmapUtil.assetsPurchaseApplyDetailService.AssetsPurchaseReject(assetsApplyId);
                break;
            case "2"://    入库单
        }

        return true;
    }


}
