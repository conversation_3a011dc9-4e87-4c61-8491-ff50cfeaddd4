package com.ibms.service.assets.extent.flowable.extension.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 按钮设置
 * <AUTHOR>
 * @Date 2022/11/30 11:57
 */
@Data
@ApiModel("按钮设置")
@TableName("act_extension_buttons")
public class FlowButton implements Serializable {

    private static final long serialVersionUID = 959321862793421941L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 按钮名称
     */
    @ApiModelProperty("按钮名称")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty("编码")
    private String code;

    /**
     * 是否隐藏
     */
    @ApiModelProperty("是否隐藏")
    private String isHide;

    /**
     * 下一节点审核人
     */
    @ApiModelProperty("下一节点审核人")
    private String nextAssignee;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 任务节点外键
     */
    @ApiModelProperty("任务节点外键")
    private String taskDefId;

    /**
     * create_time
     */
    @ApiModelProperty("create_date")
    private Date createTime;

    /**
     * create_by
     */
    @ApiModelProperty("create_by")
    private String createBy;

    /**
     * update_time
     */
    @ApiModelProperty("update_date")
    private Date updateTime;

    /**
     * update_by
     */
    @ApiModelProperty("update_by")
    private String updateBy;

}
