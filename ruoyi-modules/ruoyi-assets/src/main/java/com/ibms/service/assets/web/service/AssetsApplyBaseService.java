package com.ibms.service.assets.web.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ibms.service.assets.web.controller.assetsapplybase.param.*;
import com.ibms.service.assets.web.controller.assetsapplybase.vo.AssetsApplyBaseHistoricVo;
import com.ibms.service.assets.web.controller.assetsapplybase.vo.FlowInfoVo;
import com.ibms.service.assets.web.controller.assetsapplybase.vo.FlowaBleViewVo;
import com.ibms.service.assets.web.domain.AssetsApplyBase;
import com.ibms.service.assets.web.mapper.assetsreceivegetdetail.dto.NotReceivedListDto;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/12/30 11:09
 */
public interface AssetsApplyBaseService extends IService<AssetsApplyBase> {

    /**
     * 根据类型生成主键id并返回实体
     * 1.BasePlusEntity种的字段不需要传
     * 2.id会根据base_type类型自动生成
     *
     * @param assetsReceiveDetail
     * @return
     */
    public AssetsApplyBase savePlus(AssetsApplyBase assetsReceiveDetail);

    List<AssetsApplyBase> selectList(AssetsApplyBaseListParam param);

    FlowInfoVo flowInfo(FlowInfoParam param);

    ResponseEntity todoListData(Page<AssetsApplyBase> page, ToListParam param);

    ResponseEntity historicList(Page<AssetsApplyBaseHistoricVo> page, HistoricList param);

    FlowaBleViewVo flowaBleView(FowaBleViewParam param);

    List<AssetsApplyBase> myApplyList(MyApplyListParam param);

    List<AssetsApplyBase> getReceiveList(AssetsApplyBaseListParam param);
}
