package com.ibms.service.assets.web.controller.cireceivedetail.param;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/1/6 10:56
 */
@Data
@ApiModel(value = "获取仓管未分配的耗材申请单参数")
public class KeeperNoAllotParam {

    /**
     * id(单号)
     */
    @ApiModelProperty("id(单号)")
    private String id;

    /**
     * 使用范围（0办公，1项目）
     */
    @ApiModelProperty("使用范围（0办公，1项目）")
    private String useScope;

    /**
     * 申请级别（1：普通，2：重要 3：紧急）
     */
    @ApiModelProperty("申请级别（1：普通，2：重要 3：紧急）")
    private String applyLevel;

    /**
     * 时间
     */
    @ApiModelProperty("申请时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startApplyTime;

    /**
     * 时间
     */
    @ApiModelProperty("申请时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endApplyTime;

    public Date getStartApplyTime() {
        if (startApplyTime != null) {
            endApplyTime = DateUtil.beginOfDay(startApplyTime);
        }
        return startApplyTime;
    }

    public Date getEndApplyTime() {
        if (startApplyTime != null) {
            endApplyTime = DateUtil.endOfDay(this.endApplyTime);
        }
        return endApplyTime;
    }


}
