package com.ibms.service.assets.web.controller.ciInventoryDetail.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ibms.service.assets.web.domain.AssetsApplyBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description 申请基础信息
 * <AUTHOR>
 * @Date 2022/12/30 11:02
 */
@Data
@ApiModel("申请基础信息")
public class ListVo extends AssetsApplyBase {
    /**
     * 盘点月份
     */
    @ApiModelProperty("盘点月份")
    @JsonFormat(pattern = "yyyy-MM")
    private Date applyTime;

    /**
     * 申请单BaseId
     */
    @ApiModelProperty("申请单BaseId")
    private String applyBaseId;
}
