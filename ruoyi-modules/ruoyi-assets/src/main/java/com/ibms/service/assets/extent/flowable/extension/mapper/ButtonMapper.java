package com.ibms.service.assets.extent.flowable.extension.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ibms.service.assets.extent.flowable.extension.domain.Button;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/28 17:03
 */
@Mapper
public interface ButtonMapper extends BaseMapper<Button> {

    /**
     * 获取重复的数量，排除自己的id
     * @param param
     * @return
     */
//    Integer selectRepeatCount(@Param("param") ButtonSavaOrUpdateParam param);

}
