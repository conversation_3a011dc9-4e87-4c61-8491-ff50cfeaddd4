package com.ibms.service.assets.web.controller.assetsreceivegetdetail;

import com.ibms.service.assets.web.controller.assetsreceivedetail.param.AssetsGetNotReceivedGetDetialParam;
import com.ibms.service.assets.web.controller.assetsreceivedetail.vo.AssetsGetNotReceivedGetDetialVo;
import com.ibms.service.assets.web.controller.assetsreceivegetdetail.param.AssetsReceiveGetDetailParam;
import com.ibms.service.assets.web.controller.cireceivedetail.param.GetNotReceivedParam;
import com.ibms.service.assets.web.domain.AssetsApplyBase;
import com.ibms.service.assets.web.mapper.assetsreceivegetdetail.dto.NotReceivedListDto;
import com.ibms.service.assets.web.service.AssetsReceiveGetDetailService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 资产接收
 * <AUTHOR>
 * @Date 2023/1/17 11:57
 */
@Api(tags = "固定资产接收")
@RestController
@RequestMapping("/assetsReceiveGetDetail")
public class AssetsReceiveGetDetailController extends BaseController {

    @Autowired
    private AssetsReceiveGetDetailService assetsReceiveGetDetailService;

    @PutMapping("/receive")
    @ApiOperation(value = "资产接收")
    public AjaxResult receive(@Validated @RequestBody AssetsReceiveGetDetailParam param) {

        boolean b = assetsReceiveGetDetailService.receive(param);

        return toAjax(b);
    }

    @GetMapping("/getNotReceivedApplyBase")
    @ApiOperation(value = "获取未被接收的资产-申请单列表")
    public TableDataInfo<AssetsApplyBase> getNotReceivedApplyBase(@Validated GetNotReceivedParam param) {
        startPage();
        List<AssetsApplyBase> list = assetsReceiveGetDetailService.getNotReceivedApplyBase(param);
        return getDataTable(list);
    }

    @GetMapping("/getNotReceivedGetDetail")
    @ApiOperation(value = "获取未接收的资产-Get详情列表")
    public TAjaxResult<AssetsGetNotReceivedGetDetialVo> getNotReceived(@Validated AssetsGetNotReceivedGetDetialParam param) {
        if ("4".equals(param.getBaseType()) || "6".equals(param.getBaseType())) {// 个人领用单和借用
            List<AssetsGetNotReceivedGetDetialVo> list = assetsReceiveGetDetailService.getNotReceivedGetDetial(param);
            return new TAjaxResult(list);
        } else {// 退库单10 和归还
            List<NotReceivedListDto> list = assetsReceiveGetDetailService.getNotGiveBackAndReturnStockReceivedList(param);
            return new TAjaxResult(list);
        }

    }


}
