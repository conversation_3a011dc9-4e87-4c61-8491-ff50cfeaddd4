package com.ibms.service.assets.extent.flowable.flow.interceptor;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.flowable.idm.api.User;
import org.flowable.idm.engine.impl.persistence.entity.UserEntityImpl;
import org.flowable.ui.common.security.SecurityUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 请求拦截器，Flowable动态设置用户信息
 *
 * <AUTHOR>
 * @version 2021-8-19
 * 需要增加拦截器，动态设置Flowable用户信息
 */
public class FlowableHandlerInterceptor implements HandlerInterceptor {

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String servletPath = request.getServletPath();

        LoginUser loginUser = com.ruoyi.common.security.utils.SecurityUtils.getLoginUser();

        // 下载bpmn.xml文件时会拦截请求，但不需要获取用户信息特做全部处理
        SysUser sysUser = null;
        if (loginUser == null) {
            sysUser = new SysUser();
        } else {
            sysUser = loginUser.getSysUser();
        }

        if (servletPath.startsWith("/app") || servletPath.startsWith("/idm")) {
            User currentUserObject = SecurityUtils.getCurrentUserObject();
            if (currentUserObject == null || StrUtil.isBlank(currentUserObject.getId())) {
                User user = new UserEntityImpl();
                user.setId(StrUtil.toString(sysUser.getUserId()));
                user.setFirstName(sysUser.getNickName());
                user.setLastName("");
                user.setEmail(sysUser.getEmail());
                SecurityUtils.assumeUser(user);
            }
        }
        return true;
    }

    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {

    }

    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {

    }


}
