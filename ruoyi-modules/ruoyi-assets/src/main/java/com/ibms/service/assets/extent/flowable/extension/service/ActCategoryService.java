package com.ibms.service.assets.extent.flowable.extension.service;


import com.ibms.service.assets.extent.flowable.extension.controller.actcategory.param.ActCategoryDeleteIdsParam;
import com.ibms.service.assets.extent.flowable.extension.controller.actcategory.param.ActCategoryListParam;
import com.ibms.service.assets.extent.flowable.extension.controller.actcategory.param.ActCategorySavaOrUpdateParam;
import com.ibms.service.assets.extent.flowable.extension.domain.ActCategory;
import com.ibms.service.assets.extent.flowable.extension.controller.actcategory.param.ActCategoryGetByIdParam;
import com.ibms.service.assets.extent.flowable.extension.service.dto.ActCategoryDto;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/11/29 15:33
 */
public interface ActCategoryService {

    List<ActCategory> list(ActCategoryListParam param);

    ActCategoryDto queryById(ActCategoryGetByIdParam param);

    Integer saveOrUpdate(ActCategorySavaOrUpdateParam param);

    Integer deleteByIds(ActCategoryDeleteIdsParam param);
}
