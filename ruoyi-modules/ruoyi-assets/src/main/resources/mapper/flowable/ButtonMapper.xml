<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.plugins.flowable.extension.mapper.ButtonMapper">

    <resultMap id="BaseResultMap" type="com.ibms.service.assets.extent.flowable.extension.domain.Button">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="sort" property="sort"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        name,
        code,
        sort,
        create_time,
        create_by,
        update_time,
        update_by
    </sql>


</mapper>
