<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.assets.web.mapper.stockchangerecord.StockChangeRecordMapper">


    <resultMap id="BaseResultMap" type="com.ibms.service.assets.web.domain.StockChangeRecord">
        <result column="id" property="id"/>
        <result column="change_type" property="type"/>
        <result column="stock_data_id" property="stockDataId"/>
        <result column="assets_id" property="assetsId"/>
        <result column="last_number" property="lastNumber"/>
        <result column="changed_number" property="changedNumber"/>
        <result column="change_after_number" property="changeAfterNumber"/>
        <result column="original_id" property="originalId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        change_type,
        stock_data_id,
        assets_id,
        last_number,
        changed_number,
        change_after_number,
        original_id,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        remark
    </sql>
    <select id="getList"
            resultType="com.ibms.service.assets.web.controller.stockchangerecord.vo.StockChangeRecordListVo"
            parameterType="com.ibms.service.assets.web.controller.stockchangerecord.param.StockChangeRecordListParam">
        SELECT
        a.id,
        a.change_type,
        a.stock_data_id,
        a.assets_id,
        a.last_number,
        a.changed_number,
        a.change_after_number,
        a.original_id,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time,
        a.del_flag,
        a.remark,
        b.name AS stockDataName,
        b.type as stockDataType,
        b.specification AS specification,
        b.use_scope AS useScope,
        c.base_type AS baseType
        FROM
        t_stock_change_record AS a
        LEFT JOIN t_stock_data_info AS b ON a.stock_data_id = b.id
        LEFT JOIN t_assets_apply_base AS c ON a.original_id = c.id
        <where>
            <if test="stockDataName != null and stockDataName">
                and b.name like concat('%',#{stockDataName},'%')
            </if>
            <if test="stockDataType != null and stockDataType">
                and b.type = #{stockDataType}
            </if>
            <if test="changeType != null and changeType">
                and a.change_type = #{changeType}
            </if>
            <if test="baseType != null and baseType">
                and c.base_type = #{baseType}
            </if>
            <if test="useScope != null and useScope">
                and b.use_scope = #{useScope}
            </if>
        </where>
        order by create_time desc
    </select>

</mapper>
