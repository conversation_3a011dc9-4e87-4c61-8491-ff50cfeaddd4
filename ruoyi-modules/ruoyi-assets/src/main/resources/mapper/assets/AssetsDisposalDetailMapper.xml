<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.assetsDisposalDetail.AssetsDisposalDetailMapper">
    <resultMap id="BaseResultMap"
               type="com.ibms.service.assets.web.domain.AssetsDisposalDetail">
        <result column="id" property="id"/>
        <result column="parent_base_id" property="parentBaseId"/>
        <result column="assets_id" property="assetsId"/>
        <result column="disposal_type" property="disposalType"/>
        <result column="disposal_mode" property="disposalMode"/>
        <result column="disposal_time" property="disposalTime"/>
        <result column="disposal_cost" property="disposalCost"/>
        <result column="disposal_income" property="disposalIncome"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        parent_base_id,
        assets_id,
        disposal_type,
        disposal_mode,
        disposal_time,
        disposal_cost,
        disposal_income,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        remark
    </sql>
</mapper>
