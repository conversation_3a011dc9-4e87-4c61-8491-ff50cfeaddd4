<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.fixedAssetsArchivesBase.FixedAssetsArchivesBaseMapper">

    <select id="getNotGiveBack"
            resultType="com.ibms.service.assets.web.controller.assetsborrowdetail.vo.AssetsBorrowDetailGetNotGiveBackVo">
        select
        f.id as assetsId,
        f.name as name,
        f.specification as specification,
        f.classify_id as classifyId,
        f.unit as unit,
        f.brand as brand,
        d.id as assetsBorrowGetDetailId,
        f.stock_data_id as stockDataId,
        p.place_storage as placeName,
        f.use_scope as useScope
        from t_assets_receive_get_detail d
        LEFT JOIN t_fixed_assets_archives_base f ON d.assets_id = f.id
        LEFT JOIN t_assets_place_info p ON f.place_id = p.id
        where
        d.recipient_id = ${userId}
        AND d.receive_status = 2
        and d.base_type = '6'
        and d.special_status = '0'
        <if test="null != assetsId and '' != assetsId">
            and f.id = ${assetsId}
        </if>
        <if test="null != assetsName and '' != assetsName">
            and f.name like concat('%',${assetsName},'%')
        </if>
        <if test="useScope != null and useScope != ''">
            and f.use_scope = #{useScope}
        </if>
        order by d.create_time desc

    </select>

    <select id="getByStockDataId"
            resultType="com.ibms.service.assets.web.mapper.fixedAssetsArchivesBase.dto.ArchivesGetByStockDataId">
        SELECT
        a.id as assets_id,
        a.stock_data_id,
        a.name as assetsName,
        a.brand,
        a.specification,
        a.classify_id,
        a.unit,
        a.status,
        a.place_id,
        c.place_storage,
        b.id as detailId,
        a.use_scope as useScope
        FROM
        t_fixed_assets_archives_base AS a
        LEFT JOIN t_assets_borrow_detail AS b ON a.stock_data_id = b.stock_data_id
        LEFT JOIN t_assets_place_info as c on a.place_id = c.id
        WHERE
        a.status = '1' and a.del_flag = '0'
        <if test="assetsId != null and assetsId != ''">
            and a.id like concat('%',#{assetsId},'%')
        </if>
        <if test="assetsName != null and assetsName != ''">
            and a.name like concat('%',#{assetsName},'%')
        </if>
        <if test="useScope != null and useScope != ''">
            and a.use_scope = #{useScope}
        </if>
        <if test="detailIdList != null ">
            and b.id in
            <foreach collection="detailIdList" item="detailId" open="(" close=")" separator=",">
                #{detailId}
            </foreach>
        </if>
        order by a.create_time desc
    </select>
    <select id="getAssetStatusAnalysisList"
            resultType="com.ibms.service.assets.web.controller.assetshomepage.vo.AssetStatusAnalysisVo">
        SELECT
        value,
        name,
        concat(ROUND( value / ( SELECT count( 1 ) AS value FROM t_fixed_assets_archives_base WHERE del_flag = '0' ) *
        100, 1 ),'%') AS percent
        FROM
        (
        SELECT
        count(1) as value,
        CASE status
        WHEN 1 THEN'未使用'
        WHEN 2 THEN'使用中'
        when 3 THEN'维修中'
        when 4 THEN'正在处置'
        when 5 THEN'已处置'
        when 6 THEN'已丢失'
        END AS name
        FROM
        t_fixed_assets_archives_base
        where del_flag = '0'
        GROUP BY
        status
        ) a
        GROUP BY
        name
        ORDER BY
        value DESC
    </select>
    <select id="getStockTotalNumberAndTotalNumber"
            resultType="com.ibms.service.assets.web.mapper.fixedAssetsArchivesBase.dto.TotalNumberAndTotalNumberDto">
        SELECT
        SUM( stock_number + maintenance_number + use_number ) as assetsTotalNumber,
        SUM(stock_number) as stockTotalNumber
        FROM
        t_stock_data_info
        where del_flag = '0'

    </select>
    <select id="getUseNumberByUserId" resultType="java.lang.Integer" parameterType="java.lang.Long">
        SELECT
        COUNT(1)
        FROM
        t_fixed_assets_archives_base
        WHERE
        del_flag = '0'
        AND use_user_id = #{userId}
    </select>
    <select id="getAssetsUsePercent"
            resultType="com.ibms.service.assets.web.mapper.fixedAssetsArchivesBase.dto.AssetsUsePercentDto">
        SELECT
        SUM(stock_number+use_number+maintenance_number) as assetsTotalNumber,
        sum(stock_number) AS stockTotalNumber,
        SUM(use_number) as useTotalNumber,
        ROUND( SUM(use_number) /SUM(stock_number+use_number+maintenance_number) * 100, 1 ) as usePercent
        FROM
        t_stock_data_info
        WHERE
        type = '1'
        and del_flag = '0'
    </select>
    <select id="getPreWarningNumber" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        t_stock_data_info
        WHERE
        type = '1'
        and del_flag = '0'
        and warning = '1'
        and prewarning_value > stock_number
    </select>
    <select id="getRepairWarningNumber" resultType="java.lang.Integer">
        SELECT
        COUNT(id)
        FROM
        t_fixed_assets_archives_base
        WHERE
        del_flag = '0'
        AND DATE_FORMAT( DATE_sub( warranty_end_time, INTERVAL 30 day), '%Y-%m-%d' ) &lt; DATE_FORMAT(
        NOW(),
        '%Y-%m-%d')
    </select>
    <select id="getMaintainWarningNumber" resultType="java.lang.Integer">
        SELECT
        COUNT(t.id)
        FROM
        t_assets_maintain_info t
        JOIN (
        SELECT
        SUBSTRING_INDEX( group_concat( id ORDER BY create_time DESC ), ',', 1 ) AS id
        FROM
        t_assets_maintain_info
        GROUP BY assets_id ) tmp
        ON t.id = tmp.id where DATE_FORMAT( DATE_sub( next_maintain_time, INTERVAL 30 day), '%Y-%m-%d' ) &lt;
        DATE_FORMAT(
        NOW(),
        '%Y-%m-%d')
    </select>

    <select id="fillInStorageTimef" resultType="java.util.Date">
        SELECT lc.create_time
        FROM t_life_cycle lc
        INNER JOIN t_assets_apply_base aab ON lc.base_id = aab.id
        WHERE aab.base_type = '1'
        AND lc.assets_id = #{assetsId}
    </select>

    <!--    <select id="getByStockDataId"-->
    <!--            resultType="com.ibms.service.assets.web.mapper.fixedAssetsArchivesBase.dto.ArchivesGetByStockDataId">-->
    <!--       select-->
    <!--            a.id as assetsId,-->
    <!--            a.name as name,-->
    <!--            a.specification as specification,-->
    <!--            a.classify_id as classifyId,-->
    <!--            a.unit as unit,-->
    <!--            a.brand as brand,-->
    <!--            p.place_storage as placeStorage-->

    <!--       from-->
    <!--          t_fixed_assets_archives_base a,-->
    <!--          t_assets_place_info p-->
    <!--       where a.stock_data_id = ${stockDataId}-->
    <!--        and a.status = 1-->
    <!--        or (a.place_id = p.id)-->
    <!--        <if test="null != assetsName and '' != assetsName">-->
    <!--            and a.name like '%${assetsName}%'-->
    <!--        </if>-->
    <!--        <if test="null != assetsId and '' != assetsId">-->
    <!--            and a.id like '%${assetsId}%'-->
    <!--        </if>-->
    <!--        order by a.create_time desc-->
    <!--    </select>-->


</mapper>
