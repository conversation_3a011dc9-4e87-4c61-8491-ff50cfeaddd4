<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.ciInventoryDetail.CiInventoryDetailMapper">
    <resultMap id="BaseResultMap"
               type="com.ibms.service.assets.web.domain.CiInventoryDetail">
        <result column="id" property="id"/>
        <result column="parent_base_id" property="parentBaseId"/>
        <result column="stock_data_id" property="stockDataId"/>
        <result column="inventory_time" property="inventoryTime"/>
        <result column="stock_number" property="stockNumber"/>
        <result column="firm_offer_number" property="firmOfferNumber"/>
        <result column="damage_number" property="damageNumber"/>
        <result column="inventory_status" property="inventoryStatus"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!--    <resultMap id="WaitingProcessListVo"-->
    <!--               type="com.ibms.service.assets.web.controller.ciInventoryDetail.vo.WaitingProcessListVo">-->
    <!--        <result column="apply_time" property="applyTime"/>-->
    <!--        <result column="inventory_date" property="inventoryDate"/>-->
    <!--        <result column="apply_user_name" property="applyUserName"/>-->
    <!--        <result column="apply_dept_name" property="applyDeptName"/>-->
    <!--        <result column="name" property="ciName"/>-->
    <!--        <result column="specification" property="specification"/>-->
    <!--        <result column="classify_name" property="classifyName"/>-->
    <!--        <result column="unit" property="unit"/>-->
    <!--        <result column="brand" property="brand"/>-->
    <!--        <result column="stock_number" property="stockNumber"/>-->
    <!--        <result column="firm_offer_number" property="firmOfferNumber"/>-->
    <!--        <result column="remark" property="remark"/>-->
    <!--    </resultMap>-->

    <sql id="Base_Column_List">
        id,
        parent_base_id,
        stock_data_id,
        inventory_time,
        stock_number,
        firm_offer_number,
        damage_number,
        inventory_status,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        remark
    </sql>


    <select id="selectByCondition" resultType="java.util.Map">
        select aab.apply_time as applyTime,
        cid.inventory_time as inventoryTime,
        aab.apply_user_name as applyUserName,
        aab.apply_dept_name as applyDeptName,
        sdi.name as ciName,
        sdi.specification as specification,
        sdi.classify_id as classifyId,
        sdi.unit as unit,
        sdi.brand as brand,
        sdi.use_scope as useScope,
        cid.id as ciId,
        cid.stock_number as stockNumber,
        cid.firm_offer_number as firmOfferNumber,
        cid.remark as remark
        from t_assets_apply_base aab
        left join t_ci_inventory_detail cid on aab.id = cid.parent_base_id
        left join t_stock_data_info sdi on sdi.id = cid.stock_data_id
        <where>
            <if test="applyUserName != null and applyUserName != ''">
                and aab.apply_user_name like concat('%', #{applyUserName}, '%')
            </if>
            <if test="applyTime != null">
                and aab.apply_time = #{applyTime}
            </if>
            <if test="applyUserId != null and applyUserId != ''">
                and aab.apply_user_id = #{applyUserId}
            </if>
            <if test="baseType != null and baseType != ''">
                and aab.base_type = #{baseType}
            </if>
            <if test="inventoryStatus != null and inventoryStatus != ''">
                and cid.inventory_status = #{inventoryStatus}
            </if>
            <if test="useScope != null and useScope != ''">
                and sdi.use_scope = #{useScope}
            </if>
        </where>
    </select>
    <select id="getCiAndAssetsInventoryNumber" resultType="java.lang.Integer" parameterType="java.lang.Long">
        SELECT
        COUNT(1)
        FROM
        t_assets_apply_base aab
        LEFT JOIN t_ci_inventory_detail cid ON aab.id = cid.parent_base_id
        LEFT JOIN t_stock_data_info sdi ON sdi.id = cid.stock_data_id
        LEFT JOIN t_assets_inventory_detail aid ON aab.id = aid.parent_base_id
        WHERE
        aab.apply_user_id = #{userId}
        AND aab.base_type IN (15,14 )
        AND cid.inventory_status = 1 or aid.inventory_status = 1
    </select>
</mapper>
