<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ibms.service.assets.web.mapper.lifeCycle.LifeCycleMapper">
    <resultMap id="BaseResultMap" type="com.ibms.service.assets.web.domain.LifeCycle">
        <result column="assets_id" property="assetsId"/>
        <result column="base_id" property="baseId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        assets_id,
        base_id,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        remark
    </sql>
</mapper>
