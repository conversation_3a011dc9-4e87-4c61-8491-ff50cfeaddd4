package com.ruoyi.homecare.housekeeper.service;

import java.util.List;

import com.ruoyi.homecare.housekeeper.domain.HomeStreetInfo;
import com.ruoyi.homecare.housekeeper.domain.TreeSelect;

/**
 * 街道信息管理Service接口
 *
 * <AUTHOR>
 * @date 2022-10-08
 */
public interface IHomeStreetInfoService {
    /**
     * 查询街道信息管理
     *
     * @param id 街道信息管理主键
     * @return 街道信息管理
     */
    public HomeStreetInfo selectHomeStreetInfoById(Long id);

    /**
     * 查询街道信息管理列表
     *
     * @param homeStreetInfo 街道信息管理
     * @return 街道信息管理集合
     */
    public List<HomeStreetInfo> selectHomeStreetInfoList(HomeStreetInfo homeStreetInfo);

    /**
     * 新增街道信息管理
     *
     * @param homeStreetInfo 街道信息管理
     * @return 结果
     */
    public int insertHomeStreetInfo(HomeStreetInfo homeStreetInfo);

    /**
     * 修改街道信息管理
     *
     * @param homeStreetInfo 街道信息管理
     * @return 结果
     */
    public int updateHomeStreetInfo(HomeStreetInfo homeStreetInfo);

    /**
     * 批量删除街道信息管理
     *
     * @param ids 需要删除的街道信息管理主键集合
     * @return 结果
     */
    public int deleteHomeStreetInfoByIds(Long[] ids);

    /**
     * 删除街道信息管理信息
     *
     * @param id 街道信息管理主键
     * @return 结果
     */
    public int deleteHomeStreetInfoById(Long id);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildStreetInfoTreeSelect(List<HomeStreetInfo> homeStreetInfoList);

    /**
     * 构建前端所需要树结构
     *
     * @param
     * @return 树结构列表
     */
    public List<HomeStreetInfo> buildTree(List<HomeStreetInfo> homeStreetInfoList);

    /**
     * 是否存在子节点
     *
     * @param id 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    boolean hasChildByStreetId(Long id);

    /**
     * 校验菜单名称是否唯一
     *
     * @param homeStreetInfo 信息
     * @return 结果
     */
    String checkStreetNameUnique(HomeStreetInfo homeStreetInfo);
}
