package com.ruoyi.homecare.volunteers.domain.request;

import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 小程序加入社团参数
 *
 * <AUTHOR>
 * @date 2022-10-10
 */
@Data
@ApiModel(value = "小程序加入社团参数")
public class HomeAssociationVolunteerIndexParam extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "社团id")
    private Long associationId;
    @ApiModelProperty(value = "社团名称")
    private String name;
    @ApiModelProperty(value = "状态")
    private String status;


}
