package com.ruoyi.homecare.elderlyPeople.domain;

import cn.hutool.json.JSONArray;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 调查问卷历史对象 t_questionnaire_records
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
public class QuestionnaireRecords extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 评估报告id
     */
    @Excel(name = "评估报告id")
    private Long baseId;

    /**
     * 选中的评估调查问卷集合
     */
    @Excel(name = "选中的评估调查问卷集合")
    private String jsonData;

    private JSONArray list;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBaseId() {
        return baseId;
    }

    public void setBaseId(Long baseId) {
        this.baseId = baseId;
    }

    public String getJsonData() {
        return jsonData;
    }

    public void setJsonData(String jsonData) {
        this.jsonData = jsonData;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public JSONArray getList() {
        return list;
    }

    public void setList(JSONArray list) {
        this.list = list;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("baseId", getBaseId())
                .append("jsonData", getJsonData())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }
}
