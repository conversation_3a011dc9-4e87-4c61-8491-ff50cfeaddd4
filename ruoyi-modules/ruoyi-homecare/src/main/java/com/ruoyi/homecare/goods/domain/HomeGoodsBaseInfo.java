package com.ruoyi.homecare.goods.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName HomeGoodsBaseInfo
 * @Description
 * <AUTHOR>
 * @Date 2022/7/11 15:28
 */
@Data
@TableName("t_home_goods_base_info")
@ApiModel("商品管理")
public class HomeGoodsBaseInfo implements Serializable {

    public static final Long DEL_FLAG_SHOW = 0L;// 逻辑删除标记（0：显示；1：隐藏
    public static final Long DEL_FLAG_HIDING = 1L;
    public static final Long GOODS_TYPE_GENERAL = 1L;// 商品类型：1:普通商品 2：菜品
    public static final Long GOODS_TYPE_MEAL = 2L;
    public static final Long GOODS_STATUS_UP = 1L;// 1：已上架 2：商家下架 3：平台下架
    public static final Long GOODS_STATUS_BUS_DOWN = 2L;// 1：已上架 2：商家下架 3：平台下架
    public static final Long GOODS_STATUS_PL_DOWN = 3L;// 1：已上架 2：商家下架 3：平台下架
    private static final long serialVersionUID = -4872800524977205067L;
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("id")
    private Long id;

    /**
     * 商品分类
     */
    @ApiModelProperty("商品分类")
    private Long categoryId;

    /**
     * 商家id
     */
    @ApiModelProperty("商家id")
    private Long providerId;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String goodsName;

    /**
     * 规格
     */
    @ApiModelProperty("规格")
    private String specification;

    /**
     * 价格
     */
    @ApiModelProperty("价格")
    private BigDecimal price;

    /**
     * 库存
     */
    @ApiModelProperty("库存")
    private Long inventory;

    /**
     * 佣金比率
     */
    @ApiModelProperty("佣金比率 0-100 ")
    private Long commissionRate;

    /**
     * 商品图片
     */
    @ApiModelProperty("商品图片")
    private String img;

    /**
     * 商品介绍
     */
    @ApiModelProperty("商品介绍")
    private String introduce;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    private Date createTime;

    /**
     * 创建人员
     */
    @ApiModelProperty("创建人员")
    private String createBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /**
     * 修改人员
     */
    @ApiModelProperty("修改人员")
    private String updateBy;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    @ApiModelProperty("逻辑删除标记（0：显示；1：隐藏")
    private Long delFlag;

    /**
     * 备注
     */
    @ApiModelProperty("备注（商品介绍）")
    private String remark;

    /**
     * 备注2
     */
    @ApiModelProperty(" 备注2（禁忌说明）s")
    private String remarkTwo;

    /**
     * 备注3
     */
    @ApiModelProperty("备注3")
    private String remarkThree;

    /**
     * 商品类型：1:普通商品 2：菜品
     */
    @ApiModelProperty("商品类型：1:普通商品 2：菜品")
    private Long type;

    @ApiModelProperty("销量")
    private Long sales;

    @ApiModelProperty("状态- 1：已上架 2：商家下架 3：平台下架")
    private Long status;

}
