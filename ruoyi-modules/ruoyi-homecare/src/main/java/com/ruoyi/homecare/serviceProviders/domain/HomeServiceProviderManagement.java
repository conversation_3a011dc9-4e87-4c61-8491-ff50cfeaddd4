package com.ruoyi.homecare.serviceProviders.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.homecare.utils.DictUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 服务商管理对象 t_home_service_provider_management
 *
 * <AUTHOR>
 * @date 2022-06-14
 */
@ApiModel(value = "服务商管理")
@TableName("t_home_service_provider_management")
public class HomeServiceProviderManagement extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 名称
     */
    @Excel(name = "名称")
    @ApiModelProperty("名称")
    private String name;

    /**
     * 性质
     */
    @Excel(name = "商家性质")
    @ApiModelProperty("商家性质")
    private String nature;

    /**
     * 登录名
     */
    @Excel(name = "登录名")
    @ApiModelProperty("登录名")
    private String userName;

    /**
     * 密码
     */
    @Excel(name = "密码")
    @ApiModelProperty("密码")
    private String password;

    /**
     * 负责人名称
     */
    @Excel(name = "负责人名称")
    @ApiModelProperty("负责人名称")
    private String principal;

    /**
     * 负责人电话
     */
    @Excel(name = "负责人电话")
    @ApiModelProperty("负责人电话")
    private String phone;

    /**
     * 地址
     */
    @Excel(name = "地址")
    @ApiModelProperty("地址")
    private String address;

    /**
     * 是否营业：0正在营业，1暂停休息
     */
    @Excel(name = "是否营业：0正在营业，1暂停休息")
    @ApiModelProperty("是否营业：0正在营业，1暂停休息")
    private String serviceState;

    /**
     * 节假日是否提供服务
     */
    @Excel(name = "节假日是否提供服务")
    @ApiModelProperty("节假日是否提供服务")
    private String holidaySeason;

    /**
     * 周末是否提供服务
     */
    @Excel(name = "周末是否提供服务")
    @ApiModelProperty("周末是否提供服务")
    private String weekendFlag;

    /**
     * 营业时间
     */
    @Excel(name = "营业时间")
    @ApiModelProperty("营业时间")
    private String businessHours;

    /**
     * 固定电话
     */
    @Excel(name = "固定电话")
    @ApiModelProperty("固定电话")
    private String fixedTelephone;

    /**
     * 服务范围
     */
    @Excel(name = "服务范围")
    @ApiModelProperty("服务范围")
    private String serviceType;

    /**
     * 服务范围
     */
    @Excel(name = "服务范围")
    @ApiModelProperty("服务范围")
    private String serviceTypeLabel;

    /**
     * 标签
     */
    @Excel(name = "标签")
    @ApiModelProperty("标签")
    private String label;

    /**
     * 营业执照
     */
    @Excel(name = "营业执照")
    @ApiModelProperty("营业执照")
    private String businessLicense;

    /**
     * 商家照片
     */
    @Excel(name = "商家照片")
    @ApiModelProperty("商家照片")
    private String merchantsPhotos;

    /**
     * 合同附件
     */
    @Excel(name = "合同附件")
    @ApiModelProperty("合同附件")
    private String contractAttachment;

    /**
     * 状态：0未通过，0未审核，2拒绝
     */
    @Excel(name = "状态：0未审核，1已通过，2拒绝")
    @ApiModelProperty("状态：0未审核，1已通过，2拒绝")
    private String state;

    /**
     * 新增类型：0.adminpc,1.服务商pc,2服务商wx
     */
    @Excel(name = "新增类型：0.adminpc,1.服务商pc,2服务商wx")
    @ApiModelProperty("新增类型：0.adminpc,1.服务商pc,2服务商wx")
    private String createClient;

    /**
     * 社区id
     */
    @Excel(name = "社区id")
    @ApiModelProperty("社区id")
    private String communityId;

    /**
     * 审核时间
     */
    @Excel(name = "审核时间")
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
     * 审核拒绝原因
     */
    @Excel(name = "审核拒绝原因")
    @ApiModelProperty("审核拒绝原因")
    private String auditCause;

    /**
     * 系统用户id
     */
    @Excel(name = "系统用户id")
    @ApiModelProperty("系统用户id")
    private Long sysUserId;

    /**
     * 已售量
     */
    @Excel(name = "已售量")
    @ApiModelProperty("已售量")
    private Integer totalSales;

    /**
     * 好评数
     */
    @Excel(name = "好评数")
    @ApiModelProperty("好评数")
    private Integer praiseNum;
    /**
     * 差评数
     */
    @Excel(name = "差评数")
    @ApiModelProperty("差评数")
    private Integer badNum;

    /**
     * 总订单数
     */
    @Excel(name = "总订单数")
    @ApiModelProperty("总订单数")
    private Integer totalOrderNum;

    /**
     * 已评价数
     */
    @Excel(name = "已评价数")
    @ApiModelProperty("已评价数")
    private Integer evaluateNum;

    /**
     * 已评价总星数值
     */
    @Excel(name = "已评价总星数值")
    @ApiModelProperty("已评价总星数值")
    private Integer evaluateNumValue;


    /**
     * 封禁状态
     */
    @Excel(name = "封禁状态(0否 1是)")
    @ApiModelProperty("封禁状态(0否 1是)")
    private Integer banFlag;

    /**
     * 好评率(计算出来的)
     */
    @Excel(name = "好评率(计算出来的)")
    @ApiModelProperty("好评率(计算出来的)")
    private String praiseRate;

    @ApiModelProperty("标签list")
    private List<String> labelList;

    @ApiModelProperty("服务范围list")
    private List<String> serviceTypeList;

    @ApiModelProperty("社区id-list")
    private List<Integer> communityIdList;

    @ApiModelProperty("营业执照list")
    private List<String> businessLicenseList;

    @ApiModelProperty("合同附件list")
    private List<String> contractAttachmentList;

    @ApiModelProperty("商家照片list")
    private List<String> merchantsPhotosList;


    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNature() {
        return nature;
    }

    public void setNature(String nature) {
        this.nature = nature;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getServiceState() {
        return serviceState;
    }

    public void setServiceState(String serviceState) {
        this.serviceState = serviceState;
    }

    public String getHolidaySeason() {
        return holidaySeason;
    }

    public void setHolidaySeason(String holidaySeason) {
        this.holidaySeason = holidaySeason;
    }

    public String getWeekendFlag() {
        return weekendFlag;
    }

    public void setWeekendFlag(String weekendFlag) {
        this.weekendFlag = weekendFlag;
    }

    public String getBusinessHours() {
        return businessHours;
    }

    public void setBusinessHours(String businessHours) {
        this.businessHours = businessHours;
    }

    public String getFixedTelephone() {
        return fixedTelephone;
    }

    public void setFixedTelephone(String fixedTelephone) {
        this.fixedTelephone = fixedTelephone;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getBusinessLicense() {
        return businessLicense;
    }

    public void setBusinessLicense(String businessLicense) {
        this.businessLicense = businessLicense;
    }

    public String getMerchantsPhotos() {
        return merchantsPhotos;
    }

    public void setMerchantsPhotos(String merchantsPhotos) {
        this.merchantsPhotos = merchantsPhotos;
    }

    public String getContractAttachment() {
        return contractAttachment;
    }

    public void setContractAttachment(String contractAttachment) {
        this.contractAttachment = contractAttachment;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        if ("".equals(delFlag) || delFlag == null) {
            this.delFlag = "0";// 去除该属性的前后空格并进行非空非null判断
        } else {
            this.delFlag = delFlag;
        }
    }

    public String getServiceTypeLabel() {
        {
            if (StringUtils.isEmpty(serviceType)) {
                return "";
            }
            return getLabel(this.serviceType, "service_type");
        }
    }

    public void setServiceTypeLabel(String serviceTypeLabel) {
        this.serviceTypeLabel = serviceTypeLabel;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCreateClient() {
        return createClient;
    }

    public void setCreateClient(String createClient) {
        this.createClient = createClient;
    }

    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }


    public Long getSysUserId() {
        return sysUserId;
    }

    public void setSysUserId(Long sysUserId) {
        this.sysUserId = sysUserId;
    }

    public String getAuditCause() {
        return auditCause;
    }

    public void setAuditCause(String auditCause) {
        this.auditCause = auditCause;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getTotalSales() {
        if (null == totalSales) {
            return 0;
        }
        return totalSales;
    }

    public void setTotalSales(Integer totalSales) {
        this.totalSales = totalSales;
    }

    public Integer getPraiseNum() {
        return praiseNum;
    }

    public void setPraiseNum(Integer praiseNum) {
        this.praiseNum = praiseNum;
    }

    public Integer getBadNum() {
        if (null == this.badNum) {
            return 0;
        }
        return badNum;
    }

    public void setBadNum(Integer badNum) {
        this.badNum = badNum;
    }

    public Integer getTotalOrderNum() {
        if (null == this.totalOrderNum) {
            return 0;
        }
        return totalOrderNum;
    }

    public void setTotalOrderNum(Integer totalOrderNum) {
        this.totalOrderNum = totalOrderNum;
    }

    public Integer getBanFlag() {
        return banFlag;
    }

    public void setBanFlag(Integer banFlag) {
        this.banFlag = banFlag;
    }

    public String getPraiseRate() {
        return praiseRate;
    }

    public void setPraiseRate(String praiseRate) {
        this.praiseRate = praiseRate;
    }

    public Integer getEvaluateNum() {
        if (null == evaluateNum) {
            return 0;
        }
        return evaluateNum;
    }

    public void setEvaluateNum(Integer evaluateNum) {
        this.evaluateNum = evaluateNum;
    }

    public Integer getEvaluateNumValue() {
        if (null == evaluateNumValue) {
            return 0;
        }
        return evaluateNumValue;
    }

    public void setEvaluateNumValue(Integer evaluateNumValue) {
        this.evaluateNumValue = evaluateNumValue;
    }

    @Override
    public String toString() {
        return "HomeServiceProviderManagement{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", nature='" + nature + '\'' +
                ", userName='" + userName + '\'' +
                ", password='" + password + '\'' +
                ", principal='" + principal + '\'' +
                ", phone='" + phone + '\'' +
                ", address='" + address + '\'' +
                ", serviceState='" + serviceState + '\'' +
                ", holidaySeason='" + holidaySeason + '\'' +
                ", weekendFlag='" + weekendFlag + '\'' +
                ", businessHours='" + businessHours + '\'' +
                ", fixedTelephone='" + fixedTelephone + '\'' +
                ", serviceType='" + serviceType + '\'' +
                ", serviceTypeLabel='" + serviceTypeLabel + '\'' +
                ", label='" + label + '\'' +
                ", businessLicense='" + businessLicense + '\'' +
                ", merchantsPhotos='" + merchantsPhotos + '\'' +
                ", contractAttachment='" + contractAttachment + '\'' +
                ", state='" + state + '\'' +
                ", createClient='" + createClient + '\'' +
                ", communityId='" + communityId + '\'' +
                ", auditTime=" + auditTime +
                ", auditCause='" + auditCause + '\'' +
                ", sysUserId=" + sysUserId +
                ", totalSales=" + totalSales +
                ", praiseNum=" + praiseNum +
                ", badNum=" + badNum +
                ", totalOrderNum=" + totalOrderNum +
                ", banFlag=" + banFlag +
                ", delFlag='" + delFlag + '\'' +
                '}';
    }

    private String getLabel(String value, String type) {
        String[] split = value.split(",");
        String data = "";
        for (int i = 0; i < split.length; i++) {
            if (i == 0) {
                data += DictUtils.selectDictLabel(type, split[i]);
            } else {
                data += "、" + DictUtils.selectDictLabel(type, split[i]);
            }
        }
        return data;
    }


    public List<String> getLabelList() {
        return labelList;
    }

    public void setLabelList(List<String> labelList) {
        this.labelList = labelList;
    }

    public List<String> getServiceTypeList() {
        return serviceTypeList;
    }

    public void setServiceTypeList(List<String> serviceTypeList) {
        this.serviceTypeList = serviceTypeList;
    }

    public List<Integer> getCommunityIdList() {
        return communityIdList;
    }

    public void setCommunityIdList(List<Integer> communityIdList) {
        this.communityIdList = communityIdList;
    }

    public List<String> getBusinessLicenseList() {
        return businessLicenseList;
    }

    public void setBusinessLicenseList(List<String> businessLicenseList) {
        this.businessLicenseList = businessLicenseList;
    }

    public List<String> getContractAttachmentList() {
        return contractAttachmentList;
    }

    public void setContractAttachmentList(List<String> contractAttachmentList) {
        this.contractAttachmentList = contractAttachmentList;
    }

    public List<String> getMerchantsPhotosList() {
        return merchantsPhotosList;
    }

    public void setMerchantsPhotosList(List<String> merchantsPhotosList) {
        this.merchantsPhotosList = merchantsPhotosList;
    }
}
