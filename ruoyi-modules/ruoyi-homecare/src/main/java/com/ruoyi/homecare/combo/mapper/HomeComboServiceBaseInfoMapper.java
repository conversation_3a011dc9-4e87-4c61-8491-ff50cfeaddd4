package com.ruoyi.homecare.combo.mapper;

import com.ruoyi.homecare.combo.domain.HomeComboServiceBaseInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 套餐设置服务项Mapper接口
 *
 * <AUTHOR>
 * @date 2022-06-27
 */
@Mapper
public interface HomeComboServiceBaseInfoMapper {
    /**
     * 查询套餐设置服务项
     *
     * @param id 套餐设置服务项主键
     * @return 套餐设置服务项
     */
    public HomeComboServiceBaseInfo selectHomeComboServiceBaseInfoById(Long id);

    /**
     * 查询套餐设置服务项列表
     *
     * @param homeComboServiceBaseInfo 套餐设置服务项
     * @return 套餐设置服务项集合
     */
    public List<HomeComboServiceBaseInfo> selectHomeComboServiceBaseInfoList(HomeComboServiceBaseInfo homeComboServiceBaseInfo);

    /**
     * 新增套餐设置服务项
     *
     * @param homeComboServiceBaseInfo 套餐设置服务项
     * @return 结果
     */
    public int insertHomeComboServiceBaseInfo(HomeComboServiceBaseInfo homeComboServiceBaseInfo);

    /**
     * 修改套餐设置服务项
     *
     * @param homeComboServiceBaseInfo 套餐设置服务项
     * @return 结果
     */
    public int updateHomeComboServiceBaseInfo(HomeComboServiceBaseInfo homeComboServiceBaseInfo);

    /**
     * 删除套餐设置服务项
     *
     * @param id 套餐设置服务项主键
     * @return 结果
     */
    public int deleteHomeComboServiceBaseInfoById(Long id);

    /**
     * 批量删除套餐设置服务项
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeComboServiceBaseInfoByIds(Long[] ids);
}
