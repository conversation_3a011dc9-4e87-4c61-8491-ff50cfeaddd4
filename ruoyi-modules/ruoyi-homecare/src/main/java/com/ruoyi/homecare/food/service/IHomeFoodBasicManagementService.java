package com.ruoyi.homecare.food.service;

import java.util.List;

import com.ruoyi.homecare.food.domain.HomeFoodBasicManagement;

/**
 * 餐饮管理基础设置Service接口
 *
 * <AUTHOR>
 * @date 2022-06-27
 */
public interface IHomeFoodBasicManagementService {
    /**
     * 查询餐饮管理基础设置
     *
     * @param id 餐饮管理基础设置主键
     * @return 餐饮管理基础设置
     */
    public HomeFoodBasicManagement selectHomeFoodBasicManagementById(Long id);

    /**
     * 查询餐饮管理基础设置列表
     *
     * @param homeFoodBasicManagement 餐饮管理基础设置
     * @return 餐饮管理基础设置集合
     */
    public List<HomeFoodBasicManagement> selectHomeFoodBasicManagementList(HomeFoodBasicManagement homeFoodBasicManagement);

    /**
     * 新增餐饮管理基础设置
     *
     * @param homeFoodBasicManagement 餐饮管理基础设置
     * @return 结果
     */
    public int insertHomeFoodBasicManagement(HomeFoodBasicManagement homeFoodBasicManagement);

    /**
     * 修改餐饮管理基础设置
     *
     * @param homeFoodBasicManagement 餐饮管理基础设置
     * @return 结果
     */
    public int updateHomeFoodBasicManagement(HomeFoodBasicManagement homeFoodBasicManagement);

    /**
     * 批量删除餐饮管理基础设置
     *
     * @param ids 需要删除的餐饮管理基础设置主键集合
     * @return 结果
     */
    public int deleteHomeFoodBasicManagementByIds(Long[] ids);

    /**
     * 删除餐饮管理基础设置信息
     *
     * @param id 餐饮管理基础设置主键
     * @return 结果
     */
    public int deleteHomeFoodBasicManagementById(Long id);
}
