package com.ruoyi.homecare.volunteers.service.impl;

import java.util.Date;
import java.util.List;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.homecare.volunteers.domain.HomeGiftSet;
import com.ruoyi.homecare.volunteers.domain.HomeVolunteerBaseInfo;
import com.ruoyi.homecare.volunteers.domain.vo.ChangeVolunteerTimeCoinVo;
import com.ruoyi.homecare.volunteers.mapper.HomeGiftSetMapper;
import com.ruoyi.homecare.volunteers.mapper.HomeVolunteerBaseInfoMapper;
import com.ruoyi.homecare.volunteers.service.IHomeVolunteerTimeCoinChangeLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.homecare.volunteers.mapper.HomeExchangeRecordMapper;
import com.ruoyi.homecare.volunteers.domain.HomeExchangeRecord;
import com.ruoyi.homecare.volunteers.service.IHomeExchangeRecordService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 礼品兑换记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-17
 */
@Service
public class HomeExchangeRecordServiceImpl implements IHomeExchangeRecordService {
    @Autowired
    protected IHomeVolunteerTimeCoinChangeLogService homeVolunteerTimeCoinChangeLogService;
    @Autowired
    private HomeExchangeRecordMapper homeExchangeRecordMapper;
    @Autowired
    private HomeVolunteerBaseInfoMapper homeVolunteerBaseInfoMapper;
    @Autowired
    private HomeGiftSetMapper homeGiftSetMapper;

    /**
     * 查询礼品兑换记录
     *
     * @param id 礼品兑换记录主键
     * @return 礼品兑换记录
     */
    @Override
    public HomeExchangeRecord selectHomeExchangeRecordById(Long id) {
        return homeExchangeRecordMapper.selectHomeExchangeRecordById(id);
    }

    /**
     * 查询礼品兑换记录列表
     *
     * @param homeExchangeRecord 礼品兑换记录
     * @return 礼品兑换记录
     */
    @Override
    public List<HomeExchangeRecord> selectHomeExchangeRecordList(HomeExchangeRecord homeExchangeRecord) {
        return homeExchangeRecordMapper.selectHomeExchangeRecordList(homeExchangeRecord);
    }

    /**
     * 新增礼品兑换记录
     *
     * @param homeExchangeRecord 礼品兑换记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult insertHomeExchangeRecord(HomeExchangeRecord homeExchangeRecord) {
        HomeVolunteerBaseInfo homeVolunteerBaseInfo = homeVolunteerBaseInfoMapper.selectHomeVolunteerBaseInfoById(homeExchangeRecord.getVolunteerId());
        homeExchangeRecord.setBeforeTimeCoin(homeVolunteerBaseInfo.getCurrentTimeCoin());
        HomeGiftSet homeGiftSet = homeGiftSetMapper.selectHomeGiftSetById(homeExchangeRecord.getGiftId());
        if (homeGiftSet.getQuantity() < 1) {
            return AjaxResult.error("当前库存不足");
        }
        Long timeCoin = homeVolunteerBaseInfo.getCurrentTimeCoin() - homeGiftSet.getTimeCoin();
        int flag = Long.signum(timeCoin);
        if (flag == -1) {
            return AjaxResult.error("当前时间币不足");
        }
        /*homeVolunteerBaseInfo.setCurrentTimeCoin(timeCoin);
        homeVolunteerBaseInfoMapper.updateHomeVolunteerBaseInfo(homeVolunteerBaseInfo);*/
        // 更新志愿者时间币
        ChangeVolunteerTimeCoinVo changeVo = new ChangeVolunteerTimeCoinVo();// 时间币变更记录
        changeVo.setVolunteerId(homeExchangeRecord.getVolunteerId());
        changeVo.setType("2");
        changeVo.setChangeType("4");// 礼品兑换
        changeVo.setChangedAmount(homeGiftSet.getTimeCoin());
        changeVo.setDescribe("兑换“" + homeGiftSet.getName() + "”礼品兑换：" + homeGiftSet.getTimeCoin());
        changeVo.setOriginalNumber(String.valueOf(homeExchangeRecord.getId()));
        homeVolunteerTimeCoinChangeLogService.changeVolunteerTimeCoin(changeVo);

        // 更新礼品库存
        Long quantity = homeGiftSet.getQuantity();
        quantity = quantity - 1;
        homeGiftSet.setQuantity(quantity);
        Long hasChange = homeGiftSet.getHasChange();
        hasChange = hasChange + 1;
        homeGiftSet.setHasChange(hasChange);
        homeGiftSetMapper.updateHomeGiftSet(homeGiftSet);
        // 更新兑换记录
        homeExchangeRecord.setCreateTime(DateUtils.getNowDate());
        homeExchangeRecord.setTimeCoin(homeGiftSet.getTimeCoin());
        homeExchangeRecord.setGiftName(homeGiftSet.getName());
        homeExchangeRecord.setSurplusTimeCoin(timeCoin);
        homeExchangeRecord.setExchangeTime(new Date());
        homeExchangeRecord.setVolunteerName(homeVolunteerBaseInfo.getName());
        return AjaxResult.success(homeExchangeRecordMapper.insertHomeExchangeRecord(homeExchangeRecord));
    }

    /**
     * 修改礼品兑换记录
     *
     * @param homeExchangeRecord 礼品兑换记录
     * @return 结果
     */
    @Override
    public int updateHomeExchangeRecord(HomeExchangeRecord homeExchangeRecord) {
        homeExchangeRecord.setUpdateTime(DateUtils.getNowDate());
        return homeExchangeRecordMapper.updateHomeExchangeRecord(homeExchangeRecord);
    }

    /**
     * 批量删除礼品兑换记录
     *
     * @param ids 需要删除的礼品兑换记录主键
     * @return 结果
     */
    @Override
    public int deleteHomeExchangeRecordByIds(Long[] ids) {
        return homeExchangeRecordMapper.deleteHomeExchangeRecordByIds(ids);
    }

    /**
     * 删除礼品兑换记录信息
     *
     * @param id 礼品兑换记录主键
     * @return 结果
     */
    @Override
    public int deleteHomeExchangeRecordById(Long id) {
        return homeExchangeRecordMapper.deleteHomeExchangeRecordById(id);
    }
}
