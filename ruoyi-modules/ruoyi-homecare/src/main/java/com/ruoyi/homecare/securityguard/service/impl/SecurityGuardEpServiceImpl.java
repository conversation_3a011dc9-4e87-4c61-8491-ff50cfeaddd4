package com.ruoyi.homecare.securityguard.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardEp;
import com.ruoyi.homecare.securityguard.mapper.SecurityGuardEpMapper;
import com.ruoyi.homecare.securityguard.service.ISecurityGuardEpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ep设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-01
 */
@Service
public class SecurityGuardEpServiceImpl implements ISecurityGuardEpService {
    @Autowired
    private SecurityGuardEpMapper securityGuardEpMapper;

    /**
     * 查询ep设备
     *
     * @param id ep设备主键
     * @return ep设备
     */
    @Override
    public SecurityGuardEp selectSecurityGuardEpById(Long id) {
        return securityGuardEpMapper.selectSecurityGuardEpById(id);
    }

    /**
     * 查询ep设备列表
     *
     * @param securityGuardEp ep设备
     * @return ep设备
     */
    @Override
    public List<SecurityGuardEp> selectSecurityGuardEpList(SecurityGuardEp securityGuardEp) {
        return securityGuardEpMapper.selectSecurityGuardEpList(securityGuardEp);
    }

    /**
     * 新增ep设备
     *
     * @param securityGuardEp ep设备
     * @return 结果
     */
    @Override
    public int insertSecurityGuardEp(SecurityGuardEp securityGuardEp) {
        securityGuardEp.setCreateTime(DateUtils.getNowDate());
        return securityGuardEpMapper.insertSecurityGuardEp(securityGuardEp);
    }

    /**
     * 修改ep设备
     *
     * @param securityGuardEp ep设备
     * @return 结果
     */
    @Override
    public int updateSecurityGuardEp(SecurityGuardEp securityGuardEp) {
        securityGuardEp.setUpdateTime(DateUtils.getNowDate());
        return securityGuardEpMapper.updateSecurityGuardEp(securityGuardEp);
    }

    /**
     * 批量删除ep设备
     *
     * @param ids 需要删除的ep设备主键
     * @return 结果
     */
    @Override
    public int deleteSecurityGuardEpByIds(Long[] ids) {
        return securityGuardEpMapper.deleteSecurityGuardEpByIds(ids);
    }

    /**
     * 删除ep设备信息
     *
     * @param id ep设备主键
     * @return 结果
     */
    @Override
    public int deleteSecurityGuardEpById(Long id) {
        return securityGuardEpMapper.deleteSecurityGuardEpById(id);
    }

    @Override
    public SecurityGuardEp selectSecurityGuardByEPIEEE(String ep, String ieee) {
        return securityGuardEpMapper.selectSecurityGuardByEPIEEE(ep, ieee);
    }
}
