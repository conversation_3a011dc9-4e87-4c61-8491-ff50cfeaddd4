package com.ruoyi.homecare.elderlyPeople.mapper;

import com.ruoyi.homecare.elderlyPeople.domain.ReviewRecordsInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 能力评估复核Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@Mapper
public interface HomeCareReviewRecordsInfoMapper {
    /**
     * 查询能力评估复核
     *
     * @param id 能力评估复核主键
     * @return 能力评估复核
     */
    public ReviewRecordsInfo selectReviewRecordsInfoById(Long id);

    /**
     * 查询能力评估复核列表
     *
     * @param reviewRecordsInfo 能力评估复核
     * @return 能力评估复核集合
     */
    public List<ReviewRecordsInfo> selectReviewRecordsInfoList(ReviewRecordsInfo reviewRecordsInfo);

    /**
     * 新增能力评估复核
     *
     * @param reviewRecordsInfo 能力评估复核
     * @return 结果
     */
    public int insertReviewRecordsInfo(ReviewRecordsInfo reviewRecordsInfo);

    /**
     * 修改能力评估复核
     *
     * @param reviewRecordsInfo 能力评估复核
     * @return 结果
     */
    public int updateReviewRecordsInfo(ReviewRecordsInfo reviewRecordsInfo);

    /**
     * 删除能力评估复核
     *
     * @param id 能力评估复核主键
     * @return 结果
     */
    public int deleteReviewRecordsInfoById(Long id);

    /**
     * 批量删除能力评估复核
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteReviewRecordsInfoByIds(Long[] ids);

    /**
     * 根据评估id获取复核数据
     *
     * @param baseId
     * @return
     */
    public ReviewRecordsInfo getBaseInfo(Long baseId);
}
