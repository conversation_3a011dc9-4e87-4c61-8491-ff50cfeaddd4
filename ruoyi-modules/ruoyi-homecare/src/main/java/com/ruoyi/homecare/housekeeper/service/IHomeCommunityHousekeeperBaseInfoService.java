package com.ruoyi.homecare.housekeeper.service;

import java.util.List;

import com.ruoyi.homecare.housekeeper.domain.HomeCommunityHousekeeperBaseInfo;

/**
 * 社区管家Service接口
 *
 * <AUTHOR>
 * @date 2022-06-22
 */
public interface IHomeCommunityHousekeeperBaseInfoService {
    /**
     * 查询社区管家
     *
     * @param id 社区管家主键
     * @return 社区管家
     */
    public HomeCommunityHousekeeperBaseInfo selectHomeCommunityHousekeeperBaseInfoById(Long id);

    /**
     * 查询社区管家列表
     *
     * @param homeCommunityHousekeeperBaseInfo 社区管家
     * @return 社区管家集合
     */
    public List<HomeCommunityHousekeeperBaseInfo> selectHomeCommunityHousekeeperBaseInfoList(HomeCommunityHousekeeperBaseInfo homeCommunityHousekeeperBaseInfo);

    /**
     * 新增社区管家
     *
     * @param homeCommunityHousekeeperBaseInfo 社区管家
     * @return 结果
     */
    public int insertHomeCommunityHousekeeperBaseInfo(HomeCommunityHousekeeperBaseInfo homeCommunityHousekeeperBaseInfo);

    /**
     * 修改社区管家
     *
     * @param homeCommunityHousekeeperBaseInfo 社区管家
     * @return 结果
     */
    public int updateHomeCommunityHousekeeperBaseInfo(HomeCommunityHousekeeperBaseInfo homeCommunityHousekeeperBaseInfo);

    /**
     * 批量删除社区管家
     *
     * @param ids 需要删除的社区管家主键集合
     * @return 结果
     */
    public int deleteHomeCommunityHousekeeperBaseInfoByIds(Long[] ids);

    /**
     * 删除社区管家信息
     *
     * @param id 社区管家主键
     * @return 结果
     */
    public int deleteHomeCommunityHousekeeperBaseInfoById(Long id);
}
