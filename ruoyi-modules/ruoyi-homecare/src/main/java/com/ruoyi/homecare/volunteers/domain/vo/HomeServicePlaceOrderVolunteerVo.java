package com.ruoyi.homecare.volunteers.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 老人移动端志愿者服务对象 t_home_service_place_order_volunteer
 *
 * <AUTHOR>
 * @date 2022-07-15
 */
@ApiModel(value = "志愿者移动端待完成详情")
@Data
public class HomeServicePlaceOrderVolunteerVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 要求服务时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "要求服务时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "要求服务时间")
    private Date serviceDate;

    /**
     * 服务id
     */
    @Excel(name = "服务id")
    @ApiModelProperty(value = "服务id")
    private Long serviceId;

    /**
     * 老人id
     */
    @Excel(name = "老人id")
    @ApiModelProperty(value = "老人id")
    private String userId;

    /**
     * 志愿者id
     */
    @Excel(name = "志愿者id")
    @ApiModelProperty(value = "志愿者id")
    private Long volunteerId;

    /**
     * 服务地址
     */
    @Excel(name = "服务地址")
    @ApiModelProperty(value = "服务地址")
    private String serviceAddress;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 是否指定人员：0未指定，1指定，2已接单
     */
    @Excel(name = "是否指定人员：0未指定，1指定，2已接单")
    @ApiModelProperty(value = "是否指定人员：0未指定，1指定，2已接单")
    private String flag;

    /**
     * 社区id
     */
    @Excel(name = "社区id")
    @ApiModelProperty(value = "社区id")
    private Long communityId;

    /**
     * 工单号
     */
    @Excel(name = "工单号")
    @ApiModelProperty(value = "工单号")
    private String orderNumber;

    /**
     * 服务状态0未开始，1服务中，2已结束
     */
    @Excel(name = "服务状态0未开始，1服务中，2已结束")
    @ApiModelProperty(value = "服务状态0未开始，1服务中，2已结束")
    private String state;

    /**
     * 老人名称
     */
    @Excel(name = "老人名称")
    @ApiModelProperty(value = "老人名称")
    private String userName;

    /**
     * 志愿者名称
     */
    @Excel(name = "志愿者名称")
    @ApiModelProperty(value = "志愿者名称")
    private String volunteerName;

    /**
     * 开始服务图片
     */
    @Excel(name = "开始服务图片")
    private String beginImg;
    /**
     * 服务中图片
     */
    @Excel(name = "服务中图片")
    private String img;

    /**
     * 结束服务图片
     */
    @Excel(name = "结束服务图片")
    private String endImg;
    /**
     * 服务开始时间
     */
    @Excel(name = "服务开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "服务开始时间")
    private Date beginTime;

    /**
     * 服务结束时间
     */
    @Excel(name = "服务结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "服务结束时间")
    private Date endTime;

    /**
     * 服务业务表id
     */
    @Excel(name = "服务业务表id")
    @ApiModelProperty(value = "服务业务表id")
    private Long infoId;
    /**
     * 服务名称
     */
    @Excel(name = "服务名称")
    @ApiModelProperty(value = "服务名称")
    private String serviceName;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

}
