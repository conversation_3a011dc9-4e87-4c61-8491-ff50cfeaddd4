package com.ruoyi.homecare.goodsOrder.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName HomeOrderDeliveryRequest01
 * @Description
 * <AUTHOR>
 * @Date 2022/7/22 14:50
 */
@Data
@ApiModel(description = "参数")
public class HomeOrderDeliveryRequest01 {

    @NotBlank(message = "配送订单id必填！")
    @ApiModelProperty("配送订单id")
    private String deliveryId;

    @NotBlank(message = "工作图片必填！")
    @ApiModelProperty(value = "工作图片", required = true)
    private String workerImg;

}
