package com.ruoyi.homecare.elderlyPeople.mapper;

import cn.hutool.json.JSONObject;
import com.ruoyi.homecare.elderlyPeople.domain.DiseaseInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 疾病类型Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-12
 */
@Mapper
public interface HomeCareDiseaseInfoMapper {
    /**
     * 查询疾病类型
     *
     * @param id 疾病类型主键
     * @return 疾病类型
     */
    public DiseaseInfo selectDiseaseInfoById(String id);

    /**
     * 查询疾病类型列表
     *
     * @param diseaseInfo 疾病类型
     * @return 疾病类型集合
     */
    public List<DiseaseInfo> selectDiseaseInfoList(DiseaseInfo diseaseInfo);

    /**
     * 新增疾病类型
     *
     * @param diseaseInfo 疾病类型
     * @return 结果
     */
    public int insertDiseaseInfo(DiseaseInfo diseaseInfo);

    /**
     * 修改疾病类型
     *
     * @param diseaseInfo 疾病类型
     * @return 结果
     */
    public int updateDiseaseInfo(DiseaseInfo diseaseInfo);

    /**
     * 删除疾病类型
     *
     * @param id 疾病类型主键
     * @return 结果
     */
    public int deleteDiseaseInfoById(String id);

    /**
     * 批量删除疾病类型
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDiseaseInfoByIds(String[] ids);


    /**
     * 获取所有的疾病类型组成数组
     *
     * @return
     */
    List<JSONObject> getGroupConcatDisease();


    /**
     * 通过类型获取疾病数值
     *
     * @param type
     * @return
     */
    List<JSONObject> getDiseaseInfoJson(String type);
}
