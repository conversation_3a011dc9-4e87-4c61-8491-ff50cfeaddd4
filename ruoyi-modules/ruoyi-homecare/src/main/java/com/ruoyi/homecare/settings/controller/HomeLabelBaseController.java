package com.ruoyi.homecare.settings.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.json.JSONArray;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.settings.domain.HomeLabelBase;
import com.ruoyi.homecare.settings.service.IHomeLabelBaseService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 标签管理Controller
 *
 * <AUTHOR>
 * @date 2022-07-14
 */
@RestController
@RequestMapping("/homeLabelBase")
@Api(value = "工单管理-标签管理", tags = "工单管理-标签管理")
public class HomeLabelBaseController extends BaseController {
    @Autowired
    private IHomeLabelBaseService homeLabelBaseService;

    /**
     * 查询标签管理列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "标签管理列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "name", value = "名称", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型", required = false, dataTypeClass = String.class)
    })
    public TableDataInfo<HomeLabelBase> list(@ApiIgnore HomeLabelBase homeLabelBase) {
        startPage();
        List<HomeLabelBase> list = homeLabelBaseService.selectHomeLabelBaseList(homeLabelBase);
        return getDataTable(list);
    }

    /**
     * 导出标签管理列表
     */
    @Log(platform = "2", title = "标签管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiIgnore
    public void export(HttpServletResponse response, HomeLabelBase homeLabelBase) {
        List<HomeLabelBase> list = homeLabelBaseService.selectHomeLabelBaseList(homeLabelBase);
        ExcelUtil<HomeLabelBase> util = new ExcelUtil<HomeLabelBase>(HomeLabelBase.class);
        util.exportExcel(response, list, "标签管理数据");
    }

    /**
     * 获取标签管理详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取标签管理详细信息")
    public TAjaxResult<HomeLabelBase> getInfo(@PathVariable("id") String id) {
        return new TAjaxResult(homeLabelBaseService.selectHomeLabelBaseById(id));
    }

    /**
     * 获取是否有相同名称
     *
     * @param name
     * @return
     */
    @GetMapping("checkLabelUnique")
    @ApiOperation(value = "获取是否有相同名称")
    public AjaxResult checkLabelUnique(String name, String id) {
        return homeLabelBaseService.checkLabelUnique(name, id);
    }

    /**
     * 保存标签管理
     */
    @Log(platform = "2", title = "标签管理", businessType = BusinessType.INSERT)
    @PostMapping("save")
    @ApiOperation(value = "保存标签管理")
    public AjaxResult save(@RequestBody HomeLabelBase homeLabelBase) {
        if (StringUtils.isEmpty(homeLabelBase.getId())) {
            return add(homeLabelBase);
        } else {
            return edit(homeLabelBase);
        }
    }


    /**
     * 新增标签管理
     */
    @Log(platform = "2", title = "标签管理", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody HomeLabelBase homeLabelBase) {
        return toAjax(homeLabelBaseService.insertHomeLabelBase(homeLabelBase));
    }

    /**
     * 修改标签管理
     */
    @Log(platform = "2", title = "标签管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody HomeLabelBase homeLabelBase) {
        return toAjax(homeLabelBaseService.updateHomeLabelBase(homeLabelBase));
    }

    /**
     * 删除标签管理
     */
    @Log(platform = "2", title = "标签管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除标签管理")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(homeLabelBaseService.deleteHomeLabelBaseByIds(ids));
    }

}
