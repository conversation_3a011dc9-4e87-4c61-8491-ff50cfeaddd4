package com.ruoyi.homecare.serviceWorkOrder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.homecare.serviceWorkOrder.domain.HomeOrderServiceWork;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description home_service_work_orderMapper
 * @date 2022-07-18
 */
@Mapper
public interface HomeServiceWorkOrderMapper extends BaseMapper<HomeOrderServiceWork> {

    @Select(
            "<script>select t0.* from home_service_work_order t0 " +
                    // add here if need left join
                    "where 1=1" +
                    "<when test='id!=null and id!=&apos;&apos; '> and t0.id=#{id}</when> " +
                    "<when test='serviceType!=null and serviceType!=&apos;&apos; '> and t0.service_type=#{serviceType}</when> " +
                    "<when test='orderId!=null and orderId!=&apos;&apos; '> and t0.order_id=#{orderId}</when> " +
                    "<when test='serviceId!=null and serviceId!=&apos;&apos; '> and t0.service_id=#{serviceId}</when> " +
                    "<when test='serviceName!=null and serviceName!=&apos;&apos; '> and t0.service_name=#{serviceName}</when> " +
                    "<when test='reserveTime!=null and reserveTime!=&apos;&apos; '> and t0.reserve_time=#{reserveTime}</when> " +
                    "<when test='elderlyPeopleId!=null and elderlyPeopleId!=&apos;&apos; '> and t0.elderly_people_id=#{elderlyPeopleId}</when> " +
                    "<when test='serviceStartTime!=null and serviceStartTime!=&apos;&apos; '> and t0.service_start_time=#{serviceStartTime}</when> " +
                    "<when test='serviceEndTime!=null and serviceEndTime!=&apos;&apos; '> and t0.service_end_time=#{serviceEndTime}</when> " +
                    "<when test='serviceTime!=null and serviceTime!=&apos;&apos; '> and t0.service_time=#{serviceTime}</when> " +
                    "<when test='workerId!=null and workerId!=&apos;&apos; '> and t0.worker_id=#{workerId}</when> " +
                    "<when test='status!=null and status!=&apos;&apos; '> and t0.status=#{status}</when> " +
                    "<when test='serviceAddress!=null and serviceAddress!=&apos;&apos; '> and t0.service_address=#{serviceAddress}</when> " +
                    "<when test='serviceProviderId!=null and serviceProviderId!=&apos;&apos; '> and t0.service_provider_id=#{serviceProviderId}</when> " +
                    "<when test='serviceComboId!=null and serviceComboId!=&apos;&apos; '> and t0.service_combo_id=#{serviceComboId}</when> " +
                    "<when test='createTime!=null and createTime!=&apos;&apos; '> and t0.create_time=#{createTime}</when> " +
                    "<when test='createBy!=null and createBy!=&apos;&apos; '> and t0.create_by=#{createBy}</when> " +
                    "<when test='updateTime!=null and updateTime!=&apos;&apos; '> and t0.update_time=#{updateTime}</when> " +
                    "<when test='updateBy!=null and updateBy!=&apos;&apos; '> and t0.update_by=#{updateBy}</when> " +
                    "<when test='delFlag!=null and delFlag!=&apos;&apos; '> and t0.del_flag=#{delFlag}</when> " +
                    "<when test='remark!=null and remark!=&apos;&apos; '> and t0.remark=#{remark}</when> " +
                    // add here if need page limit
                    //" limit ${page},${limit} " +
                    " </script>")
    List<HomeOrderServiceWork> pageAll(HomeOrderServiceWork queryParamDTO, int page, int limit);

    @Select("<script>select count(1) from home_service_work_order t0 " +
            // add here if need left join
            "where 1=1" +
            "<when test='id!=null and id!=&apos;&apos; '> and t0.id=#{id}</when> " +
            "<when test='serviceType!=null and serviceType!=&apos;&apos; '> and t0.service_type=#{serviceType}</when> " +
            "<when test='orderId!=null and orderId!=&apos;&apos; '> and t0.order_id=#{orderId}</when> " +
            "<when test='serviceId!=null and serviceId!=&apos;&apos; '> and t0.service_id=#{serviceId}</when> " +
            "<when test='serviceName!=null and serviceName!=&apos;&apos; '> and t0.service_name=#{serviceName}</when> " +
            "<when test='reserveTime!=null and reserveTime!=&apos;&apos; '> and t0.reserve_time=#{reserveTime}</when> " +
            "<when test='elderlyPeopleId!=null and elderlyPeopleId!=&apos;&apos; '> and t0.elderly_people_id=#{elderlyPeopleId}</when> " +
            "<when test='serviceStartTime!=null and serviceStartTime!=&apos;&apos; '> and t0.service_start_time=#{serviceStartTime}</when> " +
            "<when test='serviceEndTime!=null and serviceEndTime!=&apos;&apos; '> and t0.service_end_time=#{serviceEndTime}</when> " +
            "<when test='serviceTime!=null and serviceTime!=&apos;&apos; '> and t0.service_time=#{serviceTime}</when> " +
            "<when test='workerId!=null and workerId!=&apos;&apos; '> and t0.worker_id=#{workerId}</when> " +
            "<when test='status!=null and status!=&apos;&apos; '> and t0.status=#{status}</when> " +
            "<when test='serviceAddress!=null and serviceAddress!=&apos;&apos; '> and t0.service_address=#{serviceAddress}</when> " +
            "<when test='serviceProviderId!=null and serviceProviderId!=&apos;&apos; '> and t0.service_provider_id=#{serviceProviderId}</when> " +
            "<when test='serviceComboId!=null and serviceComboId!=&apos;&apos; '> and t0.service_combo_id=#{serviceComboId}</when> " +
            "<when test='createTime!=null and createTime!=&apos;&apos; '> and t0.create_time=#{createTime}</when> " +
            "<when test='createBy!=null and createBy!=&apos;&apos; '> and t0.create_by=#{createBy}</when> " +
            "<when test='updateTime!=null and updateTime!=&apos;&apos; '> and t0.update_time=#{updateTime}</when> " +
            "<when test='updateBy!=null and updateBy!=&apos;&apos; '> and t0.update_by=#{updateBy}</when> " +
            "<when test='delFlag!=null and delFlag!=&apos;&apos; '> and t0.del_flag=#{delFlag}</when> " +
            "<when test='remark!=null and remark!=&apos;&apos; '> and t0.remark=#{remark}</when> " +
            " </script>")
    int countAll(HomeOrderServiceWork queryParamDTO);
}
