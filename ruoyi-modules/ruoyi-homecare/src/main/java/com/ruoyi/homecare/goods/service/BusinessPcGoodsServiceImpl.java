package com.ruoyi.homecare.goods.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.homecare.elderlyPeople.domain.vo.UserDataInfoResult;
import com.ruoyi.homecare.goods.domain.HomeGoodsBaseInfo;
import com.ruoyi.homecare.goods.domain.HomeGoodsCategory;
import com.ruoyi.homecare.goods.mapper.HomeGoodsBaseInfoMapper;
import com.ruoyi.homecare.goods.mapper.HomeGoodsCategoryMapper;
import com.ruoyi.homecare.goods.request.AddOrUpdateGoodsParam;
import com.ruoyi.homecare.goods.request.BusinessPcGoodsListParam;
import com.ruoyi.homecare.goods.request.GoodsUpOrDownRequest;
import com.ruoyi.homecare.goods.vo.BusinessPcGoodsAllVo;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement;
import com.ruoyi.homecare.serviceProviders.service.IHomeServiceProviderManagementService;
import com.ruoyi.homecare.utils.SysUserUtils;
import com.ruoyi.homecare.utils.UserComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @ClassName BusinessPcGoodsServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/7/26 15:13
 */
@Service
@Slf4j
public class BusinessPcGoodsServiceImpl implements BusinessPcGoodsService {

    @Autowired
    private HomeGoodsBaseInfoMapper homeGoodsBaseInfoMapper;
    @Autowired
    private HomeGoodsCategoryMapper homeGoodsCategoryMapper;
    @Autowired
    private UserComponent userComponent;


    @Override
    public List<BusinessPcGoodsAllVo> getList(BusinessPcGoodsListParam businessPcGoodsListParam) {


        UserDataInfoResult userDataInfoResult = SysUserUtils.getInfoBySysUserId(null);
        Long serviceProviderId = userDataInfoResult.getServiceProviderId();// 商家id

        String goodsName = businessPcGoodsListParam.getGoodsName();

        StringBuffer stringBuffer = new StringBuffer("select b.*,c.name as category_name from t_home_goods_base_info b left join t_home_goods_category c on b.category_id = c.id where 1=1 ");

        stringBuffer.append(" and b.del_flag = " + HomeGoodsBaseInfo.DEL_FLAG_SHOW);
        stringBuffer.append(" and b.type = " + businessPcGoodsListParam.getType());
        stringBuffer.append(" and b.provider_id = " + serviceProviderId);

        if (StrUtil.isNotBlank(goodsName)) {
            stringBuffer.append(" and b.goods_name like '%" + goodsName + "%'");
        }

        stringBuffer.append(" order by sales desc");

        Page page = new Page<>(businessPcGoodsListParam.getPageNum(), businessPcGoodsListParam.getPageSize());

        Page<BusinessPcGoodsAllVo> homeGoodsBaseInfoPage = homeGoodsBaseInfoMapper.getAll(page, stringBuffer.toString());

        return homeGoodsBaseInfoPage.getRecords();
    }

    @Override
    public HomeGoodsBaseInfo getByGoodsId(String goodsId) {

        HomeGoodsBaseInfo homeGoodsBaseInfo = homeGoodsBaseInfoMapper.selectById(goodsId);

        return homeGoodsBaseInfo;
    }

    @Override
    @Transactional
    public TAjaxResult addOrUpdate(AddOrUpdateGoodsParam addOrUpdateGoodsParam) {

        Long goodsId = addOrUpdateGoodsParam.getId();
        Date date = new Date();

        if (null == goodsId) {// 新增
            // 获取商品分类
            Long categoryId = addOrUpdateGoodsParam.getCategoryId();
            HomeGoodsCategory homeGoodsCategory = homeGoodsCategoryMapper.selectById(categoryId);

            HomeServiceProviderManagement homeServiceProviderManagement = userComponent.verifyBus();

            HomeGoodsBaseInfo homeGoodsBaseInfo = new HomeGoodsBaseInfo();
            BeanUtil.copyProperties(addOrUpdateGoodsParam, homeGoodsBaseInfo);
            homeGoodsBaseInfo.setProviderId(homeServiceProviderManagement.getId());

            homeGoodsBaseInfo.setCreateTime(date);
            homeGoodsBaseInfo.setUpdateTime(date);
            homeGoodsBaseInfo.setCreateBy(String.valueOf(homeServiceProviderManagement.getId()));
            homeGoodsBaseInfo.setDelFlag(HomeGoodsBaseInfo.DEL_FLAG_SHOW);
            homeGoodsBaseInfo.setSales(0L);
            homeGoodsBaseInfo.setStatus(HomeGoodsBaseInfo.GOODS_STATUS_UP);

            int insert = homeGoodsBaseInfoMapper.insert(homeGoodsBaseInfo);
            return new TAjaxResult().success(insert);
        }

        // 更新
        HomeGoodsBaseInfo homeGoodsBaseInfo = homeGoodsBaseInfoMapper.selectById(goodsId);
        if (null == homeGoodsBaseInfo) {
            throw new ServiceException("未查询到此商品！");
        }

        BeanUtil.copyProperties(addOrUpdateGoodsParam, homeGoodsBaseInfo);

        homeGoodsBaseInfo.setProviderId(homeGoodsBaseInfo.getProviderId());
        homeGoodsBaseInfo.setUpdateTime(date);

        int insert = homeGoodsBaseInfoMapper.updateById(homeGoodsBaseInfo);
        return new TAjaxResult().success(insert);
    }

    @Override
    @Transactional
    public TAjaxResult upOrDown(GoodsUpOrDownRequest goodsUpOrDownRequest) {

        Long sysUserId = SecurityUtils.getUserId();
        HomeServiceProviderManagement homeServiceProviderManagement = userComponent.verifyBus();

        Long id = goodsUpOrDownRequest.getId();
        Long status = goodsUpOrDownRequest.getStatus();

        HomeGoodsBaseInfo homeGoodsBaseInfo = homeGoodsBaseInfoMapper.selectById(id);
        if (null == homeGoodsBaseInfo) {
            throw new ServiceException("未查询到此商品！");
        }

        if (status == 1) {// 上架

            Long statusOld = homeGoodsBaseInfo.getStatus();

            if (!HomeGoodsBaseInfo.GOODS_STATUS_BUS_DOWN.equals(statusOld)) {
                throw new ServiceException("该商品已被管理员下架！");
            }
            if (homeGoodsBaseInfo.getStatus().equals(HomeGoodsBaseInfo.GOODS_STATUS_UP)) {// 如果是上架
                throw new ServiceException("该商品已经上架！");
            }

            homeGoodsBaseInfo.setStatus(HomeGoodsBaseInfo.GOODS_STATUS_UP);
            homeGoodsBaseInfo.setUpdateTime(new Date());
            homeGoodsBaseInfo.setUpdateBy(String.valueOf(sysUserId));
            int i = homeGoodsBaseInfoMapper.updateById(homeGoodsBaseInfo);
            return new TAjaxResult().success(i);
        }

        if (status == 2) {// 下架

            if (!homeGoodsBaseInfo.getStatus().equals(HomeGoodsBaseInfo.GOODS_STATUS_UP)) {// 如果是上架
                throw new ServiceException("该商品下经上架！");
            }

            homeGoodsBaseInfo.setStatus(HomeGoodsBaseInfo.GOODS_STATUS_BUS_DOWN);
            homeGoodsBaseInfo.setUpdateTime(new Date());
            homeGoodsBaseInfo.setUpdateBy(String.valueOf(sysUserId));

            int i = homeGoodsBaseInfoMapper.updateById(homeGoodsBaseInfo);
            return new TAjaxResult().success(i);

        }

        throw new ServiceException("status状态参数错误！");
    }

    @Override
    @Transactional
    public TAjaxResult delete(Long id) {

        userComponent.verifyBus();
        Long sysUserId = SecurityUtils.getUserId();

        HomeGoodsBaseInfo homeGoodsBaseInfo = homeGoodsBaseInfoMapper.selectById(id);
        if (null == homeGoodsBaseInfo) {
            throw new ServiceException("未查询到此商品！");
        }

        Long delFlag = homeGoodsBaseInfo.getDelFlag();
        if (HomeGoodsBaseInfo.DEL_FLAG_HIDING.equals(delFlag)) {
            throw new ServiceException("该商品已删除！");
        }

        homeGoodsBaseInfo.setDelFlag(HomeGoodsBaseInfo.DEL_FLAG_HIDING);
        homeGoodsBaseInfo.setUpdateTime(new Date());
        homeGoodsBaseInfo.setUpdateBy(String.valueOf(sysUserId));
        int i = homeGoodsBaseInfoMapper.updateById(homeGoodsBaseInfo);

        return new TAjaxResult().success(i);
    }


}
