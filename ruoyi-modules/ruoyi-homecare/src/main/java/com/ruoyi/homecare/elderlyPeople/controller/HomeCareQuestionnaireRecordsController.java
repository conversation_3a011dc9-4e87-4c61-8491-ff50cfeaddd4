package com.ruoyi.homecare.elderlyPeople.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.elderlyPeople.domain.QuestionnaireRecords;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareQuestionnaireRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 居家调查问卷历史Controller
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@RestController
@RequestMapping("/homeCareQuestionnaireRecords")
@ApiIgnore
public class HomeCareQuestionnaireRecordsController extends BaseController {
    @Autowired
    private IHomeCareQuestionnaireRecordsService questionnaireRecordsService;

    /**
     * 查询居家调查问卷历史列表
     */
    //@RequiresPermissions("elderlyPeople:questionnaireRecords:list")
    @GetMapping("/list")
    public TableDataInfo list(QuestionnaireRecords questionnaireRecords) {
        startPage();
        List<QuestionnaireRecords> list = questionnaireRecordsService.selectQuestionnaireRecordsList(questionnaireRecords);
        return getDataTable(list);
    }

    /**
     * 导出居家调查问卷历史列表
     */
    //@RequiresPermissions("elderlyPeople:questionnaireRecords:export")
    @Log(platform = "2", title = "居家调查问卷历史", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QuestionnaireRecords questionnaireRecords) {
        List<QuestionnaireRecords> list = questionnaireRecordsService.selectQuestionnaireRecordsList(questionnaireRecords);
        ExcelUtil<QuestionnaireRecords> util = new ExcelUtil<QuestionnaireRecords>(QuestionnaireRecords.class);
        util.exportExcel(response, list, "居家调查问卷历史数据");
    }

    /**
     * 获取居家调查问卷历史详细信息
     */
    //@RequiresPermissions("elderlyPeople:questionnaireRecords:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(questionnaireRecordsService.selectQuestionnaireRecordsById(id));
    }

    /**
     * 新增居家调查问卷历史
     */
    //@RequiresPermissions("elderlyPeople:questionnaireRecords:add")
    @Log(platform = "2", title = "居家调查问卷历史", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QuestionnaireRecords questionnaireRecords) {
        return toAjax(questionnaireRecordsService.insertQuestionnaireRecords(questionnaireRecords));
    }

    /**
     * 修改居家调查问卷历史
     */
    //@RequiresPermissions("elderlyPeople:questionnaireRecords:edit")
    @Log(platform = "2", title = "居家调查问卷历史", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QuestionnaireRecords questionnaireRecords) {
        return toAjax(questionnaireRecordsService.updateQuestionnaireRecords(questionnaireRecords));
    }

    /**
     * 删除居家调查问卷历史
     */
    //@RequiresPermissions("elderlyPeople:questionnaireRecords:remove")
    @Log(platform = "2", title = "居家调查问卷历史", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(questionnaireRecordsService.deleteQuestionnaireRecordsByIds(ids));
    }
}
