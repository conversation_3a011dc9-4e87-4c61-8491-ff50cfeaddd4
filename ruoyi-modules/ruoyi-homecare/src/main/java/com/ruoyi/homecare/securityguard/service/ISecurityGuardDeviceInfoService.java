package com.ruoyi.homecare.securityguard.service;

import java.util.List;

import com.ruoyi.homecare.securityguard.domain.SecurityGuardDeviceInfo;

/**
 * 设备信息Service接口
 *
 * <AUTHOR>
 * @date 2023-02-03
 */
public interface ISecurityGuardDeviceInfoService {
    /**
     * 查询设备信息
     *
     * @param id 设备信息主键
     * @return 设备信息
     */
    public SecurityGuardDeviceInfo selectSecurityGuardDeviceInfoById(Long id);

    /**
     * 查询设备信息列表
     *
     * @param securityGuardDeviceInfo 设备信息
     * @return 设备信息集合
     */
    public List<SecurityGuardDeviceInfo> selectSecurityGuardDeviceInfoList(SecurityGuardDeviceInfo securityGuardDeviceInfo);

    /**
     * 新增设备信息
     *
     * @param securityGuardDeviceInfo 设备信息
     * @return 结果
     */
    public int insertSecurityGuardDeviceInfo(SecurityGuardDeviceInfo securityGuardDeviceInfo);

    /**
     * 修改设备信息
     *
     * @param securityGuardDeviceInfo 设备信息
     * @return 结果
     */
    public int updateSecurityGuardDeviceInfo(SecurityGuardDeviceInfo securityGuardDeviceInfo);

    /**
     * 批量删除设备信息
     *
     * @param ids 需要删除的设备信息主键集合
     * @return 结果
     */
    public int deleteSecurityGuardDeviceInfoByIds(Long[] ids);

    /**
     * 删除设备信息信息
     *
     * @param id 设备信息主键
     * @return 结果
     */
    public int deleteSecurityGuardDeviceInfoById(Long id);
}
