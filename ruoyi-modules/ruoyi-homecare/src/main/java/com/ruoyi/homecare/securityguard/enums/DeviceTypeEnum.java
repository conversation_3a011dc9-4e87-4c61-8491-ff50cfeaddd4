package com.ruoyi.homecare.securityguard.enums;

/**
 * 设备类型
 *
 * <AUTHOR>
 */
public enum DeviceTypeEnum {

    身体检测("1", "身体检测"),
    门防监控("2", "门防监控");

    String value;
    String description;

    DeviceTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static DeviceTypeEnum getByValue(String type) {
        if (type == null) {
            return null;
        }
        for (DeviceTypeEnum deviceTypeEnum : DeviceTypeEnum.values()) {
            if (deviceTypeEnum.value.equals(type)) {
                return deviceTypeEnum;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
