package com.ruoyi.homecare.elderlyPeople.mapper;

import cn.hutool.json.JSONObject;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 老人基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-24
 */
@Mapper
public interface HomeCareElderlyPeopleInfoMapper extends MPJBaseMapper<ElderlyPeopleInfo> {
    /**
     * 查询老人基础信息
     *
     * @param id 老人基础信息主键
     * @return 老人基础信息
     */
    public ElderlyPeopleInfo selectElderlyPeopleInfoById(String id);

    /**
     * 查询老人基础信息列表
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 老人基础信息集合
     */
    public List<ElderlyPeopleInfo> selectElderlyPeopleInfoList(ElderlyPeopleInfo elderlyPeopleInfo);

    /**
     * 健康管理获取老人全量列表
     *
     * @param name
     * @return
     */
    public List<JSONObject> getUserList(@Param("name") String name, @Param("state") String state);

    /**
     * 新增老人基础信息
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 结果
     */
    public int insertElderlyPeopleInfo(ElderlyPeopleInfo elderlyPeopleInfo);

    /**
     * 修改老人基础信息
     *
     * @param elderlyPeopleInfo 老人基础信息
     * @return 结果
     */
    public int updateElderlyPeopleInfo(ElderlyPeopleInfo elderlyPeopleInfo);

    /**
     * 删除老人基础信息
     *
     * @param id 老人基础信息主键
     * @return 结果
     */
    public int deleteElderlyPeopleInfoById(Long id);

    /**
     * 批量删除老人基础信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteElderlyPeopleInfoByIds(Long[] ids);

    int logicalDeleteElderlyPeopleInfoByIds(String[] ids);


    /**
     * 通过系统userid查询老人基础信息
     *
     * @param sysUserId
     * @return
     */
    ElderlyPeopleInfo getElderlyPeopleInfoBySysUserId(Long sysUserId);
}
