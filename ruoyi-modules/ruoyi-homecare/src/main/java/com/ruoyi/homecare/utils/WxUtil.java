package com.ruoyi.homecare.utils;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class WxUtil {

    private static WxUtil wxUtil;
    @Autowired
    private RedisService redisService;
    private String xcxToken = "";
    private String gzhToken = "";
    @Value("${xcx.appid}")
    private String xcxAppId;
    @Value("${xcx.appsecret}")
    private String xcxAppSecret;
    @Value("${gzh.appid}")
    private String gzhAppId;
    @Value("${gzh.appsecret}")
    private String gzhAppSecret;

    @PostConstruct
    public void init() {
        wxUtil = this;
        wxUtil.redisService = this.redisService;
        wxUtil.getXcxAccessToken();
        wxUtil.getGzhAccessToken();
    }

    private String getXcxAccessToken() {
        Object token = wxUtil.redisService.getCacheObject("XCX_ACCESS_TOKEN");
        if (token != null && StringUtils.isNotBlank(token.toString())) {
            return token.toString();
        } else {
            String baseUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
            String url = String.format(baseUrl, wxUtil.xcxAppId, wxUtil.xcxAppSecret);

            String result = HttpUtil.get(url);
            Object at = JSONUtil.parseObj(result).get("access_token");
            Object ei = JSONUtil.parseObj(result).get("expires_in");
            if (at != null) {
                wxUtil.redisService.setCacheObject("XCX_ACCESS_TOKEN", at.toString(), Long.valueOf(ei.toString()), TimeUnit.SECONDS);
                return at.toString();
            }
            return null;
        }
    }

    private String getGzhAccessToken() {
        Object token = wxUtil.redisService.getCacheObject("GZH_ACCESS_TOKEN");
        if (token != null && StringUtils.isNotBlank(token.toString())) {
            return token.toString();
        } else {
            String baseUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
            String url = String.format(baseUrl, wxUtil.gzhAppId, wxUtil.gzhAppSecret);

            String result = HttpUtil.get(url);
            Object at = JSONUtil.parseObj(result).get("access_token");
            Object ei = JSONUtil.parseObj(result).get("expires_in");
            if (at != null) {
                wxUtil.redisService.setCacheObject("GZH_ACCESS_TOKEN", at.toString(), Long.valueOf(ei.toString()), TimeUnit.SECONDS);
                return at.toString();
            }
            return null;
        }
    }


}
