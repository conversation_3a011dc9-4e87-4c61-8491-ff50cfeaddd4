package com.ruoyi.homecare.settings.service;

import java.util.List;

import cn.hutool.json.JSONArray;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.homecare.settings.domain.HomeLabelBase;

/**
 * 标签管理Service接口
 *
 * <AUTHOR>
 * @date 2022-07-14
 */
public interface IHomeLabelBaseService {
    /**
     * 查询标签管理
     *
     * @param id 标签管理主键
     * @return 标签管理
     */
    public HomeLabelBase selectHomeLabelBaseById(String id);

    /**
     * 查询标签管理列表
     *
     * @param homeLabelBase 标签管理
     * @return 标签管理集合
     */
    public List<HomeLabelBase> selectHomeLabelBaseList(HomeLabelBase homeLabelBase);

    /**
     * 新增标签管理
     *
     * @param homeLabelBase 标签管理
     * @return 结果
     */
    public int insertHomeLabelBase(HomeLabelBase homeLabelBase);

    /**
     * 修改标签管理
     *
     * @param homeLabelBase 标签管理
     * @return 结果
     */
    public int updateHomeLabelBase(HomeLabelBase homeLabelBase);

    /**
     * 批量删除标签管理
     *
     * @param ids 需要删除的标签管理主键集合
     * @return 结果
     */
    public int deleteHomeLabelBaseByIds(String[] ids);

    /**
     * 删除标签管理信息
     *
     * @param id 标签管理主键
     * @return 结果
     */
    public int deleteHomeLabelBaseById(String id);

    /**
     * 获取是否有相同名称
     *
     * @param name
     * @return
     */
    AjaxResult checkLabelUnique(String name, String id);

    /**
     * 根据选择的类型获取商家标签列表
     *
     * @param types
     * @return
     */
    JSONArray getServiceProviderLabel(String types);
}
