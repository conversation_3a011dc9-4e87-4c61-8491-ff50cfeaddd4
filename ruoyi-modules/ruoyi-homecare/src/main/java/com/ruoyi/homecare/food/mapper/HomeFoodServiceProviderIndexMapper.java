package com.ruoyi.homecare.food.mapper;

import com.ruoyi.homecare.food.domain.HomeFoodServiceProviderIndex;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 餐品与服务商关联Mapper接口
 *
 * <AUTHOR>
 * @date 2022-07-05
 */
@Mapper
public interface HomeFoodServiceProviderIndexMapper {
    /**
     * 查询餐品与服务商关联
     *
     * @param id 餐品与服务商关联主键
     * @return 餐品与服务商关联
     */
    public HomeFoodServiceProviderIndex selectHomeFoodServiceProviderIndexById(Long id);

    /**
     * 查询餐品与服务商关联列表
     *
     * @param homeFoodServiceProviderIndex 餐品与服务商关联
     * @return 餐品与服务商关联集合
     */
    public List<HomeFoodServiceProviderIndex> selectHomeFoodServiceProviderIndexList(HomeFoodServiceProviderIndex homeFoodServiceProviderIndex);

    /**
     * 新增餐品与服务商关联
     *
     * @param homeFoodServiceProviderIndex 餐品与服务商关联
     * @return 结果
     */
    public int insertHomeFoodServiceProviderIndex(HomeFoodServiceProviderIndex homeFoodServiceProviderIndex);

    /**
     * 修改餐品与服务商关联
     *
     * @param homeFoodServiceProviderIndex 餐品与服务商关联
     * @return 结果
     */
    public int updateHomeFoodServiceProviderIndex(HomeFoodServiceProviderIndex homeFoodServiceProviderIndex);

    /**
     * 删除餐品与服务商关联
     *
     * @param id 餐品与服务商关联主键
     * @return 结果
     */
    public int deleteHomeFoodServiceProviderIndexById(Long id);

    /**
     * 批量删除餐品与服务商关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeFoodServiceProviderIndexByIds(Long[] ids);
}
