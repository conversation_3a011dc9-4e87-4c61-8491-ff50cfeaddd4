package com.ruoyi.homecare.goods.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.homecare.food.domain.HomeFoodBaseInfo;
import com.ruoyi.homecare.goods.request.UserMobileGoodsAllParam;
import com.ruoyi.homecare.goods.service.UserGoodsService;
import com.ruoyi.homecare.goods.vo.GoodsCategoryResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName AdminPcGoodsController
 * @Description 管理员PC端口
 * <AUTHOR>
 * @Date 2022/7/12 16:16
 */
@RestController
@RequestMapping("/userGoods")
@Api(tags = "商品管理-用户移动端")
public class UserMobileGoodsController extends BaseController {

    @Autowired
    private UserGoodsService userGoodsService;

    @GetMapping("/getAll")
    @ApiOperation(value = "根据商家id获取商品、餐品")
    public TAjaxResult<HomeFoodBaseInfo> getAll(@Valid UserMobileGoodsAllParam userMobileGoodsAllParam) {

        List<GoodsCategoryResult> list = userGoodsService.getAll(userMobileGoodsAllParam);

        return new TAjaxResult(list);
    }
}
