package com.ruoyi.homecare.volunteers.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.volunteers.domain.HomeAssociationVolunteerIndex;
import com.ruoyi.homecare.volunteers.service.IHomeAssociationVolunteerIndexService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 志愿者和社团关联Controller
 *
 * <AUTHOR>
 * @date 2022-10-10
 */
@RestController
@RequestMapping("/homeAssociationVolunteerIndex")
public class HomeAssociationVolunteerIndexController extends BaseController {
    @Autowired
    private IHomeAssociationVolunteerIndexService homeAssociationVolunteerIndexService;

    /**
     * 查询志愿者和社团关联列表
     */
    @RequiresPermissions("volunteers:homeAssociationVolunteerIndex:list")
    @GetMapping("/list")
    public TableDataInfo list(HomeAssociationVolunteerIndex homeAssociationVolunteerIndex) {
        startPage();
        List<HomeAssociationVolunteerIndex> list = homeAssociationVolunteerIndexService.selectHomeAssociationVolunteerIndexList(homeAssociationVolunteerIndex);
        return getDataTable(list);
    }

    /**
     * 导出志愿者和社团关联列表
     */
    @RequiresPermissions("volunteers:homeAssociationVolunteerIndex:export")
    @Log(title = "志愿者和社团关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeAssociationVolunteerIndex homeAssociationVolunteerIndex) {
        List<HomeAssociationVolunteerIndex> list = homeAssociationVolunteerIndexService.selectHomeAssociationVolunteerIndexList(homeAssociationVolunteerIndex);
        ExcelUtil<HomeAssociationVolunteerIndex> util = new ExcelUtil<HomeAssociationVolunteerIndex>(HomeAssociationVolunteerIndex.class);
        util.exportExcel(response, list, "志愿者和社团关联数据");
    }

    /**
     * 获取志愿者和社团关联详细信息
     */
    @RequiresPermissions("volunteers:homeAssociationVolunteerIndex:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(homeAssociationVolunteerIndexService.selectHomeAssociationVolunteerIndexById(id));
    }

    /**
     * 新增志愿者和社团关联
     */
    @RequiresPermissions("volunteers:homeAssociationVolunteerIndex:add")
    @Log(title = "志愿者和社团关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeAssociationVolunteerIndex homeAssociationVolunteerIndex) {
        return toAjax(homeAssociationVolunteerIndexService.insertHomeAssociationVolunteerIndex(homeAssociationVolunteerIndex));
    }

    /**
     * 修改志愿者和社团关联
     */
    @RequiresPermissions("volunteers:homeAssociationVolunteerIndex:edit")
    @Log(title = "志愿者和社团关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeAssociationVolunteerIndex homeAssociationVolunteerIndex) {
        return toAjax(homeAssociationVolunteerIndexService.updateHomeAssociationVolunteerIndex(homeAssociationVolunteerIndex));
    }

    /**
     * 删除志愿者和社团关联
     */
    @RequiresPermissions("volunteers:homeAssociationVolunteerIndex:remove")
    @Log(title = "志愿者和社团关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeAssociationVolunteerIndexService.deleteHomeAssociationVolunteerIndexByIds(ids));
    }
}
