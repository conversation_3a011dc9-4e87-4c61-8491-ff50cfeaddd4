package com.ruoyi.homecare.goods.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.homecare.food.domain.HomeFoodBaseInfo;
import com.ruoyi.homecare.goods.domain.HomeGoodsCategory;
import com.ruoyi.homecare.goods.request.GoodsCategoryAddRequest;
import com.ruoyi.homecare.goods.service.HomeGoodsCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName HomeGoodsCategoryController
 * @Description 商品分类
 * <AUTHOR>
 * @Date 2022/7/11 11:55
 */
@RestController
@RequestMapping("/goodsCategory")
@Api(tags = "商品分类")
public class HomeGoodsCategoryController extends BaseController {

    @Autowired
    private HomeGoodsCategoryService homeGoodsCategoryService;

    /**
     * 获取所有商品分类
     */
    @GetMapping("/getAllByType")
    @ApiOperation(value = "获取商品分类列表")
    public TAjaxResult<HomeFoodBaseInfo> list(
            @RequestParam(value = "type", required = true)
            @ApiParam(name = "type", value = "类型：1:商品 2：餐品！")
            @NotNull(message = "类型必填")
            Long type
    ) {

        List<HomeGoodsCategory> list = homeGoodsCategoryService.getAllByType(type);

        return new TAjaxResult(list);
    }

    @ApiOperation(httpMethod = "POST", value = "新增普通商品分类")
    @PostMapping(value = "/add")
    public TAjaxResult add(@RequestBody @Valid GoodsCategoryAddRequest goodsCategoryAddRequest) {
        return homeGoodsCategoryService.add(goodsCategoryAddRequest);
    }


}
