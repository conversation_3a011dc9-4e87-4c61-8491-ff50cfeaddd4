package com.ruoyi.homecare.merchantSettlement.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 商家结算信息表
 * @date 2022-08-10
 */
@Data
@TableName("t_home_merchant_settlement")
@ApiModel("商家结算信息表")
public class MerchantSettlement implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 商家id
     */
    @ApiModelProperty("商家id")
    private Long serviceProviderId;

    /**
     * 商家名称
     */
    @ApiModelProperty("商家名称")
    private String serviceProviderName;

    /**
     * 账单名称
     */
    @ApiModelProperty("账单名称")
    private String billName;

    /**
     * 账单月份
     */
    @ApiModelProperty("账单月份")
    private String billMonth;

    /**
     * 交易单数
     */
    @ApiModelProperty("交易单数")
    private Integer businessNum;

    /**
     * 交易金额
     */
    @ApiModelProperty("交易金额")
    private BigDecimal businessAmount;

    /**
     * 退款单数
     */
    @ApiModelProperty("退款单数")
    private Integer refundNum;

    /**
     * 退款金额
     */
    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

    /**
     * 提成金额
     */
    @ApiModelProperty("提成金额")
    private BigDecimal commissionAmount;

    /**
     * 结算金额
     */
    @ApiModelProperty("结算金额")
    private BigDecimal settlementAmount;

    /**
     * 状态（0未结算，1已结算）
     */
    @ApiModelProperty("状态（0未结算，1已结算）")
    private int status;


    @ApiModelProperty("状态（0未结算，1已结算）")
    @TableField(exist = false)
    private String statusStr;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    public MerchantSettlement() {
    }


    public String getStatusStr() {
        if (this.status == 0) {
            return "未结算";
        } else {
            return "已结算";
        }
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }
}
