package com.ruoyi.homecare.app.service.impl;

import java.util.Date;
import java.util.List;

import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.homecare.app.mapper.HomeSysUserWxMapper;
import com.ruoyi.homecare.app.domain.HomeSysUserWx;
import com.ruoyi.homecare.app.service.IHomeSysUserWxService;

/**
 * 系统用户和微信openid关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-27
 */
@Service
public class HomeSysUserWxServiceImpl implements IHomeSysUserWxService {
    @Autowired
    private HomeSysUserWxMapper homeSysUserWxMapper;

    /**
     * 查询系统用户和微信openid关联
     *
     * @param openId 系统用户和微信openid关联主键
     * @return 系统用户和微信openid关联
     */
    @Override
    public List<HomeSysUserWx> selectHomeSysUserWxByOpenId(String openId) {
        return homeSysUserWxMapper.selectHomeSysUserWxByOpenId(openId);
    }

    /**
     * 查询系统用户和微信openid关联列表
     *
     * @param homeSysUserWx 系统用户和微信openid关联
     * @return 系统用户和微信openid关联
     */
    @Override
    public List<HomeSysUserWx> selectHomeSysUserWxList(HomeSysUserWx homeSysUserWx) {
        return homeSysUserWxMapper.selectHomeSysUserWxList(homeSysUserWx);
    }

    /**
     * 新增系统用户和微信openid关联
     *
     * @param homeSysUserWx 系统用户和微信openid关联
     * @return 结果
     */
    @Override
    public int insertHomeSysUserWx(HomeSysUserWx homeSysUserWx) {
        homeSysUserWx.setCreateDate(new Date());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        homeSysUserWx.setCreateBy(String.valueOf(loginUser.getUserid()));
        return homeSysUserWxMapper.insertHomeSysUserWx(homeSysUserWx);
    }

    /**
     * 修改系统用户和微信openid关联
     *
     * @param homeSysUserWx 系统用户和微信openid关联
     * @return 结果
     */
    @Override
    public int updateHomeSysUserWx(HomeSysUserWx homeSysUserWx) {
        homeSysUserWx.setUpdateDate(new Date());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        homeSysUserWx.setUpdateBy(String.valueOf(loginUser.getUserid()));
        return homeSysUserWxMapper.updateHomeSysUserWx(homeSysUserWx);
    }

    /**
     * 批量删除系统用户和微信openid关联
     *
     * @param openIds 需要删除的系统用户和微信openid关联主键
     * @return 结果
     */
    @Override
    public int deleteHomeSysUserWxByOpenIds(String[] openIds) {
        return homeSysUserWxMapper.deleteHomeSysUserWxByOpenIds(openIds);
    }

    /**
     * 删除系统用户和微信openid关联信息
     *
     * @param openId 系统用户和微信openid关联主键
     * @return 结果
     */
    @Override
    public int deleteHomeSysUserWxByOpenId(String openId) {
        return homeSysUserWxMapper.deleteHomeSysUserWxByOpenId(openId);
    }

    /**
     * 根据系统id删除系统用户和微信openid关联信息
     *
     * @param sysUserId 系统用户和微信openid关联主键
     * @return
     */
    @Override
    public int deleteHomeSysUserWxBySysUserId(Long sysUserId) {
        return homeSysUserWxMapper.deleteHomeSysUserWxBySysUserId(sysUserId);
    }
}
