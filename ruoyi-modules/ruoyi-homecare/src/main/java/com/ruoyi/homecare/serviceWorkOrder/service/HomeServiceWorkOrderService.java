package com.ruoyi.homecare.serviceWorkOrder.service;

import com.ruoyi.homecare.serviceWorkOrder.domain.HomeOrderServiceWork;
import com.ruoyi.homecare.serviceWorkOrder.param.HomeServiceWorkOrderParam;
import com.ruoyi.homecare.serviceWorkOrder.vo.HomeAppServiceWorkOrderInfoRequestVo;
import com.ruoyi.homecare.serviceWorkOrder.vo.HomeServiceWorkOrderRequestVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description home_service_work_order服务层
 * @date 2022-07-18
 */
@Service
public interface HomeServiceWorkOrderService {

    int save(HomeOrderServiceWork homeOrderServiceWork);

    /**
     * 分配人员
     *
     * @param homeServiceWorkOrderParam
     * @return
     */
    int assignWorker(HomeServiceWorkOrderParam homeServiceWorkOrderParam);

    List<HomeOrderServiceWork> getWorkOrderList(HomeOrderServiceWork homeOrderServiceWork);

    /**
     * 服务人员开始服务
     *
     * @param requestVo
     * @return
     */
    int startService(HomeServiceWorkOrderRequestVo requestVo);

    /**
     * 服务人员服务签退
     *
     * @param requestVo
     * @return
     */
    int endService(HomeServiceWorkOrderRequestVo requestVo);

    /**
     * 服务人员现场照片保存
     *
     * @param requestVo
     * @return
     */
    int servingUpload(HomeServiceWorkOrderRequestVo requestVo);

    /**
     * 服务人员移动待完成服务列表
     *
     * @param homeOrderServiceWork
     * @return
     */
    List<HomeOrderServiceWork> getNotCompletedServiceWorkOrderListByWork(HomeOrderServiceWork homeOrderServiceWork);

    /**
     * 服务人员移动已完成服务列表
     *
     * @param homeOrderServiceWork
     * @return
     */
    List<HomeOrderServiceWork> getCompletedServiceWorkOrderListByWork(HomeOrderServiceWork homeOrderServiceWork);

    /**
     * 服务人员移动服务详情
     *
     * @param id
     * @return
     */
    HomeAppServiceWorkOrderInfoRequestVo getServiceWorkOrderInfoById(String id);
}
