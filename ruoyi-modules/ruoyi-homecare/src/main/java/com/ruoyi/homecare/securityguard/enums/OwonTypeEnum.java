package com.ruoyi.homecare.securityguard.enums;

/**
 * 用户状态
 *
 * <AUTHOR>
 */
public enum OwonTypeEnum {

    login("login"),
    wifiConfig("wifiConfig"),
    zigbeeConfig("zigbeeConfig"),
    smartPlug("smartPlug"),
    light("light"),
    doorSensor("doorSensor"),
    motionSensor("motionSensor"),
    smokeSensor("smokeSensor"),
    WA201("WA201"),
    PCT501("PCT501"),
    Monitor("Monitor"),
    System("System"),
    Warning("Warning"),
    Update("Update");

    private final String info;

    OwonTypeEnum(String info) {
        this.info = info;
    }

    public String getInfo() {
        return info;
    }
}
