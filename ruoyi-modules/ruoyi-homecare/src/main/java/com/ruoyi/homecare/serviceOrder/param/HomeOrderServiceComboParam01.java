package com.ruoyi.homecare.serviceOrder.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description t_home_order_service_info
 * @date 2022-07-14
 */
@Data
public class HomeOrderServiceComboParam01 implements Serializable {

    private static final long serialVersionUID = -6728530530389151278L;


    @ApiModelProperty("套餐-服务订单id")
    private Long orderComboDetailsId;

    @ApiModelProperty("数量")
    private Integer number;

    @ApiModelProperty("预约时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date reserveTime;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("手机号码")
    private String phone;

    @ApiModelProperty("名字")
    private String name;

}
