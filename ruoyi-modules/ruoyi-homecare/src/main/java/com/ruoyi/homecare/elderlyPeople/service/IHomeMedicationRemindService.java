package com.ruoyi.homecare.elderlyPeople.service;

import java.util.List;

import com.ruoyi.homecare.elderlyPeople.domain.HomeMedicationRemind;

/**
 * 用药提醒Service接口
 *
 * <AUTHOR>
 * @date 2022-06-15
 */
public interface IHomeMedicationRemindService {
    /**
     * 查询用药提醒
     *
     * @param id 用药提醒主键
     * @return 用药提醒
     */
    public HomeMedicationRemind selectHomeMedicationRemindById(Long id);

    /**
     * 查询用药提醒列表
     *
     * @param homeMedicationRemind 用药提醒
     * @return 用药提醒集合
     */
    public List<HomeMedicationRemind> selectHomeMedicationRemindList(HomeMedicationRemind homeMedicationRemind);

    /**
     * 新增用药提醒
     *
     * @param homeMedicationRemind 用药提醒
     * @return 结果
     */
    public int insertHomeMedicationRemind(HomeMedicationRemind homeMedicationRemind);

    /**
     * 修改用药提醒
     *
     * @param homeMedicationRemind 用药提醒
     * @return 结果
     */
    public int updateHomeMedicationRemind(HomeMedicationRemind homeMedicationRemind);

    /**
     * 批量删除用药提醒
     *
     * @param ids 需要删除的用药提醒主键集合
     * @return 结果
     */
    public int deleteHomeMedicationRemindByIds(Long[] ids);

    /**
     * 删除用药提醒信息
     *
     * @param id 用药提醒主键
     * @return 结果
     */
    public int deleteHomeMedicationRemindById(Long id);
}
