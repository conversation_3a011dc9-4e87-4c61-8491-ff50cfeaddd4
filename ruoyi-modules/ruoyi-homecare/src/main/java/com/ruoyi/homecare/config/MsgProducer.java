package com.ruoyi.homecare.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @packageName：
 * @description：
 * @author： xumiaofeng
 * @date： 2019/3/20 0020 17:02
 */

@Component
public class MsgProducer {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RabbitAdmin rabbitAdmin;

    /**
     * 发送消息
     *
     * @param uuid
     * @param message 消息
     */
    public void send(String uuid, Object message) {
        CorrelationData correlationId = new CorrelationData(uuid);
        rabbitTemplate.convertAndSend(ExchangeConfig.EXCHANGE, RabbitConfig.ROUTINGKEY2,
                message, correlationId);
    }

    /**
     * 发送消息
     *
     * @param uuid
     * @param message 消息
     */
    public void sendAdmin(String uuid, Object message) {

        DirectExchange directExchange = new DirectExchange(ExchangeConfig.EXCHANGE, true, false);
        rabbitAdmin.declareExchange(directExchange);

        Queue queue = new Queue("testQueue", true, false, false);
        rabbitAdmin.declareQueue(queue);

        Binding binding = BindingBuilder.bind(queue).to(directExchange).with("testKey");
        rabbitAdmin.declareBinding(binding);

        CorrelationData correlationId = new CorrelationData(uuid);
        rabbitTemplate.convertAndSend(ExchangeConfig.EXCHANGE, "testKey", message, correlationId);

        String testQueue = (String) rabbitTemplate.receiveAndConvert("testQueue");
        System.out.println(testQueue);

    }

}

