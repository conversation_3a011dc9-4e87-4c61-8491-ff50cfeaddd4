package com.ruoyi.homecare.volunteers.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 礼品兑换记录对象 t_home_exchange_record
 *
 * <AUTHOR>
 * @date 2022-06-17
 */
@ApiModel("礼品兑换记录")
public class HomeExchangeRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 志愿者id
     */
    @Excel(name = "志愿者id")
    @ApiModelProperty("志愿者id")
    private Long volunteerId;

    /**
     * 志愿者名称
     */
    @Excel(name = "志愿者名称")
    @ApiModelProperty("志愿者名称")
    private String volunteerName;

    /**
     * 礼品id
     */
    @Excel(name = "礼品id")
    @ApiModelProperty("礼品id")
    private Long giftId;

    /**
     * 礼品名称
     */
    @Excel(name = "礼品名称")
    @ApiModelProperty("礼品名称")
    private String giftName;

    /**
     * 兑换前时间币
     */
    @Excel(name = "兑换前时间币")
    @ApiModelProperty("兑换前时间币")
    private Long beforeTimeCoin;

    /**
     * 所需时间币
     */
    @Excel(name = "所需时间币")
    @ApiModelProperty("所需时间币")
    private Long timeCoin;

    /**
     * 剩余时间币
     */
    @Excel(name = "剩余时间币")
    @ApiModelProperty("剩余时间币")
    private Long surplusTimeCoin;


    /**
     * 兑换时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("兑换时间")
    @Excel(name = "兑换时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date exchangeTime;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getVolunteerId() {
        return volunteerId;
    }

    public void setVolunteerId(Long volunteerId) {
        this.volunteerId = volunteerId;
    }

    public String getVolunteerName() {
        return volunteerName;
    }

    public void setVolunteerName(String volunteerName) {
        this.volunteerName = volunteerName;
    }

    public Long getGiftId() {
        return giftId;
    }

    public void setGiftId(Long giftId) {
        this.giftId = giftId;
    }

    public String getGiftName() {
        return giftName;
    }

    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    public Long getTimeCoin() {
        return timeCoin;
    }

    public void setTimeCoin(Long timeCoin) {
        this.timeCoin = timeCoin;
    }

    public Date getExchangeTime() {
        return exchangeTime;
    }

    public void setExchangeTime(Date exchangeTime) {
        this.exchangeTime = exchangeTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public Long getSurplusTimeCoin() {
        return surplusTimeCoin;
    }

    public void setSurplusTimeCoin(Long surplusTimeCoin) {
        this.surplusTimeCoin = surplusTimeCoin;
    }

    public Long getBeforeTimeCoin() {
        return beforeTimeCoin;
    }

    public void setBeforeTimeCoin(Long beforeTimeCoin) {
        this.beforeTimeCoin = beforeTimeCoin;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("volunteerId", getVolunteerId())
                .append("volunteerName", getVolunteerName())
                .append("giftId", getGiftId())
                .append("giftName", getGiftName())
                .append("timeCoin", getTimeCoin())
                .append("exchangeTime", getExchangeTime())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }
}
