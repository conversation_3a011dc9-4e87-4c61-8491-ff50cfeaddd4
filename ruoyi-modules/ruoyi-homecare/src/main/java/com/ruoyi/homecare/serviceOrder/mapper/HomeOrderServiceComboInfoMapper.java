package com.ruoyi.homecare.serviceOrder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.homecare.goods.vo.AdminPcGoodsPageListVo;
import com.ruoyi.homecare.serviceOrder.domain.HomeOrderServiceComboInfo;
import com.ruoyi.homecare.serviceOrder.vo.SoldComboListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description home_order_service_combo_infoMapper
 * @date 2022-07-15
 */
@Mapper
public interface HomeOrderServiceComboInfoMapper extends BaseMapper<HomeOrderServiceComboInfo> {

    @Select(
            "<script>select t0.* from home_order_service_combo_info t0 " +
                    // add here if need left join
                    "where 1=1" +
                    "<when test='id!=null and id!=&apos;&apos; '> and t0.id=#{id}</when> " +
                    "<when test='orderId!=null and orderId!=&apos;&apos; '> and t0.order_id=#{orderId}</when> " +
                    "<when test='serviceComboId!=null and serviceComboId!=&apos;&apos; '> and t0.service_combo_id=#{serviceComboId}</when> " +
                    "<when test='serviceComboName!=null and serviceComboName!=&apos;&apos; '> and t0.service_combo_name=#{serviceComboName}</when> " +
                    "<when test='userId!=null and userId!=&apos;&apos; '> and t0.user_id=#{userId}</when> " +
                    "<when test='elderlyPeopleId!=null and elderlyPeopleId!=&apos;&apos; '> and t0.elderly_people_id=#{elderlyPeopleId}</when> " +
                    "<when test='price!=null and price!=&apos;&apos; '> and t0.price=#{price}</when> " +
                    "<when test='img!=null and img!=&apos;&apos; '> and t0.img=#{img}</when> " +
                    "<when test='describe!=null and describe!=&apos;&apos; '> and t0.describe=#{describe}</when> " +
                    "<when test='createTime!=null and createTime!=&apos;&apos; '> and t0.create_time=#{createTime}</when> " +
                    "<when test='updateTime!=null and updateTime!=&apos;&apos; '> and t0.update_time=#{updateTime}</when> " +
                    "<when test='completedStatus!=null and completedStatus!=&apos;&apos; '> and t0.completed_status=#{completedStatus}</when> " +
                    "<when test='expiredDate!=null and expiredDate!=&apos;&apos; '> and t0.expired_date=#{expiredDate}</when> " +
                    "<when test='status!=null and status!=&apos;&apos; '> and t0.status=#{status}</when> " +
                    // add here if need page limit
                    //" limit ${page},${limit} " +
                    " </script>")
    List<HomeOrderServiceComboInfo> pageAll(HomeOrderServiceComboInfo queryParamDTO, int page, int limit);

    @Select("<script>select count(1) from home_order_service_combo_info t0 " +
            // add here if need left join
            "where 1=1" +
            "<when test='id!=null and id!=&apos;&apos; '> and t0.id=#{id}</when> " +
            "<when test='orderId!=null and orderId!=&apos;&apos; '> and t0.order_id=#{orderId}</when> " +
            "<when test='serviceComboId!=null and serviceComboId!=&apos;&apos; '> and t0.service_combo_id=#{serviceComboId}</when> " +
            "<when test='serviceComboName!=null and serviceComboName!=&apos;&apos; '> and t0.service_combo_name=#{serviceComboName}</when> " +
            "<when test='userId!=null and userId!=&apos;&apos; '> and t0.user_id=#{userId}</when> " +
            "<when test='elderlyPeopleId!=null and elderlyPeopleId!=&apos;&apos; '> and t0.elderly_people_id=#{elderlyPeopleId}</when> " +
            "<when test='price!=null and price!=&apos;&apos; '> and t0.price=#{price}</when> " +
            "<when test='img!=null and img!=&apos;&apos; '> and t0.img=#{img}</when> " +
            "<when test='describe!=null and describe!=&apos;&apos; '> and t0.describe=#{describe}</when> " +
            "<when test='createTime!=null and createTime!=&apos;&apos; '> and t0.create_time=#{createTime}</when> " +
            "<when test='updateTime!=null and updateTime!=&apos;&apos; '> and t0.update_time=#{updateTime}</when> " +
            "<when test='completedStatus!=null and completedStatus!=&apos;&apos; '> and t0.completed_status=#{completedStatus}</when> " +
            "<when test='expiredDate!=null and expiredDate!=&apos;&apos; '> and t0.expired_date=#{expiredDate}</when> " +
            "<when test='status!=null and status!=&apos;&apos; '> and t0.status=#{status}</when> " +
            " </script>")
    int countAll(HomeOrderServiceComboInfo queryParamDTO);

    List<HomeOrderServiceComboInfo> getSoldComboList(String serviceProviderId);

    @Select({"${sql}"})
    Page<SoldComboListVo> getSoldComboListNew(Page page, @Param("sql") String sql);
}
