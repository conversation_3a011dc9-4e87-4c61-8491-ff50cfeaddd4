package com.ruoyi.homecare.volunteers.service.impl;

import java.util.List;

import cn.hutool.json.JSONArray;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.homecare.volunteers.mapper.HomeGiftSetMapper;
import com.ruoyi.homecare.volunteers.domain.HomeGiftSet;
import com.ruoyi.homecare.volunteers.service.IHomeGiftSetService;

/**
 * 礼品设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-17
 */
@Service
public class HomeGiftSetServiceImpl implements IHomeGiftSetService {
    @Autowired
    private HomeGiftSetMapper homeGiftSetMapper;

    /**
     * 查询礼品设置
     *
     * @param id 礼品设置主键
     * @return 礼品设置
     */
    @Override
    public HomeGiftSet selectHomeGiftSetById(Long id) {
        return homeGiftSetMapper.selectHomeGiftSetById(id);
    }

    /**
     * 查询礼品设置列表
     *
     * @param homeGiftSet 礼品设置
     * @return 礼品设置
     */
    @Override
    public List<HomeGiftSet> selectHomeGiftSetList(HomeGiftSet homeGiftSet) {
        return homeGiftSetMapper.selectHomeGiftSetList(homeGiftSet);
    }

    /**
     * 新增礼品设置
     *
     * @param homeGiftSet 礼品设置
     * @return 结果
     */
    @Override
    public int insertHomeGiftSet(HomeGiftSet homeGiftSet) {
        homeGiftSet.setCreateTime(DateUtils.getNowDate());
        return homeGiftSetMapper.insertHomeGiftSet(homeGiftSet);
    }

    /**
     * 修改礼品设置
     *
     * @param homeGiftSet 礼品设置
     * @return 结果
     */
    @Override
    public int updateHomeGiftSet(HomeGiftSet homeGiftSet) {
        homeGiftSet.setUpdateTime(DateUtils.getNowDate());
        return homeGiftSetMapper.updateHomeGiftSet(homeGiftSet);
    }

    /**
     * 批量删除礼品设置
     *
     * @param ids 需要删除的礼品设置主键
     * @return 结果
     */
    @Override
    public int deleteHomeGiftSetByIds(Long[] ids) {
        return homeGiftSetMapper.deleteHomeGiftSetByIds(ids);
    }

    /**
     * 删除礼品设置信息
     *
     * @param id 礼品设置主键
     * @return 结果
     */
    @Override
    public int deleteHomeGiftSetById(Long id) {
        return homeGiftSetMapper.deleteHomeGiftSetById(id);
    }

    /**
     * 获取全量的礼品信息
     *
     * @return
     */
    @Override
    public JSONArray getAllGiftList() {
        return homeGiftSetMapper.getAllGiftList();
    }

    @Override
    public int giftPut(HomeGiftSet giftSet) {
        HomeGiftSet homeGiftSet = selectHomeGiftSetById(giftSet.getId());
        Long quantity = homeGiftSet.getQuantity();
        quantity = quantity + giftSet.getNum();
        homeGiftSet.setQuantity(quantity);
        return updateHomeGiftSet(homeGiftSet);
    }
}
