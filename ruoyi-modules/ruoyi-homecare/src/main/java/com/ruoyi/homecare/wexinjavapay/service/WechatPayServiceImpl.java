package com.ruoyi.homecare.wexinjavapay.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.ruoyi.homecare.goodsOrder.domain.HomeOrderBaseInfo;
import com.ruoyi.homecare.goodsOrder.domain.HomeOrderRefundInfo;
import com.ruoyi.homecare.goodsOrder.mapper.HomeOrderBaseInfoMapper;
import com.ruoyi.homecare.goodsOrder.mapper.HomeOrderRefundInfoMapper;
import com.ruoyi.homecare.refund.domain.WxOrderRefund;
import com.ruoyi.homecare.refund.mapper.WxOrderRefundMapper;
import com.ruoyi.homecare.settlement.domain.HomePaymentUserFees;
import com.ruoyi.homecare.settlement.service.IHomePaymentUserFeesService;
import com.ruoyi.homecare.utils.OrderUtils;
import com.ruoyi.homecare.wexinjavapay.components.WxPayComponent;
import com.ruoyi.homecare.wxpayorderinfo.domain.WxPayOrderInfo;
import com.ruoyi.homecare.wxpayorderinfo.mapper.WxPayOrderInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @ClassName MiniAppPayServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/7/7 10:59
 */
@Service
@Slf4j
public class WechatPayServiceImpl implements WechatPayService {

    @Autowired
    private WxPayService wxPayService;
    @Autowired
    private HomeOrderBaseInfoMapper homeOrderBaseInfoMapper;
    @Autowired
    private HomeOrderRefundInfoMapper homeOrderRefundInfoMapper;
    @Autowired
    private WxPayOrderInfoMapper wxPayOrderInfoMapper;
    @Autowired
    private WxPayComponent wxPayComponent;
    @Autowired
    private IHomePaymentUserFeesService iHomePaymentUserFeesService;

    @Autowired
    private WxOrderRefundMapper wxOrderRefundMapper;

    /**
     * <AUTHOR>
     * @Description 支付成功回调通知处理
     * @Date 2022/7/7
     **/
    @Override
    @Transactional
    public String parseOrderNotifyResult(String xmlData) {

        log.debug("收到支付通知信息：" + xmlData);

        WxPayOrderNotifyResult result = null;
        try {
            result = wxPayService.parseOrderNotifyResult(xmlData);
        } catch (WxPayException e) {
            log.error("支付成功回调处理失败：" + e.getErrCodeDes() + e.getReturnMsg());
            return WxPayNotifyResponse.fail("失败");
        }

        String returnCode = result.getReturnCode();
        if ("FAIL".equalsIgnoreCase(returnCode)) {// return_code为SUCCESS的时候有返回
            return WxPayNotifyResponse.fail("通信失败");
        }

        String resultCode = result.getResultCode();
        if ("FAIL".equalsIgnoreCase(resultCode)) {// 业务结果 交易失败
            // 业务失败则逻辑。。。。。。。
            String errCode = result.getErrCode();
            String errCodeDes = result.getErrCodeDes();
            return WxPayNotifyResponse.fail("业务失败");
        }

        log.info("接收到微信的回调信息为：" + JSONUtil.toJsonStr(result));

        // 交易成功
        String openid = result.getOpenid();
        String tradeType = result.getTradeType();
        String bankType = result.getBankType();
        Integer totalFee = result.getTotalFee();
        String transactionId = result.getTransactionId();// 微信支付订单号
        String outTradeNo = result.getOutTradeNo();// 商户订单号
        String timeEnd = result.getTimeEnd();// **************  支付完成时间 如2009年12月25日9点10分10秒表示为**************
        DateTime dateTime = DateUtil.parse(timeEnd, "yyyyMMddHHmmss");
        Date currentDate = new Date();

        if (outTradeNo.contains(OrderUtils.topUpOrder)) {// 充值
            WxPayOrderInfo wxPayOrderInfo = wxPayOrderInfoMapper.selectById(outTradeNo);
            if (null == wxPayOrderInfo) {
                log.info("订单号为：" + outTradeNo + "未查询到该订单信息，请检查！");
                return "FAIL";
            }

            int status = wxPayOrderInfo.getStatus();
            if (!WxPayOrderInfo.STATUS_ALREADY.equals(status)) {// 如果不是已付款
                wxPayOrderInfo.setStatus(WxPayOrderInfo.STATUS_ALREADY);// 已付款
                wxPayOrderInfo.setTransactionId(transactionId);
                wxPayOrderInfo.setBankType(bankType);
                wxPayOrderInfo.setTradeType(tradeType);
                wxPayOrderInfo.setTimeEnd(dateTime);
                wxPayOrderInfoMapper.updateById(wxPayOrderInfo);

                // 充值记录
                HomePaymentUserFees homePaymentUserFees = new HomePaymentUserFees();
                homePaymentUserFees.setUserId(wxPayOrderInfo.getUserId());
                homePaymentUserFees.setPaymentMoney(wxPayOrderInfo.getAmount());
                homePaymentUserFees.setCreateClient("2");
                homePaymentUserFees.setMethodPayment("2");
                homePaymentUserFees.setOrderId(String.valueOf(wxPayOrderInfo.getId()));
                int i = iHomePaymentUserFeesService.insertHomePaymentUserFees(homePaymentUserFees);
                return "SUCCESS";
            }
            return "SUCCESS";
        }

        if (outTradeNo.contains(OrderUtils.BaseOrder)) {// 如果订单是常规订单

            WxPayOrderInfo wxPayOrderInfo = wxPayOrderInfoMapper.selectById(outTradeNo);
            if (null == wxPayOrderInfo) {
                log.info("订单号为：" + outTradeNo + "未查询到该wx订单信息，请检查！");
                return "FAIL";
            }

            int status = wxPayOrderInfo.getStatus();
            if (!WxPayOrderInfo.STATUS_ALREADY.equals(status)) {// 如果不是已付款
                wxPayOrderInfo.setStatus(WxPayOrderInfo.STATUS_ALREADY);// 已付款
                wxPayOrderInfo.setTransactionId(transactionId);
                wxPayOrderInfo.setBankType(bankType);
                wxPayOrderInfo.setTradeType(tradeType);
                wxPayOrderInfo.setTimeEnd(dateTime);
                wxPayOrderInfo.setUpdateTime(currentDate);
                wxPayOrderInfoMapper.updateById(wxPayOrderInfo);

                // 维护base表
                HomeOrderBaseInfo homeOrderBaseInfo = homeOrderBaseInfoMapper.selectById(outTradeNo);
                if (null == wxPayOrderInfo) {
                    log.info("订单号为：" + outTradeNo + "未查询到该Base订单信息，请检查！");
                    return "FAIL";
                }

                homeOrderBaseInfo.setStatus(HomeOrderBaseInfo.STATUS_PAID);
                homeOrderBaseInfo.setPayTime(currentDate);
                homeOrderBaseInfo.setUpdateTime(currentDate);
                homeOrderBaseInfoMapper.updateById(homeOrderBaseInfo);

                return "SUCCESS";
            }
            return "SUCCESS";

        }

        // TODO 根据自己业务场景需要构造返回对象
        return WxPayNotifyResponse.success("成功");

    }

    /**
     * <AUTHOR>
     * @Description 退款回调通知处理
     * @Date 2022/7/7
     **/
    @Override
    @Transactional
    public String parseRefundNotifyResult(String xmlData) {

        log.debug("收到退款通知信息：" + xmlData);
        Date date = new Date();
        WxPayRefundNotifyResult result = null;
        try {
            result = wxPayService.parseRefundNotifyResult(xmlData);
        } catch (WxPayException e) {
            throw new RuntimeException(e);
        }
        String returnCode = result.getReturnCode();
        if ("FAIL".equalsIgnoreCase(returnCode)) {// return_code为SUCCESS的时候有返回
            return WxPayNotifyResponse.fail("微信支通信失败！");
        }

        WxPayRefundNotifyResult.ReqInfo reqInfo = result.getReqInfo();
        if (null == reqInfo) {
            return WxPayNotifyResponse.fail("req_info加密信息为空");
        }

        // 退款成功
        String transactionId = reqInfo.getTransactionId();
        String outTradeNo = reqInfo.getOutTradeNo();// 商户订单号
        String refundId = reqInfo.getRefundId();
        String outRefundNo = reqInfo.getOutRefundNo();// 商户退款单号
        Integer totalFee = reqInfo.getTotalFee();
        Integer refundFee = reqInfo.getRefundFee();
        Integer settlementRefundFee = reqInfo.getSettlementRefundFee();

        HomeOrderBaseInfo homeOrderBaseInfo = homeOrderBaseInfoMapper.selectById(outTradeNo);
        if (null == homeOrderBaseInfo) {
            log.info("Base订单号：" + outRefundNo + "未查询到退订单信息！");
            return WxPayNotifyResponse.fail("未查询退款订单信息。 ");
        }

        Integer afterSalesStatus = homeOrderBaseInfo.getAfterSalesStatus();// 售后状态 0:未售后 1：正在售后 2：退款完成 3：退款失败
        if (afterSalesStatus.equals(2)) {// 2：退款完成
            log.info("退款已完成,重复操作无效！");
            return WxPayNotifyResponse.success("退款已完成。 ");
        }

        HomeOrderRefundInfo homeOrderRefundInfo = homeOrderRefundInfoMapper.selectById(outRefundNo);
        if (null == homeOrderRefundInfo) {
            log.info("退款订单号：" + outRefundNo + "未查询到退款订单信息！");
            return WxPayNotifyResponse.fail("未查询退款订单信息。 ");
        }

        Integer refundOrderStatus = homeOrderRefundInfo.getRefundStatus();
        if (refundOrderStatus == 2) {// 对于已经成功退款的订单，则不再处理
            log.info("退款已完成,重复操作无效！");
            return WxPayNotifyResponse.success("已退款成功！");
        }

        String returnStr = "";
        String refundStatus = reqInfo.getRefundStatus();// 退款状态
        // https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_16&index=10
        if ("CHANGE".equalsIgnoreCase(refundStatus)) {// CHANGE-退款异常
            log.info("微信退款异常！");
            homeOrderRefundInfo.setRefundStatus(3);
            homeOrderRefundInfo.setFailMsg("退款异常");
            homeOrderBaseInfo.setAfterSalesStatus(3);
            returnStr = WxPayNotifyResponse.fail("退款异常。");
        }
        if ("REFUNDCLOSE".equalsIgnoreCase(refundStatus)) {// REFUNDCLOSE—退款关闭
            log.info("微信退款关闭！");
            homeOrderRefundInfo.setRefundStatus(3);
            homeOrderBaseInfo.setAfterSalesStatus(3);
            homeOrderRefundInfo.setFailMsg("退款关闭");
            returnStr = WxPayNotifyResponse.fail("退款关闭。 ");
        }
        if ("SUCCESS".equalsIgnoreCase(refundStatus)) {// 正常
            log.info("微信退款成功关！");
            homeOrderRefundInfo.setRefundStatus(2);
            homeOrderBaseInfo.setAfterSalesStatus(2);
            returnStr = WxPayNotifyResponse.success("成功");

            // 存储微信退款订单详情
            WxOrderRefund wxOrderRefund = new WxOrderRefund();
            BeanUtil.copyProperties(reqInfo, wxOrderRefund);
            wxOrderRefund.setUpdateTime(date);
            wxOrderRefund.setCreateTime(date);
            wxOrderRefundMapper.insert(wxOrderRefund);
        }
        homeOrderBaseInfoMapper.updateById(homeOrderBaseInfo);
        homeOrderRefundInfoMapper.updateById(homeOrderRefundInfo);
        return returnStr;
    }


}
