package com.ruoyi.homecare.serviceOrder.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.homecare.goodsOrder.domain.HomeOrderBaseInfo;
import com.ruoyi.homecare.serviceWorkOrder.domain.HomeOrderServiceWork;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName
 * @Description
 * <AUTHOR>
 * @Date 2022/8/05 14:51
 */
@Data
@ApiModel("管理端PC服务列表订单返回值")
public class HomeAdminOrderServiceListVo {


    @ApiModelProperty("服务商名称")
    private String providerName;

    @ApiModelProperty("工作人员名称")
    private String workName;

    @ApiModelProperty("工单状态")
    private String statusLabel;

    @ApiModelProperty(value = "单价价格")
    private BigDecimal price;

    @ApiModelProperty(value = "订单号")
    private String orderId;

    @ApiModelProperty(value = "工作人服务单号")
    private String id;

    @ApiModelProperty(value = "服务名称")
    private String serviceName;

    @ApiModelProperty(value = "商品类型 1:商品 2：餐品 3：服务 4：服务套餐")
    private String type;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始服务时间")
    private Date startTime;


}
