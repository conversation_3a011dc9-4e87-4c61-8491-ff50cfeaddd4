package com.ruoyi.homecare.goodsOrder.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @ClassName 处理退款参数
 * @Description
 * <AUTHOR>
 * @Date 2022/7/18 11:38
 */
@Data
@ApiModel(description = "参数")
public class RefundDealwithParam {

    @NotNull(message = "处理决定！")
    @ApiModelProperty("0： 拒绝 1：同意")
    private Integer decision;

    @NotBlank(message = "退款单id必填！")
    @ApiModelProperty("退款单id")
    private String refundId;


}
