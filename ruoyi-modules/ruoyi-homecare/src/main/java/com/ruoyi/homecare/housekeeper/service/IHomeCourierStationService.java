package com.ruoyi.homecare.housekeeper.service;

import java.util.List;

import cn.hutool.json.JSONArray;
import com.ruoyi.homecare.housekeeper.domain.HomeCourierStation;

/**
 * 管家驿站Service接口
 *
 * <AUTHOR>
 * @date 2022-06-16
 */
public interface IHomeCourierStationService {
    /**
     * 查询管家驿站
     *
     * @param id 管家驿站主键
     * @return 管家驿站
     */
    public HomeCourierStation selectHomeCourierStationById(Long id);

    /**
     * 查询管家驿站列表
     *
     * @param homeCourierStation 管家驿站
     * @return 管家驿站集合
     */
    public List<HomeCourierStation> selectHomeCourierStationList(HomeCourierStation homeCourierStation);

    /**
     * 新增管家驿站
     *
     * @param homeCourierStation 管家驿站
     * @return 结果
     */
    public int insertHomeCourierStation(HomeCourierStation homeCourierStation);

    /**
     * 修改管家驿站
     *
     * @param homeCourierStation 管家驿站
     * @return 结果
     */
    public int updateHomeCourierStation(HomeCourierStation homeCourierStation);

    /**
     * 批量删除管家驿站
     *
     * @param ids 需要删除的管家驿站主键集合
     * @return 结果
     */
    public int deleteHomeCourierStationByIds(Long[] ids);

    /**
     * 删除管家驿站信息
     *
     * @param id 管家驿站主键
     * @return 结果
     */
    public int deleteHomeCourierStationById(Long id);

    /**
     * 获取全量的管家驿站信息
     *
     * @return
     */
    JSONArray getAllCourierStationList();
}
