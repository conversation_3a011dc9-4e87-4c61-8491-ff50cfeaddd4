package com.ruoyi.homecare.housekeeper.domain;

import com.ruoyi.homecare.utils.DictUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 社区管家服务项目对象 t_home_housekeeper_service_project
 *
 * <AUTHOR>
 * @date 2022-06-22
 */
@ApiModel(value = "社区管家服务项目对象")
public class HomeHousekeeperServiceProject extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 所属驿站
     */
    @Excel(name = "所属驿站")
    @ApiModelProperty(value = "所属驿站")
    private Long courierStation;

    /**
     * 服务类型
     */
    @Excel(name = "服务类型")
    @ApiModelProperty(value = "服务类型")
    private String serviceType;

    /**
     * 服务类型Label
     */
    @Excel(name = "服务类型Label")
    @ApiModelProperty(value = "服务类型Label")
    private String serviceTypeLabel;

    /**
     * 服务项目
     */
    @Excel(name = "服务项目")
    @ApiModelProperty(value = "服务项目")
    private String projectName;

    /**
     * 收费单价
     */
    @Excel(name = "收费单价")
    @ApiModelProperty(value = "收费单价")
    private String price;

    /**
     * 计费方式
     */
    @Excel(name = "计费方式")
    @ApiModelProperty(value = "计费方式")
    private String chargeMode;

    /**
     * 计费方式Label
     */
    @Excel(name = "计费方式Label")
    @ApiModelProperty(value = "计费方式Label")
    private String chargeModeLabel;


    /**
     * 佣金比率
     */
    @Excel(name = "佣金比率")
    @ApiModelProperty(value = "佣金比率")
    private String commissionRate;

    /**
     * 项目图片
     */
    @Excel(name = "项目图片")
    @ApiModelProperty(value = "项目图片")
    private String projectImg;

    /**
     * 项目介绍
     */
    @Excel(name = "项目介绍")
    @ApiModelProperty(value = "项目介绍")
    private String projectRemark;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCourierStation() {
        return courierStation;
    }

    public void setCourierStation(Long courierStation) {
        this.courierStation = courierStation;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getChargeMode() {
        return chargeMode;
    }

    public void setChargeMode(String chargeMode) {
        this.chargeMode = chargeMode;
    }

    public String getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(String commissionRate) {
        this.commissionRate = commissionRate;
    }

    public String getProjectImg() {
        return projectImg;
    }

    public void setProjectImg(String projectImg) {
        this.projectImg = projectImg;
    }

    public String getProjectRemark() {
        return projectRemark;
    }

    public void setProjectRemark(String projectRemark) {
        this.projectRemark = projectRemark;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getServiceTypeLabel() {
        if (StringUtils.isEmpty(this.serviceType)) {
            return "";
        }
        return getLabel(serviceType, "service_type");
    }

    public void setServiceTypeLabel(String serviceTypeLabel) {
        this.serviceTypeLabel = serviceTypeLabel;
    }

    public String getChargeModeLabel() {
        if (StringUtils.isEmpty(this.chargeMode)) {
            return "";
        }
        return getLabel(chargeMode, "charge_mode");
    }

    public void setChargeModeLabel(String chargeModeLabel) {
        this.chargeModeLabel = chargeModeLabel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("courierStation", getCourierStation())
                .append("serviceType", getServiceType())
                .append("projectName", getProjectName())
                .append("price", getPrice())
                .append("chargeMode", getChargeMode())
                .append("commissionRate", getCommissionRate())
                .append("projectImg", getProjectImg())
                .append("projectRemark", getProjectRemark())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }


    private String getLabel(String value, String type) {
        String[] split = value.split(",");
        String data = "";
        for (int i = 0; i < split.length; i++) {
            if (i == 0) {
                data += DictUtils.selectDictLabel(type, split[i]);
            } else {
                data += "、" + DictUtils.selectDictLabel(type, split[i]);
            }
        }
        return data;
    }
}
