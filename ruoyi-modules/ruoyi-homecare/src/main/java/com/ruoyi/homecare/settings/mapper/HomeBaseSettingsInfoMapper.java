package com.ruoyi.homecare.settings.mapper;

import com.ruoyi.homecare.settings.domain.HomeBaseSettingsInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 基础设置Mapper接口
 *
 * <AUTHOR>
 * @date 2022-07-13
 */
@Mapper
public interface HomeBaseSettingsInfoMapper {
    /**
     * 查询基础设置
     *
     * @param id 基础设置主键
     * @return 基础设置
     */
    public HomeBaseSettingsInfo selectHomeBaseSettingsInfoById(Long id);

    /**
     * 查询基础设置列表
     *
     * @param homeBaseSettingsInfo 基础设置
     * @return 基础设置集合
     */
    public List<HomeBaseSettingsInfo> selectHomeBaseSettingsInfoList(HomeBaseSettingsInfo homeBaseSettingsInfo);

    /**
     * 新增基础设置
     *
     * @param homeBaseSettingsInfo 基础设置
     * @return 结果
     */
    public int insertHomeBaseSettingsInfo(HomeBaseSettingsInfo homeBaseSettingsInfo);

    /**
     * 修改基础设置
     *
     * @param homeBaseSettingsInfo 基础设置
     * @return 结果
     */
    public int updateHomeBaseSettingsInfo(HomeBaseSettingsInfo homeBaseSettingsInfo);

    /**
     * 删除基础设置
     *
     * @param id 基础设置主键
     * @return 结果
     */
    public int deleteHomeBaseSettingsInfoById(Long id);

    /**
     * 批量删除基础设置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeBaseSettingsInfoByIds(Long[] ids);
}
