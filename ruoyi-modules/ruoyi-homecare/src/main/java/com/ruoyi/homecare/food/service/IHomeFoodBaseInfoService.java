package com.ruoyi.homecare.food.service;

import java.util.List;

import com.ruoyi.homecare.food.domain.HomeFoodBaseInfo;

/**
 * 餐品管理信息Service接口
 *
 * <AUTHOR>
 * @date 2022-06-27
 */
public interface IHomeFoodBaseInfoService {
    /**
     * 查询餐品管理信息
     *
     * @param id 餐品管理信息主键
     * @return 餐品管理信息
     */
    public HomeFoodBaseInfo selectHomeFoodBaseInfoById(Long id);

    /**
     * 查询餐品管理信息列表
     *
     * @param homeFoodBaseInfo 餐品管理信息
     * @return 餐品管理信息集合
     */
    public List<HomeFoodBaseInfo> selectHomeFoodBaseInfoList(HomeFoodBaseInfo homeFoodBaseInfo);

    /**
     * 新增餐品管理信息
     *
     * @param homeFoodBaseInfo 餐品管理信息
     * @return 结果
     */
    public int insertHomeFoodBaseInfo(HomeFoodBaseInfo homeFoodBaseInfo);

    /**
     * 修改餐品管理信息
     *
     * @param homeFoodBaseInfo 餐品管理信息
     * @return 结果
     */
    public int updateHomeFoodBaseInfo(HomeFoodBaseInfo homeFoodBaseInfo);

    /**
     * 批量删除餐品管理信息
     *
     * @param ids 需要删除的餐品管理信息主键集合
     * @return 结果
     */
    public int deleteHomeFoodBaseInfoByIds(Long[] ids);

    /**
     * 删除餐品管理信息信息
     *
     * @param id 餐品管理信息主键
     * @return 结果
     */
    public int deleteHomeFoodBaseInfoById(Long id);
}
