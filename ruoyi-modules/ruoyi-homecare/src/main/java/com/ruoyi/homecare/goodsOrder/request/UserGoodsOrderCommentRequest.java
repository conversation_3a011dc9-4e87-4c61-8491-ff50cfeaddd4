package com.ruoyi.homecare.goodsOrder.request;

import com.ruoyi.homecare.utils.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @ClassName UserGoodsOrderAllByIdRequest
 * @Description
 * <AUTHOR>
 * @Date 2022/7/18 11:38
 */
@Data
@ApiModel(description = "参数")
public class UserGoodsOrderCommentRequest {

    @NotBlank(message = "订单id必填！")
    @ApiModelProperty("订单id")
    private String orderId;

    @ApiModelProperty("评论内容")
    @NotBlank(message = "评论内容必填！")
    private String comment;

    @Min(value = 0, message = "星级最小为0")
    @Max(value = 5, message = "星级最高为5")
    @NotNull(message = "星评必填！")
    @ApiModelProperty("星评： 0-5星")
    private Integer commentStar;


}
