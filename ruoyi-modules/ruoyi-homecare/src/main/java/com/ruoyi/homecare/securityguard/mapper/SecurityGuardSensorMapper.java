package com.ruoyi.homecare.securityguard.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.homecare.securityguard.domain.SecurityGuardSensor;

import java.util.List;

/**
 * 拉绳传感器报警信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-11-30
 */
public interface SecurityGuardSensorMapper extends MPJBaseMapper<SecurityGuardSensor> {
    /**
     * 查询拉绳传感器报警信息
     *
     * @param id 拉绳传感器报警信息主键
     * @return 拉绳传感器报警信息
     */
    public SecurityGuardSensor selectSecurityGuardSensorById(Long id);

    /**
     * 查询拉绳传感器报警信息列表
     *
     * @param securityGuardSensor 拉绳传感器报警信息
     * @return 拉绳传感器报警信息集合
     */
    public List<SecurityGuardSensor> selectSecurityGuardSensorList(SecurityGuardSensor securityGuardSensor);

    /**
     * 新增拉绳传感器报警信息
     *
     * @param securityGuardSensor 拉绳传感器报警信息
     * @return 结果
     */
    public int insertSecurityGuardSensor(SecurityGuardSensor securityGuardSensor);

    /**
     * 修改拉绳传感器报警信息
     *
     * @param securityGuardSensor 拉绳传感器报警信息
     * @return 结果
     */
    public int updateSecurityGuardSensor(SecurityGuardSensor securityGuardSensor);

    /**
     * 删除拉绳传感器报警信息
     *
     * @param id 拉绳传感器报警信息主键
     * @return 结果
     */
    public int deleteSecurityGuardSensorById(Long id);

    /**
     * 批量删除拉绳传感器报警信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSecurityGuardSensorByIds(Long[] ids);
}
