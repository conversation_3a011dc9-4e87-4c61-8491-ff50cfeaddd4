package com.ruoyi.homecare.merchantSettlement.service;

import com.ruoyi.homecare.merchantSettlement.domain.MerchantSettlement;
import com.ruoyi.homecare.merchantSettlement.vo.MSOrderInfoResVo;
import com.ruoyi.homecare.merchantSettlement.vo.MerchantSettlementListReqVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 商家结算信息表服务层
 * @date 2022-08-10
 */
public interface MerchantSettlementService {

    List<MerchantSettlement> selectList(MerchantSettlementListReqVo reqVo);

    List<MSOrderInfoResVo> getOrderList(MerchantSettlementListReqVo reqVo);

    void generateMonthBill();

    int settlementBill(String id, BigDecimal settlementAmount);

}
