package com.ruoyi.homecare.volunteers.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 老人移动端志愿者服务对象 t_home_service_place_order_volunteer
 *
 * <AUTHOR>
 * @date 2022-07-15
 */
@ApiModel(value = "老人移动端志愿者服务")
public class HomeServicePlaceOrderVolunteer extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 要求服务时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "要求服务时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "要求服务时间")
    private Date serviceDate;

    /**
     * 服务id
     */
    @Excel(name = "服务id")
    @ApiModelProperty(value = "服务id")
    private Long serviceId;

    /**
     * 老人id
     */
    @Excel(name = "老人id")
    @ApiModelProperty(value = "老人id")
    private String userId;

    /**
     * 志愿者id
     */
    @Excel(name = "志愿者id")
    @ApiModelProperty(value = "志愿者id")
    private Long volunteerId;

    /**
     * 服务地址
     */
    @Excel(name = "服务地址")
    @ApiModelProperty(value = "服务地址")
    private String serviceAddress;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 是否指定人员：0未指定，1指定，2已接单
     */
    @Excel(name = "是否指定人员：0未指定，1指定，2已接单")
    @ApiModelProperty(value = "是否指定人员：0未指定，1指定，2已接单")
    private String flag;

    /**
     * 社区id
     */
    @Excel(name = "社区id")
    @ApiModelProperty(value = "社区id")
    private Long communityId;

    /**
     * 工单号
     */
    @Excel(name = "工单号")
    @ApiModelProperty(value = "工单号")
    private String orderNumber;

    /**
     * 服务状态0未开始，1服务中，2已结束
     */
    @Excel(name = "服务状态0未开始，1服务中，2已结束")
    @ApiModelProperty(value = "服务状态0未开始，1服务中，2已结束")
    private String state;

    /**
     * 老人名称
     */
    @Excel(name = "老人名称")
    @ApiModelProperty(value = "老人名称")
    private String userName;

    /**
     * 志愿者名称
     */
    @Excel(name = "志愿者名称")
    @ApiModelProperty(value = "志愿者名称")
    private String volunteerName;

    /**
     * 服务名称
     */
    @Excel(name = "服务名称")
    @ApiModelProperty(value = "服务名称")
    private String serviceName;
    /**
     * 开始时间
     */
    @Excel(name = "开始时间")
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;
    /**
     * 结束时间
     */
    @Excel(name = "结束时间")
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 评价
     */
    @Excel(name = "评价")
    @ApiModelProperty(value = "评价")
    private String evaluate;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getServiceDate() {
        return serviceDate;
    }

    public void setServiceDate(Date serviceDate) {
        this.serviceDate = serviceDate;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Long getVolunteerId() {
        return volunteerId;
    }

    public void setVolunteerId(Long volunteerId) {
        this.volunteerId = volunteerId;
    }

    public String getServiceAddress() {
        return serviceAddress;
    }

    public void setServiceAddress(String serviceAddress) {
        this.serviceAddress = serviceAddress;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public Long getCommunityId() {
        return communityId;
    }

    public void setCommunityId(Long communityId) {
        this.communityId = communityId;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getVolunteerName() {
        return volunteerName;
    }

    public void setVolunteerName(String volunteerName) {
        this.volunteerName = volunteerName;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getEvaluate() {
        return evaluate;
    }

    public void setEvaluate(String evaluate) {
        this.evaluate = evaluate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("serviceDate", getServiceDate())
                .append("serviceId", getServiceId())
                .append("userId", getUserId())
                .append("volunteerId", getVolunteerId())
                .append("serviceAddress", getServiceAddress())
                .append("phone", getPhone())
                .append("flag", getFlag())
                .append("communityId", getCommunityId())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("remark", getRemark())
                .toString();
    }
}
