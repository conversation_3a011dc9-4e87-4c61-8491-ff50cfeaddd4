package com.ruoyi.homecare.service.service;

import cn.hutool.json.JSONArray;
import com.ruoyi.homecare.service.domain.HomeServiceProject;
import com.ruoyi.homecare.service.domain.HomeServiceProviderServiceType;
import com.ruoyi.homecare.service.vo.AppServiceProjectResVo;

import java.util.List;

/**
 * 服务项目Service接口
 *
 * <AUTHOR>
 * @date 2022-06-14
 */
public interface IHomeServiceProjectService {
    /**
     * 查询服务项目
     *
     * @param id 服务项目主键
     * @return 服务项目
     */
    public HomeServiceProject selectHomeServiceProjectById(Long id);

    /**
     * 查询服务项目列表
     *
     * @param homeServiceProject 服务项目
     * @return 服务项目集合
     */
    public List<HomeServiceProject> selectHomeServiceProjectList(HomeServiceProject homeServiceProject);

    /**
     * 新增服务项目
     *
     * @param homeServiceProject 服务项目
     * @return 结果
     */
    public int insertHomeServiceProject(HomeServiceProject homeServiceProject);

    /**
     * 修改服务项目
     *
     * @param homeServiceProject 服务项目
     * @return 结果
     */
    public int updateHomeServiceProject(HomeServiceProject homeServiceProject);

    /**
     * 批量删除服务项目
     *
     * @param ids 需要删除的服务项目主键集合
     * @return 结果
     */
    public int deleteHomeServiceProjectByIds(Long[] ids);

    /**
     * 删除服务项目信息
     *
     * @param id 服务项目主键
     * @return 结果
     */
    public int deleteHomeServiceProjectById(Long id);


    /**
     * 获取keyValue列表
     *
     * @return
     */
    JSONArray getProjectList(String serviceProviderId);

    Integer doListingOperate(Long id, String type);

    int addServiceType(Long serviceProviderId, String typeName);

    List<HomeServiceProviderServiceType> selectServiceType(Long serviceProviderId);

    int deleteServiceType(Long serviceProviderId, Long id);

    List<AppServiceProjectResVo> getAppServiceList(Long serviceProviderId);
}
