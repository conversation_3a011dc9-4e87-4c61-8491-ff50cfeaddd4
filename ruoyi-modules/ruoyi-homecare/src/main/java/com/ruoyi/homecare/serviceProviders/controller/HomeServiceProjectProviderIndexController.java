package com.ruoyi.homecare.serviceProviders.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.json.JSONArray;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProjectProviderIndex;
import com.ruoyi.homecare.serviceProviders.service.IHomeServiceProjectProviderIndexService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 服务项目和服务商关联Controller
 *
 * <AUTHOR>
 * @date 2022-07-05
 */
@RestController
@RequestMapping("/homeServiceProjectProviderIndex")
@Api(value = "服务商管理-服务项目和服务商关联", tags = "服务商管理-服务项目和服务商关联")
public class HomeServiceProjectProviderIndexController extends BaseController {
    @Autowired
    private IHomeServiceProjectProviderIndexService homeServiceProjectProviderIndexService;

    /**
     * 查询服务项目和服务商关联列表
     */
    //@RequiresPermissions("serviceProviders:homeServiceProjectProviderIndex:list")
    @GetMapping("/list")
    public TableDataInfo list(HomeServiceProjectProviderIndex homeServiceProjectProviderIndex) {
        startPage();
        List<HomeServiceProjectProviderIndex> list = homeServiceProjectProviderIndexService.selectHomeServiceProjectProviderIndexList(homeServiceProjectProviderIndex);
        return getDataTable(list);
    }

    /**
     * 导出服务项目和服务商关联列表
     */
    //@RequiresPermissions("serviceProviders:homeServiceProjectProviderIndex:export")
    @Log(platform = "2", title = "服务项目和服务商关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeServiceProjectProviderIndex homeServiceProjectProviderIndex) {
        List<HomeServiceProjectProviderIndex> list = homeServiceProjectProviderIndexService.selectHomeServiceProjectProviderIndexList(homeServiceProjectProviderIndex);
        ExcelUtil<HomeServiceProjectProviderIndex> util = new ExcelUtil<HomeServiceProjectProviderIndex>(HomeServiceProjectProviderIndex.class);
        util.exportExcel(response, list, "服务项目和服务商关联数据");
    }

    /**
     * 获取服务项目和服务商关联详细信息
     */
    //@RequiresPermissions("serviceProviders:homeServiceProjectProviderIndex:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(homeServiceProjectProviderIndexService.selectHomeServiceProjectProviderIndexById(id));
    }

    /**
     * 新增服务项目和服务商关联
     */
    //@RequiresPermissions("serviceProviders:homeServiceProjectProviderIndex:add")
    @Log(platform = "2", title = "服务项目和服务商关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeServiceProjectProviderIndex homeServiceProjectProviderIndex) {
        return toAjax(homeServiceProjectProviderIndexService.insertHomeServiceProjectProviderIndex(homeServiceProjectProviderIndex));
    }

    /**
     * 修改服务项目和服务商关联
     */
    //@RequiresPermissions("serviceProviders:homeServiceProjectProviderIndex:edit")
    @Log(platform = "2", title = "服务项目和服务商关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeServiceProjectProviderIndex homeServiceProjectProviderIndex) {
        return toAjax(homeServiceProjectProviderIndexService.updateHomeServiceProjectProviderIndex(homeServiceProjectProviderIndex));
    }

    /**
     * 删除服务项目和服务商关联
     */
    //@RequiresPermissions("serviceProviders:homeServiceProjectProviderIndex:remove")
    @Log(platform = "2", title = "服务项目和服务商关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeServiceProjectProviderIndexService.deleteHomeServiceProjectProviderIndexByIds(ids));
    }

    /**
     * 获取服务项目和服务商关联信息下拉列表
     */
    //@RequiresPermissions("serviceProviders:homeServiceProjectProviderIndex:query")
    @GetMapping("getByProjectIdList")
    @ApiOperation(value = "获取服务项目和服务商关联信息下拉列表")
    public AjaxResult getByProjectIdList(Long projectId) {
        JSONArray jsonArray = homeServiceProjectProviderIndexService.getByProjectIdList(projectId);
        return AjaxResult.success(jsonArray);
    }


}
