package com.ruoyi.homecare.housekeeper.service.impl;

import java.util.List;

import cn.hutool.json.JSONArray;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.homecare.housekeeper.mapper.HomeCourierStationMapper;
import com.ruoyi.homecare.housekeeper.domain.HomeCourierStation;
import com.ruoyi.homecare.housekeeper.service.IHomeCourierStationService;

/**
 * 管家驿站Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-16
 */
@Service
public class HomeCourierStationServiceImpl implements IHomeCourierStationService {
    @Autowired
    private HomeCourierStationMapper homeCourierStationMapper;

    /**
     * 查询管家驿站
     *
     * @param id 管家驿站主键
     * @return 管家驿站
     */
    @Override
    public HomeCourierStation selectHomeCourierStationById(Long id) {
        return homeCourierStationMapper.selectHomeCourierStationById(id);
    }

    /**
     * 查询管家驿站列表
     *
     * @param homeCourierStation 管家驿站
     * @return 管家驿站
     */
    @Override
    public List<HomeCourierStation> selectHomeCourierStationList(HomeCourierStation homeCourierStation) {
        return homeCourierStationMapper.selectHomeCourierStationList(homeCourierStation);
    }

    /**
     * 新增管家驿站
     *
     * @param homeCourierStation 管家驿站
     * @return 结果
     */
    @Override
    public int insertHomeCourierStation(HomeCourierStation homeCourierStation) {
        homeCourierStation.setCreateTime(DateUtils.getNowDate());
        return homeCourierStationMapper.insertHomeCourierStation(homeCourierStation);
    }

    /**
     * 修改管家驿站
     *
     * @param homeCourierStation 管家驿站
     * @return 结果
     */
    @Override
    public int updateHomeCourierStation(HomeCourierStation homeCourierStation) {
        homeCourierStation.setUpdateTime(DateUtils.getNowDate());
        return homeCourierStationMapper.updateHomeCourierStation(homeCourierStation);
    }

    /**
     * 批量删除管家驿站
     *
     * @param ids 需要删除的管家驿站主键
     * @return 结果
     */
    @Override
    public int deleteHomeCourierStationByIds(Long[] ids) {
        return homeCourierStationMapper.deleteHomeCourierStationByIds(ids);
    }

    /**
     * 删除管家驿站信息
     *
     * @param id 管家驿站主键
     * @return 结果
     */
    @Override
    public int deleteHomeCourierStationById(Long id) {
        return homeCourierStationMapper.deleteHomeCourierStationById(id);
    }

    /**
     * 获取全量的管家驿站信息
     *
     * @return
     */
    @Override
    public JSONArray getAllCourierStationList() {
        return homeCourierStationMapper.getAllCourierStationList();
    }
}
