package com.ruoyi.homecare.volunteers.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 物资捐赠详情对象 t_home_material_donation_info
 *
 * <AUTHOR>
 * @date 2022-10-10
 */
public class HomeMaterialDonationInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 物资捐赠图片
     */
    @Excel(name = "物资捐赠图片")
    private String img;

    /**
     * 捐赠类型：0个人捐赠，1企业/组织捐赠
     */
    @Excel(name = "捐赠类型：0个人捐赠，1企业/组织捐赠")
    private String donationType;

    /**
     * 物资类型
     */
    @Excel(name = "物资类型")
    private String materialType;

    /**
     * 个人名称/企业名称
     */
    @Excel(name = "个人名称/企业名称")
    private String name;

    /**
     * 企业地址
     */
    @Excel(name = "企业地址")
    private String address;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    private String phone;

    /**
     * 捐赠内容
     */
    @Excel(name = "捐赠内容")
    private String donationContent;

    /**
     * 逻辑删除0正常，1已删除
     */
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getDonationType() {
        return donationType;
    }

    public void setDonationType(String donationType) {
        this.donationType = donationType;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getDonationContent() {
        return donationContent;
    }

    public void setDonationContent(String donationContent) {
        this.donationContent = donationContent;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("img", getImg())
                .append("donationType", getDonationType())
                .append("materialType", getMaterialType())
                .append("name", getName())
                .append("address", getAddress())
                .append("phone", getPhone())
                .append("donationContent", getDonationContent())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
