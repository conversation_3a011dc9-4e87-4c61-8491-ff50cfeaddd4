package com.ruoyi.homecare.goodsOrder.response;

import com.ruoyi.homecare.goodsOrder.domain.HomeOrderBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName ProviderHomeOrderBaseInfoVo
 * @Description
 * <AUTHOR>
 * @Date 2022/8/15 17:03
 */
@Data
@ApiModel("ProviderHomeOrderBaseInfoVo")
public class ProviderHomeOrderBaseInfoVo extends HomeOrderBaseInfo {

    @ApiModelProperty("付款方式")
    private String payWayLabel;

    @ApiModelProperty("订单状态")
    private String statusLabel;

    public String getPayWayLabel() {
        // 1:余额付款 2：微信在线支付
        switch (super.getPayWay()) {
            case 1:
                return "余额支付";
            case 2:
                return "微信支付";
            default:
                return "";
        }
    }

    // 0：已关闭 1：待支付 2：已付款 3：已接单 4：已完成  5:已评价 6：退款
    public String getStatusLabel() {

        switch (super.getStatus()) {
            case 0:
                return "已关闭";
            case 1:
                return "待支付";
            case 2:
                return "已付款";
            case 3:
                return "已接单";
            case 4:
                return "已完成";
            case 5:
                return "已评价";
            case 6:
                return "退款";
            default:
                return "";
        }
    }


}
