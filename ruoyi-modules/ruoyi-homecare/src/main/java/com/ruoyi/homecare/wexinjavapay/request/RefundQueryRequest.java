package com.ruoyi.homecare.wexinjavapay.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

/**
 * @ClassName RefundQueryRequest
 * @Description
 * <AUTHOR>
 * @Date 2022/7/8 14:41
 */
@Data
public class RefundQueryRequest {

    //************以下四选一************
    /**
     * <pre>
     * 微信订单号
     * transaction_id
     * String(32)
     * 1217752501201407033233368018
     * 微信订单号
     * </pre>
     */
    private String transactionId;

    /**
     * <pre>
     * 商户订单号
     * out_trade_no
     * String(32)
     * 1217752501201407033233368018
     * 商户系统内部的订单号
     * </pre>
     */
    private String outTradeNo;

    /**
     * <pre>
     * 商户退款单号
     * out_refund_no
     * String(32)
     * 1217752501201407033233368018
     * 商户侧传给微信的退款单号
     * </pre>
     */
    private String outRefundNo;

    /**
     * <pre>
     * 微信退款单号
     * refund_id
     * String(28)
     * 1217752501201407033233368018
     * 微信生成的退款单号，在申请退款接口有返回
     * </pre>
     */
    private String refundId;

}
