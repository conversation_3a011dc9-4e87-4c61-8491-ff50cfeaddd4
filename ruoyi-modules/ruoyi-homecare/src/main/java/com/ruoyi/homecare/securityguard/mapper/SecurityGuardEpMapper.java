package com.ruoyi.homecare.securityguard.mapper;

import com.ruoyi.homecare.securityguard.domain.SecurityGuardEp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ep设备Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-01
 */
public interface SecurityGuardEpMapper {
    /**
     * 查询ep设备
     *
     * @param id ep设备主键
     * @return ep设备
     */
    public SecurityGuardEp selectSecurityGuardEpById(Long id);

    /**
     * 查询ep设备列表
     *
     * @param securityGuardEp ep设备
     * @return ep设备集合
     */
    public List<SecurityGuardEp> selectSecurityGuardEpList(SecurityGuardEp securityGuardEp);

    /**
     * 新增ep设备
     *
     * @param securityGuardEp ep设备
     * @return 结果
     */
    public int insertSecurityGuardEp(SecurityGuardEp securityGuardEp);

    /**
     * 修改ep设备
     *
     * @param securityGuardEp ep设备
     * @return 结果
     */
    public int updateSecurityGuardEp(SecurityGuardEp securityGuardEp);

    /**
     * 删除ep设备
     *
     * @param id ep设备主键
     * @return 结果
     */
    public int deleteSecurityGuardEpById(Long id);

    /**
     * 批量删除ep设备
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSecurityGuardEpByIds(Long[] ids);

    SecurityGuardEp selectSecurityGuardByEPIEEE(@Param("ep") String ep, @Param("ieee") String ieee);

}
