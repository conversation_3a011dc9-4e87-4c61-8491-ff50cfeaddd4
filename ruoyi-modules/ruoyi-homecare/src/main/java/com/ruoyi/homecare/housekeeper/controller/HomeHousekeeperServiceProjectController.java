package com.ruoyi.homecare.housekeeper.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.web.domain.TAjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.housekeeper.domain.HomeHousekeeperServiceProject;
import com.ruoyi.homecare.housekeeper.service.IHomeHousekeeperServiceProjectService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 社区管家服务项目Controller
 *
 * <AUTHOR>
 * @date 2022-06-22
 */
@RestController
@RequestMapping("/homeHousekeeperServiceProject")
@Api(value = "社区管家-服务项目", tags = "社区管家-服务项目")
public class HomeHousekeeperServiceProjectController extends BaseController {
    @Autowired
    private IHomeHousekeeperServiceProjectService homeHousekeeperServiceProjectService;

    /**
     * 查询社区管家服务项目列表
     */
    ////@RequiresPermissions("housekeeper:homeHousekeeperServiceProject:list")
    @GetMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(value = "服务项目名称", name = "projectName", dataTypeClass = String.class, paramType = "query"),
    })
    @ApiOperation(value = "查询社区管家服务项目列表")
    public TableDataInfo<HomeHousekeeperServiceProject> list(@ApiIgnore HomeHousekeeperServiceProject homeHousekeeperServiceProject) {
        startPage();
        List<HomeHousekeeperServiceProject> list = homeHousekeeperServiceProjectService.selectHomeHousekeeperServiceProjectList(homeHousekeeperServiceProject);
        return getDataTable(list);
    }

    /**
     * 导出社区管家服务项目列表
     */
    ////@RequiresPermissions("housekeeper:homeHousekeeperServiceProject:export")
    @Log(platform = "2", title = "社区管家服务项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出社区管家服务项目列表")
    public void export(HttpServletResponse response, HomeHousekeeperServiceProject homeHousekeeperServiceProject) {
        List<HomeHousekeeperServiceProject> list = homeHousekeeperServiceProjectService.selectHomeHousekeeperServiceProjectList(homeHousekeeperServiceProject);
        ExcelUtil<HomeHousekeeperServiceProject> util = new ExcelUtil<HomeHousekeeperServiceProject>(HomeHousekeeperServiceProject.class);
        util.exportExcel(response, list, "社区管家服务项目数据");
    }

    /**
     * 获取社区管家服务项目详细信息
     */
    ////@RequiresPermissions("housekeeper:homeHousekeeperServiceProject:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取社区管家服务项目详细信息")
    public TAjaxResult<HomeHousekeeperServiceProject> getInfo(@PathVariable("id") Long id) {
        return new TAjaxResult(homeHousekeeperServiceProjectService.selectHomeHousekeeperServiceProjectById(id));
    }

    /**
     * 保存社区管家服务项目
     */
    @ApiOperation(value = "保存社区管家服务项目")
    @PostMapping("save")
    public AjaxResult save(@RequestBody HomeHousekeeperServiceProject homeHousekeeperServiceProject) {
        if (null == homeHousekeeperServiceProject.getId() || 0 == homeHousekeeperServiceProject.getId()) {
            return add(homeHousekeeperServiceProject);
        } else {
            return edit(homeHousekeeperServiceProject);
        }
    }

    /**
     * 新增社区管家服务项目
     */
    ////@RequiresPermissions("housekeeper:homeHousekeeperServiceProject:add")
    @Log(platform = "2", title = "社区管家服务项目", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody HomeHousekeeperServiceProject homeHousekeeperServiceProject) {
        return toAjax(homeHousekeeperServiceProjectService.insertHomeHousekeeperServiceProject(homeHousekeeperServiceProject));
    }

    /**
     * 修改社区管家服务项目
     */
    ////@RequiresPermissions("housekeeper:homeHousekeeperServiceProject:edit")
    @Log(platform = "2", title = "社区管家服务项目", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody HomeHousekeeperServiceProject homeHousekeeperServiceProject) {
        return toAjax(homeHousekeeperServiceProjectService.updateHomeHousekeeperServiceProject(homeHousekeeperServiceProject));
    }

    /**
     * 删除社区管家服务项目
     */
    ////@RequiresPermissions("housekeeper:homeHousekeeperServiceProject:remove")
    @Log(platform = "2", title = "社区管家服务项目", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除社区管家服务项目")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeHousekeeperServiceProjectService.deleteHomeHousekeeperServiceProjectByIds(ids));
    }
}
