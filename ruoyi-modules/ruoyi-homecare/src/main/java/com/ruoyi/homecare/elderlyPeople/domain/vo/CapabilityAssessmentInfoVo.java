package com.ruoyi.homecare.elderlyPeople.domain.vo;

import cn.hutool.json.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("调查问卷实体类Vo")
public class CapabilityAssessmentInfoVo {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "老人id")
    private String userId;
    @ApiModelProperty(value = "调查问卷列表Json")
    private JSONArray arr;
    @ApiModelProperty(value = "评估时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date evaluationTime;
    @ApiModelProperty(value = "评估人id")
    private String assessorId;
    @ApiModelProperty(value = "评估人名称")
    private String assessorName;
    @ApiModelProperty(value = "评估原因值")
    private String assessReasonValue;
    @ApiModelProperty(value = "评估原因文字")
    private String assessReasonLabel;
    @ApiModelProperty(value = "评估意见")
    private String evaluationOpinion;

}
