package com.ruoyi.homecare.volunteers.service.impl;

import java.util.List;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.homecare.volunteers.domain.HomeAssociationInfo;
import com.ruoyi.homecare.volunteers.mapper.HomeAssociationInfoMapper;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.homecare.volunteers.mapper.HomeAssociationVolunteerIndexMapper;
import com.ruoyi.homecare.volunteers.domain.HomeAssociationVolunteerIndex;
import com.ruoyi.homecare.volunteers.service.IHomeAssociationVolunteerIndexService;

/**
 * 志愿者和社团关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-10
 */
@Service
public class HomeAssociationVolunteerIndexServiceImpl implements IHomeAssociationVolunteerIndexService {
    @Autowired
    private HomeAssociationVolunteerIndexMapper homeAssociationVolunteerIndexMapper;

    @Autowired
    private HomeAssociationInfoMapper homeAssociationInfoMapper;

    /**
     * 查询志愿者和社团关联
     *
     * @param id 志愿者和社团关联主键
     * @return 志愿者和社团关联
     */
    @Override
    public HomeAssociationVolunteerIndex selectHomeAssociationVolunteerIndexById(Long id) {
        return homeAssociationVolunteerIndexMapper.selectHomeAssociationVolunteerIndexById(id);
    }

    /**
     * 查询志愿者和社团关联列表
     *
     * @param homeAssociationVolunteerIndex 志愿者和社团关联
     * @return 志愿者和社团关联
     */
    @Override
    public List<HomeAssociationVolunteerIndex> selectHomeAssociationVolunteerIndexList(HomeAssociationVolunteerIndex homeAssociationVolunteerIndex) {
        return homeAssociationVolunteerIndexMapper.selectHomeAssociationVolunteerIndexList(homeAssociationVolunteerIndex);
    }

    /**
     * 新增志愿者和社团关联
     *
     * @param homeAssociationVolunteerIndex 志愿者和社团关联
     * @return 结果
     */
    @Override
    public int insertHomeAssociationVolunteerIndex(HomeAssociationVolunteerIndex homeAssociationVolunteerIndex) {
        homeAssociationVolunteerIndex.setCreateTime(DateUtils.getNowDate());
        // 判断是否有正在审核以及已经通过的创建社团
        HomeAssociationInfo associationInfo = homeAssociationInfoMapper.selectHomeAssociationInfoByFirstContacts(homeAssociationVolunteerIndex.getVolunteerId());
        if (null != associationInfo) {
            throw new ServiceException("已注册过社团 不可加入社团！");
        }
        // 判断是否有参加正在审核以及审核通过的社团
        HomeAssociationVolunteerIndex volunteerIndex = homeAssociationVolunteerIndexMapper.selectHomeAssociationVolunteerIndexByVolunteerId(homeAssociationVolunteerIndex.getVolunteerId());
        if (null != volunteerIndex) {
            throw new ServiceException("已申请加入社团 不可注册社团！");
        }
        return homeAssociationVolunteerIndexMapper.insertHomeAssociationVolunteerIndex(homeAssociationVolunteerIndex);
    }

    /**
     * 修改志愿者和社团关联
     *
     * @param homeAssociationVolunteerIndex 志愿者和社团关联
     * @return 结果
     */
    @Override
    public int updateHomeAssociationVolunteerIndex(HomeAssociationVolunteerIndex homeAssociationVolunteerIndex) {
        homeAssociationVolunteerIndex.setUpdateTime(DateUtils.getNowDate());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        homeAssociationVolunteerIndex.setUpdateBy(String.valueOf(loginUser.getUserid()));
        return homeAssociationVolunteerIndexMapper.updateHomeAssociationVolunteerIndex(homeAssociationVolunteerIndex);
    }

    /**
     * 批量删除志愿者和社团关联
     *
     * @param ids 需要删除的志愿者和社团关联主键
     * @return 结果
     */
    @Override
    public int deleteHomeAssociationVolunteerIndexByIds(Long[] ids) {
        return homeAssociationVolunteerIndexMapper.deleteHomeAssociationVolunteerIndexByIds(ids);
    }

    /**
     * 删除志愿者和社团关联信息
     *
     * @param id 志愿者和社团关联主键
     * @return 结果
     */
    @Override
    public int deleteHomeAssociationVolunteerIndexById(Long id) {
        return homeAssociationVolunteerIndexMapper.deleteHomeAssociationVolunteerIndexById(id);
    }
}
