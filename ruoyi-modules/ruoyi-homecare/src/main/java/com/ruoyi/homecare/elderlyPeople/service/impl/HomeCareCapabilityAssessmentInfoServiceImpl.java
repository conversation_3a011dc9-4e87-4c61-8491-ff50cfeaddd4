package com.ruoyi.homecare.elderlyPeople.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.homecare.elderlyPeople.domain.CapabilityAssessmentInfo;
import com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleInfo;
import com.ruoyi.homecare.elderlyPeople.domain.QuestionnaireRecords;
import com.ruoyi.homecare.elderlyPeople.domain.vo.CapabilityAssessmentInfoVo;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareCapabilityAssessmentInfoMapper;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeopleInfoMapper;
import com.ruoyi.homecare.elderlyPeople.mapper.HomeCareQuestionnaireRecordsMapper;
import com.ruoyi.homecare.elderlyPeople.service.IHomeCareCapabilityAssessmentInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 能力评估报告Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@Service
public class HomeCareCapabilityAssessmentInfoServiceImpl implements IHomeCareCapabilityAssessmentInfoService {
    @Autowired
    private HomeCareCapabilityAssessmentInfoMapper homeCareCapabilityAssessmentInfoMapper;

    @Autowired
    private HomeCareQuestionnaireRecordsMapper homeCareQuestionnaireRecordsMapper;

    @Autowired
    private HomeCareElderlyPeopleInfoMapper homeCareElderlyPeopleInfoMapper;

    /**
     * 查询能力评估报告
     *
     * @param id 能力评估报告主键
     * @return 能力评估报告
     */
    @Override
    public CapabilityAssessmentInfo selectCapabilityAssessmentInfoById(Long id) {
        return homeCareCapabilityAssessmentInfoMapper.selectCapabilityAssessmentInfoById(id);
    }

    /**
     * 查询能力评估报告列表
     *
     * @param capabilityAssessmentInfo 能力评估报告
     * @return 能力评估报告
     */
    @Override
    public List<CapabilityAssessmentInfo> selectCapabilityAssessmentInfoList(CapabilityAssessmentInfo capabilityAssessmentInfo) {
        return homeCareCapabilityAssessmentInfoMapper.selectCapabilityAssessmentInfoList(capabilityAssessmentInfo);
    }

    /**
     * 新增能力评估报告
     *
     * @param capabilityAssessmentInfo 能力评估报告
     * @return 结果
     */
    @Override
    public int insertCapabilityAssessmentInfo(CapabilityAssessmentInfo capabilityAssessmentInfo) {
        capabilityAssessmentInfo.setCreateTime(DateUtils.getNowDate());
        return homeCareCapabilityAssessmentInfoMapper.insertCapabilityAssessmentInfo(capabilityAssessmentInfo);
    }

    /**
     * 修改能力评估报告
     *
     * @param capabilityAssessmentInfo 能力评估报告
     * @return 结果
     */
    @Override
    public int updateCapabilityAssessmentInfo(CapabilityAssessmentInfo capabilityAssessmentInfo) {
        capabilityAssessmentInfo.setUpdateTime(DateUtils.getNowDate());
        return homeCareCapabilityAssessmentInfoMapper.updateCapabilityAssessmentInfo(capabilityAssessmentInfo);
    }

    /**
     * 批量删除能力评估报告
     *
     * @param ids 需要删除的能力评估报告主键
     * @return 结果
     */
    @Override
    public int deleteCapabilityAssessmentInfoByIds(Long[] ids) {
        return homeCareCapabilityAssessmentInfoMapper.deleteCapabilityAssessmentInfoByIds(ids);
    }

    /**
     * 删除能力评估报告信息
     *
     * @param id 能力评估报告主键
     * @return 结果
     */
    @Override
    public int deleteCapabilityAssessmentInfoById(Long id) {
        return homeCareCapabilityAssessmentInfoMapper.deleteCapabilityAssessmentInfoById(id);
    }

    /**
     * 保存评估报告
     *
     * @param vo
     * @return
     */
    @Override
    public int saveCapabilityAssessmentInfo(CapabilityAssessmentInfoVo vo) {
        CapabilityAssessmentInfo info = new CapabilityAssessmentInfo();
        int value1 = 0;
        int value2 = 0;
        int value3 = 0;
        int value4 = 0;
        info.setEvaluationTime(vo.getEvaluationTime());
        info.setAssessorId(vo.getAssessorId());
        info.setAssessReasonValue(vo.getAssessReasonValue());
        info.setAssessReasonLabel(vo.getAssessReasonLabel());
        info.setEvaluationOpinion(vo.getEvaluationOpinion());
        info.setUserId(vo.getUserId());
        info.setIndustryStandard("民政行业标准");
        Integer ysspValue = 0;
        Integer tlValue = 0;
        Integer slValue = 0;
        Integer gtjlValue = 0;
        JSONArray list = vo.getArr();
        for (Object object : list) {
            JSONObject json = (JSONObject) object;
            JSONObject jsonObject = json.getJSONObject("score");
            if ("1".equals(jsonObject.getStr("classType"))) {// 计算调查问卷-日常生活活动评估总分值
                value1 += jsonObject.getInt("value");
            } else if ("2".equals(jsonObject.getStr("classType"))) {// 计算调查问卷-感知觉与沟通评估总分值
                value2 += jsonObject.getInt("value");
            } else if ("3".equals(jsonObject.getStr("classType"))) {// 计算调查问卷-精神状态评估总分值
                value3 += jsonObject.getInt("value");
                // 意识水平选中的值
                if ("yssp".equals(jsonObject.getStr("type"))) {
                    ysspValue = jsonObject.getInt("value");
                }
                // 听力选中的值
                if ("sl".equals(jsonObject.getStr("type"))) {
                    tlValue = jsonObject.getInt("value");
                }
                // 视力选中的值
                if ("tl".equals(jsonObject.getStr("type"))) {
                    slValue = jsonObject.getInt("value");
                }
                // 获取沟通交流选中的值
                if ("gtjl".equals(jsonObject.getStr("type"))) {
                    gtjlValue = jsonObject.getInt("value");
                }
            } else {// 计算调查问卷-感知觉与社会参与评估总分值
                value4 += jsonObject.getInt("value");
            }
        }
        // 调查问卷日常生活活动评估总分值
        info.setSelfCareAbilityAssessmentScore(value1);
        // 调查问卷精神状态评估总分值
        info.setMentalStateAssessmentScore(value2);
        // 调查问卷感知觉与沟通评估总分值
        info.setAthleticAbilityAssessmentScore(value3);
        // 调查问卷感知觉与社会参与评估总分值
        info.setPerceptionSocialEngagementAssessmentScore(value4);
        int assessmentValue1 = getAssessmentLabel("1", value1);
        int assessmentValue2 = getAssessmentLabel("2", value2);
        int assessmentValue3 = getGzAssessment(ysspValue, tlValue, slValue, gtjlValue);
        int assessmentValue4 = getAssessmentLabel("4", value4);
        // 日常生活活动评估值
        info.setSelfCareAbilityAssessmentValue(assessmentValue1);
        // 精神状态评估值
        info.setMentalStateAssessmentValue(assessmentValue2);
        // 感知觉与沟通评估值
        info.setAthleticAbilityAssessmentValue(assessmentValue3);
        // 感知觉与社会参与评估值
        info.setPerceptionSocialEngagementAssessmentValue(assessmentValue4);
        int num = getPreliminaryAssessment(assessmentValue1, assessmentValue2, assessmentValue3, assessmentValue4);
        // 初次老年人能力评估等级
        info.setPreliminaryAssessmentLevel(String.valueOf(num));
        // 最终老年人能力评估等级
        info.setElderlyAbilityFinalLevel(num);
        int i = homeCareCapabilityAssessmentInfoMapper.insertCapabilityAssessmentInfo(info);
        QuestionnaireRecords questionnaireRecords = new QuestionnaireRecords();
        questionnaireRecords.setBaseId(info.getId());
        questionnaireRecords.setJsonData(list.toString());
        homeCareQuestionnaireRecordsMapper.insertQuestionnaireRecords(questionnaireRecords);
        // 改变老人失能情况基础信息
        ElderlyPeopleInfo elderlyPeopleInfo = homeCareElderlyPeopleInfoMapper.selectElderlyPeopleInfoById(info.getUserId());
        elderlyPeopleInfo.setDisability(info.getPreliminaryAssessmentLevel());
        homeCareElderlyPeopleInfoMapper.updateElderlyPeopleInfo(elderlyPeopleInfo);
        return 1;
    }

    /**
     * 日常生活等三个逻辑运算
     *
     * @param type  类型
     * @param value 总值
     * @return
     */
    private int getAssessmentLabel(String type, int value) {
        int label = 0;
        // 日常生活活动分级标准
        if ("1".equals(type)) {
            if (value == 100) {
                label = 0;// 能力完好
            } else if (value < 100 && value >= 61) {
                label = 1;// 轻度受损
            } else if (value < 40 && value >= 60) {
                label = 2;// 中度受损
            } else if (value <= 40) {
                label = 3;// 重度受损
            }
        }
        // 精神状态分级标准
        if ("2".equals(type)) {
            if (value == 0) {
                label = 0;// 能力完好
            } else if (value == 1) {
                label = 1;// 轻度受损
            } else if (value < 3 && value >= 2) {
                label = 2;// 中度受损
            } else if (value < 7 && value >= 4) {
                label = 3;// 重度受损
            }
        }
        // 社会参与分级标准
        if ("4".equals(type)) {
            if (value < 3 && value <= 0) {
                label = 0;// 能力完好
            } else if (value < 8 && value >= 3) {
                label = 1;// 轻度受损
            } else if (value < 14 && value >= 8) {
                label = 2;// 中度受损
            } else if (value < 21 && value >= 14) {
                label = 3;// 重度受损
            }
        }
        return label;
    }

    /**
     * 感知觉与沟通评估值逻辑运算
     *
     * @param ysspVal 意识水平分值
     * @param tlVal   听力分值
     * @param slVal   视力分值
     * @param gtjlVal 沟通交流分值
     * @return
     */
    private int getGzAssessment(int ysspVal, int tlVal, int slVal, int gtjlVal) {
        int label = 0;
        if (ysspVal < 2) {// 意识水平清醒和嗜睡
            if (ysspVal == 0 && tlVal < 4 || slVal < 4 || gtjlVal < 3) { // 意识清醒 且视力和听力至少有一项都小于4，或者沟通小于3
                if (tlVal < 2 && slVal < 2 && gtjlVal == 0) {// 视力和听力为0或1，沟通为0
                    label = 0;// 能力完好
                } else if (tlVal == 2 || slVal == 2 || gtjlVal == 1) {// 视力或听力至少有一项为2，或者沟通为1
                    label = 1;// 轻度受损
                } else if (tlVal == 3 || slVal == 3 || gtjlVal == 2) {// 视力或听力至少有一项为3，或者沟通为2
                    label = 2;// 中度受损
                }
            } else {
                if (ysspVal == 1 && tlVal < 3 || slVal < 3 && gtjlVal < 3) { // 意识水平嗜睡 视力听力为3级一下 沟通为2级一下
                    label = 2;// 中度受损
                }
                if (tlVal == 4 || slVal == 4 && gtjlVal == 3) {// 意识水平清醒或嗜睡 力或听力至少有一项为4，或者沟通为3
                    label = 3;// 重度受损
                }
            }
        } else { // 意识水平 等于昏睡或昏迷
            label = 3;// 重度受损
        }
        return label;
    }

    /**
     * 老年人评估报告逻辑运算
     *
     * @param rchdValue 日常生活活动级别值
     * @param jsztValue 精神状态级别值
     * @param gzValue   感知觉与沟通级别值
     * @param shcyValue 社会参与级别值
     * @return
     */
    public int getPreliminaryAssessment(int rchdValue, int jsztValue, int gzValue, int shcyValue) {
        int label = 0;
        if (rchdValue < 3) {
            if (rchdValue == 0) {// 日常生活活动的分级为0
                if (jsztValue == 0 && gzValue == 0 && shcyValue < 2) {// 日常生活活动、精神状态、感知觉与沟通分级均为0，社会参与的分级为0或1
                    label = 0;// 能力完好
                } else if (jsztValue <= 2 || gzValue <= 2 || shcyValue == 2) {// 日常生活活动分级为0，但精神状态、感知觉与沟通中至少一项分级为1或2，或社会参与的分级为2:
                    label = 1;// 轻度失能
                } else if (jsztValue == 3 || gzValue == 3 || shcyValue == 3) {//
                    label = 2;// 中度失能
                }
            } else if (rchdValue == 1) {// 日常生活活动的分级为1
                if (jsztValue < 2 || gzValue < 2 || shcyValue < 2) {// 日常生活活动分级为1，精神状态、感知觉与沟通、社会参与中至少有一项的分级为0或1
                    label = 1;// 轻度失能
                } else if (jsztValue == 2 && gzValue == 2 && shcyValue == 2 || jsztValue == 3 || gzValue == 3 || shcyValue == 3) {// 日常生活活动分级为1，但精神状态、感知觉与沟通、社会参与均为2，或有一项为3
                    label = 2;// 中度失能
                }
            } else if (rchdValue == 2) {// 日常生活活动的分级为2
                if (jsztValue == 2 && gzValue == 2 && shcyValue == 2) {// 日常生活活动、精神状态、感知觉与沟通、社会参与分级均为2:
                    label = 3;// 重度失能
                } else if (jsztValue < 3 || gzValue < 3 || shcyValue < 3) {// 日常生活活动分级为2，且精神状态、感知觉与沟通、社会参与中有1-2项的分级为1或2
                    label = 2;// 中度失能
                } else if (jsztValue == 3 || gzValue == 3 || shcyValue == 3) {// 日常生活活动分级为2，且精神状态、感知觉与沟通、社会参与中至少有一项分级为3
                    label = 3;// 重度失能
                }
            }

        } else {// 日常生活活动的分级为3
            label = 3;// 重度失能
        }
        return label;
    }


    /**
     * 查看调查问卷历史
     *
     * @param id
     * @return
     */
    @Override
    public CapabilityAssessmentInfoVo getAssessmentInfoVo(Long id) {
        CapabilityAssessmentInfoVo vo = new CapabilityAssessmentInfoVo();
        CapabilityAssessmentInfo capabilityAssessmentInfo = selectCapabilityAssessmentInfoById(id);
        vo.setAssessorId(capabilityAssessmentInfo.getAssessorId());
        vo.setAssessorName(capabilityAssessmentInfo.getAssessorName());
        vo.setAssessReasonValue(capabilityAssessmentInfo.getAssessReasonValue());
        vo.setEvaluationTime(capabilityAssessmentInfo.getEvaluationTime());
        vo.setEvaluationOpinion(capabilityAssessmentInfo.getEvaluationOpinion());
        QuestionnaireRecords records = homeCareQuestionnaireRecordsMapper.getByBaseId(id);
        if (!records.getJsonData().isEmpty()) {
            vo.setArr(JSONUtil.parseArray(records.getJsonData()));
        }

        return vo;
    }

}
