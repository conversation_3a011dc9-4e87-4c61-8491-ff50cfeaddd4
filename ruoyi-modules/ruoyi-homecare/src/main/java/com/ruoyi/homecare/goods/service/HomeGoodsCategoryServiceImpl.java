package com.ruoyi.homecare.goods.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.homecare.elderlyPeople.domain.vo.UserDataInfoResult;
import com.ruoyi.homecare.goods.domain.HomeGoodsCategory;
import com.ruoyi.homecare.goods.mapper.HomeGoodsCategoryMapper;
import com.ruoyi.homecare.goods.request.GoodsCategoryAddRequest;
import com.ruoyi.homecare.utils.SysUserUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @ClassName HomeGoodsCategoryServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/7/11 13:31
 */
@Service
@Slf4j
public class HomeGoodsCategoryServiceImpl implements HomeGoodsCategoryService {

    @Autowired
    private HomeGoodsCategoryMapper homeGoodsCategoryMapper;


    @Override
    public List<HomeGoodsCategory> getAllByType(Long type) {

        UserDataInfoResult userDataInfoResult = SysUserUtils.getInfoBySysUserId(null);

        QueryWrapper<HomeGoodsCategory> query = Wrappers.query();
        query.eq("type", type);
        query.eq("provider_id", userDataInfoResult.getServiceProviderId());

        List<HomeGoodsCategory> homeGoodsCategories = homeGoodsCategoryMapper.selectList(query);

        return homeGoodsCategories;
    }

    @Override
    @Transactional
    public TAjaxResult add(GoodsCategoryAddRequest goodsCategoryAddRequest) {

        String name = goodsCategoryAddRequest.getName();
        Long type = goodsCategoryAddRequest.getType();

        UserDataInfoResult userDataInfoResult = SysUserUtils.getInfoBySysUserId(null);

        HomeGoodsCategory homeGoodsCategory = new HomeGoodsCategory();
        homeGoodsCategory.setName(name);
        homeGoodsCategory.setType(type);
        homeGoodsCategory.setProviderId(userDataInfoResult.getServiceProviderId());

        Date currentDate = new Date();
        homeGoodsCategory.setCreateTime(currentDate);
        homeGoodsCategory.setUpdateTime(currentDate);

        int insert = homeGoodsCategoryMapper.insert(homeGoodsCategory);
        if (insert == 1) {// 插入成功
            return new TAjaxResult().success(insert);
        }

        return new TAjaxResult().error("新增商品失败，请重试！");

    }
}
