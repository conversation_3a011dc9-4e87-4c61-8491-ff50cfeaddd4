package com.ruoyi.homecare.housekeeper.service.impl;

import java.util.List;

import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.homecare.housekeeper.mapper.HomeCommunityHousekeeperBaseInfoMapper;
import com.ruoyi.homecare.housekeeper.domain.HomeCommunityHousekeeperBaseInfo;
import com.ruoyi.homecare.housekeeper.service.IHomeCommunityHousekeeperBaseInfoService;

/**
 * 社区管家Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-22
 */
@Service
public class HomeCommunityHousekeeperBaseInfoServiceImpl implements IHomeCommunityHousekeeperBaseInfoService {
    @Autowired
    private HomeCommunityHousekeeperBaseInfoMapper homeCommunityHousekeeperBaseInfoMapper;

    /**
     * 查询社区管家
     *
     * @param id 社区管家主键
     * @return 社区管家
     */
    @Override
    public HomeCommunityHousekeeperBaseInfo selectHomeCommunityHousekeeperBaseInfoById(Long id) {
        return homeCommunityHousekeeperBaseInfoMapper.selectHomeCommunityHousekeeperBaseInfoById(id);
    }

    /**
     * 查询社区管家列表
     *
     * @param homeCommunityHousekeeperBaseInfo 社区管家
     * @return 社区管家
     */
    @Override
    public List<HomeCommunityHousekeeperBaseInfo> selectHomeCommunityHousekeeperBaseInfoList(HomeCommunityHousekeeperBaseInfo homeCommunityHousekeeperBaseInfo) {
        return homeCommunityHousekeeperBaseInfoMapper.selectHomeCommunityHousekeeperBaseInfoList(homeCommunityHousekeeperBaseInfo);
    }

    /**
     * 新增社区管家
     *
     * @param homeCommunityHousekeeperBaseInfo 社区管家
     * @return 结果
     */
    @Override
    public int insertHomeCommunityHousekeeperBaseInfo(HomeCommunityHousekeeperBaseInfo homeCommunityHousekeeperBaseInfo) {
        homeCommunityHousekeeperBaseInfo.setCreateTime(DateUtils.getNowDate());
        return homeCommunityHousekeeperBaseInfoMapper.insertHomeCommunityHousekeeperBaseInfo(homeCommunityHousekeeperBaseInfo);
    }

    /**
     * 修改社区管家
     *
     * @param homeCommunityHousekeeperBaseInfo 社区管家
     * @return 结果
     */
    @Override
    public int updateHomeCommunityHousekeeperBaseInfo(HomeCommunityHousekeeperBaseInfo homeCommunityHousekeeperBaseInfo) {
        homeCommunityHousekeeperBaseInfo.setUpdateTime(DateUtils.getNowDate());
        return homeCommunityHousekeeperBaseInfoMapper.updateHomeCommunityHousekeeperBaseInfo(homeCommunityHousekeeperBaseInfo);
    }

    /**
     * 批量删除社区管家
     *
     * @param ids 需要删除的社区管家主键
     * @return 结果
     */
    @Override
    public int deleteHomeCommunityHousekeeperBaseInfoByIds(Long[] ids) {
        return homeCommunityHousekeeperBaseInfoMapper.deleteHomeCommunityHousekeeperBaseInfoByIds(ids);
    }

    /**
     * 删除社区管家信息
     *
     * @param id 社区管家主键
     * @return 结果
     */
    @Override
    public int deleteHomeCommunityHousekeeperBaseInfoById(Long id) {
        return homeCommunityHousekeeperBaseInfoMapper.deleteHomeCommunityHousekeeperBaseInfoById(id);
    }
}
