package com.ruoyi.homecare.volunteers.mapper;

import com.ruoyi.homecare.volunteers.domain.HomeExchangeRecord;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 礼品兑换记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-06-17
 */
@Mapper
public interface HomeExchangeRecordMapper {
    /**
     * 查询礼品兑换记录
     *
     * @param id 礼品兑换记录主键
     * @return 礼品兑换记录
     */
    public HomeExchangeRecord selectHomeExchangeRecordById(Long id);

    /**
     * 查询礼品兑换记录列表
     *
     * @param homeExchangeRecord 礼品兑换记录
     * @return 礼品兑换记录集合
     */
    public List<HomeExchangeRecord> selectHomeExchangeRecordList(HomeExchangeRecord homeExchangeRecord);

    /**
     * 新增礼品兑换记录
     *
     * @param homeExchangeRecord 礼品兑换记录
     * @return 结果
     */
    public int insertHomeExchangeRecord(HomeExchangeRecord homeExchangeRecord);

    /**
     * 修改礼品兑换记录
     *
     * @param homeExchangeRecord 礼品兑换记录
     * @return 结果
     */
    public int updateHomeExchangeRecord(HomeExchangeRecord homeExchangeRecord);

    /**
     * 删除礼品兑换记录
     *
     * @param id 礼品兑换记录主键
     * @return 结果
     */
    public int deleteHomeExchangeRecordById(Long id);

    /**
     * 批量删除礼品兑换记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeExchangeRecordByIds(Long[] ids);
}
