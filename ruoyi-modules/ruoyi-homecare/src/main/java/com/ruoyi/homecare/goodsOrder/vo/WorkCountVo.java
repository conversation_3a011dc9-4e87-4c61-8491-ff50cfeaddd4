package com.ruoyi.homecare.goodsOrder.vo;

import com.ruoyi.homecare.goodsOrder.domain.HomeOrderRefundInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName AdminHomeOrderRefundInfoVo
 * @Description
 * <AUTHOR>
 * @Date 2022/8/15 13:53
 */
@Data
@ApiModel("AdminHomeOrderRefundInfoVo")
public class WorkCountVo {

    @ApiModelProperty("待配送数量")
    private Integer waitDeliveryNum;

    @ApiModelProperty("待服务数量")
    private Integer waitServiceNum;

}
