package com.ruoyi.homecare.serviceProviders.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.homecare.serviceProviders.domain.HomeServiceProviderManagement;
import com.ruoyi.homecare.serviceProviders.domain.vo.AppServiceProviderManagementVo;
import com.ruoyi.homecare.serviceProviders.service.IHomeServiceProviderManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 服务商管理Controller
 *
 * <AUTHOR>
 * @date 2022-06-14
 */
@RestController
@RequestMapping("/AppHomeServiceProviderManagement")
@Api(tags = "移动端-服务商管理", value = "移动端-服务商管理")
public class HomeAppServiceProviderManagementController extends BaseController {
    @Autowired
    private IHomeServiceProviderManagementService homeServiceProviderManagementService;

    /**
     * app通过类别标签查询服务商列表
     */
    @GetMapping("/getServiceProviderListInfo")
    @ApiOperation(value = "App服务商管理-app通过类别标签查询服务商列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "name", value = "名称", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "communityId", value = "社区id", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "label", value = "标签", dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "serviceType", value = "服务类型", dataTypeClass = String.class)
    })
    public TableDataInfo<HomeServiceProviderManagement> getServiceProviderListInfo(@ApiIgnore AppServiceProviderManagementVo homeServiceProviderManagement) {
        homeServiceProviderManagement.setServiceState("0");
        startPage();
        List<AppServiceProviderManagementVo> list = homeServiceProviderManagementService.getServiceProviderListInfo(homeServiceProviderManagement);
        return getDataTable(list);
    }


}
