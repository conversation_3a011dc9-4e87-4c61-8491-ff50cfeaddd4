package com.ruoyi.homecare.volunteers.controller;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.log.enums.OperatorType;
import com.ruoyi.homecare.elderlyPeople.domain.vo.UserDataInfoResult;
import com.ruoyi.homecare.utils.Base64ToMultipartUtils;
import com.ruoyi.homecare.utils.SysUserUtils;
import com.ruoyi.homecare.volunteers.domain.HomeServicePlaceOrderVolunteer;
import com.ruoyi.homecare.volunteers.domain.HomeVolunteerBaseInfo;
import com.ruoyi.homecare.volunteers.domain.vo.HomeServicePlaceOrderVolunteerVo;
import com.ruoyi.homecare.volunteers.service.IHomeServicePlaceOrderVolunteerInfoService;
import com.ruoyi.homecare.volunteers.service.IHomeServicePlaceOrderVolunteerService;
import com.ruoyi.homecare.volunteers.service.IHomeVolunteerBaseInfoService;
import com.ruoyi.system.api.domain.SysFile;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 志愿者服务操作Controller
 *
 * <AUTHOR>
 * @date 2022-07-18
 */
@RestController
@RequestMapping("/homeAppServicePlaceOrderVolunteerInfo")
@Api(tags = "志愿者移动端-订单模块以及注册功能")
public class HomeAppServicePlaceOrderVolunteerInfoController extends BaseController {
    @Autowired
    private IHomeServicePlaceOrderVolunteerInfoService homeServicePlaceOrderVolunteerInfoService;
    @Autowired
    private IHomeServicePlaceOrderVolunteerService homeServicePlaceOrderVolunteerService;
    @Autowired
    private IHomeVolunteerBaseInfoService homeVolunteerBaseInfoService;


    /**
     * 注册志愿者信息
     */
    @PostMapping("/register")
    @ApiOperation("注册志愿者信息")
    public AjaxResult register(@RequestBody HomeVolunteerBaseInfo homeVolunteerBaseInfo) {
        return toAjax(homeVolunteerBaseInfoService.register(homeVolunteerBaseInfo));
    }

    /**
     * 志愿者移动端-查询志愿者移动端的抢单列表
     */
    @GetMapping("grabOrderList")
    @ApiOperation(value = "志愿者移动端-查询志愿者移动端的抢单列表")
    public TableDataInfo<HomeServicePlaceOrderVolunteer> getGrabOrderList(@ApiIgnore HomeServicePlaceOrderVolunteer homeServicePlaceOrderVolunteer) {
        startPage();
        List<HomeServicePlaceOrderVolunteer> list = homeServicePlaceOrderVolunteerService.getGrabOrderList(homeServicePlaceOrderVolunteer);
        return getDataTable(list);
    }

    /**
     * 志愿者移动端-查询待完成列表
     */
    @GetMapping("getNeedAccomplishList")
    @ApiOperation(value = "志愿者移动端-查询待完成列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(value = "客户名称", name = "userName", dataTypeClass = String.class, paramType = "query")
    })
    public TableDataInfo<HomeServicePlaceOrderVolunteer> getNeedAccomplishList(@ApiIgnore HomeServicePlaceOrderVolunteer homeServicePlaceOrderVolunteer) {
        startPage();
        List<HomeServicePlaceOrderVolunteer> list = homeServicePlaceOrderVolunteerService.getNeedAccomplishList(homeServicePlaceOrderVolunteer);
        return getDataTable(list);
    }

    /**
     * 志愿者移动端-查询已完成列表
     */
    @GetMapping("getAccomplishList")
    @ApiOperation(value = "志愿者移动端-查询已完成列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(value = "客户名称", name = "userName", dataTypeClass = String.class, paramType = "query")
    })
    public TableDataInfo<HomeServicePlaceOrderVolunteer> getAccomplishList(@ApiIgnore HomeServicePlaceOrderVolunteer homeServicePlaceOrderVolunteer) {
        startPage();
        List<HomeServicePlaceOrderVolunteer> list = homeServicePlaceOrderVolunteerService.getAccomplishList(homeServicePlaceOrderVolunteer);
        return getDataTable(list);
    }

    /**
     * 志愿者移动端-根据id查询待完成和已完成详情
     */
    @GetMapping("getNeedAccomplishInfo")
    @ApiOperation(value = "志愿者移动端-根据id查询待完成和已完成详情")
    public TAjaxResult<HomeServicePlaceOrderVolunteerVo> getNeedAccomplishInfo(Long id) {
        HomeServicePlaceOrderVolunteerVo data = homeServicePlaceOrderVolunteerService.getNeedAccomplishInfo(id);
        return new TAjaxResult(data);
    }

    /**
     * 志愿者移动端-抢单操作
     */
    @GetMapping("grabOrder")
    @ApiOperation(value = "志愿者移动端-抢单操作")
    @Log(platform = "2", title = "移动端志愿者抢单操作", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public AjaxResult grabOrder(Long id) {
        int i = homeServicePlaceOrderVolunteerService.grabOrder(id);
        return toAjax(i);
    }

    /**
     * 志愿者移动端-待完成的开始服务
     */
    @PostMapping("beginService")
    @ApiOperation(value = "志愿者移动端-开始服务")
    @Log(platform = "2", title = "移动端志愿者开始服务", businessType = BusinessType.INSERT, operatorType = OperatorType.MOBILE)
    public AjaxResult beginService(@RequestBody HomeServicePlaceOrderVolunteerVo volunteerVo) {
        Long id = volunteerVo.getId();
        String beginImg = volunteerVo.getBeginImg();
        return toAjax(homeServicePlaceOrderVolunteerService.beginService(id, beginImg));
    }

    /**
     * 志愿者移动端-服务中上传图片保存
     */
    @PostMapping("servicePeriodImg")
    @ApiOperation(value = "志愿者移动端-服务中上传图片保存")
    @Log(platform = "2", title = "移动端志愿者服务中上传图片保存", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public AjaxResult servicePeriodImg(@RequestBody HomeServicePlaceOrderVolunteerVo volunteerVo) {
        Long infoId = volunteerVo.getInfoId();
        String img = volunteerVo.getImg();
        return toAjax(homeServicePlaceOrderVolunteerService.servicePeriodImg(infoId, img));
    }

    /**
     * 志愿者移动端-签退
     */
    @PostMapping("endService")
    @ApiOperation(value = "志愿者移动端-签退")
    @Log(platform = "2", title = "移动端志愿者服签退", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    public AjaxResult endService(@RequestBody HomeServicePlaceOrderVolunteerVo volunteerVo) {
        int i = homeServicePlaceOrderVolunteerService.endService(volunteerVo);
        return toAjax(i);
    }


    /**
     * 老人移动端-志愿者服务记录
     */
    @GetMapping("getVolunteerServiceListByUserId")
    @ApiOperation(value = "志愿者移动端-查询已完成列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(value = "志愿者姓名", name = "volunteerName", dataTypeClass = String.class, paramType = "query")
    })
    public TableDataInfo<HomeServicePlaceOrderVolunteer> getVolunteerServiceListByUserId(@ApiIgnore HomeServicePlaceOrderVolunteer homeServicePlaceOrderVolunteer) {
        startPage();
        List<HomeServicePlaceOrderVolunteer> list = homeServicePlaceOrderVolunteerService.getVolunteerServiceListByUserId(homeServicePlaceOrderVolunteer);
        return getDataTable(list);
    }

}
