package com.ruoyi.homecare.complaint.controller;

import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.homecare.complaint.domain.HomeOrderComplaint;
import com.ruoyi.homecare.complaint.request.AdminComplaintAllRequest;
import com.ruoyi.homecare.complaint.request.AdminComplaintDealWithRequest;
import com.ruoyi.homecare.complaint.service.ComplaintService;
import com.ruoyi.homecare.complaint.vo.AdminComplaintVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * @ClassName AdminComplaintController
 * @Description 管理员端投诉
 * <AUTHOR>
 * @Date 2022/7/19 15:13
 */
@RestController
@RequestMapping("/adminComplaint")
@Api(tags = "投诉管理-管理员PC")
public class AdminComplaintController {

    @Autowired
    private ComplaintService complaintService;

    @GetMapping("/getList")
    @ApiOperation(value = "分页查询 投诉记录")
    public TableDataInfo<AdminComplaintVo> getAllByProviderId(@Valid AdminComplaintAllRequest adminComplaintAllRequest) {

        TableDataInfo tableDataInfo = complaintService.getAdminComplaintAllList(adminComplaintAllRequest);

        return tableDataInfo;
    }

    @ApiOperation(httpMethod = "POST", value = "处理投诉")
    @PostMapping(value = "/dealWith")
    public TAjaxResult dealWith(@RequestBody @Valid AdminComplaintDealWithRequest adminComplaintDealWithRequest) {
        TAjaxResult tAjaxResult = complaintService.dealWith(adminComplaintDealWithRequest);
        return tAjaxResult;
    }

    @GetMapping("/getOneById")
    @ApiOperation(value = "根据id查询投诉详情")
    public TAjaxResult<HomeOrderComplaint> getOneById(
            @RequestParam(value = "complaintId", required = true)
            @ApiParam(name = "complaintId", value = "投诉id")
            @NotBlank(message = "投诉id必填！")
            String complaintId
    ) {

        TAjaxResult tAjaxResult = complaintService.getOneById(complaintId);

        return tAjaxResult;
    }


}
