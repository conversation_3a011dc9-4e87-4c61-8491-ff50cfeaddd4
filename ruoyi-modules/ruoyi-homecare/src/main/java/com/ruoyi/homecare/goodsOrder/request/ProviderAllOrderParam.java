package com.ruoyi.homecare.goodsOrder.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.homecare.utils.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName ProviderGoodsOrderAllByIdRequest
 * @Description 商户获取订单列表
 * <AUTHOR>
 * @Date 2022/7/18 11:38
 */
@Data
@ApiModel(description = "参数")
public class ProviderAllOrderParam extends BasePageRequest {

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

}
