package com.ruoyi.homecare.app.service;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.homecare.app.domain.WxEeventType;
import com.ruoyi.homecare.app.domain.WxServiceMsgDto;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletRequest;

/**
 * 小程序-用户openIdService接口
 *
 * <AUTHOR>
 * @date 2022-07-27
 */
public interface IHomeAppApiService {
    /**
     * 根据code获取openId
     *
     * @param code
     * @return
     */
    public ResponseEntity getOpenIdByCode(String code);

    /**
     * 根据openId获取用户token
     *
     * @param
     * @return
     */
    R getTokenByOpenId(String openId, String flag);

    /**
     * 根据openId绑定用户信息
     *
     * @param openId
     * @return
     */
    int setOpenIdToUser(HttpServletRequest request, String openId);

    /**
     * 根据openId解绑用户信息
     *
     * @param openId
     * @return
     */
    int deleteOpenIdToUser(String openId);

    /**
     * 移动端通过token获取各个小程序的用户信息
     *
     * @param flag 移动版本标识
     * @return
     */
    AjaxResult getAppUserDataInfo(String flag);

    void saveWxgzhInfo(WxServiceMsgDto wxServiceMsgDto, WxEeventType wxEeventType);
}
