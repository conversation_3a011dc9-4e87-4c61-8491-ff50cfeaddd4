package com.ruoyi.homecare.volunteers.controller;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.log.enums.OperatorType;
import com.ruoyi.homecare.elderlyPeople.domain.vo.UserDataInfoResult;
import com.ruoyi.homecare.utils.SysUserUtils;
import com.ruoyi.homecare.volunteers.domain.HomeActivityApplyIndex;
import com.ruoyi.homecare.volunteers.domain.HomePublicWelfareActivities;
import com.ruoyi.homecare.volunteers.domain.vo.HomeAppPublicWelfareActivitiesVo;
import com.ruoyi.homecare.volunteers.service.IHomeActivityApplyIndexService;
import com.ruoyi.homecare.volunteers.service.IHomePublicWelfareActivitiesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 志愿者服务操作Controller
 *
 * <AUTHOR>
 * @date 2022-07-18
 */
@RestController
@RequestMapping("/homeAppVolunteerInfoActivities")
@Api(tags = "志愿者移动端-公益活动模块")
public class HomeAppVolunteerInfoActivitiesController extends BaseController {
    @Autowired
    private IHomePublicWelfareActivitiesService homePublicWelfareActivitiesService;
    @Autowired
    private IHomeActivityApplyIndexService homeActivityApplyIndexService;


    /**
     * 查询公益活动列表
     */
    @GetMapping("/getNotInvolvedActivitiesList")
    @ApiOperation(value = "查询公益活动列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(value = "活动名称", name = "name", dataTypeClass = String.class, paramType = "query"),
    })
    public TableDataInfo<HomeAppPublicWelfareActivitiesVo> getNotInvolvedActivitiesList(@ApiIgnore HomeAppPublicWelfareActivitiesVo homePublicWelfareActivities) {
        UserDataInfoResult infoBySysUserId = SysUserUtils.getInfoBySysUserId("zyz");
        Long volunteerId = infoBySysUserId.getVolunteerId();
        homePublicWelfareActivities.setVolunteerId(volunteerId);
        startPage();
        List<HomeAppPublicWelfareActivitiesVo> list = homePublicWelfareActivitiesService.getInvolvedActivitiesList(homePublicWelfareActivities);
        return getDataTable(list);
    }


    /**
     * 志愿者公益活动报名
     *
     * @param homeActivityApplyIndex
     * @return
     */
    @ApiOperation(value = "志愿者公益活动报名")
    @Log(platform = "2", title = "移动端志愿者公益活动报名", businessType = BusinessType.INSERT, operatorType = OperatorType.MOBILE)
    @PostMapping("volunteerApply")
    public AjaxResult volunteerApply(@RequestBody HomeActivityApplyIndex homeActivityApplyIndex) {
        int i = homeActivityApplyIndexService.HomeActivityApplyIndex(homeActivityApplyIndex);
        return toAjax(i);
    }

    /**
     * 志愿者公益活动签到
     *
     * @param indexId
     * @return
     */
    @ApiOperation(value = "志愿者公益活动签到")
    @Log(platform = "2", title = "移动端志愿者公益活动签到", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    @GetMapping("volunteerSignIn")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "签到id", name = "indexId", dataType = "Long", paramType = "query")
    })
    public AjaxResult volunteerSignIn(Long indexId) {
        HomeActivityApplyIndex index = homeActivityApplyIndexService.selectHomeActivityApplyIndexById(indexId);
        if (null == index) {
            throw new ServiceException("系统异常请刷新重试！");
        }
        index.setSignIn("1");
        return toAjax(homeActivityApplyIndexService.updateHomeActivityApplyIndex(index));
    }

    /**
     * 移动端志愿者公益活动退出
     *
     * @param indexId
     * @return
     */
    @ApiOperation(value = "移动端志愿者公益活动退出")
    @Log(platform = "2", title = "移动端志愿者公益活动退出", businessType = BusinessType.UPDATE, operatorType = OperatorType.MOBILE)
    @GetMapping("delApplyIndex")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "indexId", name = "indexId", dataType = "Long", paramType = "query")
    })
    public AjaxResult delApplyIndex(Long indexId) {
        return toAjax(homeActivityApplyIndexService.deleteHomeActivityApplyIndexById(indexId));
    }


    /**
     * 获取移动端公益活动详细信息
     */
    @GetMapping("getAppActivityInfo")
    @ApiOperation(value = "获取移动端公益活动详细信息")
    public TAjaxResult<HomeAppPublicWelfareActivitiesVo> getAppActivityInfo(Long id) {
        HomeAppPublicWelfareActivitiesVo homeAppPublicWelfareActivitiesVo = homePublicWelfareActivitiesService.getAppActivityInfo(id);
        return new TAjaxResult(homeAppPublicWelfareActivitiesVo);
    }


    /**
     * 我的发布-查询志愿者本人发布公益活动列表
     */
    @GetMapping("/getMyIssuedList")
    @ApiOperation(value = "我的发布-查询志愿者本人发布公益活动列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(value = "活动名称", name = "name", dataTypeClass = String.class, paramType = "query"),
    })
    public TableDataInfo<HomeAppPublicWelfareActivitiesVo> getMyIssuedList(@ApiIgnore HomeAppPublicWelfareActivitiesVo homePublicWelfareActivities) {
        UserDataInfoResult infoBySysUserId = SysUserUtils.getInfoBySysUserId("zyz");
        Long volunteerId = infoBySysUserId.getVolunteerId();
        homePublicWelfareActivities.setVolunteerId(volunteerId);
        startPage();
        List<HomeAppPublicWelfareActivitiesVo> list = homePublicWelfareActivitiesService.getMyIssuedList(homePublicWelfareActivities);
        return getDataTable(list);
    }


    /**
     * 查询已报名公益活动列表
     */
    @GetMapping("/getInvolvedActivitiesList")
    @ApiOperation(value = "查询已报名公益活动列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(value = "活动名称", name = "name", dataTypeClass = String.class, paramType = "query"),
    })
    public TableDataInfo<HomeAppPublicWelfareActivitiesVo> getInvolvedActivitiesList(@ApiIgnore HomeAppPublicWelfareActivitiesVo homePublicWelfareActivities) {
        UserDataInfoResult infoBySysUserId = SysUserUtils.getInfoBySysUserId("zyz");
        Long volunteerId = infoBySysUserId.getVolunteerId();
        homePublicWelfareActivities.setVolunteerId(volunteerId);
        homePublicWelfareActivities.setSelectFlag("1");
        startPage();
        List<HomeAppPublicWelfareActivitiesVo> list = homePublicWelfareActivitiesService.getInvolvedActivitiesList(homePublicWelfareActivities);
        return getDataTable(list);
    }


    /**
     * 我的发布-移动端保存公益活动
     */
    @Log(platform = "2", title = "移动端保存公益活动", businessType = BusinessType.INSERT, operatorType = OperatorType.MOBILE)
    @PostMapping("appSaveActivities")
    @ApiOperation(value = "我的发布-保存公益活动")
    public AjaxResult appSaveActivities(@RequestBody HomePublicWelfareActivities homePublicWelfareActivities) {
        return toAjax(homePublicWelfareActivitiesService.appSaveActivities(homePublicWelfareActivities));
    }


    /**
     * 我的发布-移动端公益活动结束、终止、取消操作
     *
     * @param id                 活动id
     * @param state              活动状态
     * @param realEarnedTimeCoin 异常终止时所给的时间币
     * @return
     */
    @GetMapping("/appActivityEnd")
    @ApiOperation(value = "我的发布-移动端公益活动结束、终止、取消操作")
    public AjaxResult appActivityEnd(Long id, String state, Long realEarnedTimeCoin) {
        return homePublicWelfareActivitiesService.activityEnd(id, state, realEarnedTimeCoin);
    }


}
