package com.ruoyi.homecare.service.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 居家养老服务套餐
 * @date 2022-07-13
 */
@Data
@ApiModel("居家养老服务套餐")
@TableName("t_Home_Service_Combo_Info")
public class HomeServiceComboInfo implements Serializable {

    private static final long serialVersionUID = -7596477286028643826L;

    @TableId(type = IdType.AUTO)
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 套餐名称
     */
    @ApiModelProperty("套餐名称")
    private String comboName;

    /**
     * 介绍
     */
    @ApiModelProperty("介绍")
    private String introduce;

    @ApiModelProperty("图片")
    private String img;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal price;

    /**
     * 有效期
     */
    @ApiModelProperty("有效期")
    private Integer validityPeriod;

    @ApiModelProperty("是否长期有效")
    private Integer validFlag;
    /**
     * 服务商id
     */
    @ApiModelProperty("服务商id")
    private Long serviceProviderId;

    @ApiModelProperty("服务商名称")
    @TableField(exist = false)
    private String serviceProviderName;

    /**
     * 佣金比例
     */
    @ApiModelProperty("佣金比例")
    private BigDecimal commissionRate;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String status;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    private Date createTime;

    /**
     * 创建人员
     */
    @ApiModelProperty("创建人员")
    private String createBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;

    /**
     * 修改人员
     */
    @ApiModelProperty("修改人员")
    private String updateBy;

    /**
     * 逻辑删除标记（0：显示；1：隐藏
     */
    @ApiModelProperty("逻辑删除标记（0：显示；1：隐藏")
    private String delFlag;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("销量")
    private Long sales;

    public HomeServiceComboInfo() {
    }
}
