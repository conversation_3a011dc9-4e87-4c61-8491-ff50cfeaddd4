package com.ruoyi.homecare.wexinjavapay.service;

import cn.hutool.json.JSONObject;
import com.github.binarywang.wxpay.bean.result.*;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import com.ruoyi.homecare.goodsOrder.request.ApplyForRefundRaram;
import com.ruoyi.homecare.goodsOrder.request.UserGoodsOrderTopUpRequest;
import com.ruoyi.homecare.wexinjavapay.request.RefundQueryRequest;
import com.ruoyi.homecare.wexinjavapay.request.RefundRequeest;
import com.ruoyi.homecare.wexinjavapay.request.UnifiedorderRequest;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName MiniAppPayService
 * @Description 微信小程序支付service接口
 * <AUTHOR>
 * @Date 2022/7/7 10:58
 */
public interface WechatPayService {


    /**
     * <AUTHOR>
     * @Description 支付成功回调处理
     * @Date 2022/7/7
     **/
    String parseOrderNotifyResult(String xmlData);

    /**
     * <AUTHOR>
     * @Description 退款回调通知处理
     * @Date 2022/7/7
     **/
    String parseRefundNotifyResult(String xmlData);


}
