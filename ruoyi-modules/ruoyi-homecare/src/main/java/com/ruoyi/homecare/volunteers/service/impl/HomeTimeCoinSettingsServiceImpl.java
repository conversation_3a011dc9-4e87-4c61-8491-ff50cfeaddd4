package com.ruoyi.homecare.volunteers.service.impl;

import java.util.List;

import cn.hutool.json.JSONArray;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.homecare.volunteers.mapper.HomeTimeCoinSettingsMapper;
import com.ruoyi.homecare.volunteers.domain.HomeTimeCoinSettings;
import com.ruoyi.homecare.volunteers.service.IHomeTimeCoinSettingsService;

/**
 * 服务项与时间币设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-17
 */
@Service
public class HomeTimeCoinSettingsServiceImpl implements IHomeTimeCoinSettingsService {
    @Autowired
    private HomeTimeCoinSettingsMapper homeTimeCoinSettingsMapper;

    /**
     * 查询服务项与时间币设置
     *
     * @param id 服务项与时间币设置主键
     * @return 服务项与时间币设置
     */
    @Override
    public HomeTimeCoinSettings selectHomeTimeCoinSettingsById(Long id) {
        return homeTimeCoinSettingsMapper.selectHomeTimeCoinSettingsById(id);
    }

    /**
     * 查询服务项与时间币设置列表
     *
     * @param homeTimeCoinSettings 服务项与时间币设置
     * @return 服务项与时间币设置
     */
    @Override
    public List<HomeTimeCoinSettings> selectHomeTimeCoinSettingsList(HomeTimeCoinSettings homeTimeCoinSettings) {
        return homeTimeCoinSettingsMapper.selectHomeTimeCoinSettingsList(homeTimeCoinSettings);
    }

    /**
     * 新增服务项与时间币设置
     *
     * @param homeTimeCoinSettings 服务项与时间币设置
     * @return 结果
     */
    @Override
    public int insertHomeTimeCoinSettings(HomeTimeCoinSettings homeTimeCoinSettings) {
        homeTimeCoinSettings.setCreateTime(DateUtils.getNowDate());
        return homeTimeCoinSettingsMapper.insertHomeTimeCoinSettings(homeTimeCoinSettings);
    }

    /**
     * 修改服务项与时间币设置
     *
     * @param homeTimeCoinSettings 服务项与时间币设置
     * @return 结果
     */
    @Override
    public int updateHomeTimeCoinSettings(HomeTimeCoinSettings homeTimeCoinSettings) {
        homeTimeCoinSettings.setUpdateTime(DateUtils.getNowDate());
        return homeTimeCoinSettingsMapper.updateHomeTimeCoinSettings(homeTimeCoinSettings);
    }

    /**
     * 批量删除服务项与时间币设置
     *
     * @param ids 需要删除的服务项与时间币设置主键
     * @return 结果
     */
    @Override
    public int deleteHomeTimeCoinSettingsByIds(Long[] ids) {
        return homeTimeCoinSettingsMapper.deleteHomeTimeCoinSettingsByIds(ids);
    }

    /**
     * 删除服务项与时间币设置信息
     *
     * @param id 服务项与时间币设置主键
     * @return 结果
     */
    @Override
    public int deleteHomeTimeCoinSettingsById(Long id) {
        return homeTimeCoinSettingsMapper.deleteHomeTimeCoinSettingsById(id);
    }

    /**
     * 获取全量服务技能
     *
     * @return
     */
    @Override
    public JSONArray getSettingsList() {
        return homeTimeCoinSettingsMapper.getSettingsList();
    }
}
