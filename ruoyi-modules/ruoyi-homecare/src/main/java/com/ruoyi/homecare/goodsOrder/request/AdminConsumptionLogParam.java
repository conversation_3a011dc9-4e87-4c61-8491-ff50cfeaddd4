package com.ruoyi.homecare.goodsOrder.request;

import com.ruoyi.homecare.utils.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName
 * @Description getAll
 * <AUTHOR>
 * @Date 2022/7/18 11:38
 */
@Data
@ApiModel(description = "老人消费记录参数")
public class AdminConsumptionLogParam extends BasePageRequest {


    @ApiModelProperty("老人名称")
    private String elderlyName;
    @ApiModelProperty("开始时间")
    private Date beginTime;
    @ApiModelProperty("结束时间")
    private Date endTime;

}
