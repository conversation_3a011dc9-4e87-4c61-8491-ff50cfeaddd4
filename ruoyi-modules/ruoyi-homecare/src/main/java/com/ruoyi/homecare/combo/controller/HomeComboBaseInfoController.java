package com.ruoyi.homecare.combo.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.json.JSONArray;
import com.ruoyi.common.core.web.domain.TAjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.homecare.combo.domain.HomeComboBaseInfo;
import com.ruoyi.homecare.combo.service.IHomeComboBaseInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 套餐设置Controller
 *
 * <AUTHOR>
 * @date 2022-06-27
 */
@RestController
@RequestMapping("/homeComboBaseInfo")
@Api(value = "套餐管理-套餐设置", tags = "套餐管理-套餐设置")
public class HomeComboBaseInfoController extends BaseController {
    @Autowired
    private IHomeComboBaseInfoService homeComboBaseInfoService;

    /**
     * 查询套餐设置列表
     */
    ////@RequiresPermissions("combo:homeComboBaseInfo:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询套餐设置列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页码", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页条数", required = false, dataTypeClass = String.class),
            @ApiImplicitParam(paramType = "query", name = "level", value = "等级", required = false, dataTypeClass = String.class),
    })
    public TableDataInfo<HomeComboBaseInfo> list(@ApiIgnore HomeComboBaseInfo homeComboBaseInfo) {
        startPage();
        List<HomeComboBaseInfo> list = homeComboBaseInfoService.selectHomeComboBaseInfoList(homeComboBaseInfo);
        return getDataTable(list);
    }

    /**
     * 导出套餐设置列表
     */
    ////@RequiresPermissions("combo:homeComboBaseInfo:export")
    @Log(platform = "2", title = "套餐设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出套餐设置列表")
    public void export(HttpServletResponse response, HomeComboBaseInfo homeComboBaseInfo) {
        List<HomeComboBaseInfo> list = homeComboBaseInfoService.selectHomeComboBaseInfoList(homeComboBaseInfo);
        ExcelUtil<HomeComboBaseInfo> util = new ExcelUtil<HomeComboBaseInfo>(HomeComboBaseInfo.class);
        util.exportExcel(response, list, "套餐设置数据");
    }

    /**
     * 获取套餐设置详细信息
     */
    ////@RequiresPermissions("combo:homeComboBaseInfo:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取套餐设置详细信息")
    public TAjaxResult<HomeComboBaseInfo> getInfo(@PathVariable("id") Long id) {
        return new TAjaxResult(homeComboBaseInfoService.selectHomeComboBaseInfoById(id));
    }

    /**
     * 保存套餐设置
     */
    @ApiOperation(value = "保存套餐设置")
    @PostMapping("save")
    public AjaxResult save(@RequestBody HomeComboBaseInfo homeComboBaseInfo) {
        if (null == homeComboBaseInfo.getId() || 0 == homeComboBaseInfo.getId()) {
            return add(homeComboBaseInfo);
        } else {
            return edit(homeComboBaseInfo);
        }
    }

    /**
     * 新增套餐设置
     */
    ////@RequiresPermissions("combo:homeComboBaseInfo:add")
    @Log(platform = "2", title = "套餐设置", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiIgnore
    public AjaxResult add(@RequestBody HomeComboBaseInfo homeComboBaseInfo) {
        return toAjax(homeComboBaseInfoService.insertHomeComboBaseInfo(homeComboBaseInfo));
    }

    /**
     * 修改套餐设置
     */
    ////@RequiresPermissions("combo:homeComboBaseInfo:edit")
    @Log(platform = "2", title = "套餐设置", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiIgnore
    public AjaxResult edit(@RequestBody HomeComboBaseInfo homeComboBaseInfo) {
        return toAjax(homeComboBaseInfoService.updateHomeComboBaseInfo(homeComboBaseInfo));
    }

    /**
     * 删除套餐设置
     */
    ////@RequiresPermissions("combo:homeComboBaseInfo:remove")
    @Log(platform = "2", title = "套餐设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除套餐设置")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeComboBaseInfoService.deleteHomeComboBaseInfoByIds(ids));
    }


    /**
     * 获取套餐设置keyValue列表
     *
     * @return
     */
    @GetMapping("getComboList")
    @ApiOperation(value = "获取套餐设置keyValue列表")
    public TAjaxResult<HomeComboBaseInfo> getComboList(String level) {
        JSONArray jsonArray = homeComboBaseInfoService.getComboList(level);
        return new TAjaxResult(jsonArray);
    }

}
