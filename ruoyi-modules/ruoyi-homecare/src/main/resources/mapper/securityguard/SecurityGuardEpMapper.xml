<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.securityguard.mapper.SecurityGuardEpMapper">

    <resultMap type="SecurityGuardEp" id="SecurityGuardEpResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="ep" column="ep"/>
        <result property="ieee" column="ieee"/>
        <result property="devicetype" column="deviceType"/>
        <result property="linkstatus" column="linkStatus"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="profileid" column="ProfileId"/>
        <result property="devmodel" column="devModel"/>
        <result property="iaszonetype" column="IASZoneType"/>
        <result property="netdevicetype" column="netDeviceType"/>
        <result property="clusterflag" column="clusterFlag"/>
        <result property="manucode" column="manuCode"/>
    </resultMap>

    <sql id="selectSecurityGuardEpVo">
        select id, name, ep, ieee, deviceType, linkStatus, create_time, update_time, ProfileId, devModel, IASZoneType,
        netDeviceType, clusterFlag, manuCode from t_security_guard_ep
    </sql>

    <select id="selectSecurityGuardEpList" parameterType="SecurityGuardEp" resultMap="SecurityGuardEpResult">
        <include refid="selectSecurityGuardEpVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="devicetype != null ">and deviceType = #{devicetype}</if>
            <if test="linkstatus != null ">and linkStatus = #{linkstatus}</if>
        </where>
    </select>

    <select id="selectSecurityGuardEpById" parameterType="Long" resultMap="SecurityGuardEpResult">
        <include refid="selectSecurityGuardEpVo"/>
        where id = #{id}
    </select>

    <select id="selectSecurityGuardByEPIEEE" resultMap="SecurityGuardEpResult">
        <include refid="selectSecurityGuardEpVo"/>
        where ep = #{ep} and ieee=#{ieee}
    </select>

    <insert id="insertSecurityGuardEp" parameterType="SecurityGuardEp" useGeneratedKeys="true" keyProperty="id">
        insert into t_security_guard_ep
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="ep != null">ep,</if>
            <if test="ieee != null">ieee,</if>
            <if test="devicetype != null">deviceType,</if>
            <if test="linkstatus != null">linkStatus,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="profileid != null">ProfileId,</if>
            <if test="devmodel != null">devModel,</if>
            <if test="iaszonetype != null">IASZoneType,</if>
            <if test="netdevicetype != null">netDeviceType,</if>
            <if test="clusterflag != null">clusterFlag,</if>
            <if test="manucode != null">manuCode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="ep != null">#{ep},</if>
            <if test="ieee != null">#{ieee},</if>
            <if test="devicetype != null">#{devicetype},</if>
            <if test="linkstatus != null">#{linkstatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="profileid != null">#{profileid},</if>
            <if test="devmodel != null">#{devmodel},</if>
            <if test="iaszonetype != null">#{iaszonetype},</if>
            <if test="netdevicetype != null">#{netdevicetype},</if>
            <if test="clusterflag != null">#{clusterflag},</if>
            <if test="manucode != null">#{manucode},</if>
        </trim>
    </insert>

    <update id="updateSecurityGuardEp" parameterType="SecurityGuardEp">
        update t_security_guard_ep
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="ep != null">ep = #{ep},</if>
            <if test="ieee != null">ieee = #{ieee},</if>
            <if test="devicetype != null">deviceType = #{devicetype},</if>
            <if test="linkstatus != null">linkStatus = #{linkstatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="profileid != null">ProfileId = #{profileid},</if>
            <if test="devmodel != null">devModel = #{devmodel},</if>
            <if test="iaszonetype != null">IASZoneType = #{iaszonetype},</if>
            <if test="netdevicetype != null">netDeviceType = #{netdevicetype},</if>
            <if test="clusterflag != null">clusterFlag = #{clusterflag},</if>
            <if test="manucode != null">manuCode = #{manucode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecurityGuardEpById" parameterType="Long">
        delete from t_security_guard_ep where id = #{id}
    </delete>

    <delete id="deleteSecurityGuardEpByIds" parameterType="String">
        delete from t_security_guard_ep where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
