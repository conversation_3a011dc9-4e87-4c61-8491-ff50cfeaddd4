<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.serviceProviders.mapper.HomeServiceProjectProviderIndexMapper">


    <resultMap type="com.ruoyi.homecare.serviceProviders.domain.HomeServiceProjectProviderIndex"
               id="HomeServiceProjectProviderIndexResult">
        <result property="id" column="id"/>
        <result property="providerId" column="provider_id"/>
        <result property="serviceProjectId" column="service_project_id"/>
        <result property="createTime" column="create_time"/>
        <result property="marketingPrice" column="marketing_price"/>
        <result property="price" column="price"/>
        <result property="stock" column="stock"/>
        <result property="freight" column="freight"/>
        <result property="packing" column="packing"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeServiceProjectProviderIndexVo">
        select id, provider_id, service_project_id, create_time, marketing_price, price, stock, freight, packing,
        create_by, update_time, update_by, del_flag, remark from t_home_service_project_provider_index
    </sql>

    <select id="selectHomeServiceProjectProviderIndexList"
            parameterType="com.ruoyi.homecare.serviceProviders.domain.HomeServiceProjectProviderIndex"
            resultMap="HomeServiceProjectProviderIndexResult">
        <include refid="selectHomeServiceProjectProviderIndexVo"/>
        <where>
            <if test="providerId != null ">and provider_id = #{providerId}</if>
            <if test="serviceProjectId != null ">and service_project_id = #{serviceProjectId}</if>
            <if test="marketingPrice != null ">and marketing_price = #{marketingPrice}</if>
            <if test="params.beginPrice != null and params.beginPrice != '' and params.endPrice != null and params.endPrice != ''">
                and price between #{params.beginPrice} and #{params.endPrice}
            </if>
            <if test="stock != null ">and stock = #{stock}</if>
            <if test="freight != null ">and freight = #{freight}</if>
            <if test="packing != null ">and packing = #{packing}</if>
        </where>
    </select>

    <select id="selectHomeServiceProjectProviderIndexById" parameterType="Long"
            resultMap="HomeServiceProjectProviderIndexResult">
        <include refid="selectHomeServiceProjectProviderIndexVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeServiceProjectProviderIndex"
            parameterType="com.ruoyi.homecare.serviceProviders.domain.HomeServiceProjectProviderIndex"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_service_project_provider_index
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="providerId != null">provider_id,</if>
            <if test="serviceProjectId != null">service_project_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="marketingPrice != null">marketing_price,</if>
            <if test="price != null">price,</if>
            <if test="stock != null">stock,</if>
            <if test="freight != null">freight,</if>
            <if test="packing != null">packing,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="providerId != null">#{providerId},</if>
            <if test="serviceProjectId != null">#{serviceProjectId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="marketingPrice != null">#{marketingPrice},</if>
            <if test="price != null">#{price},</if>
            <if test="stock != null">#{stock},</if>
            <if test="freight != null">#{freight},</if>
            <if test="packing != null">#{packing},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeServiceProjectProviderIndex"
            parameterType="com.ruoyi.homecare.serviceProviders.domain.HomeServiceProjectProviderIndex">
        update t_home_service_project_provider_index
        <trim prefix="SET" suffixOverrides=",">
            <if test="providerId != null">provider_id = #{providerId},</if>
            <if test="serviceProjectId != null">service_project_id = #{serviceProjectId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="marketingPrice != null">marketing_price = #{marketingPrice},</if>
            <if test="price != null">price = #{price},</if>
            <if test="stock != null">stock = #{stock},</if>
            <if test="freight != null">freight = #{freight},</if>
            <if test="packing != null">packing = #{packing},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeServiceProjectProviderIndexById" parameterType="Long">
        delete from t_home_service_project_provider_index where id = #{id}
    </delete>


    <delete id="deleteHomeServiceProjectProviderIndexByIds" parameterType="String">
        update t_home_service_project_provider_index set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getByProjectIdList" parameterType="Long" resultType="cn.hutool.json.JSONObject">
        SELECT
        a.provider_id AS value,
        b.name AS label
        FROM
        t_home_service_project_provider_index AS a
        LEFT JOIN t_home_service_provider_management AS b ON a.provider_id = b.id
        where a.del_flag = '0' and a.service_project_id = #{projectId}
    </select>
</mapper>
