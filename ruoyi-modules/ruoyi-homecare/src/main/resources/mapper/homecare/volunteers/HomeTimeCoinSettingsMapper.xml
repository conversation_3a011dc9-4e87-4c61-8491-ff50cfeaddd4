<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.volunteers.mapper.HomeTimeCoinSettingsMapper">

    <resultMap type="com.ruoyi.homecare.volunteers.domain.HomeTimeCoinSettings" id="HomeTimeCoinSettingsResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="exchangeRate" column="exchange_rate"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="state" column="state"/>
    </resultMap>

    <sql id="selectHomeTimeCoinSettingsVo">
        select id, name, exchange_rate, create_time,state, create_by, update_time, update_by, del_flag, remark from
        t_home_time_coin_settings
    </sql>

    <select id="selectHomeTimeCoinSettingsList"
            parameterType="com.ruoyi.homecare.volunteers.domain.HomeTimeCoinSettings"
            resultMap="HomeTimeCoinSettingsResult">
        <include refid="selectHomeTimeCoinSettingsVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="exchangeRate != null  and exchangeRate != ''">and exchange_rate = #{exchangeRate}</if>
            <if test="state != null  and state != ''">and state = #{state}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectHomeTimeCoinSettingsById" parameterType="Long" resultMap="HomeTimeCoinSettingsResult">
        <include refid="selectHomeTimeCoinSettingsVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeTimeCoinSettings" parameterType="com.ruoyi.homecare.volunteers.domain.HomeTimeCoinSettings"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_time_coin_settings
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="exchangeRate != null">exchange_rate,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="state != null">state,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="exchangeRate != null">#{exchangeRate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="state != null">#{state},</if>
        </trim>
    </insert>

    <update id="updateHomeTimeCoinSettings" parameterType="com.ruoyi.homecare.volunteers.domain.HomeTimeCoinSettings">
        update t_home_time_coin_settings
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="exchangeRate != null">exchange_rate = #{exchangeRate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="state != null">state = #{state},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeTimeCoinSettingsById" parameterType="Long">
        delete from t_home_time_coin_settings where id = #{id}
    </delete>

    <delete id="deleteHomeTimeCoinSettingsByIds" parameterType="String">
        update t_home_time_coin_settings set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getSettingsList" resultType="cn.hutool.json.JSONObject">
        select id as value,name as label from t_home_time_coin_settings where del_flag= '0' and state = '0' order by
        create_time desc
    </select>
</mapper>
