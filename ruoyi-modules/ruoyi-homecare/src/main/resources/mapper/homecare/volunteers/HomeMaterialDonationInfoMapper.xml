<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.volunteers.mapper.HomeMaterialDonationInfoMapper">

    <resultMap type="HomeMaterialDonationInfo" id="HomeMaterialDonationInfoResult">
        <result property="id" column="id"/>
        <result property="img" column="img"/>
        <result property="donationType" column="donation_type"/>
        <result property="materialType" column="material_type"/>
        <result property="name" column="name"/>
        <result property="address" column="address"/>
        <result property="phone" column="phone"/>
        <result property="donationContent" column="donation_content"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeMaterialDonationInfoVo">
        select id, img, donation_type, material_type, name, address, phone, donation_content, del_flag, create_by,
        create_time, update_by, update_time, remark from t_home_material_donation_info
    </sql>

    <select id="selectHomeMaterialDonationInfoList" parameterType="HomeMaterialDonationInfo"
            resultMap="HomeMaterialDonationInfoResult">
        <include refid="selectHomeMaterialDonationInfoVo"/>
        <where>
            del_flag = '0'
            <if test="donationType != null  and donationType != ''">and donation_type = #{donationType}</if>
            <if test="materialType != null  and materialType != ''">and material_type = #{materialType}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="address != null  and address != ''">and address like concat('%', #{address}, '%')</if>
            <if test="phone != null  and phone != ''">and phone like concat('%', #{phone}, '%')</if>
            <if test="donationContent != null  and donationContent != ''">and donation_content like concat('%',
                #{donationContent}, '%')
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectHomeMaterialDonationInfoById" parameterType="Long" resultMap="HomeMaterialDonationInfoResult">
        <include refid="selectHomeMaterialDonationInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeMaterialDonationInfo" parameterType="HomeMaterialDonationInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_home_material_donation_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="img != null">img,</if>
            <if test="donationType != null">donation_type,</if>
            <if test="materialType != null">material_type,</if>
            <if test="name != null">name,</if>
            <if test="address != null">address,</if>
            <if test="phone != null">phone,</if>
            <if test="donationContent != null">donation_content,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="img != null">#{img},</if>
            <if test="donationType != null">#{donationType},</if>
            <if test="materialType != null">#{materialType},</if>
            <if test="name != null">#{name},</if>
            <if test="address != null">#{address},</if>
            <if test="phone != null">#{phone},</if>
            <if test="donationContent != null">#{donationContent},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeMaterialDonationInfo" parameterType="HomeMaterialDonationInfo">
        update t_home_material_donation_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="img != null">img = #{img},</if>
            <if test="donationType != null">donation_type = #{donationType},</if>
            <if test="materialType != null">material_type = #{materialType},</if>
            <if test="name != null">name = #{name},</if>
            <if test="address != null">address = #{address},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="donationContent != null">donation_content = #{donationContent},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeMaterialDonationInfoById" parameterType="Long">
        delete from t_home_material_donation_info where id = #{id}
    </delete>

    <delete id="deleteHomeMaterialDonationInfoByIds" parameterType="String">
        update t_home_material_donation_info set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
