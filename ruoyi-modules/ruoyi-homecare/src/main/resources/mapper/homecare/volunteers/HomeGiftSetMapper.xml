<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.volunteers.mapper.HomeGiftSetMapper">

    <resultMap type="com.ruoyi.homecare.volunteers.domain.HomeGiftSet" id="HomeGiftSetResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property=" timeCoin" column=" time_coin"/>
        <result property="originalPrice" column="original_price"/>
        <result property="quantity" column="quantity"/>
        <result property="hasChange" column="has_change"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectHomeGiftSetVo">
        select id, name, time_coin, original_price, quantity, has_change, create_time, create_by, update_time,
        update_by, del_flag, remark from t_home_gift_set
    </sql>

    <select id="selectHomeGiftSetList" parameterType="com.ruoyi.homecare.volunteers.domain.HomeGiftSet"
            resultMap="HomeGiftSetResult">
        <include refid="selectHomeGiftSetVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test=" timeCoin != null  and timeCoin != ''">and time_coin = #{ timeCoin}</if>
            <if test="originalPrice != null ">and original_price = #{originalPrice}</if>
            <if test="quantity != null ">and quantity = #{quantity}</if>
            <if test="hasChange != null ">and has_change = #{hasChange}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectHomeGiftSetById" parameterType="Long" resultMap="HomeGiftSetResult">
        <include refid="selectHomeGiftSetVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeGiftSet" parameterType="com.ruoyi.homecare.volunteers.domain.HomeGiftSet"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_home_gift_set
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test=" timeCoin != null">time_coin,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="quantity != null">quantity,</if>
            <if test="hasChange != null">has_change,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test=" timeCoin != null">#{ timeCoin},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="hasChange != null">#{hasChange},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeGiftSet" parameterType="com.ruoyi.homecare.volunteers.domain.HomeGiftSet">
        update t_home_gift_set
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test=" timeCoin != null">time_coin = #{ timeCoin},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="hasChange != null">has_change = #{hasChange},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeGiftSetById" parameterType="Long">
        delete from t_home_gift_set where id = #{id}
    </delete>

    <delete id="deleteHomeGiftSetByIds" parameterType="String">
        update t_home_gift_set set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getAllGiftList" resultType="cn.hutool.json.JSONObject">
        select id as value,name as label,time_coin as timeCoin,quantity as quantity from t_home_gift_set where del_flag
        = '0'
    </select>
</mapper>
