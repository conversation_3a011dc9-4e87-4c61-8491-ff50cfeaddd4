<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.homecare.elderlyPeople.mapper.HomeCareElderlyPeopleInfoMapper">

    <resultMap type="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleInfo" id="HomeCareElderlyPeopleInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="idCardNum" column="id_card_num"/>
        <result property="phone" column="phone"/>
        <result property="dateBirth" column="date_birth"/>
        <result property="age" column="age"/>
        <result property="nation" column="nation"/>
        <result property="marriageStatus" column="marriage_status"/>
        <result property="livingSituation" column="living_situation"/>
        <result property="homeAddress" column="home_address"/>
        <result property="emergencyContactName" column="emergency_contact_name"/>
        <result property="emergencyContactPhone" column="emergency_contact_phone"/>
        <result property="relation" column="relation"/>
        <result property="economicSources" column="economic_sources"/>
        <result property="monthlyIncome" column="monthly_income"/>
        <result property="socialSecurityNo" column="social_security_no"/>
        <result property="status" column="status"/>
        <result property="img" column="img"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="serviceStationId" column="service_station_id"/>
        <result property="careLevel" column="care_level"/>
        <result property="serviceComboId" column="service_combo_id"/>
        <result property="disability" column="disability"/>
        <result property="amount" column="amount"/>
        <result property="sysUserId" column="sys_user_id"/>
        <result property="volunteerFlag" column="volunteer_flag"/>
    </resultMap>

    <sql id="selectElderlyPeopleInfoVo">
        SELECT
        a.id,
        a.name,
        a.sex,
        a.id_card_num,
        a.phone,
        a.date_birth,
        a.age,
        a.amount,
        a.nation,
        a.marriage_status,
        a.living_situation,
        a.home_address,
        a.emergency_contact_name,
        a.emergency_contact_phone,
        a.relation,
        a.economic_sources,
        a.disability,
        a.monthly_income,
        a.social_security_no,
        a.img,
        a.service_station_id,
        a.care_level,
        a.service_combo_id,
        a.sys_user_id,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.del_flag,
        a.volunteer_flag,
        a.remark
        FROM
        t_home_elderly_people_info as a
    </sql>

    <select id="selectElderlyPeopleInfoList" parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleInfo"
            resultMap="HomeCareElderlyPeopleInfoResult">
        <include refid="selectElderlyPeopleInfoVo"/>
        <where>
            a.del_flag = '0'
            <if test="name != null  and name != ''">and a.name like concat('%', #{name}, '%')</if>
            <if test="sex != null  and sex != ''">and a.sex = #{sex}</if>
            <if test="disability != null  and disability != ''">and a.disability = #{disability}</if>
            <if test="idCardNum != null  and idCardNum != ''">and a.id_card_num like concat('%', #{idCardNum}, '%')</if>
            <if test="phone != null  and phone != ''">and a.phone like concat('%', #{phone}, '%')</if>
            <if test="dateBirth != null ">and a.date_birth = #{dateBirth}</if>
            <if test="params.beginAge != null and params.beginAge != '' and params.endAge != null and params.endAge != ''">
                and a.age between #{params.beginAge} and #{params.endAge}
            </if>
            <if test="nation != null  and nation != ''">and a.nation = #{nation}</if>
            <if test="marriageStatus != null  and marriageStatus != ''">and a.marriage_status = #{marriageStatus}</if>
            <if test="livingSituation != null  and livingSituation != ''">and a.living_situation = #{livingSituation}
            </if>
            <if test="homeAddress != null  and homeAddress != ''">and a.home_address = #{homeAddress}</if>
            <if test="emergencyContactName != null  and emergencyContactName != ''">and a.emergency_contact_name like
                concat('%', #{emergencyContactName}, '%')
            </if>
            <if test="emergencyContactPhone != null  and emergencyContactPhone != ''">and a.emergency_contact_phone =
                #{emergencyContactPhone}
            </if>
            <if test="relation != null  and relation != ''">and a.relation = #{relation}</if>
            <if test="economicSources != null  and economicSources != ''">and a.economic_sources = #{economicSources}
            </if>
            <if test="monthlyIncome != null  and monthlyIncome != ''">and a.monthly_income = #{monthlyIncome}</if>
            <if test="socialSecurityNo != null  and socialSecurityNo != ''">and a.social_security_no =
                #{socialSecurityNo}
            </if>
            <if test="img != null  and img != ''">and a.img = #{img}</if>
            <if test="volunteerFlag != null  and volunteerFlag != ''">and a.volunteer_flag = #{volunteerFlag}</if>
        </where>
        order by a.create_time desc
    </select>

    <select id="selectElderlyPeopleInfoById" parameterType="String" resultMap="HomeCareElderlyPeopleInfoResult">
        <include refid="selectElderlyPeopleInfoVo"/>
        where a.id = #{id}
    </select>

    <select id="getElderlyPeopleInfoBySysUserId" parameterType="Long" resultMap="HomeCareElderlyPeopleInfoResult">
        <include refid="selectElderlyPeopleInfoVo"/>
        where a.sys_user_id = #{sysUserId}
    </select>

    <select id="getUserList" parameterType="String" resultType="cn.hutool.json.JSONObject">
        SELECT
        a.id,
        a.name,
        a.sex,
        a.id_card_num,
        a.phone,
        a.age,
        a.del_flag
        FROM
        t_home_elderly_people_info AS a
        <where>
            a.del_flag = '0'
            <if test="name != null and name != '' ">
                and a.name like concat('%', #{name}, '%')
            </if>
            <if test="state != null and state != '' ">
                and a.state = #{state}
            </if>
        </where>
        order by a.create_time desc
    </select>

    <insert id="insertElderlyPeopleInfo" parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleInfo">
        insert into t_home_elderly_people_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="sysUserId != null">sys_user_id,</if>
            <if test="sex != null">sex,</if>
            <if test="amount != null">amount,</if>
            <if test="idCardNum != null">id_card_num,</if>
            <if test="phone != null">phone,</if>
            <if test="dateBirth != null">date_birth,</if>
            <if test="age != null">age,</if>
            <if test="nation != null">nation,</if>
            <if test="marriageStatus != null">marriage_status,</if>
            <if test="livingSituation != null">living_situation,</if>
            <if test="homeAddress != null">home_address,</if>
            <if test="emergencyContactName != null">emergency_contact_name,</if>
            <if test="emergencyContactPhone != null">emergency_contact_phone,</if>
            <if test="relation != null">relation,</if>
            <if test="economicSources != null">economic_sources,</if>
            <if test="monthlyIncome != null">monthly_income,</if>
            <if test="socialSecurityNo != null">social_security_no,</if>
            <if test="status != null">status,</if>
            <if test="img != null">img,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="disability != null">disability,</if>
            <if test="serviceStationId != null">service_station_id,</if>
            <if test="careLevel != null">care_level,</if>
            <if test="serviceComboId != null">service_combo_id,</if>
            <if test="volunteerFlag != null">volunteer_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="sysUserId != null">#{sysUserId},</if>
            <if test="sex != null">#{sex},</if>
            <if test="amount != null">#{amount},</if>
            <if test="idCardNum != null">#{idCardNum},</if>
            <if test="phone != null">#{phone},</if>
            <if test="dateBirth != null">#{dateBirth},</if>
            <if test="age != null">#{age},</if>
            <if test="nation != null">#{nation},</if>
            <if test="marriageStatus != null">#{marriageStatus},</if>
            <if test="livingSituation != null">#{livingSituation},</if>
            <if test="homeAddress != null">#{homeAddress},</if>
            <if test="emergencyContactName != null">#{emergencyContactName},</if>
            <if test="emergencyContactPhone != null">#{emergencyContactPhone},</if>
            <if test="relation != null">#{relation},</if>
            <if test="economicSources != null">#{economicSources},</if>
            <if test="monthlyIncome != null">#{monthlyIncome},</if>
            <if test="socialSecurityNo != null">#{socialSecurityNo},</if>
            <if test="status != null">#{status},</if>
            <if test="img != null">#{img},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="disability != null">#{disability},</if>
            <if test="serviceStationId != null">#{serviceStationId},</if>
            <if test="careLevel != null">#{careLevel},</if>
            <if test="serviceComboId != null">#{serviceComboId},</if>
            <if test="volunteerFlag != null">#{volunteerFlag},</if>
        </trim>
    </insert>

    <update id="updateElderlyPeopleInfo" parameterType="com.ruoyi.homecare.elderlyPeople.domain.ElderlyPeopleInfo">
        update t_home_elderly_people_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="sysUserId != null">sys_user_id = #{sysUserId},</if>
            <if test="serviceStationId != null">service_station_id = #{serviceStationId},</if>
            <if test="careLevel != null">care_level = #{careLevel},</if>
            <if test="serviceComboId != null">service_combo_id = #{serviceComboId},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="idCardNum != null">id_card_num = #{idCardNum},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="dateBirth != null">date_birth = #{dateBirth},</if>
            <if test="age != null">age = #{age},</if>
            <if test="nation != null">nation = #{nation},</if>
            <if test="marriageStatus != null">marriage_status = #{marriageStatus},</if>
            <if test="livingSituation != null">living_situation = #{livingSituation},</if>
            <if test="homeAddress != null">home_address = #{homeAddress},</if>
            <if test="emergencyContactName != null">emergency_contact_name = #{emergencyContactName},</if>
            <if test="emergencyContactPhone != null">emergency_contact_phone = #{emergencyContactPhone},</if>
            <if test="relation != null">relation = #{relation},</if>
            <if test="economicSources != null">economic_sources = #{economicSources},</if>
            <if test="monthlyIncome != null">monthly_income = #{monthlyIncome},</if>
            <if test="socialSecurityNo != null">social_security_no = #{socialSecurityNo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="img != null">img = #{img},</if>
            <if test="disability != null">disability = #{disability},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="volunteerFlag != null">volunteer_flag = #{volunteerFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElderlyPeopleInfoById" parameterType="Long">
        delete from t_home_elderly_people_info where id = #{id}
    </delete>

    <delete id="deleteElderlyPeopleInfoByIds" parameterType="String">
        delete from t_home_elderly_people_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <update id="logicalDeleteElderlyPeopleInfoByIds" parameterType="String">
        update t_home_elderly_people_info set del_flag= '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
