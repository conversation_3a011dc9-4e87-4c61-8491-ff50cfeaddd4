<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.CleaningPlanMapper">

    <resultMap type="CleaningPlan" id="CleaningPlanResult">
        <result property="taskId" column="task_id"/>
        <result property="planName" column="plan_name"/>
        <result property="validityStart" column="validity_start"/>
        <result property="validityEnd" column="validity_end"/>
        <result property="regionId" column="region_id"/>
        <result property="regionName" column="region_name"/>
        <result property="patrolFrequency" column="patrol_frequency"/>
        <result property="dailyPatrolTimes" column="daily_patrol_times"/>
        <result property="taskPerformer" column="task_performer"/>
        <result property="taskPerformerName" column="task_performer_name"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="patrolTimes" column="patrol_times"/>
        <result property="excuteTime" column="excute_time"/>
    </resultMap>

    <sql id="selectCleaningPlanVo">
        select task_id,
        plan_name,
        validity_start,
        validity_end,
        region_id,
        region_name,
        patrol_frequency,
        daily_patrol_times,
        task_performer,
        task_performer_name,
        create_by_id,
        create_by,
        create_time,
        status,
        patrol_times,
        excute_time
        from t_cleaning_plan
    </sql>

    <select id="selectCleaningPlanList" parameterType="CleaningPlan" resultMap="CleaningPlanResult">
        <include refid="selectCleaningPlanVo"/>
        <where>
            <if test="planName != null  and planName != ''">and plan_name like concat('%', #{planName}, '%')</if>
            <if test="validityStart != null ">and validity_start <![CDATA[<= #{validityStart}]]></if>
            <if test="validityEnd != null ">and validity_end >= #{validityEnd}</if>
            <if test="createTimeStart != null ">and create_time >= #{createTimeStart}</if>
            <if test="createTimeEnd != null ">and create_time <![CDATA[<= #{createTimeEnd}]]></if>
            <if test="regionId != null ">and region_id = #{regionId}</if>
            <if test="regionName != null  and regionName != ''">and region_name like concat('%', #{regionName}, '%')
            </if>
            <if test="patrolFrequency != null ">and patrol_frequency = #{patrolFrequency}</if>
            <if test="dailyPatrolTimes != null ">and daily_patrol_times = #{dailyPatrolTimes}</if>
            <if test="taskPerformer != null ">and task_performer = #{taskPerformer}</if>
            <if test="taskPerformerName != null  and taskPerformerName != ''">and task_performer_name like concat('%',
                #{taskPerformerName}, '%')
            </if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
            <if test="createById != null ">and create_by_id = #{createById}</if>
            <if test="createBy != null  and createBy != ''">and create_by like concat('%', #{createBy}, '%')</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="patrolTimes != null ">and patrol_times = #{patrolTimes}</if>
            <if test="statusList != null and statusList.size() > 0">
                <foreach collection="statusList" item="status" open=" and status in (" close=")" separator=",">
                    #{status}
                </foreach>
            </if>

        </where>
        order by create_time desc
    </select>

    <select id="selectCleaningPlanByTaskId" parameterType="String" resultMap="CleaningPlanResult">
        <include refid="selectCleaningPlanVo"/>
        where task_id = #{taskId}
    </select>

    <insert id="insertCleaningPlan" parameterType="CleaningPlan">
        insert into t_cleaning_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="planName != null and planName != ''">plan_name,</if>
            <if test="validityStart != null">validity_start,</if>
            <if test="validityEnd != null">validity_end,</if>
            <if test="regionId != null">region_id,</if>
            <if test="regionName != null and regionName != ''">region_name,</if>
            <if test="patrolFrequency != null">patrol_frequency,</if>
            <if test="dailyPatrolTimes != null">daily_patrol_times,</if>
            <if test="taskPerformer != null">task_performer,</if>
            <if test="taskPerformerName != null">task_performer_name,</if>
            <if test="createTime!= null">create_time,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="status != null">status,</if>
            <if test="patrolTimes != null">patrol_times,</if>
            <if test="excuteTime != null">excute_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="planName != null and planName != ''">#{planName},</if>
            <if test="validityStart != null">#{validityStart},</if>
            <if test="validityEnd != null">#{validityEnd},</if>
            <if test="regionId != null">#{regionId},</if>
            <if test="regionName != null and regionName != ''">#{regionName},</if>
            <if test="patrolFrequency != null">#{patrolFrequency},</if>
            <if test="dailyPatrolTimes != null">#{dailyPatrolTimes},</if>
            <if test="taskPerformer != null">#{taskPerformer},</if>
            <if test="taskPerformerName != null">#{taskPerformerName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createById != null">#{createById},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="status != null">#{status},</if>
            <if test="patrolTimes != null">#{patrolTimes},</if>
            <if test="excuteTime != null">#{excuteTime},</if>
        </trim>
    </insert>

    <update id="updateCleaningPlan" parameterType="CleaningPlan">
        update t_cleaning_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="planName != null and planName != ''">plan_name = #{planName},</if>
            validity_start = #{validityStart},
            validity_end = #{validityEnd},
            <if test="regionId != null">region_id = #{regionId},</if>
            <if test="regionName != null and regionName != ''">region_name = #{regionName},</if>
            <if test="patrolFrequency != null">patrol_frequency = #{patrolFrequency},</if>
            <if test="dailyPatrolTimes != null">daily_patrol_times = #{dailyPatrolTimes},</if>
            <if test="taskPerformer != null">task_performer = #{taskPerformer},</if>
            <if test="taskPerformerName != null">task_performer_name = #{taskPerformerName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="patrolTimes != null">patrol_times = #{patrolTimes},</if>
            <if test="excuteTime != null">excute_time = #{excuteTime},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteCleaningPlanByTaskId" parameterType="String">
        delete
        from t_cleaning_plan
        where task_id = #{taskId}
    </delete>

    <delete id="deleteCleaningPlanByTaskIds" parameterType="String">
        delete from t_cleaning_plan where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper>
