<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.VehicleApplicationMapper">

    <resultMap type="VehicleApplication" id="VehicleApplicationResult">
        <result property="id" column="id"/>
        <result property="applicationNumber" column="application_number"/>
        <result property="vehicleId" column="vehicle_id"/>
        <result property="departmentId" column="department_id"/>
        <result property="departmentName" column="department_name"/>
        <result property="departureTime" column="departure_time"/>
        <result property="estimatedReturnTime" column="estimated_return_time"/>
        <result property="departureMileage" column="departure_mileage"/>
        <result property="estimatedCost" column="estimated_cost"/>
        <result property="driver" column="driver"/>
        <result property="contact" column="contact"/>
        <result property="status" column="status"/>
        <result property="auditorId" column="auditor_id"/>
        <result property="auditor" column="auditor"/>
        <result property="auditTime" column="audit_time"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="vehiclePurpose" column="vehicle_purpose"/>
        <result property="attachmentUrl" column="attachment_url"/>
        <result property="returnTime" column="return_time"/>
        <result property="returnMileage" column="return_mileage"/>
        <result property="returnPersonId" column="return_person_id"/>
        <result property="returnPerson" column="return_person"/>
        <result property="returnRemarks" column="return_remarks"/>
        <result property="returnAttachment" column="return_attachment"/>


        <result property="licensePlateNumber" column="license_plate_number"/>
    </resultMap>
    <!--        `auditor_id` int(11) DEFAULT NULL COMMENT '审核人id',-->
    <!--        `auditor` varchar(255) DEFAULT NULL,-->
    <!--        `audit_time` datetime DEFAULT NULL,-->
    <sql id="selectVehicleApplicationVo">
        SELECT id,
        application_number,
        vehicle_id,
        department_id,
        department_name,
        departure_time,
        estimated_return_time,
        departure_mileage,
        estimated_cost,
        driver,
        contact,
        status,
        auditor_id,
        auditor,
        audit_time,
        creator_id,
        creator,
        create_time,
        vehicle_purpose,
        attachment_url,
        return_time,
        return_mileage,
        return_person_id,
        return_person,
        return_remarks,
        return_attachment
        FROM t_vehicle_application
    </sql>

    <select id="selectVehicleApplicationList" parameterType="com.ibms.service.realty.web.domain.VehicleApplication"
            resultMap="VehicleApplicationResult">
        SELECT tva.id,
        tva.application_number,
        tva.vehicle_id,
        tva.department_id,
        tva.department_name,
        tva.departure_time,
        tva.estimated_return_time,
        tva.departure_mileage,
        tva.estimated_cost,
        tva.driver,
        tva.contact,
        tva.status,
        tva.auditor_id,
        tva.auditor,
        tva.audit_time,
        tva.creator_id,
        tva.creator,
        tva.create_time,
        tva.vehicle_purpose,
        tva.attachment_url,
        tva.return_time,
        tva.return_mileage,
        tva.return_person_id,
        tva.return_person,
        tva.return_remarks,
        tva.return_attachment,

        tvi.license_plate_number
        FROM t_vehicle_application tva
        LEFT JOIN t_vehicle_information tvi ON tva.vehicle_id = tvi.id
        <where>
            <if test="applicationNumber != null  and applicationNumber != ''">and tva.application_number like
                concat('%',
                #{applicationNumber}, '%')
            </if>
            <if test="licensePlateNumber != null  and licensePlateNumber != ''">and tvi.license_plate_number like
                concat('%',
                #{licensePlateNumber}, '%')
            </if>
            <if test="vehicleId != null ">and tva.vehicle_id = #{vehicleId}</if>
            <if test="departmentId != null ">and tva.department_id = #{departmentId}</if>
            <if test="departmentName != null  and departmentName != ''">and tva.department_name like concat('%',
                #{departmentName}, '%')
            </if>
            <if test="departureTime != null ">and tva.departure_time = #{departureTime}</if>
            <if test="estimatedReturnTime != null ">and tva.estimated_return_time = #{estimatedReturnTime}</if>
            <if test="departureMileage != null ">and tva.departure_mileage = #{departureMileage}</if>
            <if test="estimatedCost != null ">and tva.estimated_cost = #{estimatedCost}</if>
            <if test="driver != null  and driver != ''">and tva.driver = #{driver}</if>
            <if test="contact != null  and contact != ''">and tva.contact = #{contact}</if>
            <if test="status != null  and status != ''">and tva.status = #{status}</if>
            <if test="creatorId != null ">and tva.creator_id = #{creatorId}</if>
            <if test="createTimeStart != null ">and DATE_FORMAT(tva.create_time,'%y-%m-%d') >=
                DATE_FORMAT(#{createTimeStart},'%y-%m-%d')
            </if>
            <if test="createTimeEnd != null ">and DATE_FORMAT(tva.create_time,'%y-%m-%d')
                <![CDATA[<= DATE_FORMAT(#{createTimeEnd},'%y-%m-%d')]]></if>
            <if test="creator != null  and creator != ''">and tva.creator like concat('%', #{creator}, '%')</if>
            <if test="vehiclePurpose != null  and vehiclePurpose != ''">and tva.vehicle_purpose = #{vehiclePurpose}</if>
            <if test="attachmentUrl != null  and attachmentUrl != ''">and tva.attachment_url = #{attachmentUrl}</if>
        </where>
        order by tva.create_time desc
    </select>

    <select id="selectVehicleApplicationById" parameterType="Integer" resultMap="VehicleApplicationResult">
        <include refid="selectVehicleApplicationVo"/>
        where id = #{id}
    </select>

    <insert id="insertVehicleApplication" parameterType="VehicleApplication" useGeneratedKeys="true" keyProperty="id">
        insert into t_vehicle_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applicationNumber != null">application_number,</if>
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="departmentId != null">department_id,</if>
            <if test="departmentName != null">department_name,</if>
            <if test="departureTime != null">departure_time,</if>
            <if test="estimatedReturnTime != null">estimated_return_time,</if>
            <if test="departureMileage != null">departure_mileage,</if>
            <if test="estimatedCost != null">estimated_cost,</if>
            <if test="driver != null">driver,</if>
            <if test="contact != null">contact,</if>
            <if test="status != null">status,</if>
            <if test="auditorId != null">auditor_id,</if>
            <if test="auditor != null">auditor,
            </if>
            <if test="auditTime != null">audit_time,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="creator != null">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="vehiclePurpose != null">vehicle_purpose,</if>
            <if test="attachmentUrl != null">attachment_url,</if>
            <if test="returnTime != null">return_time,</if>
            <if test="returnMileage != null">return_mileage,</if>
            <if test="returnPersonId != null">return_person_id,</if>
            <if test="returnPerson != null">return_person,</if>
            <if test="returnRemarks != null">return_remarks,</if>
            <if test="returnAttachment != null">return_attachment,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applicationNumber != null">#{applicationNumber},</if>
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="departmentId != null">#{departmentId},</if>
            <if test="departmentName != null">#{departmentName},</if>
            <if test="departureTime != null">#{departureTime},</if>
            <if test="estimatedReturnTime != null">#{estimatedReturnTime},</if>
            <if test="departureMileage != null">#{departureMileage},</if>
            <if test="estimatedCost != null">#{estimatedCost},</if>
            <if test="driver != null">#{driver},</if>
            <if test="contact != null">#{contact},</if>
            <if test="status != null">#{status},</if>
            <if test="auditorId != null">#{auditorId},</if>
            <if test="auditor != null">#{auditor},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="vehiclePurpose != null">#{vehiclePurpose},</if>
            <if test="attachmentUrl != null">#{attachmentUrl},</if>
            <if test="returnTime != null">#{returnTime},</if>
            <if test="returnMileage != null">#{returnMileage},</if>
            <if test="returnPersonId != null">#{returnPersonId},</if>
            <if test="returnPerson != null">#{returnPerson},</if>
            <if test="returnRemarks != null">#{returnRemarks},</if>
            <if test="returnAttachment != null">#{returnAttachment},</if>
        </trim>
    </insert>

    <update id="updateVehicleApplication" parameterType="VehicleApplication">
        update t_vehicle_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationNumber != null">application_number = #{applicationNumber},</if>
            <if test="vehicleId != null">vehicle_id = #{vehicleId},</if>
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="departmentName != null">department_name = #{departmentName},</if>
            <if test="departureTime != null">departure_time = #{departureTime},</if>
            <if test="estimatedCost != null">estimated_cost = #{estimatedCost},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditorId != null">auditor_id = #{auditorId},</if>
            <if test="auditor != null">auditor = #{auditor},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="returnTime != null">return_time = #{returnTime},</if>
            <if test="returnMileage != null">return_mileage = #{returnMileage},</if>
            <if test="returnPersonId != null">return_person_id = #{returnPersonId},</if>
            <if test="returnPerson != null">return_person = #{returnPerson},</if>
            <if test="returnRemarks != null">return_remarks = #{returnRemarks},</if>
            <if test="returnAttachment != null">return_attachment = #{returnAttachment},</if>
            attachment_url = #{attachmentUrl},
            estimated_return_time = #{estimatedReturnTime},
            departure_mileage = #{departureMileage},
            driver = #{driver},
            contact = #{contact},
            vehicle_purpose = #{vehiclePurpose},
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVehicleApplicationById" parameterType="Integer">
        DELETE
        FROM t_vehicle_application
        WHERE id = #{id}
    </delete>

    <delete id="deleteVehicleApplicationByIds" parameterType="String">
        delete from t_vehicle_application where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
