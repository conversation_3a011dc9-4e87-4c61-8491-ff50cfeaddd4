<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.VisitorInfoMapper">

    <resultMap type="VisitorInfo" id="VisitorInfoResult">
        <result property="id" column="id"/>
        <result property="visitorName" column="visitor_name"/>
        <result property="licensePlate" column="license_plate"/>
        <result property="companyName" column="company_name"/>
        <result property="contactNumber" column="contact_number"/>
        <result property="visitTime" column="visit_time"/>
        <result property="interviewee" column="interviewee"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>

    </resultMap>

    <sql id="selectVisitorInfoVo">
        select id, visitor_name, license_plate, company_name, contact_number, visit_time, interviewee,create_time,
        create_user_id
        from t_visitor_info
    </sql>

    <select id="selectVisitorInfoList" parameterType="VisitorInfo" resultMap="VisitorInfoResult">
        <include refid="selectVisitorInfoVo"/>
        <where>
            <if test="visitorName != null  and visitorName != ''">and visitor_name like concat('%', #{visitorName},
                '%')
            </if>
            <if test="licensePlate != null  and licensePlate != ''">and license_plate = #{licensePlate}</if>
            <if test="companyName != null  and companyName != ''">and company_name like concat('%', #{companyName},
                '%')
            </if>
            <if test="contactNumber != null  and contactNumber != ''">and contact_number = #{contactNumber}</if>
            <if test="visitTime != null ">and visit_time = #{visitTime}</if>
            <if test="createUserId != null ">and create_user_id = #{createUserId}</if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
            <if test="interviewee != null  and interviewee != ''">and interviewee = #{interviewee}</if>
        </where>
        ORDER BY
        DATE_FORMAT( visit_time, '%y-%m-%d' ) = DATE_FORMAT(now(),'%y-%m-%d'),visit_time desc
    </select>

    <select id="selectVisitorInfoById" parameterType="Integer" resultMap="VisitorInfoResult">
        <include refid="selectVisitorInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertVisitorInfo" parameterType="VisitorInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_visitor_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="visitorName != null and visitorName != ''">visitor_name,</if>
            <if test="licensePlate != null">license_plate,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="contactNumber != null">contact_number,</if>
            <if test="visitTime != null">visit_time,</if>
            <if test="interviewee != null">interviewee,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createUserId != null">create_user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="visitorName != null and visitorName != ''">#{visitorName},</if>
            <if test="licensePlate != null">#{licensePlate},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="contactNumber != null">#{contactNumber},</if>
            <if test="visitTime != null">#{visitTime},</if>
            <if test="interviewee != null">#{interviewee},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createUserId != null">#{createUserId},</if>
        </trim>
    </insert>

    <update id="updateVisitorInfo" parameterType="VisitorInfo">
        update t_visitor_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="visitorName != null and visitorName != ''">visitor_name = #{visitorName},</if>
            <if test="licensePlate != null">license_plate = #{licensePlate},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="contactNumber != null">contact_number = #{contactNumber},</if>
            <if test="visitTime != null">visit_time = #{visitTime},</if>
            <if test="interviewee != null">interviewee = #{interviewee},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createUserId != null">create_user_id = #{createUserId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVisitorInfoById" parameterType="Integer">
        delete
        from t_visitor_info
        where id = #{id}
    </delete>

    <delete id="deleteVisitorInfoByIds" parameterType="String">
        delete from t_visitor_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
