<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.PatrolProblemRecordMapper">

    <resultMap type="PatrolProblemRecord" id="PatrolProblemRecordResult">
        <result property="id" column="id"/>
        <result property="relationTaskId" column="relation_task_id"/>
        <result property="relationTask" column="relation_task"/>
        <result property="status" column="status"/>
        <result property="patrolPointId" column="patrol_point_id"/>
        <result property="patrolPoint" column="patrol_point"/>
        <result property="description" column="description"/>
        <result property="creatTime" column="creat_time"/>
        <result property="solvingTime" column="solving_time"/>
        <result property="submiter" column="submiter"/>
        <result property="submiterName" column="submiter_name"/>
        <result property="principal" column="principal"/>
        <result property="principalName" column="principal_name"/>
        <result property="img" column="img"/>
        <result property="solvingDescription" column="solving_description"/>
        <result property="solvingImg" column="solving_img"/>
    </resultMap>

    <sql id="selectPatrolProblemRecordVo">
        select id,
        relation_task_id,
        relation_task,
        status,
        patrol_point_id,
        patrol_point,
        description,
        creat_time,
        solving_time,
        submiter,
        submiter_name,
        principal,
        principal_name,
        img,
        solving_description,
        solving_img
        from t_patrol_problem_record
    </sql>

    <select id="selectPatrolProblemRecordList" parameterType="PatrolProblemRecord"
            resultMap="PatrolProblemRecordResult">
        <include refid="selectPatrolProblemRecordVo"/>
        <where>
            <if test="relationTaskId != null ">and relation_task_id = #{relationTaskId}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="patrolPointId != null ">and patrol_point_id = #{patrolPointId}</if>
            <if test="patrolPoint != null  and patrolPoint != ''">and patrol_point = #{patrolPoint}</if>
            <if test="description != null  and description != ''">and description like concat('%', #{description},
                '%')
            </if>
            <if test="creatTime != null ">and creat_time = #{creatTime}</if>
            <if test="creatTimeStart != null ">and DATE_FORMAT(creat_time,'%Y%m%d') >=
                DATE_FORMAT(#{creatTimeStart},'%Y%m%d')
            </if>
            <if test="creatTimeEnd != null ">and DATE_FORMAT(creat_time,'%Y%m%d')
                <![CDATA[<= DATE_FORMAT(#{creatTimeEnd},'%Y%m%d')]]></if>
            <if test="solvingTimeStart != null ">and DATE_FORMAT(solving_time,'%Y%m%d') >=
                DATE_FORMAT(#{solvingTimeStart},'%Y%m%d')
            </if>
            <if test="solvingTimeEnd != null ">and DATE_FORMAT(solving_time,'%Y%m%d')
                <![CDATA[<= DATE_FORMAT(#{solvingTimeEnd},'%Y%m%d')]]></if>
            <if test="solvingTime != null ">and solving_time = #{solvingTime}</if>
            <if test="submiter != null ">and submiter = #{submiter}</if>
            <if test="submiterName != null ">and submiter_name = #{submiterName}</if>
            <if test="principal != null ">and principal = #{principal}</if>
            <if test="principalName != null and principalName != ''">and principal_name like concat('%',
                #{principalName},
                '%')
            </if>
            <if test="img != null  and img != ''">and img = #{img}</if>
            <if test="solvingDescription != null ">and solving_description = #{solvingDescription}</if>
            <if test="solvingImg != null  and solvingImg != ''">and solving_img = #{solvingImg}</if>
        </where>
        order by creat_time desc
    </select>

    <select id="selectPatrolProblemRecordById" parameterType="Integer" resultMap="PatrolProblemRecordResult">
        <include refid="selectPatrolProblemRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertPatrolProblemRecord" parameterType="PatrolProblemRecord" useGeneratedKeys="true" keyProperty="id">
        insert into t_patrol_problem_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="relationTaskId != null">relation_task_id,</if>
            <if test="relationTask != null">relation_task,</if>
            <if test="status != null">status,</if>
            <if test="patrolPointId != null">patrol_point_id,</if>
            <if test="patrolPoint != null and patrolPoint != ''">patrol_point,</if>
            <if test="description != null">description,</if>
            <if test="creatTime != null">creat_time,</if>
            <if test="solvingTime != null">solving_time,</if>
            <if test="submiter != null">submiter,</if>
            <if test="submiterName != null">submiterName,</if>
            <if test="principal != null">principal,</if>
            <if test="principalName != null">principal_name,</if>
            <if test="img != null">img,</if>
            <if test="solvingDescription != null">solving_description,</if>
            <if test="solvingImg != null">solving_img,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="relationTaskId != null">#{relationTaskId},</if>
            <if test="relationTask != null">#{relationTask},</if>
            <if test="status != null">#{status},</if>
            <if test="patrolPointId != null">#{patrolPointId},</if>
            <if test="patrolPoint != null and patrolPoint != ''">#{patrolPoint},</if>
            <if test="description != null">#{description},</if>
            <if test="creatTime != null">#{creatTime},</if>
            <if test="solvingTime != null">#{solvingTime},</if>
            <if test="submiter != null">#{submiter},</if>
            <if test="submiterName != null">#{submiterName},</if>
            <if test="principal != null">#{principal},</if>
            <if test="principalName != null">#{principalName},</if>
            <if test="img != null">#{img},</if>
            <if test="solvingDescription != null">#{solvingDescription},</if>
            <if test="solvingImg != null">#{solvingImg},</if>
        </trim>
    </insert>

    <update id="updatePatrolProblemRecord" parameterType="PatrolProblemRecord">
        update t_patrol_problem_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="relationTaskId != null">relation_task_id = #{relationTaskId},</if>
            <if test="relationTask != null">relation_task = #{relationTask},</if>
            <if test="status != null">status = #{status},</if>
            <if test="patrolPointId != null">patrol_point_id = #{patrolPointId},</if>
            <if test="patrolPoint != null and patrolPoint != ''">patrol_point = #{patrolPoint},</if>
            <if test="description != null">description = #{description},</if>
            <if test="creatTime != null">creat_time = #{creatTime},</if>
            <if test="solvingTime != null">solving_time = #{solvingTime},</if>
            <if test="submiter != null">submiter = #{submiter},</if>
            <if test="submiterName != null">submiter_name = #{submiterName},</if>
            <if test="principal != null">principal = #{principal},</if>
            <if test="principalName != null">principal_name = #{principalName},</if>
            <if test="img != null">img = #{img},</if>
            <if test="solvingDescription != null">solving_description = #{solvingDescription},</if>
            <if test="solvingImg != null">solving_img = #{solvingImg},</if>

        </trim>
        where id = #{id}
    </update>

    <delete id="deletePatrolProblemRecordById" parameterType="Integer">
        delete
        from t_patrol_problem_record
        where id = #{id}
    </delete>

    <delete id="deletePatrolProblemRecordByIds" parameterType="String">
        delete from t_patrol_problem_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
