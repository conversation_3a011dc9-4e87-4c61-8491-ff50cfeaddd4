<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.PropertyResourceInfoMapper">

    <resultMap type="PropertyResourceInfo" id="PropertyResourceInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="parentId" column="parent_id"/>
        <result property="area" column="area"/>
        <result property="createTime" column="create_time"/>
        <result property="sort" column="sort"/>
        <result property="owner" column="owner"/>
        <result property="ownerPhone" column="owner_phone"/>
        <result property="lessee" column="lessee"/>
        <result property="lesseePhone" column="lessee_phone"/>
        <result property="lesseeInDate" column="lessee_in_date"/>
        <result property="decorationDate" column="decoration_date"/>
        <result property="roomStatus" column="room_status"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectPropertyResourceInfoVo">
        select id,
        name,
        type,
        parent_id,
        area,
        create_time,
        sort,
        owner,
        owner_phone,
        lessee,
        lessee_phone,
        lessee_in_date,
        decoration_date,
        room_status,
        status,
        remark
        from t_property_resource_info
    </sql>

    <select id="selectPropertyResourceInfoList" parameterType="PropertyResourceInfo"
            resultMap="PropertyResourceInfoResult">
        <include refid="selectPropertyResourceInfoVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="type != null ">and type = #{type}</if>
            <if test="parentId != null ">and parent_id = #{parentId}</if>
            <if test="area != null  and area != ''">and area = #{area}</if>
            <if test="sort != null ">and sort = #{sort}</if>
            <if test="owner != null  and owner != ''">and owner = #{owner}</if>
            <if test="ownerPhone != null  and ownerPhone != ''">and owner_phone = #{ownerPhone}</if>
            <if test="lessee != null  and lessee != ''">and lessee = #{lessee}</if>
            <if test="lesseePhone != null  and lesseePhone != ''">and lessee_phone = #{lesseePhone}</if>
            <if test="lesseeInDate != null ">and lessee_in_date = #{lesseeInDate}</if>
            <if test="decorationDate != null ">and decoration_date = #{decorationDate}</if>
            <if test="roomStatus != null ">and room_status = #{roomStatus}</if>
            <if test="status != null ">and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectPropertyResourceInfoById" parameterType="Integer" resultMap="PropertyResourceInfoResult">
        <include refid="selectPropertyResourceInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectRoomList" resultType="com.ibms.service.realty.web.domain.PropertyResourceInfo">
        select t1.id,
        t1.name,
        t1.type,
        t1.parent_id,
        t1.area,
        t1.create_time,
        t1.sort,
        t1.owner,
        t1.owner_phone,
        t1.lessee,
        t1.lessee_phone,
        t1.lessee_in_date,
        t1.decoration_date,
        t1.room_status,
        t1.status,
        t1.remark,
        t2.name AS floorName,
        t3.name AS buildingName
        from t_property_resource_info t1
        LEFT JOIN
        t_property_resource_info AS t2 ON t1.parent_id = t2.id
        LEFT JOIN
        t_property_resource_info AS t3 ON t2.parent_id = t3.id
        where t1.type = 2
        <if test="id != null">
            and t1.id = #{id}
        </if>
        <if test="floorId != null">
            and t2.id = #{floorId}
        </if>
        <if test="buildingId != null">
            and t3.id = #{buildingId}
        </if>
        <if test="name != null and name != ''">
            and t1.name like concat('%', #{name}, '%')
        </if>
    </select>

    <select id="selectRoomInfo" resultType="com.ibms.service.realty.web.domain.PropertyResourceInfo">
        select t1.id,
        t1.name,
        t1.type,
        t1.parent_id,
        t1.area,
        t1.create_time,
        t1.sort,
        t1.owner,
        t1.owner_phone,
        t1.lessee,
        t1.lessee_phone,
        t1.lessee_in_date,
        t1.decoration_date,
        t1.room_status,
        t1.status,
        t1.remark,
        t2.name AS floorName,
        t3.name AS buildingName
        from t_property_resource_info t1
        LEFT JOIN
        t_property_resource_info AS t2 ON t1.parent_id = t2.id
        LEFT JOIN
        t_property_resource_info AS t3 ON t2.parent_id = t3.id
        where t1.id = #{id}
    </select>

    <insert id="insertPropertyResourceInfo" parameterType="PropertyResourceInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_property_resource_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="type != null">type,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="area != null">area,</if>
            <if test="createTime != null">create_time,</if>
            <if test="sort != null">sort,</if>
            <if test="owner != null">owner,</if>
            <if test="ownerPhone != null">owner_phone,</if>
            <if test="lessee != null">lessee,</if>
            <if test="lesseePhone != null">lessee_phone,</if>
            <if test="lesseeInDate != null">lessee_in_date,</if>
            <if test="decorationDate != null">decoration_date,</if>
            <if test="roomStatus != null">room_status,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="area != null">#{area},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="sort != null">#{sort},</if>
            <if test="owner != null">#{owner},</if>
            <if test="ownerPhone != null">#{ownerPhone},</if>
            <if test="lessee != null">#{lessee},</if>
            <if test="lesseePhone != null">#{lesseePhone},</if>
            <if test="lesseeInDate != null">#{lesseeInDate},</if>
            <if test="decorationDate != null">#{decorationDate},</if>
            <if test="roomStatus != null">#{roomStatus},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updatePropertyResourceInfo" parameterType="PropertyResourceInfo">
        update t_property_resource_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="area != null">area = #{area},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="owner != null">owner = #{owner},</if>
            <if test="ownerPhone != null">owner_phone = #{ownerPhone},</if>
            <if test="lessee != null">lessee = #{lessee},</if>
            <if test="lesseePhone != null">lessee_phone = #{lesseePhone},</if>
            <if test="lesseeInDate != null">lessee_in_date = #{lesseeInDate},</if>
            <if test="decorationDate != null">decoration_date = #{decorationDate},</if>
            <if test="roomStatus != null">room_status = #{roomStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePropertyResourceInfoById" parameterType="Integer">
        delete
        from t_property_resource_info
        where id = #{id}
    </delete>

    <delete id="deletePropertyResourceInfoByIds" parameterType="String">
        delete from t_property_resource_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
