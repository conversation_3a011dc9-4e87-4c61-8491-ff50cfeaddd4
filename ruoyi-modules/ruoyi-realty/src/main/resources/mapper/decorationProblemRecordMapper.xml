<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ibms.service.realty.web.mapper.DecorationProblemRecordMapper">

    <resultMap type="DecorationProblemRecord" id="DecorationProblemRecordResult">
        <result property="id" column="id"/>
        <result property="relationTaskId" column="relation_task_id"/>
        <result property="status" column="status"/>
        <result property="description" column="description"/>
        <result property="correctiveAction" column="corrective_action"/>
        <result property="correctiveTime" column="corrective_time"/>
        <result property="creatTime" column="creat_time"/>
        <result property="solvingTime" column="solving_time"/>
        <result property="img" column="img"/>
        <result property="submiter" column="submiter"/>
        <result property="submiterName" column="submiter_name"/>
        <result property="solvingDescription" column="solving_description"/>
        <result property="solvingImg" column="solving_img"/>
        <result property="remark" column="remark"/>

        <result property="roomCode" column="room_code"/>
        <result property="roomName" column="room_name"/>
        <result property="buildingName" column="building_name"/>
        <result property="contactName" column="contact_name"/>

    </resultMap>

    <sql id="selectDecorationProblemRecordVo">
        SELECT dpr.id,
        dpr.relation_task_id,
        dpr.status,
        dpr.description,
        dpr.corrective_action,
        dpr.corrective_time,
        dpr.creat_time,
        dpr.solving_time,
        dpr.img,
        dpr.submiter,
        dpr.submiter_name,
        dpr.solving_description,
        dpr.solving_img,
        dpr.remark,
        ds.room_code,
        ds.room_name,
        pri3.name AS building_name,
        IF(pri.lessee IS NOT NULL AND pri.lessee != '', CONCAT(pri.lessee, '(租户)'),
        CONCAT(pri.owner, '(业主)')) AS contact_name
        FROM t_decoration_problem_record dpr
        LEFT JOIN t_decoration_schedule ds ON dpr.relation_task_id = ds.task_id
        LEFT JOIN t_property_resource_info pri ON ds.room_code = pri.id
        LEFT JOIN t_property_resource_info pri2 ON pri.parent_id = pri2.id
        LEFT JOIN t_property_resource_info pri3 ON pri2.parent_id = pri3.id


    </sql>

    <select id="selectDecorationProblemRecordList" parameterType="DecorationProblemRecord"
            resultMap="DecorationProblemRecordResult">
        <include refid="selectDecorationProblemRecordVo"/>
        <where>
            <if test="relationTaskId != null  and relationTaskId != ''">and dpr.relation_task_id = #{relationTaskId}
            </if>
            <if test="status != null ">and dpr.status = #{status}</if>
            <!--            <if test="patrolPointId != null ">and patrol_point_id = #{patrolPointId}</if>-->
            <!--            <if test="patrolPoint != null  and patrolPoint != ''">and patrol_point = #{patrolPoint}</if>-->
            <if test="description != null  and description != ''">and dpr.description = #{description}</if>
            <if test="correctiveAction != null  and correctiveAction != ''">and dpr.corrective_action =
                #{correctiveAction}
            </if>
            <if test="correctiveTime != null ">and dpr.corrective_time = #{correctiveTime}</if>
            <if test="creatTime != null ">and dpr.creat_time = #{creatTime}</if>
            <if test="solvingTime != null ">and dpr.solving_time = #{solvingTime}</if>
            <if test="img != null  and img != ''">and dpr.img = #{img}</if>
            <if test="submiter != null ">and dpr.submiter = #{submiter}</if>
            <if test="submiterName != null ">and dpr.submiter_name = #{submiterName}</if>
            <if test="solvingDescription != null ">and dpr.solving_description = #{solvingDescription}</if>
            <if test="solvingImg != null  and solvingImg != ''">and dpr.solving_img = #{solvingImg}</if>
        </where>
        order by dpr.creat_time desc
    </select>

    <select id="selectDecorationProblemRecordById" parameterType="Integer" resultMap="DecorationProblemRecordResult">
        <include refid="selectDecorationProblemRecordVo"/>
        where dpr.id = #{id}
    </select>

    <insert id="insertDecorationProblemRecord" parameterType="DecorationProblemRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_decoration_problem_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="relationTaskId != null and relationTaskId != ''">relation_task_id,</if>
            <if test="status != null">status,</if>
            <!--            <if test="patrolPointId != null">patrol_point_id,</if>-->
            <!--            <if test="patrolPoint != null and patrolPoint != ''">patrol_point,</if>-->
            <if test="description != null">description,</if>
            <if test="correctiveAction != null">corrective_action,</if>
            <if test="correctiveTime != null">corrective_time,</if>
            <if test="creatTime != null">creat_time,</if>
            <if test="solvingTime != null">solving_time,</if>
            <if test="img != null">img,</if>
            <if test="submiter != null">submiter,</if>
            <if test="submiterName != null">submiter_name,</if>
            <if test="solvingDescription != null">solving_description,</if>
            <if test="solvingImg != null">solving_img,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="relationTaskId != null and relationTaskId != ''">#{relationTaskId},</if>
            <if test="status != null">#{status},</if>
            <!--            <if test="patrolPointId != null">#{patrolPointId},</if>-->
            <!--            <if test="patrolPoint != null and patrolPoint != ''">#{patrolPoint},</if>-->
            <if test="description != null">#{description},</if>
            <if test="correctiveAction != null">#{correctiveAction},</if>
            <if test="correctiveTime != null">#{correctiveTime},</if>
            <if test="creatTime != null">#{creatTime},</if>
            <if test="solvingTime != null">#{solvingTime},</if>
            <if test="img != null">#{img},</if>
            <if test="submiter != null">#{submiter},</if>
            <if test="submiterName != null">#{submiterName},</if>
            <if test="solvingDescription != null">#{solvingDescription},</if>
            <if test="solvingImg != null">#{solvingImg},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDecorationProblemRecord" parameterType="DecorationProblemRecord">
        update t_decoration_problem_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="relationTaskId != null and relationTaskId != ''">relation_task_id = #{relationTaskId},</if>
            <if test="status != null">status = #{status},</if>
            <!--            <if test="patrolPointId != null">patrol_point_id = #{patrolPointId},</if>-->
            <!--            <if test="patrolPoint != null and patrolPoint != ''">patrol_point = #{patrolPoint},</if>-->
            <if test="description != null">description = #{description},</if>
            <if test="correctiveAction != null">corrective_action = #{correctiveAction},</if>
            <if test="correctiveTime != null">corrective_time = #{correctiveTime},</if>
            <if test="creatTime != null">creat_time = #{creatTime},</if>
            <if test="solvingTime != null">solving_time = #{solvingTime},</if>
            <if test="img != null">img = #{img},</if>
            <if test="submiter != null">submiter = #{submiter},</if>
            <if test="submiterName != null">submiter_name = #{submiterName},</if>
            <if test="solvingDescription != null">solving_description = #{solvingDescription},</if>
            <if test="solvingImg != null">solving_img = #{solvingImg},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDecorationProblemRecordById" parameterType="Integer">
        delete
        from t_decoration_problem_record
        where id = #{id}
    </delete>

    <delete id="deleteDecorationProblemRecordByIds" parameterType="String">
        delete from t_decoration_problem_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
