package com.ibms.service.realty.web.service;


import com.github.yulichang.base.MPJBaseService;
import com.ibms.service.realty.web.domain.CalendarManagement;

import java.util.List;

/**
 * 日历管理Service接口
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
public interface CalendarManagementService extends MPJBaseService<CalendarManagement> {
    /**
     * 查询日历管理
     *
     * @param id 日历管理主键
     * @return 日历管理
     */
    public CalendarManagement selectCalendarManagementById(Integer id);

    /**
     * 查询日历管理列表
     *
     * @param calendarManagement 日历管理
     * @return 日历管理集合
     */
    public List<CalendarManagement> selectCalendarManagementList(CalendarManagement calendarManagement);

    /**
     * 新增日历管理
     *
     * @param calendarManagement 日历管理
     * @return 结果
     */
    public int insertCalendarManagement(CalendarManagement calendarManagement);

    /**
     * 修改日历管理
     *
     * @param calendarManagement 日历管理
     * @return 结果
     */
    public int updateCalendarManagement(CalendarManagement calendarManagement);

    /**
     * 批量删除日历管理
     *
     * @param ids 需要删除的日历管理主键集合
     * @return 结果
     */
    public int deleteCalendarManagementByIds(Integer[] ids);

    /**
     * 删除日历管理信息
     *
     * @param id 日历管理主键
     * @return 结果
     */
    public int deleteCalendarManagementById(Integer id);
}
