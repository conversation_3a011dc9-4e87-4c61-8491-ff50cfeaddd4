package com.ibms.service.realty.web.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.realty.web.domain.MaintenanceApplication;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 维保申请Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@Mapper
public interface MaintenanceApplicationMapper extends MPJBaseMapper<MaintenanceApplication> {
    /**
     * 查询维保申请
     *
     * @param id 维保申请主键
     * @return 维保申请
     */
    public MaintenanceApplication selectMaintenanceApplicationById(Integer id);

    /**
     * 查询维保申请列表
     *
     * @param maintenanceApplication 维保申请
     * @return 维保申请集合
     */
    public List<MaintenanceApplication> selectMaintenanceApplicationList(MaintenanceApplication maintenanceApplication);

    /**
     * 新增维保申请
     *
     * @param maintenanceApplication 维保申请
     * @return 结果
     */
    public int insertMaintenanceApplication(MaintenanceApplication maintenanceApplication);

    /**
     * 修改维保申请
     *
     * @param maintenanceApplication 维保申请
     * @return 结果
     */
    public int updateMaintenanceApplication(MaintenanceApplication maintenanceApplication);

    /**
     * 删除维保申请
     *
     * @param id 维保申请主键
     * @return 结果
     */
    public int deleteMaintenanceApplicationById(Integer id);

    /**
     * 批量删除维保申请
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMaintenanceApplicationByIds(Integer[] ids);
}
