package com.ibms.service.realty.web.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 物业资源信息对象 t_property_resource_info
 *
 * <AUTHOR>
 * @date 2023-06-15
 */
@Data
@TableName("t_property_resource_info")
@ApiModel(value = "物业资源信息对象")
public class PropertyResourceInfo {
    private static final long serialVersionUID = -4540180291031046669L;

    /**
     * 主键id自增
     */
    @TableId(value = "id", type = com.baomidou.mybatisplus.annotation.IdType.AUTO)
    @ApiModelProperty(value = "主键id自增")
    private Integer id;

    /**
     * 名称
     */
    @Excel(name = "名称")
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 类型，0：楼，1：层，2：房间
     */
    @Excel(name = "类型，0：楼，1：层，2：房间")
    @ApiModelProperty(value = "类型，0：楼，1：层，2：房间 ")
    private Integer type;

    /**
     * 父级id
     */
    @Excel(name = "父级id")
    @ApiModelProperty(value = "父级id")
    private Integer parentId;

    /**
     * 面积，单位：平方米
     */
    @Excel(name = "面积，单位：平方米")
    @ApiModelProperty(value = "面积，单位：平方米")
    private String area;

    /**
     * 排序
     */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 子节点
     */
    @ApiModelProperty(value = "子节点")
    private List<PropertyResourceInfo> childrenNode;

    @ApiModelProperty(value = "type集合")
    @TableField(exist = false)
    private List<Integer> typeList;

    @ApiModelProperty(value = "业主")
    private String owner;

    @ApiModelProperty(value = "业主联系电话")
    private String ownerPhone;

    @ApiModelProperty(value = "租户")
    private String lessee;

    @ApiModelProperty(value = "租户联系方式")
    private String lesseePhone;

    @ApiModelProperty(value = "租户入住时间")
    private String lesseeInDate;

    @ApiModelProperty(value = "装修日期")
    private String decorationDate;

    @ApiModelProperty(value = "房间状态,0：已租，1：未租")
    private String roomStatus;

    @ApiModelProperty(value = "是否禁用，0：否，1：是")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "楼层名称")
    @TableField(exist = false)
    private String floorName;

    @ApiModelProperty(value = "楼栋名称")
    @TableField(exist = false)
    private String buildingName;

    @ApiModelProperty(value = "楼栋id")
    @TableField(exist = false)
    private Integer buildingId;

    @ApiModelProperty(value = "楼层id")
    @TableField(exist = false)
    private Integer floorId;

}

