package com.ibms.service.realty.web.controller.complaintInformation;

import com.ibms.service.realty.web.domain.ComplaintInformation;
import com.ibms.service.realty.web.service.ComplaintInformationService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 投诉信息Controller
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@RestController
@RequestMapping("/complaint")
@Api(tags = "投诉信息")
public class ComplaintInformationController extends BaseController {
    @Autowired
    private ComplaintInformationService complaintInformationService;

    /**
     * 查询投诉信息列表
     */
    @GetMapping("/list")
    @ApiOperation("查询投诉信息列表")
    public TableDataInfo list(ComplaintInformation complaintInformation) {
        startPage();
        List<ComplaintInformation> list = complaintInformationService.selectComplaintInformationList(complaintInformation);
        return getDataTable(list);
    }

    /**
     * 导出投诉信息列表
     */
    @Log(title = "投诉信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出投诉信息列表")
    public void export(HttpServletResponse response, ComplaintInformation complaintInformation) {
        List<ComplaintInformation> list = complaintInformationService.selectComplaintInformationList(complaintInformation);
        ExcelUtil<ComplaintInformation> util = new ExcelUtil<ComplaintInformation>(ComplaintInformation.class);
        util.exportExcel(response, list, "投诉信息数据");
    }

    /**
     * 获取投诉信息详细信息
     */
    @GetMapping(value = "/{complaintId}")
    @ApiOperation("获取投诉信息详细信息")
    public AjaxResult getInfo(@PathVariable("complaintId") String complaintId) {
        return AjaxResult.success(complaintInformationService.selectComplaintInformationByComplaintId(complaintId));
    }

    /**
     * 新增投诉信息
     */
    @Log(title = "投诉信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增投诉信息")
    public AjaxResult add(@RequestBody ComplaintInformation complaintInformation) {
        return toAjax(complaintInformationService.insertComplaintInformation(complaintInformation));
    }

    /**
     * 修改投诉信息
     */
    @Log(title = "投诉信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改投诉信息")
    public AjaxResult edit(@RequestBody ComplaintInformation complaintInformation) {
        return toAjax(complaintInformationService.updateComplaintInformation(complaintInformation));
    }

    /**
     * 删除投诉信息
     */
    @Log(title = "投诉信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{complaintIds}")
    @ApiOperation("删除投诉信息")
    public AjaxResult remove(@PathVariable String[] complaintIds) {
        return toAjax(complaintInformationService.deleteComplaintInformationByComplaintIds(complaintIds));
    }

    /**
     * 回复
     */
    @Log(title = "回复", businessType = BusinessType.UPDATE)
    @PutMapping("/reply")
    @ApiOperation("回复")
    public AjaxResult reply(@RequestBody ComplaintInformation complaintInformation) {
        return toAjax(complaintInformationService.reply(complaintInformation));
    }
}
