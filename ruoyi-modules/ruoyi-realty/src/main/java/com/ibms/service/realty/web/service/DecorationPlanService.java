package com.ibms.service.realty.web.service;


import com.github.yulichang.base.MPJBaseService;
import com.ibms.service.realty.web.domain.DecorationPlan;

import java.util.List;

/**
 * 装修巡检计划Service接口
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
public interface DecorationPlanService extends MPJBaseService<DecorationPlan> {
    /**
     * 查询装修巡检计划
     *
     * @param taskId 装修巡检计划主键
     * @return 装修巡检计划
     */
    public DecorationPlan selectDecorationPlanByTaskId(String taskId);

    /**
     * 查询装修巡检计划列表
     *
     * @param decorationPlan 装修巡检计划
     * @return 装修巡检计划集合
     */
    public List<DecorationPlan> selectDecorationPlanList(DecorationPlan decorationPlan);

    /**
     * 新增装修巡检计划
     *
     * @param decorationPlan 装修巡检计划
     * @return 结果
     */
    public int insertDecorationPlan(DecorationPlan decorationPlan);

    /**
     * 修改装修巡检计划
     *
     * @param decorationPlan 装修巡检计划
     * @return 结果
     */
    public int updateDecorationPlan(DecorationPlan decorationPlan);

    /**
     * 批量删除装修巡检计划
     *
     * @param taskIds 需要删除的装修巡检计划主键集合
     * @return 结果
     */
    public int deleteDecorationPlanByTaskIds(String[] taskIds);

    /**
     * 删除装修巡检计划信息
     *
     * @param taskId 装修巡检计划主键
     * @return 结果
     */
    public int deleteDecorationPlanByTaskId(String taskId);

    int openOrOff(DecorationPlan decorationPlan);
}

