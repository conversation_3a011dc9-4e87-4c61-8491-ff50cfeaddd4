package com.ibms.service.realty.web.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.realty.web.domain.VehicleApplication;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用车申请Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@Mapper
public interface VehicleApplicationMapper extends MPJBaseMapper<VehicleApplication> {
    /**
     * 查询用车申请
     *
     * @param id 用车申请主键
     * @return 用车申请
     */
    public VehicleApplication selectVehicleApplicationById(Integer id);

    /**
     * 查询用车申请列表
     *
     * @param vehicleApplication 用车申请
     * @return 用车申请集合
     */
    public List<VehicleApplication> selectVehicleApplicationList(VehicleApplication vehicleApplication);

    /**
     * 新增用车申请
     *
     * @param vehicleApplication 用车申请
     * @return 结果
     */
    public int insertVehicleApplication(VehicleApplication vehicleApplication);

    /**
     * 修改用车申请
     *
     * @param vehicleApplication 用车申请
     * @return 结果
     */
    public int updateVehicleApplication(VehicleApplication vehicleApplication);

    /**
     * 删除用车申请
     *
     * @param id 用车申请主键
     * @return 结果
     */
    public int deleteVehicleApplicationById(Integer id);

    /**
     * 批量删除用车申请
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVehicleApplicationByIds(Integer[] ids);

}

