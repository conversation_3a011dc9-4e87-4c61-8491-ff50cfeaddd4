package com.ibms.service.realty.web.service.impl;


import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ibms.service.realty.web.domain.CleaningTaskOperationRecord;
import com.ibms.service.realty.web.mapper.CleaningTaskOperationRecordMapper;
import com.ibms.service.realty.web.service.CleaningTaskOperationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 保洁巡查任务操作记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@Service
public class CleaningTaskOperationRecordServiceImpl extends MPJBaseServiceImpl<CleaningTaskOperationRecordMapper, CleaningTaskOperationRecord> implements CleaningTaskOperationRecordService {
    @Autowired
    private CleaningTaskOperationRecordMapper cleaningTaskOperationRecordMapper;

    /**
     * 查询保洁巡查任务操作记录
     *
     * @param id 保洁巡查任务操作记录主键
     * @return 保洁巡查任务操作记录
     */
    @Override
    public CleaningTaskOperationRecord selectCleaningTaskOperationRecordById(Integer id) {
        return cleaningTaskOperationRecordMapper.selectCleaningTaskOperationRecordById(id);
    }

    /**
     * 查询保洁巡查任务操作记录列表
     *
     * @param cleaningTaskOperationRecord 保洁巡查任务操作记录
     * @return 保洁巡查任务操作记录
     */
    @Override
    public List<CleaningTaskOperationRecord> selectCleaningTaskOperationRecordList(CleaningTaskOperationRecord cleaningTaskOperationRecord) {
        return cleaningTaskOperationRecordMapper.selectCleaningTaskOperationRecordList(cleaningTaskOperationRecord);
    }

    /**
     * 新增保洁巡查任务操作记录
     *
     * @param cleaningTaskOperationRecord 保洁巡查任务操作记录
     * @return 结果
     */
    @Override
    public int insertCleaningTaskOperationRecord(CleaningTaskOperationRecord cleaningTaskOperationRecord) {
        return cleaningTaskOperationRecordMapper.insertCleaningTaskOperationRecord(cleaningTaskOperationRecord);
    }

    /**
     * 修改保洁巡查任务操作记录
     *
     * @param cleaningTaskOperationRecord 保洁巡查任务操作记录
     * @return 结果
     */
    @Override
    public int updateCleaningTaskOperationRecord(CleaningTaskOperationRecord cleaningTaskOperationRecord) {
        return cleaningTaskOperationRecordMapper.updateCleaningTaskOperationRecord(cleaningTaskOperationRecord);
    }

    /**
     * 批量删除保洁巡查任务操作记录
     *
     * @param ids 需要删除的保洁巡查任务操作记录主键
     * @return 结果
     */
    @Override
    public int deleteCleaningTaskOperationRecordByIds(Integer[] ids) {
        return cleaningTaskOperationRecordMapper.deleteCleaningTaskOperationRecordByIds(ids);
    }

    /**
     * 删除保洁巡查任务操作记录信息
     *
     * @param id 保洁巡查任务操作记录主键
     * @return 结果
     */
    @Override
    public int deleteCleaningTaskOperationRecordById(Integer id) {
        return cleaningTaskOperationRecordMapper.deleteCleaningTaskOperationRecordById(id);
    }
}

