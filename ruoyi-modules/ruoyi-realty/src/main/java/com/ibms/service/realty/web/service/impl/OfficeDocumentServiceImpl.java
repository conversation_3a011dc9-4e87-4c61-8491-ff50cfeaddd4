package com.ibms.service.realty.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ibms.service.realty.web.domain.OfficeDocument;
import com.ibms.service.realty.web.mapper.OfficeDocumentMapper;
import com.ibms.service.realty.web.service.OfficeDocumentService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 办公文档资料管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@Service
public class OfficeDocumentServiceImpl extends MPJBaseServiceImpl<OfficeDocumentMapper, OfficeDocument> implements OfficeDocumentService {
    @Autowired
    private OfficeDocumentMapper officeDocumentMapper;

    /**
     * 查询办公文档资料管理
     *
     * @param id 办公文档资料管理主键
     * @return 办公文档资料管理
     */
    @Override
    public OfficeDocument selectOfficeDocumentById(Integer id) {
        return officeDocumentMapper.selectOfficeDocumentById(id);
    }

    /**
     * 查询办公文档资料管理列表
     *
     * @param officeDocument 办公文档资料管理
     * @return 办公文档资料管理
     */
    @Override
    public List<OfficeDocument> selectOfficeDocumentList(OfficeDocument officeDocument) {
        return officeDocumentMapper.selectOfficeDocumentList(officeDocument);
    }

    /**
     * 新增办公文档资料管理
     *
     * @param officeDocument 办公文档资料管理
     * @return 结果
     */
    @Override
    public int insertOfficeDocument(OfficeDocument officeDocument) {
        officeDocument.setCreatorId(SecurityUtils.getUserId());
        officeDocument.setCreator(SecurityUtils.getUsername());
        return officeDocumentMapper.insertOfficeDocument(officeDocument);
    }

    /**
     * 修改办公文档资料管理
     *
     * @param officeDocument 办公文档资料管理
     * @return 结果
     */
    @Override
    public int updateOfficeDocument(OfficeDocument officeDocument) {
        return officeDocumentMapper.updateOfficeDocument(officeDocument);
    }

    /**
     * 批量删除办公文档资料管理
     *
     * @param ids 需要删除的办公文档资料管理主键
     * @return 结果
     */
    @Override
    public int deleteOfficeDocumentByIds(Integer[] ids) {
        return officeDocumentMapper.deleteOfficeDocumentByIds(ids);
    }

    /**
     * 删除办公文档资料管理信息
     *
     * @param id 办公文档资料管理主键
     * @return 结果
     */
    @Override
    public int deleteOfficeDocumentById(Integer id) {
        return officeDocumentMapper.deleteOfficeDocumentById(id);
    }

    @Override
    public int audit(OfficeDocument officeDocument) {
        String status = officeDocument.getStatus();
        if ("0".equals(status)) { // status = 0，撤销审核/未审核
            officeDocument.setReviewerId(null);
            officeDocument.setReviewer(null);
            officeDocument.setReviewerTime(null);
        } else if ("1".equals(status)) { // status = 1，审核通过
            officeDocument.setReviewerId(SecurityUtils.getUserId());
            officeDocument.setReviewer(SecurityUtils.getUsername());
            officeDocument.setReviewerTime(new Date());
        }

        // 更新审核状态
        UpdateWrapper<OfficeDocument> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .set(OfficeDocument::getStatus, status)
                .set(OfficeDocument::getReviewerId, officeDocument.getReviewerId())
                .set(OfficeDocument::getReviewer, officeDocument.getReviewer())
                .set(OfficeDocument::getReviewerTime, officeDocument.getReviewerTime())
                .eq(OfficeDocument::getId, officeDocument.getId());

        return officeDocumentMapper.update(null, updateWrapper);
    }

    @Override
    public int addFile(OfficeDocument officeDocument) {
        UpdateWrapper<OfficeDocument> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .set(OfficeDocument::getAccessoryUrl, officeDocument.getAccessoryUrl())
                .eq(OfficeDocument::getId, officeDocument.getId());

        return officeDocumentMapper.update(null, updateWrapper);
    }

    @Override
    public int deleteFile(OfficeDocument officeDocument) {
        UpdateWrapper<OfficeDocument> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .set(OfficeDocument::getAccessoryUrl, null)
                .eq(OfficeDocument::getId, officeDocument.getId());

        return officeDocumentMapper.update(null, updateWrapper);
    }
}
