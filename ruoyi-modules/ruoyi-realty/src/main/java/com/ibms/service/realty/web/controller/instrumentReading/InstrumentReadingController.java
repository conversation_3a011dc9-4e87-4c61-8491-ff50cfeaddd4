package com.ibms.service.realty.web.controller.instrumentReading;


import com.ibms.service.realty.web.domain.InstrumentReading;
import com.ibms.service.realty.web.service.InstrumentReadingService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 仪器读数Controller
 *
 * <AUTHOR>
 * @date 2023-04-14
 */
@RestController
@RequestMapping("/reading")
@Api(tags = "仪器读数")
public class InstrumentReadingController extends BaseController {
    @Autowired
    private InstrumentReadingService instrumentReadingService;

    /**
     * 查询仪器读数列表
     */
    @GetMapping("/list")
    @ApiOperation("查询仪器读数列表")
    public TableDataInfo list(InstrumentReading instrumentReading) {
        startPage();
        List<InstrumentReading> list = instrumentReadingService.selectInstrumentReadingList(instrumentReading);
        return getDataTable(list);
    }

    /**
     * 导出仪器读数列表
     */
    @Log(title = "仪器读数", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出仪器读数列表")
    public void export(HttpServletResponse response, InstrumentReading instrumentReading) {
        List<InstrumentReading> list = instrumentReadingService.selectInstrumentReadingList(instrumentReading);
        ExcelUtil<InstrumentReading> util = new ExcelUtil<InstrumentReading>(InstrumentReading.class);
        util.exportExcel(response, list, "仪器读数数据");
    }

    /**
     * 获取仪器读数详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取仪器读数详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(instrumentReadingService.selectInstrumentReadingById(id));
    }

    /**
     * 新增仪器读数
     */
    @Log(title = "仪器读数", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增仪器读数")
    public AjaxResult add(@RequestBody InstrumentReading instrumentReading) {
        return toAjax(instrumentReadingService.insertInstrumentReading(instrumentReading));
    }

    /**
     * 修改仪器读数
     */
    @Log(title = "仪器读数", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改仪器读数")
    public AjaxResult edit(@RequestBody InstrumentReading instrumentReading) {
        return toAjax(instrumentReadingService.updateInstrumentReading(instrumentReading));
    }

    /**
     * 删除仪器读数
     */
    @Log(title = "仪器读数", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除仪器读数")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(instrumentReadingService.deleteInstrumentReadingByIds(ids));
    }
}

