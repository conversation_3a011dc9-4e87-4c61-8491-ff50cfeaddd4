package com.ibms.service.realty.web.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.realty.web.controller.venueInfo.param.UserVenueResevationVo;
import com.ibms.service.realty.web.controller.venueInfo.param.VenueReservationParam;
import com.ibms.service.realty.web.controller.venueInfo.param.VenueResevationVo;
import com.ibms.service.realty.web.domain.VenueInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 场馆信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@Mapper
public interface VenueInfoMapper extends MPJBaseMapper<VenueInfo> {
    /**
     * 查询场馆信息
     *
     * @param id 场馆信息主键
     * @return 场馆信息
     */
    public VenueInfo selectVenueInfoById(Integer id);

    /**
     * 查询场馆信息列表
     *
     * @param venueInfo 场馆信息
     * @return 场馆信息集合
     */
    public List<VenueInfo> selectVenueInfoList(VenueInfo venueInfo);

    /**
     * 新增场馆信息
     *
     * @param venueInfo 场馆信息
     * @return 结果
     */
    public int insertVenueInfo(VenueInfo venueInfo);

    /**
     * 修改场馆信息
     *
     * @param venueInfo 场馆信息
     * @return 结果
     */
    public int updateVenueInfo(VenueInfo venueInfo);

    /**
     * 删除场馆信息
     *
     * @param id 场馆信息主键
     * @return 结果
     */
    public int deleteVenueInfoById(Integer id);

    /**
     * 批量删除场馆信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVenueInfoByIds(Integer[] ids);

    List<VenueResevationVo> selectReservation(VenueReservationParam venueReservationParam);

    List<VenueResevationVo> selectFinReservationNum(VenueReservationParam venueReservationParam);

    void insertUserVenueReservation(VenueReservationParam venueReservationParam);

    void insertVenueReservation(VenueReservationParam venueReservationParam);

    List<UserVenueResevationVo> selectUserReservationList(VenueReservationParam venueReservationParam);

    List<UserVenueResevationVo> selectReservationList(VenueReservationParam venueReservationParam);
}
