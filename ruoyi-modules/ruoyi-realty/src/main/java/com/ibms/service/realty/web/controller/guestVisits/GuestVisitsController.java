package com.ibms.service.realty.web.controller.guestVisits;

import com.ibms.service.realty.web.domain.GuestVisits;
import com.ibms.service.realty.web.service.GuestVisitsService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 来人来访信息Controller
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@RestController
@RequestMapping("/visits")
@Api(tags = "来人来访信息Controller")
public class GuestVisitsController extends BaseController {
    @Autowired
    private GuestVisitsService guestVisitsService;

    /**
     * 查询来人来访信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "来人来访信息列表")
    public TableDataInfo list(GuestVisits guestVisits) {
        startPage();
        List<GuestVisits> list = guestVisitsService.selectGuestVisitsList(guestVisits);
        return getDataTable(list);
    }

    /**
     * 导出来人来访信息列表
     */
    @Log(title = "来人来访信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出来人来访信息列表")
    public void export(HttpServletResponse response, GuestVisits guestVisits) {
        List<GuestVisits> list = guestVisitsService.selectGuestVisitsList(guestVisits);
        ExcelUtil<GuestVisits> util = new ExcelUtil<GuestVisits>(GuestVisits.class);
        util.exportExcel(response, list, "来人来访信息数据");
    }

    /**
     * 获取来人来访信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取来人来访信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(guestVisitsService.selectGuestVisitsById(id));
    }

    /**
     * 新增来人来访信息
     */
    @Log(title = "来人来访信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增来人来访信息")
    public AjaxResult add(@RequestBody GuestVisits guestVisits) {
        return toAjax(guestVisitsService.insertGuestVisits(guestVisits));
    }

    /**
     * 修改来人来访信息
     */
    @Log(title = "来人来访信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改来人来访信息")
    public AjaxResult edit(@RequestBody GuestVisits guestVisits) {
        return toAjax(guestVisitsService.updateGuestVisits(guestVisits));
    }

    /**
     * 删除来人来访信息
     */
    @Log(title = "来人来访信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除来人来访信息")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(guestVisitsService.deleteGuestVisitsByIds(ids));
    }
}

