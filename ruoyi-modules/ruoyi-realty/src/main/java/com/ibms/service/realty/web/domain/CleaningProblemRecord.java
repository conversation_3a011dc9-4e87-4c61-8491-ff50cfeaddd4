package com.ibms.service.realty.web.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 保洁巡查问题记录对象 t_cleaning_problem_record
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@Data
@TableName("t_cleaning_problem_record")
@ApiModel(value = "CleaningProblemRecord", description = "保洁巡查问题记录对象")
public class CleaningProblemRecord {
    private static final long serialVersionUID = 3867898328657329035L;

    /**
     * 主键自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键自增")
    private Integer id;

    /**
     * 关联任务，关联t_cleaning_schedule表
     */
    @Excel(name = "关联任务，关联t_cleaning_schedule表")
    @ApiModelProperty(value = "关联任务，关联t_cleaning_schedule表")
    private Integer relationTaskId;

    /**
     * 状态，0:未解决，1：解决
     */
    @Excel(name = "状态，0:未解决，1：解决")
    @ApiModelProperty(value = "状态，0:未解决，1：解决")
    private Integer status;

    /**
     * 问题描述
     */
    @Excel(name = "问题描述")
    @ApiModelProperty(value = "问题描述")
    private String description;

    /**
     * 解决方式
     */
    @Excel(name = "解决方式")
    @ApiModelProperty(value = "解决方式")
    private String solution;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "提交时间")
    private Date creatTime;

    /**
     * 解决时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "解决时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "解决时间")
    private Date solvingTime;

    /**
     * 负责人id，关联系统用户表
     */
    @Excel(name = "负责人id，关联系统用户表")
    @ApiModelProperty(value = "负责人id，关联系统用户表")
    private Long principal;

    /**
     * 图片地址，多个用逗号隔开
     */
    @Excel(name = "图片地址，多个用逗号隔开")
    @ApiModelProperty(value = "图片地址，多个用逗号隔开")
    private String img;

    /**
     * 提交人id，关联系统用户表
     */
    @Excel(name = "提交人id，关联系统用户表")
    @ApiModelProperty("提交人id，关联系统用户表")
    private Long submiter;

    /**
     * 提交人
     */
    @Excel(name = "提交人")
    @ApiModelProperty("提交人")
    private String submiterName;

    /**
     * 解决描述
     */
    @Excel(name = "解决描述")
    @ApiModelProperty("解决描述")
    private String solvingDescription;

    /**
     * 解决图片
     */
    @Excel(name = "解决图片")
    @ApiModelProperty("解决图片")
    private String solvingImg;

}

