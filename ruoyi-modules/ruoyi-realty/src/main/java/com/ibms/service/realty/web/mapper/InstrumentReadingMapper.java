package com.ibms.service.realty.web.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.realty.web.domain.InstrumentReading;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 仪器读数Mapper接口
 *
 * <AUTHOR>
 * @date 2023-04-14
 */
@Mapper
public interface InstrumentReadingMapper extends MPJBaseMapper<InstrumentReading> {
    /**
     * 查询仪器读数
     *
     * @param id 仪器读数主键
     * @return 仪器读数
     */
    public InstrumentReading selectInstrumentReadingById(Integer id);

    /**
     * 查询仪器读数列表
     *
     * @param instrumentReading 仪器读数
     * @return 仪器读数集合
     */
    public List<InstrumentReading> selectInstrumentReadingList(InstrumentReading instrumentReading);

    /**
     * 新增仪器读数
     *
     * @param instrumentReading 仪器读数
     * @return 结果
     */
    public int insertInstrumentReading(InstrumentReading instrumentReading);

    /**
     * 修改仪器读数
     *
     * @param instrumentReading 仪器读数
     * @return 结果
     */
    public int updateInstrumentReading(InstrumentReading instrumentReading);

    /**
     * 删除仪器读数
     *
     * @param id 仪器读数主键
     * @return 结果
     */
    public int deleteInstrumentReadingById(Integer id);

    /**
     * 批量删除仪器读数
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstrumentReadingByIds(Integer[] ids);
}
