package com.ibms.service.realty.web.service.impl;


import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ibms.service.realty.web.domain.PatrolProblemRecord;
import com.ibms.service.realty.web.mapper.PatrolProblemRecordMapper;
import com.ibms.service.realty.web.service.PatrolProblemRecordService;
import com.ibms.service.realty.web.service.PatrolTaskOperationRecordService;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


/**
 * 巡更问题记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@Service
public class PatrolProblemRecordServiceImpl extends MPJBaseServiceImpl<PatrolProblemRecordMapper, PatrolProblemRecord> implements PatrolProblemRecordService {
    @Autowired
    private PatrolProblemRecordMapper patrolProblemRecordMapper;

    @Autowired
    private PatrolTaskOperationRecordService patrolTaskOperationRecordService;

    /**
     * 查询巡更问题记录
     *
     * @param id 巡更问题记录主键
     * @return 巡更问题记录
     */
    @Override
    public PatrolProblemRecord selectPatrolProblemRecordById(Integer id) {
        return patrolProblemRecordMapper.selectPatrolProblemRecordById(id);
    }

    /**
     * 查询巡更问题记录列表
     *
     * @param patrolProblemRecord 巡更问题记录
     * @return 巡更问题记录
     */
    @Override
    public List<PatrolProblemRecord> selectPatrolProblemRecordList(PatrolProblemRecord patrolProblemRecord) {
        return patrolProblemRecordMapper.selectPatrolProblemRecordList(patrolProblemRecord);
    }

    /**
     * 新增巡更问题记录
     *
     * @param patrolProblemRecord 巡更问题记录
     * @return 结果
     */
    @Override
    public int insertPatrolProblemRecord(PatrolProblemRecord patrolProblemRecord) {
        patrolProblemRecord.setCreatTime(new Date());
        patrolProblemRecord.setStatus("0");
        patrolProblemRecord.setSubmiter(SecurityUtils.getUserId());
        patrolProblemRecord.setSubmiterName(SecurityUtils.getUsername());
        int r = patrolProblemRecordMapper.insertPatrolProblemRecord(patrolProblemRecord);
        return r;
    }

    /**
     * 修改巡更问题记录
     *
     * @param patrolProblemRecord 巡更问题记录
     * @return 结果
     */
    @Override
    public int updatePatrolProblemRecord(PatrolProblemRecord patrolProblemRecord) {
        patrolProblemRecord.setPrincipal(SecurityUtils.getUserId());
        patrolProblemRecord.setSolvingTime(new Date());
        patrolProblemRecord.setStatus("1");
        return patrolProblemRecordMapper.updatePatrolProblemRecord(patrolProblemRecord);
    }

    /**
     * 批量删除巡更问题记录
     *
     * @param ids 需要删除的巡更问题记录主键
     * @return 结果
     */
    @Override
    public int deletePatrolProblemRecordByIds(Integer[] ids) {
        return patrolProblemRecordMapper.deletePatrolProblemRecordByIds(ids);
    }

    /**
     * 删除巡更问题记录信息
     *
     * @param id 巡更问题记录主键
     * @return 结果
     */
    @Override
    public int deletePatrolProblemRecordById(Integer id) {
        return patrolProblemRecordMapper.deletePatrolProblemRecordById(id);
    }
}
