package com.ibms.service.realty.web.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 专家知识库对象 t_knowledge_data
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@Data
@TableName("t_knowledge_data")
@ApiModel("专家知识库对象")
public class KnowledgeData {
    private static final long serialVersionUID = -4348070860980542201L;

    /**
     * 主键
     */
    @TableId(value = "id", type = com.baomidou.mybatisplus.annotation.IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 问题类型，对应字段realty_knowledge_data_type
     */
    @Excel(name = "问题类型，对应字段realty_knowledge_data_type")
    @ApiModelProperty("问题类型，对应字段realty_knowledge_data_type")
    private String questionType;

    /**
     * 标题
     */
    @Excel(name = "标题")
    @ApiModelProperty("标题")
    private String title;

    /**
     * 描述
     */
    @Excel(name = "描述")
    @ApiModelProperty("描述")
    private String represent;

    /**
     * 附件
     */
    @Excel(name = "附件URL,多个用逗号隔开")
    @ApiModelProperty("附件URL,多个用逗号隔开")
    private String annex;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String remark;

}
