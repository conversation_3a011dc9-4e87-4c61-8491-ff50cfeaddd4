package com.ibms.service.realty.web.controller.itemRelease;


import com.ibms.service.realty.web.domain.ItemRelease;
import com.ibms.service.realty.web.service.ItemReleaseService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 物品放行Controller
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@RestController
@RequestMapping("/release")
@Api(tags = "物品放行")
public class ItemReleaseController extends BaseController {
    @Autowired
    private ItemReleaseService itemReleaseService;

    /**
     * 查询物品放行列表
     */
    @PostMapping("/list")
    @ApiOperation("查询物品放行列表")
    public TableDataInfo list(@RequestBody ItemRelease itemRelease) {
        startPage();
        List<ItemRelease> list = itemReleaseService.selectItemReleaseList(itemRelease);
        return getDataTable(list);
    }

    /**
     * 导出物品放行列表
     */
    @Log(title = "物品放行", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出物品放行列表")
    public void export(HttpServletResponse response, ItemRelease itemRelease) {
        List<ItemRelease> list = itemReleaseService.selectItemReleaseList(itemRelease);
        ExcelUtil<ItemRelease> util = new ExcelUtil<ItemRelease>(ItemRelease.class);
        util.exportExcel(response, list, "物品放行数据");
    }

    /**
     * 获取物品放行详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取物品放行详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(itemReleaseService.selectItemReleaseById(id));
    }

    /**
     * 新增物品放行
     */
    @Log(title = "物品放行", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增物品放行")
    public AjaxResult add(@RequestBody ItemRelease itemRelease) {
        itemRelease.setApplyerId(SecurityUtils.getUserId());
        itemRelease.setApplyerName(SecurityUtils.getUsername());
        itemRelease.setApplyerPhone(SecurityUtils.getLoginUser().getSysUser().getPhonenumber());
        return toAjax(itemReleaseService.insertItemRelease(itemRelease));
    }

    /**
     * 修改物品放行
     */
    @Log(title = "物品放行", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改物品放行")
    public AjaxResult edit(@RequestBody ItemRelease itemRelease) {
        return toAjax(itemReleaseService.updateItemRelease(itemRelease));
    }

    /**
     * 删除物品放行
     */
    @Log(title = "物品放行", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除物品放行")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(itemReleaseService.deleteItemReleaseByIds(ids));
    }

    /**
     * 驳回
     */
    @Log(title = "驳回", businessType = BusinessType.UPDATE)
    @PutMapping("/reject")
    @ApiOperation("驳回")
    public AjaxResult reject(@RequestBody ItemRelease itemRelease) {
        ItemRelease itemReleaseParam = new ItemRelease();
        itemReleaseParam.setId(itemRelease.getId());
        itemReleaseParam.setAuditStatus("2");
        return toAjax(itemReleaseService.updateItemRelease(itemReleaseParam));
    }

    /**
     * 同意
     */
    @Log(title = "同意", businessType = BusinessType.UPDATE)
    @PutMapping("/agree")
    @ApiOperation("同意")
    public AjaxResult agree(@RequestBody ItemRelease itemRelease) {
        ItemRelease itemReleaseParam = new ItemRelease();
        itemReleaseParam.setId(itemRelease.getId());
        itemReleaseParam.setAuditStatus("1");
        return toAjax(itemReleaseService.updateItemRelease(itemReleaseParam));
    }

}
