package com.ibms.service.realty.web.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ibms.service.realty.web.domain.GuestVisits;
import com.ibms.service.realty.web.mapper.GuestVisitsMapper;
import com.ibms.service.realty.web.service.GuestVisitsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 来人来访信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@Service
public class GuestVisitsServiceImpl extends MPJBaseServiceImpl<GuestVisitsMapper, GuestVisits> implements GuestVisitsService {
    @Autowired
    private GuestVisitsMapper guestVisitsMapper;

    /**
     * 查询来人来访信息
     *
     * @param id 来人来访信息主键
     * @return 来人来访信息
     */
    @Override
    public GuestVisits selectGuestVisitsById(Integer id) {
        return guestVisitsMapper.selectGuestVisitsById(id);
    }

    /**
     * 查询来人来访信息列表
     *
     * @param guestVisits 来人来访信息
     * @return 来人来访信息
     */
    @Override
    public List<GuestVisits> selectGuestVisitsList(GuestVisits guestVisits) {
        return guestVisitsMapper.selectGuestVisitsList(guestVisits);
    }

    /**
     * 新增来人来访信息
     *
     * @param guestVisits 来人来访信息
     * @return 结果
     */
    @Override
    public int insertGuestVisits(GuestVisits guestVisits) {
        return guestVisitsMapper.insertGuestVisits(guestVisits);
    }

    /**
     * 修改来人来访信息
     *
     * @param guestVisits 来人来访信息
     * @return 结果
     */
    @Override
    public int updateGuestVisits(GuestVisits guestVisits) {
        return guestVisitsMapper.updateGuestVisits(guestVisits);
    }

    /**
     * 批量删除来人来访信息
     *
     * @param ids 需要删除的来人来访信息主键
     * @return 结果
     */
    @Override
    public int deleteGuestVisitsByIds(Integer[] ids) {
        return guestVisitsMapper.deleteGuestVisitsByIds(ids);
    }

    /**
     * 删除来人来访信息信息
     *
     * @param id 来人来访信息主键
     * @return 结果
     */
    @Override
    public int deleteGuestVisitsById(Integer id) {
        return guestVisitsMapper.deleteGuestVisitsById(id);
    }
}
