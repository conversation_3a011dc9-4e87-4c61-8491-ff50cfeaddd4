package com.ibms.service.realty.web.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.realty.web.domain.PatrolPlan;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 巡更计划Mapper接口
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@Mapper
public interface PatrolPlanMapper extends MPJBaseMapper<PatrolPlan> {
    /**
     * 查询巡更计划
     *
     * @param taskId 巡更计划主键
     * @return 巡更计划
     */
    public PatrolPlan selectPatrolPlanByTaskId(String taskId);

    /**
     * 查询巡更计划列表
     *
     * @param patrolPlan 巡更计划
     * @return 巡更计划集合
     */
    public List<PatrolPlan> selectPatrolPlanList(PatrolPlan patrolPlan);

    /**
     * 新增巡更计划
     *
     * @param patrolPlan 巡更计划
     * @return 结果
     */
    public int insertPatrolPlan(PatrolPlan patrolPlan);

    /**
     * 修改巡更计划
     *
     * @param patrolPlan 巡更计划
     * @return 结果
     */
    public int updatePatrolPlan(PatrolPlan patrolPlan);

    /**
     * 删除巡更计划
     *
     * @param taskId 巡更计划主键
     * @return 结果
     */
    public int deletePatrolPlanByTaskId(String taskId);

    /**
     * 批量删除巡更计划
     *
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePatrolPlanByTaskIds(String[] taskIds);
}

