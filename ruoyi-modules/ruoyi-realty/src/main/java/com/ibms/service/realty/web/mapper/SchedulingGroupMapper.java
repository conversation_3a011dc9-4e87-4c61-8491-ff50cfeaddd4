package com.ibms.service.realty.web.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.realty.web.domain.BaseHolidays;
import com.ibms.service.realty.web.domain.EmployeeGroup;
import com.ibms.service.realty.web.domain.SchedulingGroup;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 排班分组Mapper接口
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@Mapper
public interface SchedulingGroupMapper extends MPJBaseMapper<SchedulingGroup> {
    /**
     * 查询排班分组
     *
     * @param id 排班分组主键
     * @return 排班分组
     */
    public SchedulingGroup selectSchedulingGroupById(Integer id);

    /**
     * 查询排班分组列表
     *
     * @param schedulingGroup 排班分组
     * @return 排班分组集合
     */
    public List<SchedulingGroup> selectSchedulingGroupList(SchedulingGroup schedulingGroup);

    /**
     * 新增排班分组
     *
     * @param schedulingGroup 排班分组
     * @return 结果
     */
    public int insertSchedulingGroup(SchedulingGroup schedulingGroup);

    /**
     * 修改排班分组
     *
     * @param schedulingGroup 排班分组
     * @return 结果
     */
    public int updateSchedulingGroup(SchedulingGroup schedulingGroup);

    /**
     * 删除排班分组
     *
     * @param id 排班分组主键
     * @return 结果
     */
    public int deleteSchedulingGroupById(Integer id);

    /**
     * 批量删除排班分组
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchedulingGroupByIds(Integer[] ids);

    void insertEmployeeGroupList(List<EmployeeGroup> employeeGroups);

    void deleteEmployeeGroupByGroupId(Integer groupId);

    List<EmployeeGroup> selectEmployeeGroupByUserId(Long userId);

    List<BaseHolidays> selectLegalHoliday();

    void deleteEmployeeGroupByGroupIds(Integer[] ids);
}
