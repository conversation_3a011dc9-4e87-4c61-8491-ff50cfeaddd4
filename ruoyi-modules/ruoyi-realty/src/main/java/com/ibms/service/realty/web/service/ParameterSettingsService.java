package com.ibms.service.realty.web.service;


import com.github.yulichang.base.MPJBaseService;
import com.ibms.service.realty.web.domain.ParameterSettings;

import java.util.List;

/**
 * 参数设置Service接口
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
public interface ParameterSettingsService extends MPJBaseService<ParameterSettings> {
    /**
     * 查询参数设置
     *
     * @param id 参数设置主键
     * @return 参数设置
     */
    public ParameterSettings selectParameterSettingsById(Integer id);

    /**
     * 查询参数设置列表
     *
     * @param parameterSettings 参数设置
     * @return 参数设置集合
     */
    public List<ParameterSettings> selectParameterSettingsList(ParameterSettings parameterSettings);

    /**
     * 新增参数设置
     *
     * @param parameterSettings 参数设置
     * @return 结果
     */
    public int insertParameterSettings(ParameterSettings parameterSettings);

    /**
     * 修改参数设置
     *
     * @param parameterSettings 参数设置
     * @return 结果
     */
    public int updateParameterSettings(ParameterSettings parameterSettings);

    /**
     * 批量删除参数设置
     *
     * @param ids 需要删除的参数设置主键集合
     * @return 结果
     */
    public int deleteParameterSettingsByIds(Integer[] ids);

    /**
     * 删除参数设置信息
     *
     * @param id 参数设置主键
     * @return 结果
     */
    public int deleteParameterSettingsById(Integer id);
}
