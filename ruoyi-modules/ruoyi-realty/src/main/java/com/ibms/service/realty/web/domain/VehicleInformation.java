package com.ibms.service.realty.web.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆信息对象 t_vehicle_information
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@Data
@TableName("t_vehicle_information")
@ApiModel("车辆信息对象")
public class VehicleInformation {
    private static final long serialVersionUID = 4795072853333910307L;

    /**
     * 主键
     */
    @TableId(value = "id", type = com.baomidou.mybatisplus.annotation.IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 车牌号
     */
    @Excel(name = "车牌号", sort = 0)
    @ApiModelProperty("车牌号")
    private String licensePlateNumber;

    /**
     * 车辆品牌ID，关联t_parameter_settings表type=vehicle_brand的id
     */
    @ApiModelProperty("车辆品牌ID，关联t_parameter_settings表type=vehicle_brand的id")
    private Integer vehicleBrandId;

    /**
     * 车辆类型ID，关联t_parameter_settings表type=vehicle_type的id
     */
    @ApiModelProperty("车辆类型ID，关联t_parameter_settings表type=vehicle_type的id")
    private Integer vehicleTypeId;

    /**
     * 车架号
     */
    @ApiModelProperty("车架号")
    private String chassisNumber;

    /**
     * 发动机号
     */
    @ApiModelProperty("发动机号")
    private String engineNumber;

    /**
     * 车辆型号
     */
    @Excel(name = "车辆型号", sort = 2)
    @ApiModelProperty("车辆型号")
    private String vehicleModel;

    /**
     * 车辆状态，字典：realty_vehicle_information_vehicle_status；0:待命，1:禁用
     */
    @Excel(name = "车辆状态", readConverterExp = "0=待命,1=禁用", sort = 8)
    @ApiModelProperty("车辆状态，字典：realty_vehicle_information_vehicle_status；0:待命，1:禁用")
    private String vehicleStatus;

    /**
     * 默认司机
     */
    @ApiModelProperty("默认司机")
    private String defaultDriver;

    /**
     * 购置日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "购置日期", width = 30, dateFormat = "yyyy-MM-dd", sort = 6)
    @ApiModelProperty("购置日期")
    private Date purchaseDate;

    /**
     * 座位数
     */
    @Excel(name = "座位数", sort = 4)
    @ApiModelProperty("座位数")
    private Integer seatCount;

    /**
     * 载重，单位吨
     */
    @Excel(name = "载重", sort = 5)
    @ApiModelProperty("载重，单位吨")
    private BigDecimal loadCapacity;

    /**
     * 所属部门ID，关联部门表的id
     */
    @ApiModelProperty("所属部门ID，关联部门表的id")
    private Integer departmentId;

    /**
     * 所属部门名称
     */
    @Excel(name = "所属部门", sort = 7)
    @ApiModelProperty("所属部门名称")
    private String departmentName;

    /**
     * 附件URL，多个逗号分隔
     */
    @ApiModelProperty("附件URL，多个逗号分隔")
    private String attachmentUrl;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 车辆品牌Str
     */
    @Excel(name = "车辆品牌", sort = 1)
    @ApiModelProperty("车辆品牌Str")
    @TableField(exist = false)
    private String vehicleBrandName;

    /**
     * 车辆类型Str
     */
    @Excel(name = "车辆类型", sort = 3)
    @ApiModelProperty("车辆类型Str")
    @TableField(exist = false)
    private String vehicleTypeName;

}
