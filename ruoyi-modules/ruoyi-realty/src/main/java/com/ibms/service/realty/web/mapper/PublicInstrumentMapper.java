package com.ibms.service.realty.web.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.realty.web.domain.PublicInstrument;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公摊仪Mapper接口
 *
 * <AUTHOR>
 * @date 2023-04-14
 */

@Mapper
public interface PublicInstrumentMapper extends MPJBaseMapper<PublicInstrument> {
    /**
     * 查询公摊仪
     *
     * @param id 公摊仪主键
     * @return 公摊仪
     */
    public PublicInstrument selectPublicInstrumentById(Integer id);

    /**
     * 查询公摊仪列表
     *
     * @param publicInstrument 公摊仪
     * @return 公摊仪集合
     */
    public List<PublicInstrument> selectPublicInstrumentList(PublicInstrument publicInstrument);

    /**
     * 新增公摊仪
     *
     * @param publicInstrument 公摊仪
     * @return 结果
     */
    public int insertPublicInstrument(PublicInstrument publicInstrument);

    /**
     * 修改公摊仪
     *
     * @param publicInstrument 公摊仪
     * @return 结果
     */
    public int updatePublicInstrument(PublicInstrument publicInstrument);

    /**
     * 删除公摊仪
     *
     * @param id 公摊仪主键
     * @return 结果
     */
    public int deletePublicInstrumentById(Integer id);

    /**
     * 批量删除公摊仪
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePublicInstrumentByIds(Integer[] ids);
}

