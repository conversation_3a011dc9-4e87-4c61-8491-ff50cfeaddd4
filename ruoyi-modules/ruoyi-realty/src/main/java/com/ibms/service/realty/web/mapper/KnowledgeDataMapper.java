package com.ibms.service.realty.web.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.realty.web.domain.KnowledgeData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 专家知识库Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@Mapper
public interface KnowledgeDataMapper extends MPJBaseMapper<KnowledgeData> {
    /**
     * 查询专家知识库
     *
     * @param id 专家知识库主键
     * @return 专家知识库
     */
    public KnowledgeData selectKnowledgeDataById(Long id);

    /**
     * 查询专家知识库列表
     *
     * @param knowledgeData 专家知识库
     * @return 专家知识库集合
     */
    public List<KnowledgeData> selectKnowledgeDataList(KnowledgeData knowledgeData);

    /**
     * 新增专家知识库
     *
     * @param knowledgeData 专家知识库
     * @return 结果
     */
    public int insertKnowledgeData(KnowledgeData knowledgeData);

    /**
     * 修改专家知识库
     *
     * @param knowledgeData 专家知识库
     * @return 结果
     */
    public int updateKnowledgeData(KnowledgeData knowledgeData);

    /**
     * 删除专家知识库
     *
     * @param id 专家知识库主键
     * @return 结果
     */
    public int deleteKnowledgeDataById(Long id);

    /**
     * 批量删除专家知识库
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKnowledgeDataByIds(Long[] ids);
}

