package com.ibms.service.realty.web.controller.decorationSchedule;

import cn.hutool.core.collection.CollectionUtil;
import com.ibms.service.realty.web.controller.decorationSchedule.vo.DecorationScheduleInfoVo;
import com.ibms.service.realty.web.domain.DecorationSchedule;
import com.ibms.service.realty.web.service.DecorationScheduleService;
import com.ibms.service.realty.web.service.DecorationTaskOperationRecordService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.system.api.RemoteUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 装修巡查任务Controller
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
@RestController
@RequestMapping("/decorationSchedule")
@Api(tags = "装修巡查任务")
public class DecorationScheduleController extends BaseController {
    @Autowired
    private DecorationScheduleService decorationScheduleService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private DecorationTaskOperationRecordService decorationTaskOperationRecordService;

    /**
     * 查询装修巡查任务列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询装修巡查任务列表", notes = "查询装修巡查任务列表", httpMethod = "GET")
    public TableDataInfo list(DecorationSchedule decorationSchedule) {
        startPage();
        List<DecorationSchedule> list = decorationScheduleService.selectDecorationScheduleList(decorationSchedule);
        return getDataTable(list);
    }

    /**
     * 导出装修巡查任务列表
     */
    @Log(title = "装修巡查任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出装修巡查任务列表", notes = "导出装修巡查任务列表", httpMethod = "POST")
    public void export(HttpServletResponse response, DecorationSchedule decorationSchedule) {
        List<DecorationSchedule> list = decorationScheduleService.selectDecorationScheduleList(decorationSchedule);
        ExcelUtil<DecorationSchedule> util = new ExcelUtil<DecorationSchedule>(DecorationSchedule.class);
        util.exportExcel(response, list, "装修巡查任务数据");
    }

    /**
     * 获取装修巡查任务详细信息
     */
    @GetMapping(value = "/{taskId}")
    @ApiOperation(value = "获取装修巡查任务详细信息", notes = "获取装修巡查任务详细信息", httpMethod = "GET")
    public AjaxResult getInfo(@PathVariable("taskId") String taskId) {
        return AjaxResult.success(decorationScheduleService.selectDecorationScheduleByTaskId(taskId));
    }

    /**
     * 开始处理
     */
    @Log(title = "开始处理", businessType = BusinessType.UPDATE)
    @PutMapping("/start")
    @ApiOperation("开始处理")
    public AjaxResult sign(@RequestBody DecorationSchedule decorationSchedule) {
        decorationSchedule.setActualStartTime(new Date());
        decorationSchedule.setStatus("2");
        decorationScheduleService.updateDecorationSchedule(decorationSchedule);
        return AjaxResult.success();
    }

    /**
     * 结束
     */
    @Log(title = "结束", businessType = BusinessType.UPDATE)
    @PutMapping("/end")
    @ApiOperation("结束")
    public AjaxResult end(@RequestBody DecorationSchedule decorationSchedule) {
        Date currentDate = new Date();
        // 如果“计划结束时间”大于当前时间，则状态为“5-超时完成”，否则为“4-正常完成”
        if (decorationSchedule.getPlannedEndTime().getTime() < currentDate.getTime()) {
            decorationSchedule.setStatus("5");
        } else {
            decorationSchedule.setStatus("4");
        }
        decorationSchedule.setActualEndTime(currentDate);
        decorationScheduleService.updateDecorationSchedule(decorationSchedule);
        return AjaxResult.success();
    }

    /**
     * 修改巡检项目状态
     */
    @Log(title = "修改巡检项目状态", businessType = BusinessType.UPDATE)
    @PutMapping("/infoStatusUpdate")
    @ApiOperation("修改巡检项目状态")
    public AjaxResult infoStatusUpdate(@RequestBody DecorationScheduleInfoVo decorationScheduleInfoVo) {
        decorationScheduleService.updateDecorationScheduleInfo(decorationScheduleInfoVo);
        return AjaxResult.success();
    }

    /**
     * 新增装修巡查任务
     */
    @Log(title = "装修巡查任务", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增装修巡查任务", notes = "新增装修巡查任务", httpMethod = "POST")
    public AjaxResult add(@RequestBody DecorationSchedule decorationSchedule) {
        // 批量新增t_decoration_schedule_info
        List<DecorationScheduleInfoVo> decorationScheduleInfoVos = decorationSchedule.getDecorationScheduleInfoList();
        decorationScheduleService.insertBatchInfoVos(decorationScheduleInfoVos);

        // 新增t_decoration_schedule
        decorationScheduleService.insertDecorationSchedule(decorationSchedule);
        if (decorationSchedule.getTaskId() == null) {
            return AjaxResult.error("新增装修巡查任务失败");
        }
//        //获取用户
//        Long userid = Long.parseLong(decorationSchedule.getPatrollerId());
//        SysUser sysUser = remoteUserService.getUserInfoById(userid, SecurityConstants.INNER);
//        //插日志：系统创建任务,将任务法分配给某人
//        DecorationTaskOperationRecord decorationTaskOperationRecord = new DecorationTaskOperationRecord();
//        decorationTaskOperationRecord.setTaskId(decorationSchedule.getTaskId());
//        decorationTaskOperationRecord.setCreatTime(new Date());
//        decorationTaskOperationRecord.setContent("系统创建任务,将任务法分配给" + sysUser.getNickName());
//        decorationTaskOperationRecordService.insertDecorationTaskOperationRecord(decorationTaskOperationRecord);
        return AjaxResult.success();
    }

    /**
     * 修改装修巡查任务
     */
    @Log(title = "装修巡查任务", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改装修巡查任务", notes = "修改装修巡查任务", httpMethod = "PUT")
    public AjaxResult edit(@RequestBody DecorationSchedule decorationSchedule) {
        // 查询原来的任务信息
//        DecorationSchedule oldDecorationSchedule = decorationScheduleService.selectDecorationScheduleByTaskId(decorationSchedule.getTaskId());

        if (!CollectionUtil.isEmpty(decorationSchedule.getDecorationScheduleInfoList())) {
            // 先删除旧的
            decorationScheduleService.deleteDecorationScheduleInfoByTaskId(decorationSchedule.getTaskId());

            // 再批量新增t_decoration_schedule_info
            decorationScheduleService.insertBatchInfoVos(decorationSchedule.getDecorationScheduleInfoList());
        }

        int r = decorationScheduleService.updateDecorationSchedule(decorationSchedule);
        if (r == 0) {
            return AjaxResult.error("修改装修巡查任务失败");
        }
//        DecorationTaskOperationRecord decorationTaskOperationRecord = new DecorationTaskOperationRecord();
//        //比较原来的任务信息和现在的任务信息，比较每一个字段是否一致，不一致根据字段名称插入相关日志
//        if (oldDecorationSchedule.getStatus() != decorationSchedule.getStatus()) {
//            //插日志：任务状态由xxx变更为xxx
//            decorationTaskOperationRecord.setTaskId(decorationSchedule.getTaskId());
//            decorationTaskOperationRecord.setCreatTime(new Date());
//            decorationTaskOperationRecord.setContent("任务状态变更为：" + TaskStatus.valueOf(decorationSchedule.getStatus()).getDescription());
//        }
//        if ((oldDecorationSchedule.getRemark() == null && decorationSchedule.getRemark() != null)
//                || (decorationSchedule.getRemark() == null && oldDecorationSchedule.getRemark() != null)
//                || (oldDecorationSchedule.getRemark().equals(decorationSchedule.getRemark()))) {
//            //插日志：备注变更为xxx
//            decorationTaskOperationRecord.setTaskId(decorationSchedule.getTaskId());
//            decorationTaskOperationRecord.setCreatTime(new Date());
//            decorationTaskOperationRecord.setContent("备注变更为" + decorationSchedule.getRemark());
//        }
//        if (decorationTaskOperationRecord.getTaskId() != null) {
//            decorationTaskOperationRecordService.insertDecorationTaskOperationRecord(decorationTaskOperationRecord);
//        }
        return AjaxResult.success();
    }

    /**
     * 删除装修巡查任务
     */
    @Log(title = "装修巡查任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    @ApiOperation(value = "删除装修巡查任务", notes = "删除装修巡查任务", httpMethod = "DELETE")
    public AjaxResult remove(@PathVariable String[] taskIds) {
        // 先删除旧的
        for (String taskId : taskIds) {
            decorationScheduleService.deleteDecorationScheduleInfoByTaskId(taskId);
        }

        return toAjax(decorationScheduleService.deleteDecorationScheduleByTaskIds(taskIds));
    }
}
