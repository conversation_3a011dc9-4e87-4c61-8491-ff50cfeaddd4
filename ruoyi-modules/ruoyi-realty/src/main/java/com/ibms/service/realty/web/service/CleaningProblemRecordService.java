package com.ibms.service.realty.web.service;


import com.github.yulichang.base.MPJBaseService;
import com.ibms.service.realty.web.domain.CleaningProblemRecord;

import java.util.List;

/**
 * 保洁巡查问题记录Service接口
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
public interface CleaningProblemRecordService extends MPJBaseService<CleaningProblemRecord> {
    /**
     * 查询保洁巡查问题记录
     *
     * @param id 保洁巡查问题记录主键
     * @return 保洁巡查问题记录
     */
    public CleaningProblemRecord selectCleaningProblemRecordById(Integer id);

    /**
     * 查询保洁巡查问题记录列表
     *
     * @param cleaningProblemRecord 保洁巡查问题记录
     * @return 保洁巡查问题记录集合
     */
    public List<CleaningProblemRecord> selectCleaningProblemRecordList(CleaningProblemRecord cleaningProblemRecord);

    /**
     * 新增保洁巡查问题记录
     *
     * @param cleaningProblemRecord 保洁巡查问题记录
     * @return 结果
     */
    public int insertCleaningProblemRecord(CleaningProblemRecord cleaningProblemRecord);

    /**
     * 修改保洁巡查问题记录
     *
     * @param cleaningProblemRecord 保洁巡查问题记录
     * @return 结果
     */
    public int updateCleaningProblemRecord(CleaningProblemRecord cleaningProblemRecord);

    /**
     * 批量删除保洁巡查问题记录
     *
     * @param ids 需要删除的保洁巡查问题记录主键集合
     * @return 结果
     */
    public int deleteCleaningProblemRecordByIds(Integer[] ids);

    /**
     * 删除保洁巡查问题记录信息
     *
     * @param id 保洁巡查问题记录主键
     * @return 结果
     */
    public int deleteCleaningProblemRecordById(Integer id);
}

