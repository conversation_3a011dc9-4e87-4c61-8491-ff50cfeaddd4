package com.ibms.service.realty.web.service;


import com.github.yulichang.base.MPJBaseService;
import com.ibms.service.realty.web.domain.DecorationApplicationSettings;

import java.util.List;

/**
 * 装修申请设置Service接口
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
public interface DecorationApplicationSettingsService extends MPJBaseService<DecorationApplicationSettings> {
    /**
     * 查询装修申请设置
     *
     * @param id 装修申请设置主键
     * @return 装修申请设置
     */
    public DecorationApplicationSettings selectDecorationApplicationSettingsById(Integer id);

    /**
     * 查询装修申请设置列表
     *
     * @param decorationApplicationSettings 装修申请设置
     * @return 装修申请设置集合
     */
    public List<DecorationApplicationSettings> selectDecorationApplicationSettingsList(DecorationApplicationSettings decorationApplicationSettings);

    /**
     * 新增装修申请设置
     *
     * @param decorationApplicationSettings 装修申请设置
     * @return 结果
     */
    public int insertDecorationApplicationSettings(DecorationApplicationSettings decorationApplicationSettings);

    /**
     * 修改装修申请设置
     *
     * @param decorationApplicationSettings 装修申请设置
     * @return 结果
     */
    public int updateDecorationApplicationSettings(DecorationApplicationSettings decorationApplicationSettings);

    /**
     * 批量删除装修申请设置
     *
     * @param ids 需要删除的装修申请设置主键集合
     * @return 结果
     */
    public int deleteDecorationApplicationSettingsByIds(Integer[] ids);

    /**
     * 删除装修申请设置信息
     *
     * @param id 装修申请设置主键
     * @return 结果
     */
    public int deleteDecorationApplicationSettingsById(Integer id);
}
