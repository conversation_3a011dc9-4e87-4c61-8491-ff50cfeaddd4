package com.ibms.service.realty.web.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.realty.web.domain.UserComplaint;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户投诉Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-26
 */
@Mapper
public interface UserComplaintMapper extends MPJBaseMapper<UserComplaint> {
    /**
     * 查询用户投诉
     *
     * @param id 用户投诉主键
     * @return 用户投诉
     */
    public UserComplaint selectUserComplaintById(Integer id);

    /**
     * 查询用户投诉列表
     *
     * @param userComplaint 用户投诉
     * @return 用户投诉集合
     */
    public List<UserComplaint> selectUserComplaintList(UserComplaint userComplaint);

    /**
     * 新增用户投诉
     *
     * @param userComplaint 用户投诉
     * @return 结果
     */
    public int insertUserComplaint(UserComplaint userComplaint);

    /**
     * 修改用户投诉
     *
     * @param userComplaint 用户投诉
     * @return 结果
     */
    public int updateUserComplaint(UserComplaint userComplaint);

    /**
     * 删除用户投诉
     *
     * @param id 用户投诉主键
     * @return 结果
     */
    public int deleteUserComplaintById(Integer id);

    /**
     * 批量删除用户投诉
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserComplaintByIds(Integer[] ids);
}

