package com.ibms.service.realty.web.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 保洁记录对象 t_cleaning_record
 *
 * <AUTHOR>
 * @date 2023-07-04
 */
@Data
@TableName("t_cleaning_record")
@ApiModel(value = "CleaningRecord", description = "保洁记录对象")
public class CleaningRecord {
    private static final long serialVersionUID = -8753563872248938101L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private Integer id;

    /**
     * 保洁区域id
     */
    @ApiModelProperty(value = "保洁区域id")
    private Integer cleaningRegionId;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 保洁区域
     */
    @ApiModelProperty(value = "保洁区域")
    @Excel(name = "保洁区域")
    private String cleaningRegionName;

    /**
     * 责任人名称
     */
    @Excel(name = "责任人")
    @ApiModelProperty(value = "责任人名称")
    private String responsiblePersonName;

    /**
     * 计划完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划完成日期")
    private Date plannedCompletionDate;

    /**
     * 实际完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "实际完成日期")
    private Date actualCompletionDate;

    /**
     * 项目类型，0：日常保洁，1：消毒消杀，2：清洁保养
     */
    @Excel(name = "项目类型", readConverterExp = "0=日常保洁,1=消毒消杀,2=清洁保养")
    @ApiModelProperty(value = "项目类型，0：日常保洁，1：消毒消杀，2：清洁保养")
    private String projectType;

    /**
     * 具体内容
     */
    @Excel(name = "具体内容")
    @ApiModelProperty(value = "具体内容")
    private String content;

    /**
     * 状态，0：计划中，1：进行中，2：已完成，3：暂停，4：取消
     */
    @Excel(name = "状态", readConverterExp = "0=计划中,1=进行中,2=已完成,3=暂停,4=取消")
    @ApiModelProperty(value = "状态，0：计划中，1：进行中，2：已完成，3：暂停，4：取消")
    private String status;

    /**
     * 责任人id
     */
    @ApiModelProperty(value = "责任人id")
    private Long responsiblePersonId;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 计划完成日期开始param
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划完成日期开始param")
    private Date plannedCompletionDateStart;

    /**
     * 计划完成日期结束param
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划完成日期结束param")
    private Date plannedCompletionDateEnd;


}
