package com.ibms.service.realty.web.service.impl;


import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ibms.service.realty.web.domain.CleaningRegionInfo;
import com.ibms.service.realty.web.mapper.CleaningRegionInfoMapper;
import com.ibms.service.realty.web.service.CleaningRegionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 保洁区域信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@Service
public class CleaningRegionInfoServiceImpl extends MPJBaseServiceImpl<CleaningRegionInfoMapper, CleaningRegionInfo> implements CleaningRegionInfoService {
    @Autowired
    private CleaningRegionInfoMapper cleaningRegionInfoMapper;

    /**
     * 查询保洁区域信息
     *
     * @param id 保洁区域信息主键
     * @return 保洁区域信息
     */
    @Override
    public CleaningRegionInfo selectCleaningRegionInfoById(Integer id) {
        return cleaningRegionInfoMapper.selectCleaningRegionInfoById(id);
    }

    /**
     * 查询保洁区域信息列表
     *
     * @param cleaningRegionInfo 保洁区域信息
     * @return 保洁区域信息
     */
    @Override
    public List<CleaningRegionInfo> selectCleaningRegionInfoList(CleaningRegionInfo cleaningRegionInfo) {
        return cleaningRegionInfoMapper.selectCleaningRegionInfoList(cleaningRegionInfo);
    }

    /**
     * 新增保洁区域信息
     *
     * @param cleaningRegionInfo 保洁区域信息
     * @return 结果
     */
    @Override
    public int insertCleaningRegionInfo(CleaningRegionInfo cleaningRegionInfo) {
        return cleaningRegionInfoMapper.insertCleaningRegionInfo(cleaningRegionInfo);
    }

    /**
     * 修改保洁区域信息
     *
     * @param cleaningRegionInfo 保洁区域信息
     * @return 结果
     */
    @Override
    public int updateCleaningRegionInfo(CleaningRegionInfo cleaningRegionInfo) {
        return cleaningRegionInfoMapper.updateCleaningRegionInfo(cleaningRegionInfo);
    }

    /**
     * 批量删除保洁区域信息
     *
     * @param ids 需要删除的保洁区域信息主键
     * @return 结果
     */
    @Override
    public int deleteCleaningRegionInfoByIds(Integer[] ids) {
        return cleaningRegionInfoMapper.deleteCleaningRegionInfoByIds(ids);
    }

    /**
     * 删除保洁区域信息信息
     *
     * @param id 保洁区域信息主键
     * @return 结果
     */
    @Override
    public int deleteCleaningRegionInfoById(Integer id) {
        return cleaningRegionInfoMapper.deleteCleaningRegionInfoById(id);
    }
}
