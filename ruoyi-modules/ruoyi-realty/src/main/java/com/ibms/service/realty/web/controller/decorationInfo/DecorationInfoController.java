package com.ibms.service.realty.web.controller.decorationInfo;

import com.ibms.service.realty.web.domain.DecorationInfo;
import com.ibms.service.realty.web.service.DecorationInfoService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 巡检项目信息Controller
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@RestController
@RequestMapping("/decorationInfo")
@Api(tags = "巡检项目信息")
public class DecorationInfoController extends BaseController {
    @Autowired
    private DecorationInfoService decorationInfoService;

    /**
     * 查询巡检项目信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询巡检项目信息列表")
    public TableDataInfo list(DecorationInfo decorationInfo) {
        startPage();
        List<DecorationInfo> list = decorationInfoService.selectDecorationInfoList(decorationInfo);
        return getDataTable(list);
    }

    /**
     * 导出巡检项目信息列表
     */
    @Log(title = "巡检项目信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出巡检项目信息列表")
    public void export(HttpServletResponse response, DecorationInfo decorationInfo) {
        List<DecorationInfo> list = decorationInfoService.selectDecorationInfoList(decorationInfo);
        ExcelUtil<DecorationInfo> util = new ExcelUtil<DecorationInfo>(DecorationInfo.class);
        util.exportExcel(response, list, "巡检项目信息数据");
    }

    /**
     * 获取巡检项目信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取巡检项目信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(decorationInfoService.selectDecorationInfoById(id));
    }

    /**
     * 新增巡检项目信息
     */
    @Log(title = "巡检项目信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增巡检项目信息")
    public AjaxResult add(@RequestBody DecorationInfo decorationInfo) {
        return toAjax(decorationInfoService.insertDecorationInfo(decorationInfo));
    }

    /**
     * 修改巡检项目信息
     */
    @Log(title = "巡检项目信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改巡检项目信息")
    public AjaxResult edit(@RequestBody DecorationInfo decorationInfo) {
        return toAjax(decorationInfoService.updateDecorationInfo(decorationInfo));
    }

    /**
     * 删除巡检项目信息
     */
    @Log(title = "巡检项目信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除巡检项目信息")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(decorationInfoService.deleteDecorationInfoByIds(ids));
    }
}
