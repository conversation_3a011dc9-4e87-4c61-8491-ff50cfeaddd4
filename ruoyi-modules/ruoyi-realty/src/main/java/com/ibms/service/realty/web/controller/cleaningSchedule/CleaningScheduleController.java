package com.ibms.service.realty.web.controller.cleaningSchedule;


import com.ibms.service.realty.web.domain.CleaningSchedule;
import com.ibms.service.realty.web.domain.CleaningTaskOperationRecord;
import com.ibms.service.realty.web.service.CleaningScheduleService;
import com.ibms.service.realty.web.service.CleaningTaskOperationRecordService;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;


/**
 * 保洁巡查任务Controller
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@RestController
@RequestMapping("/cleaningSchedule")
@Api(tags = "保洁巡查任务")
public class CleaningScheduleController extends BaseController {
    @Autowired
    private CleaningScheduleService cleaningScheduleService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private CleaningTaskOperationRecordService cleaningTaskOperationRecordService;

    /**
     * 查询保洁巡查任务列表
     */
    @GetMapping("/list")
    @ApiOperation("查询保洁巡查任务列表")
    public TableDataInfo list(CleaningSchedule cleaningSchedule) {
        startPage();
        List<CleaningSchedule> list = cleaningScheduleService.selectCleaningScheduleList(cleaningSchedule);
        return getDataTable(list);
    }

    /**
     * 导出保洁巡查任务列表
     */
    @Log(title = "保洁巡查任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出保洁巡查任务列表")
    public void export(HttpServletResponse response, CleaningSchedule cleaningSchedule) {
        List<CleaningSchedule> list = cleaningScheduleService.selectCleaningScheduleList(cleaningSchedule);
        ExcelUtil<CleaningSchedule> util = new ExcelUtil<CleaningSchedule>(CleaningSchedule.class);
        util.exportExcel(response, list, "保洁巡查任务数据");
    }

    /**
     * 获取保洁巡查任务详细信息
     */
    @GetMapping(value = "/{taskId}")
    @ApiOperation("获取保洁巡查任务详细信息")
    public AjaxResult getInfo(@PathVariable("taskId") Integer taskId) {
        return AjaxResult.success(cleaningScheduleService.selectCleaningScheduleByTaskId(taskId));
    }

    /**
     * 新增保洁巡查任务
     */
    @Log(title = "保洁巡查任务", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增保洁巡查任务")
    public AjaxResult add(@RequestBody CleaningSchedule cleaningSchedule) {
        cleaningScheduleService.insertCleaningSchedule(cleaningSchedule);
        if (cleaningSchedule.getTaskId() == null) {
            return AjaxResult.error("新增保洁巡查任务失败");
        }
        // 获取当前用户
        Long userid = cleaningSchedule.getPatroller();
        SysUser sysUser = remoteUserService.getUserInfoById(userid, SecurityConstants.INNER);
        // 插日志：系统创建任务,将任务法分配给某人
        CleaningTaskOperationRecord cleaningTaskOperationRecord = new CleaningTaskOperationRecord();
        cleaningTaskOperationRecord.setTaskId(cleaningSchedule.getTaskId());
        cleaningTaskOperationRecord.setCreatTime(new Date());
        cleaningTaskOperationRecord.setContent("系统创建任务,将任务法分配给" + sysUser.getNickName());
        cleaningTaskOperationRecordService.insertCleaningTaskOperationRecord(cleaningTaskOperationRecord);
        return AjaxResult.success();
    }

    /**
     * 开始处理
     */
    @Log(title = "开始处理", businessType = BusinessType.UPDATE)
    @PutMapping("/start")
    @ApiOperation("开始处理")
    public AjaxResult sign(@RequestBody CleaningSchedule cleaningSchedule) {
        cleaningSchedule.setActualStartTime(new Date());
        cleaningSchedule.setStatus(2);
        cleaningScheduleService.updateCleaningSchedule(cleaningSchedule);
        return AjaxResult.success();
    }

    /**
     * 结束
     */
    @Log(title = "结束", businessType = BusinessType.UPDATE)
    @PutMapping("/end")
    @ApiOperation("结束")
    public AjaxResult end(@RequestBody CleaningSchedule cleaningSchedule) {
        Date currentDate = new Date();
        // 如果“计划结束时间”大于当前时间，则状态为“5-超时完成”，否则为“4-正常完成”
        if (cleaningSchedule.getPlannedEndTime().getTime() < currentDate.getTime()) {
            cleaningSchedule.setStatus(5);
        } else {
            cleaningSchedule.setStatus(4);
        }
        cleaningSchedule.setActualEndTime(currentDate);
        cleaningScheduleService.updateCleaningSchedule(cleaningSchedule);
        return AjaxResult.success();
    }


    /**
     * 修改保洁巡查任务
     */
    @Log(title = "保洁巡查任务", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改保洁巡查任务")
    public AjaxResult edit(@RequestBody CleaningSchedule cleaningSchedule) {

        // 查询原来的任务信息
        CleaningSchedule cleaningSchedule1 = cleaningScheduleService.selectCleaningScheduleByTaskId(cleaningSchedule.getTaskId());

        int r = cleaningScheduleService.updateCleaningSchedule(cleaningSchedule);
        if (r == 0) {
            return AjaxResult.error("修改保洁巡查任务失败");
        }

//        CleaningTaskOperationRecord cleaningTaskOperationRecord = new CleaningTaskOperationRecord();
//        //比较原来的任务信息和现在的任务信息，比较每一个字段是否一致，不一致根据字段名称插入相关日志
//        if (cleaningSchedule1.getStatus() != cleaningSchedule.getStatus()) {
//            //插日志：任务状态由xxx变更为xxx
//            cleaningTaskOperationRecord.setTaskId(cleaningSchedule.getTaskId());
//            cleaningTaskOperationRecord.setCreatTime(new Date());
//            cleaningTaskOperationRecord.setContent("任务状态变更为" + TaskStatus.valueOf(cleaningSchedule.getStatus()).getDescription());
//
//        }
//        if (cleaningTaskOperationRecord.getTaskId() != null) {
//            cleaningTaskOperationRecordService.insertCleaningTaskOperationRecord(cleaningTaskOperationRecord);
//        }
        return AjaxResult.success();
    }

    /**
     * 删除保洁巡查任务
     */
    @Log(title = "保洁巡查任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    @ApiOperation("删除保洁巡查任务")
    public AjaxResult remove(@PathVariable Integer[] taskIds) {
        return toAjax(cleaningScheduleService.deleteCleaningScheduleByTaskIds(taskIds));
    }
}
