package com.ibms.service.realty.web.service;


import com.github.yulichang.base.MPJBaseService;
import com.ibms.service.realty.web.domain.SecurityPost;

import java.util.List;

/**
 * 保安岗位信息Service接口
 *
 * <AUTHOR>
 * @date 2023-06-14
 */
public interface SecurityPostService extends MPJBaseService<SecurityPost> {
    /**
     * 查询保安岗位信息
     *
     * @param id 保安岗位信息主键
     * @return 保安岗位信息
     */
    public SecurityPost selectSecurityPostById(Integer id);

    /**
     * 查询保安岗位信息列表
     *
     * @param securityPost 保安岗位信息
     * @return 保安岗位信息集合
     */
    public List<SecurityPost> selectSecurityPostList(SecurityPost securityPost);

    /**
     * 新增保安岗位信息
     *
     * @param securityPost 保安岗位信息
     * @return 结果
     */
    public int insertSecurityPost(SecurityPost securityPost);

    /**
     * 修改保安岗位信息
     *
     * @param securityPost 保安岗位信息
     * @return 结果
     */
    public int updateSecurityPost(SecurityPost securityPost);

    /**
     * 批量删除保安岗位信息
     *
     * @param ids 需要删除的保安岗位信息主键集合
     * @return 结果
     */
    public int deleteSecurityPostByIds(Integer[] ids);

    /**
     * 删除保安岗位信息信息
     *
     * @param id 保安岗位信息主键
     * @return 结果
     */
    public int deleteSecurityPostById(Integer id);

//    SecuritySchedule getScheduleById(Integer id);
}
