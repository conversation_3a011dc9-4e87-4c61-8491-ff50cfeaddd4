package com.ibms.service.realty.web.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.realty.web.domain.ItemRegistration;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 物品出入登记Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Mapper
public interface ItemRegistrationMapper extends MPJBaseMapper<ItemRegistration> {
    /**
     * 查询物品出入登记
     *
     * @param id 物品出入登记主键
     * @return 物品出入登记
     */
    public ItemRegistration selectItemRegistrationById(Integer id);

    /**
     * 查询物品出入登记列表
     *
     * @param itemRegistration 物品出入登记
     * @return 物品出入登记集合
     */
    public List<ItemRegistration> selectItemRegistrationList(ItemRegistration itemRegistration);

    /**
     * 新增物品出入登记
     *
     * @param itemRegistration 物品出入登记
     * @return 结果
     */
    public int insertItemRegistration(ItemRegistration itemRegistration);

    /**
     * 修改物品出入登记
     *
     * @param itemRegistration 物品出入登记
     * @return 结果
     */
    public int updateItemRegistration(ItemRegistration itemRegistration);

    /**
     * 删除物品出入登记
     *
     * @param id 物品出入登记主键
     * @return 结果
     */
    public int deleteItemRegistrationById(Integer id);

    /**
     * 批量删除物品出入登记
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteItemRegistrationByIds(Integer[] ids);
}
