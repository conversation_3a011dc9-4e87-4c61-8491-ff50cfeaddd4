package com.ibms.service.realty.web.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 排班分组对象 t_scheduling_group
 *
 * <AUTHOR>
 * @date 2023-04-17
 */
@Data
@TableName("t_scheduling_group")
@ApiModel("排班分组对象")
public class SchedulingGroup {
    private static final long serialVersionUID = 1243679481128279166L;

    /**
     * 排班分组ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("排班分组ID")
    private Integer id;

    /**
     * 名称
     */
    @Excel(name = "名称")
    @ApiModelProperty("名称")
    private String name;

    /**
     * 排班周期，对应周期管理表id
     */
    @Excel(name = "排班周期，对应周期管理表id")
    @ApiModelProperty("排班周期，对应周期管理表id")
    private Integer cycleId;

    /**
     * 排班日历，对应日历管理表id
     */
    @Excel(name = "排班日历，对应日历管理表id")
    @ApiModelProperty("排班日历，对应日历管理表id")
    private Integer calendarId;

    /**
     * 首次排班日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "首次排班日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("首次排班日期")
    private Date firstScheduleDate;

    /**
     * 员工排班分组对象 List
     */
    @ApiModelProperty("员工排班分组对象 List")
    @TableField(exist = false)
    private List<EmployeeGroup> employeeGroupList;

    /**
     * 员工名字逗号拼接
     */
    @ApiModelProperty("员工名字逗号拼接")
    @TableField(exist = false)
    private String employeeNames;

    /**
     * 日历名称
     */
    @Excel(name = "日历名称")
    @ApiModelProperty("日历名称")
    @TableField(exist = false)
    private String calendarName;

    /**
     * 周期名称
     */
    @Excel(name = "周期名称")
    @ApiModelProperty("周期名称")
    @TableField(exist = false)
    private String cycleName;

    /**
     * 班次id逗号拼接
     */
    @ApiModelProperty("班次id逗号拼接")
    @TableField(exist = false)
    private String shiftIds;

    /**
     * 法定节假日自动休息
     */
    @ApiModelProperty("法定节假日自动休息")
    @TableField(exist = false)
    private Integer legalHolidayAutoRest;

    /**
     * 休息日模式
     */
    @ApiModelProperty("休息日模式")
    @TableField(exist = false)
    private Integer restDayMode;

    /**
     * 休息日
     */
    @ApiModelProperty("休息日")
    @TableField(exist = false)
    private String restDays;

    /**
     * 员工姓名 param
     */
    @ApiModelProperty("员工姓名 param")
    @TableField(exist = false)
    private String employeeNameParam;
}

