package com.ibms.service.realty.web.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ibms.service.realty.web.domain.RepairApplicationInfo;
import com.ibms.service.realty.web.mapper.RepairApplicationInfoMapper;
import com.ibms.service.realty.web.service.RepairApplicationInfoService;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 报修申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@Service
public class RepairApplicationInfoServiceImpl extends MPJBaseServiceImpl<RepairApplicationInfoMapper, RepairApplicationInfo> implements RepairApplicationInfoService {
    @Autowired
    private RepairApplicationInfoMapper repairApplicationInfoMapper;

    /**
     * 查询报修申请
     *
     * @param id 报修申请主键
     * @return 报修申请
     */
    @Override
    public RepairApplicationInfo selectRepairApplicationInfoById(Integer id) {
        return repairApplicationInfoMapper.selectRepairApplicationInfoById(id);
    }

    /**
     * 查询报修申请列表
     *
     * @param repairApplicationInfo 报修申请
     * @return 报修申请
     */
    @Override
    public List<RepairApplicationInfo> selectRepairApplicationInfoList(RepairApplicationInfo repairApplicationInfo) {
        return repairApplicationInfoMapper.selectRepairApplicationInfoList(repairApplicationInfo);
    }

    /**
     * 新增报修申请
     *
     * @param repairApplicationInfo 报修申请
     * @return 结果
     */
    @Override
    public int insertRepairApplicationInfo(RepairApplicationInfo repairApplicationInfo) {
        // 生成报修单号
        repairApplicationInfo.setWorkOrderNo("BX" + DateUtils.dateTimeNow("yyyyMMddHHmmss"));
        // 报修类型，0: 业主报修，1: 员工报修
        if (repairApplicationInfo.getRepairType() == 0) {
            repairApplicationInfo.setAppointmentTime(null);
            repairApplicationInfo.setRepairArea(1);
        }
        repairApplicationInfo.setSubmitId(SecurityUtils.getUserId());
        repairApplicationInfo.setSubmitName(SecurityUtils.getUsername());
        repairApplicationInfo.setSubmitTime(DateUtils.getNowDate());
        repairApplicationInfo.setContactPhone(SecurityUtils.getLoginUser().getSysUser().getPhonenumber());
        // 状态，0：待分配，1：已分配，2：待回访，3：已关闭
        repairApplicationInfo.setStatus(0);
        return repairApplicationInfoMapper.insertRepairApplicationInfo(repairApplicationInfo);
    }

    /**
     * 修改报修申请
     *
     * @param repairApplicationInfo 报修申请
     * @return 结果
     */
    @Override
    public int updateRepairApplicationInfo(RepairApplicationInfo repairApplicationInfo) {
        return repairApplicationInfoMapper.updateRepairApplicationInfo(repairApplicationInfo);
    }

    /**
     * 批量删除报修申请
     *
     * @param ids 需要删除的报修申请主键
     * @return 结果
     */
    @Override
    public int deleteRepairApplicationInfoByIds(Integer[] ids) {
        return repairApplicationInfoMapper.deleteRepairApplicationInfoByIds(ids);
    }

    /**
     * 删除报修申请信息
     *
     * @param id 报修申请主键
     * @return 结果
     */
    @Override
    public int deleteRepairApplicationInfoById(Integer id) {
        return repairApplicationInfoMapper.deleteRepairApplicationInfoById(id);
    }
}

