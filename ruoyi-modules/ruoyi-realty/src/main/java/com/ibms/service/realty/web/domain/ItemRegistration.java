package com.ibms.service.realty.web.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 物品出入登记对象 t_item_registration
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Data
@TableName("t_item_registration")
@ApiModel(value = "物品出入登记对象", description = "物品出入登记对象")
public class ItemRegistration {
    private static final long serialVersionUID = 1831223569467760320L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 对应物业资源信息表id
     */
    @Excel(name = "对应物业资源信息表id")
    @ApiModelProperty(value = "对应物业资源信息表id")
    private Integer managementAreaId;

    /**
     * 物品名称
     */
    @Excel(name = "物品名称")
    @ApiModelProperty(value = "物品名称")
    private String itemName;

    /**
     * 数量
     */
    @Excel(name = "数量")
    @ApiModelProperty(value = "数量")
    private Integer quantity;

    /**
     * 携带人
     */
    @Excel(name = "携带人")
    @ApiModelProperty(value = "携带人")
    private String carrier;

    /**
     * 带出时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "带出时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "带出时间")
    private Date takeOutTime;

    /**
     * 带入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "带入时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "带入时间")
    private Date takeInTime;

    /**
     * 证件号码
     */
    @Excel(name = "证件号码")
    @ApiModelProperty(value = "证件号码")
    private String idNumber;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactNumber;

    /**
     * 值班人员
     */
    @Excel(name = "值班人员id")
    @ApiModelProperty(value = "值班人员id")
    private Long staffOnDutyId;

    /**
     * 值班人员
     */
    @Excel(name = "值班人员名字")
    @ApiModelProperty(value = "值班人员名字")
    private Long staffOnDutyName;

    /**
     * 车牌号码
     */
    @Excel(name = "车牌号码")
    @ApiModelProperty(value = "车牌号码")
    private String licensePlateNumber;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remarks;

}
