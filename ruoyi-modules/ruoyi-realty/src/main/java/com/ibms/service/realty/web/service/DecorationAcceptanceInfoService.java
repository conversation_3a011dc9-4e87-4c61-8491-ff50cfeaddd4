package com.ibms.service.realty.web.service;


import com.ibms.service.realty.web.domain.DecorationAcceptanceInfo;

import java.util.List;

/**
 * 验收申请信息Service接口
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
public interface DecorationAcceptanceInfoService {
    /**
     * 查询验收申请信息
     *
     * @param applyId 验收申请信息主键
     * @return 验收申请信息
     */
    public DecorationAcceptanceInfo selectDecorationAcceptanceInfoByApplyId(String applyId);

    /**
     * 查询验收申请信息列表
     *
     * @param decorationAcceptanceInfo 验收申请信息
     * @return 验收申请信息集合
     */
    public List<DecorationAcceptanceInfo> selectDecorationAcceptanceInfoList(DecorationAcceptanceInfo decorationAcceptanceInfo);

    /**
     * 新增验收申请信息
     *
     * @param decorationAcceptanceInfo 验收申请信息
     * @return 结果
     */
    public int insertDecorationAcceptanceInfo(DecorationAcceptanceInfo decorationAcceptanceInfo);

    /**
     * 修改验收申请信息
     *
     * @param decorationAcceptanceInfo 验收申请信息
     * @return 结果
     */
    public int updateDecorationAcceptanceInfo(DecorationAcceptanceInfo decorationAcceptanceInfo);

    /**
     * 批量删除验收申请信息
     *
     * @param applyIds 需要删除的验收申请信息主键集合
     * @return 结果
     */
    public int deleteDecorationAcceptanceInfoByApplyIds(String[] applyIds);

    /**
     * 删除验收申请信息信息
     *
     * @param applyId 验收申请信息主键
     * @return 结果
     */
    public int deleteDecorationAcceptanceInfoByApplyId(String applyId);

    int reset(DecorationAcceptanceInfo decorationAcceptanceInfo);
}

