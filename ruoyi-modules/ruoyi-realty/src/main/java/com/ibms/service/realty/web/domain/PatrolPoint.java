package com.ibms.service.realty.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 巡更点对象 t_patrol_point
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@Data
@TableName("t_patrol_point")
@ApiModel(value = "PatrolPoint对象", description = "巡更点对象")
public class PatrolPoint {
    private static final long serialVersionUID = -4900317610159530383L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Integer id;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String code;

    /**
     * 名称
     */
    @Excel(name = "名称")
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 检查要点
     */
    @Excel(name = "检查要点")
    @ApiModelProperty(value = "检查要点")
    private String remark;

    /**
     * 状态，0：正常，1：作废
     */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态，0：正常，1：作废")
    private String status;

    /**
     * 组内排序
     */
    @Excel(name = "组内排序")
    @ApiModelProperty(value = "组内排序")
    @TableField(exist = false)
    private Integer sort;

    /**
     * content param
     */
    @ApiModelProperty(value = "content param")
    @TableField(exist = false)
    private String contentParam;

}
