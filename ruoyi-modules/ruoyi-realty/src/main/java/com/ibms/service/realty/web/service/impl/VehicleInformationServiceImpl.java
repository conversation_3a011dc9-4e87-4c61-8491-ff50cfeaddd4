package com.ibms.service.realty.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ibms.service.realty.web.controller.vehicleInformation.param.VehicleBrandParam;
import com.ibms.service.realty.web.controller.vehicleInformation.param.VehicleTypeParam;
import com.ibms.service.realty.web.domain.ParameterSettings;
import com.ibms.service.realty.web.domain.VehicleInformation;
import com.ibms.service.realty.web.mapper.VehicleInformationMapper;
import com.ibms.service.realty.web.service.ParameterSettingsService;
import com.ibms.service.realty.web.service.VehicleInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车辆信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@Service
public class VehicleInformationServiceImpl extends MPJBaseServiceImpl<VehicleInformationMapper, VehicleInformation> implements VehicleInformationService {
    @Autowired
    private VehicleInformationMapper vehicleInformationMapper;
    @Autowired
    private ParameterSettingsService parameterSettingsService;

    /**
     * 查询车辆信息
     *
     * @param id 车辆信息主键
     * @return 车辆信息
     */
    @Override
    public VehicleInformation selectVehicleInformationById(Integer id) {
        return vehicleInformationMapper.selectVehicleInformationById(id);
    }

    /**
     * 查询车辆信息列表
     *
     * @param vehicleInformation 车辆信息
     * @return 车辆信息
     */
    @Override
    public List<VehicleInformation> selectVehicleInformationList(VehicleInformation vehicleInformation) {
        return vehicleInformationMapper.selectVehicleInformationList(vehicleInformation);
    }

    /**
     * 新增车辆信息
     *
     * @param vehicleInformation 车辆信息
     * @return 结果
     */
    @Override
    public int insertVehicleInformation(VehicleInformation vehicleInformation) {
        return vehicleInformationMapper.insertVehicleInformation(vehicleInformation);
    }

    /**
     * 修改车辆信息
     *
     * @param vehicleInformation 车辆信息
     * @return 结果
     */
    @Override
    public int updateVehicleInformation(VehicleInformation vehicleInformation) {
        return vehicleInformationMapper.updateVehicleInformation(vehicleInformation);
    }

    /**
     * 批量删除车辆信息
     *
     * @param ids 需要删除的车辆信息主键
     * @return 结果
     */
    @Override
    public int deleteVehicleInformationByIds(Integer[] ids) {
        return vehicleInformationMapper.deleteVehicleInformationByIds(ids);
    }

    /**
     * 删除车辆信息信息
     *
     * @param id 车辆信息主键
     * @return 结果
     */
    @Override
    public int deleteVehicleInformationById(Integer id) {
        return vehicleInformationMapper.deleteVehicleInformationById(id);
    }

    @Override
    public Object vehicleBrandList() {
        // 车辆品牌标识
        String type = "vehicle_brand";
        QueryWrapper<ParameterSettings> queryWrapper = new QueryWrapper();
        queryWrapper.eq("type", type);
        queryWrapper.eq("status", 0);
        return parameterSettingsService.list(queryWrapper);
    }

    @Override
    public Object vehicleTypeList() {
        // 车辆类型标识
        String type = "vehicle_type";
        QueryWrapper<ParameterSettings> queryWrapper = new QueryWrapper();
        queryWrapper.eq("type", type);
        queryWrapper.eq("status", 0);
        return parameterSettingsService.list(queryWrapper);
    }

    @Override
    public int deleteVehicleBrandById(Integer id) {
        return parameterSettingsService.deleteParameterSettingsById(id);
    }

    @Override
    public int addVehicleBrand(VehicleBrandParam vehicleBrandParam) {
        ParameterSettings parameterSettings = new ParameterSettings();
        parameterSettings.setType("vehicle_brand");
        parameterSettings.setValue(vehicleBrandParam.getBrandName());
        parameterSettings.setStatus(0);
        return parameterSettingsService.insertParameterSettings(parameterSettings);
    }

    @Override
    public int deleteVehicleTypeById(Integer id) {
        return parameterSettingsService.deleteParameterSettingsById(id);
    }

    @Override
    public int addVehicleType(VehicleTypeParam vehicleTypeParam) {
        ParameterSettings parameterSettings = new ParameterSettings();
        parameterSettings.setType("vehicle_type");
        parameterSettings.setValue(vehicleTypeParam.getTypeName());
        parameterSettings.setStatus(0);
        return parameterSettingsService.insertParameterSettings(parameterSettings);
    }

    @Override
    public String getVehicleBrandById(Integer id) {
        ParameterSettings parameterSettings = parameterSettingsService.selectParameterSettingsById(id);
        return parameterSettings.getValue();
    }

    @Override
    public String getVehicleTypeById(Integer id) {
        ParameterSettings parameterSettings = parameterSettingsService.selectParameterSettingsById(id);
        return parameterSettings.getValue();
    }
}

