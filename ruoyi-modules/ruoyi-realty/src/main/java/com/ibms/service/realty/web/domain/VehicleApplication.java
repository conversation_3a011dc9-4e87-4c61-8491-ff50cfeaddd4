package com.ibms.service.realty.web.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用车申请对象 t_vehicle_application
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@Data
@TableName("t_vehicle_application")
@ApiModel("用车申请对象")
public class VehicleApplication {
    private static final long serialVersionUID = -7555726512255917217L;

    /**
     * 主键
     */
    @TableId(value = "id", type = com.baomidou.mybatisplus.annotation.IdType.AUTO)
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 申请编号
     */
    @Excel(name = "申请编号", sort = 0)
    @ApiModelProperty("申请编号")
    private String applicationNumber;

    /**
     * 车辆ID，关联车辆信息表的id
     */
    @ApiModelProperty("车辆ID，关联车辆信息表的id")
    private Integer vehicleId;

    /**
     * 用车部门ID
     */
    @ApiModelProperty("用车部门ID")
    private Integer departmentId;

    /**
     * 用车部门
     */
    @ApiModelProperty("用车部门")
    private String departmentName;

    /**
     * 出发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "出发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm", sort = 3)
    @ApiModelProperty("出发时间")
    private Date departureTime;

    /**
     * 预估还车时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty("预估还车时间")
    private Date estimatedReturnTime;

    /**
     * 出发公里数
     */
    @Excel(name = "出发公里数", sort = 4)
    @ApiModelProperty("出发公里数")
    private BigDecimal departureMileage;

    /**
     * 预估费用
     */
    @ApiModelProperty("预估费用")
    private BigDecimal estimatedCost;

    /**
     * 驾驶司机
     */
    @ApiModelProperty("驾驶司机")
    private String driver;

    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    private String contact;

    /**
     * 状态，字典：realty_vehicle_application_status，0:未审核，1:审核通过，2:审核不通过
     */
    @Excel(name = "状态", readConverterExp = "0=未审核,1=审核通过,2=审核不通过", sort = 7)
    @ApiModelProperty("状态，字典：realty_vehicle_application_status，0=未审核,1=审核通过,2=审核不通过")
    private String status;

    /**
     * 审核人id
     */
    @ApiModelProperty("审核人id")
    private Long auditorId;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    private String auditor;

    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 创建人ID
     */
    @ApiModelProperty("创建人ID")
    private Long creatorId;

    /**
     * 创建人
     */
    @Excel(name = "创建人", sort = 5)
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 车辆用途
     */
    @ApiModelProperty("车辆用途")
    private String vehiclePurpose;

    /**
     * 附件URL，多个逗号分隔
     */
    @ApiModelProperty("附件URL，多个逗号分隔")
    private String attachmentUrl;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm", sort = 6)
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 还车时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty("还车时间")
    private Date returnTime;

    /**
     * 还车公里数
     */
    @ApiModelProperty("还车公里数")
    private BigDecimal returnMileage;

    /**
     * 还车人id
     */
    @ApiModelProperty("还车人id")
    private Long returnPersonId;

    /**
     * 还车人
     */
    @ApiModelProperty("还车人")
    private String returnPerson;

    /**
     * 还车备注
     */
    @ApiModelProperty("还车备注")
    private String returnRemarks;

    /**
     * 还车附件
     */
    @ApiModelProperty("还车附件")
    private String returnAttachment;

    /**
     * 创建时间开始param
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间开始param")
    @TableField(exist = false)
    private Date createTimeStart;

    /**
     * 创建时间结束param
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间结束param")
    @TableField(exist = false)
    private Date createTimeEnd;

    /**
     * 车牌号
     */
    @Excel(name = "车牌号", sort = 1)
    @ApiModelProperty("车牌号")
    @TableField(exist = false)
    private String licensePlateNumber;
}

