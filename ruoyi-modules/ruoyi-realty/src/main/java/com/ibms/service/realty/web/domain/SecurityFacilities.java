package com.ibms.service.realty.web.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 安防设施管理对象 t_security_facilities
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@Data
@TableName("t_security_facilities")
@ApiModel(value = "安防设施管理对象", description = "安防设施管理对象")
public class SecurityFacilities {
    private static final long serialVersionUID = 6721411467253228061L;

    /**
     * 主键
     */
    @TableId(value = "id", type = com.baomidou.mybatisplus.annotation.IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 名称
     */
    @Excel(name = "名称")
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 设施类型，字典realty_security_facilities_type
     */
    @Excel(name = "设施类型", readConverterExp = "0=消防设施设备,1=消防器材,2=保安器材")
    @ApiModelProperty(value = "设施类型，字典realty_security_facilities_type")
    private String facilityType;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private Integer departmentId;

    /**
     * 部门名称
     */
    @Excel(name = "部门名称")
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 地点
     */
    @Excel(name = "地点")
    @ApiModelProperty(value = "地点")
    private String location;

    /**
     * 数量
     */
    @Excel(name = "数量")
    @ApiModelProperty(value = "数量")
    private Long quantity;

    /**
     * 规格型号
     */
    @Excel(name = "规格型号")
    @ApiModelProperty(value = "规格型号")
    private String specification;

    /**
     * 责任人id
     */
    @ApiModelProperty(value = "责任人id")
    private Long managerId;

    /**
     * 责任人名称
     */
    @Excel(name = "责任人名称")
    @ApiModelProperty(value = "责任人名称")
    private String managerName;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

}
