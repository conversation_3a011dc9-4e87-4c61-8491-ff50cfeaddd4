package com.ibms.service.realty.web.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.realty.web.domain.SecurityFacilities;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 安防设施管理Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@Mapper
public interface SecurityFacilitiesMapper extends MPJBaseMapper<SecurityFacilities> {
    /**
     * 查询安防设施管理
     *
     * @param id 安防设施管理主键
     * @return 安防设施管理
     */
    public SecurityFacilities selectSecurityFacilitiesById(Integer id);

    /**
     * 查询安防设施管理列表
     *
     * @param securityFacilities 安防设施管理
     * @return 安防设施管理集合
     */
    public List<SecurityFacilities> selectSecurityFacilitiesList(SecurityFacilities securityFacilities);

    /**
     * 新增安防设施管理
     *
     * @param securityFacilities 安防设施管理
     * @return 结果
     */
    public int insertSecurityFacilities(SecurityFacilities securityFacilities);

    /**
     * 修改安防设施管理
     *
     * @param securityFacilities 安防设施管理
     * @return 结果
     */
    public int updateSecurityFacilities(SecurityFacilities securityFacilities);

    /**
     * 删除安防设施管理
     *
     * @param id 安防设施管理主键
     * @return 结果
     */
    public int deleteSecurityFacilitiesById(Integer id);

    /**
     * 批量删除安防设施管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSecurityFacilitiesByIds(Integer[] ids);
}
