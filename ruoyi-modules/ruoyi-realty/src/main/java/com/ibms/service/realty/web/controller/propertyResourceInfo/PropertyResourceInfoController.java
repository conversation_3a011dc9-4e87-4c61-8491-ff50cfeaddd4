package com.ibms.service.realty.web.controller.propertyResourceInfo;


import com.ibms.service.realty.web.domain.PropertyResourceInfo;
import com.ibms.service.realty.web.service.PropertyResourceInfoService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;


/**
 * 物业资源信息Controller
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@RestController
@RequestMapping("/propertyResource")
public class PropertyResourceInfoController extends BaseController {
    @Autowired
    private PropertyResourceInfoService propertyResourceInfoService;

    /**
     * 查询物业资源信息列表
     */
    @GetMapping("/list")
    public AjaxResult list(PropertyResourceInfo propertyResourceInfo) {
        List<PropertyResourceInfo> list = propertyResourceInfoService.selectPropertyResourceInfoList(propertyResourceInfo);
        return AjaxResult.success(list);
    }

    /**
     * 查询物业资源信息列表,不需要构建子集
     */
    @GetMapping("/listByNoChild")
    public AjaxResult listByNoChild(PropertyResourceInfo propertyResourceInfo) {
        List<PropertyResourceInfo> list = propertyResourceInfoService.listByNoChild(propertyResourceInfo);
        return AjaxResult.success(list);
    }

    /**
     * 导出物业资源信息列表
     */
    @Log(title = "物业资源信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PropertyResourceInfo propertyResourceInfo) {
        List<PropertyResourceInfo> list = propertyResourceInfoService.selectPropertyResourceInfoList(propertyResourceInfo);
        ExcelUtil<PropertyResourceInfo> util = new ExcelUtil<PropertyResourceInfo>(PropertyResourceInfo.class);
        util.exportExcel(response, list, "物业资源信息数据");
    }

    /**
     * 获取物业资源信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(propertyResourceInfoService.selectPropertyResourceInfoById(id));
    }

    /**
     * 新增
     * 物业资源信息
     */
    @Log(title = "物业资源信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PropertyResourceInfo propertyResourceInfo) {
        propertyResourceInfo.setCreateTime(new Date());
        int r = propertyResourceInfoService.insertPropertyResourceInfo(propertyResourceInfo);
        if (r == -2) {
            return AjaxResult.error("当前节点已是最小级，不能有子节点！");
        }
        if (r == -3) {
            return AjaxResult.error("父节点不存在!");
        }
        return toAjax(r);
    }

    /**
     * 修改物业资源信息
     */
    @Log(title = "物业资源信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PropertyResourceInfo propertyResourceInfo) {
        int r = propertyResourceInfoService.updatePropertyResourceInfo(propertyResourceInfo);
        if (r == -2) {
            return AjaxResult.error("当前节点已是最小级，不能有子节点！");
        }
        if (r == -3) {
            return AjaxResult.error("父节点不存在！");
        }
        return toAjax(r);
    }

    /**
     * 删除物业资源信息
     */
    @Log(title = "物业资源信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(propertyResourceInfoService.deletePropertyResourceInfoByIds(ids));
    }
}
