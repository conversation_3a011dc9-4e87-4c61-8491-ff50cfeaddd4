package com.ibms.service.realty.web.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 保洁巡查任务操作记录对象 t_cleaning_task_operation_record
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@Data
@TableName("t_cleaning_task_operation_record")
@ApiModel("保洁巡查任务操作记录对象")
public class CleaningTaskOperationRecord {
    private static final long serialVersionUID = -2666894869435092897L;

    /**
     * 主键自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("主键自增")
    private Integer id;

    /**
     * 任务id，关联t_patrol_schedule表
     */
    @Excel(name = "任务id，关联t_patrol_schedule表")
    @ApiModelProperty("任务id，关联t_patrol_schedule表")
    private Integer taskId;

    /**
     * 内容
     */
    @Excel(name = "内容")
    @ApiModelProperty("内容")
    private String content;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date creatTime;

}
