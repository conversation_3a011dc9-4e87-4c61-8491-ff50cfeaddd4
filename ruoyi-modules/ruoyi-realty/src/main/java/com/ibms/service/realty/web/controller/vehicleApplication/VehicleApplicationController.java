package com.ibms.service.realty.web.controller.vehicleApplication;

import com.ibms.service.realty.web.domain.VehicleApplication;
import com.ibms.service.realty.web.service.VehicleApplicationService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用车申请Controller
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@RestController
@RequestMapping("/vehicleApplication")
@Api(tags = "用车申请")
public class VehicleApplicationController extends BaseController {
    @Autowired
    private VehicleApplicationService vehicleApplicationService;

    /**
     * 查询用车申请列表
     */
    @GetMapping("/list")
    @ApiOperation("查询用车申请列表")
    public TableDataInfo list(VehicleApplication vehicleApplication) {
        startPage();
        List<VehicleApplication> list = vehicleApplicationService.selectVehicleApplicationList(vehicleApplication);
        return getDataTable(list);
    }

    /**
     * 导出用车申请列表
     */
    @Log(title = "用车申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出用车申请列表")
    public void export(HttpServletResponse response, VehicleApplication vehicleApplication) {
        List<VehicleApplication> list = vehicleApplicationService.selectVehicleApplicationList(vehicleApplication);
        ExcelUtil<VehicleApplication> util = new ExcelUtil<VehicleApplication>(VehicleApplication.class);
        util.exportExcel(response, list, "用车申请数据");
    }

    /**
     * 获取用车申请详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取用车申请详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(vehicleApplicationService.selectVehicleApplicationById(id));
    }

    /**
     * 新增用车申请
     */
    @Log(title = "用车申请", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增用车申请")
    public AjaxResult add(@RequestBody VehicleApplication vehicleApplication) {
        return toAjax(vehicleApplicationService.insertVehicleApplication(vehicleApplication));
    }

    /**
     * 修改用车申请
     */
    @Log(title = "用车申请", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改用车申请")
    public AjaxResult edit(@RequestBody VehicleApplication vehicleApplication) {
        return toAjax(vehicleApplicationService.updateVehicleApplication(vehicleApplication));
    }

    /**
     * 删除用车申请
     */
    @Log(title = "用车申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除用车申请")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(vehicleApplicationService.deleteVehicleApplicationByIds(ids));
    }

    /**
     * 审核，暂定超级管理员可审核
     */
    // TODO 未完成：未来需要加上专门的审核角色
    @Log(title = "审核用车申请", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    @ApiOperation("审核用车申请")
    public AjaxResult audit(@RequestBody VehicleApplication vehicleApplication) {
        // 查看当前用户是否为超级管理员
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            return AjaxResult.error("当前用户不是超级管理员，无法审核");
        }
        return toAjax(vehicleApplicationService.audit(vehicleApplication));
    }

    /**
     * 还车
     */
    @Log(title = "还车", businessType = BusinessType.UPDATE)
    @PutMapping("/return")
    @ApiOperation("还车")
    public AjaxResult returnCar(@RequestBody VehicleApplication vehicleApplication) {
        return toAjax(vehicleApplicationService.returnCar(vehicleApplication));
    }

}

