package com.ibms.service.realty.web.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 装修验收项目信息对象 t_decoration_application_accept
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@Data
@TableName("t_decoration_application_accept")
@ApiModel(value = "DecorationApplicationAccept对象", description = "装修验收项目信息")
public class DecorationApplicationAccept {
    private static final long serialVersionUID = 6041771861436507782L;

    /**
     * 主键自增
     */
    @TableId(value = "id", type = com.baomidou.mybatisplus.annotation.IdType.AUTO)
    @ApiModelProperty(value = "主键自增")
    private Integer id;

    /**
     * 验收项目名称
     */
    @Excel(name = "验收项目名称")
    @ApiModelProperty(value = "验收项目名称")
    private String name;

    /**
     * 状态，0：启用，1：禁用
     */
    @Excel(name = "状态，0：启用，1：禁用")
    @ApiModelProperty(value = "状态，字典realty_decoration_application_accept_status，0：启用，1：禁用")
    private String status;

}
