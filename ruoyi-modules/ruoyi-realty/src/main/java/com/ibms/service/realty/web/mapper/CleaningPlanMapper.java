package com.ibms.service.realty.web.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.realty.web.domain.CleaningPlan;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 保洁巡查计划Mapper接口
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@Mapper
public interface CleaningPlanMapper extends MPJBaseMapper<CleaningPlan> {
    /**
     * 查询保洁巡查计划
     *
     * @param taskId 保洁巡查计划主键
     * @return 保洁巡查计划
     */
    public CleaningPlan selectCleaningPlanByTaskId(String taskId);

    /**
     * 查询保洁巡查计划列表
     *
     * @param cleaningPlan 保洁巡查计划
     * @return 保洁巡查计划集合
     */
    public List<CleaningPlan> selectCleaningPlanList(CleaningPlan cleaningPlan);

    /**
     * 新增保洁巡查计划
     *
     * @param cleaningPlan 保洁巡查计划
     * @return 结果
     */
    public int insertCleaningPlan(CleaningPlan cleaningPlan);

    /**
     * 修改保洁巡查计划
     *
     * @param cleaningPlan 保洁巡查计划
     * @return 结果
     */
    public int updateCleaningPlan(CleaningPlan cleaningPlan);

    /**
     * 删除保洁巡查计划
     *
     * @param taskId 保洁巡查计划主键
     * @return 结果
     */
    public int deleteCleaningPlanByTaskId(String taskId);

    /**
     * 批量删除保洁巡查计划
     *
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCleaningPlanByTaskIds(String[] taskIds);
}

