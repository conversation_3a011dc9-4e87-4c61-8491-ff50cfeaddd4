package com.ibms.service.realty.web.service.impl;


import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ibms.service.realty.web.domain.RepairApplication;
import com.ibms.service.realty.web.mapper.RepairApplicationMapper;
import com.ibms.service.realty.web.service.RepairApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 报修申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-14
 */
@Service
public class RepairApplicationServiceImpl extends MPJBaseServiceImpl<RepairApplicationMapper, RepairApplication> implements RepairApplicationService {
    @Autowired
    private RepairApplicationMapper repairApplicationMapper;

    /**
     * 查询报修申请
     *
     * @param id 报修申请主键
     * @return 报修申请
     */
    @Override
    public RepairApplication selectRepairApplicationById(String id) {
        return repairApplicationMapper.selectRepairApplicationById(id);
    }

    /**
     * 查询报修申请列表
     *
     * @param repairApplication 报修申请
     * @return 报修申请
     */
    @Override
    public List<RepairApplication> selectRepairApplicationList(RepairApplication repairApplication) {
        return repairApplicationMapper.selectRepairApplicationList(repairApplication);
    }

    /**
     * 新增报修申请
     *
     * @param repairApplication 报修申请
     * @return 结果
     */
    @Override
    public int insertRepairApplication(RepairApplication repairApplication) {
        return repairApplicationMapper.insertRepairApplication(repairApplication);
    }

    /**
     * 修改报修申请
     *
     * @param repairApplication 报修申请
     * @return 结果
     */
    @Override
    public int updateRepairApplication(RepairApplication repairApplication) {
        return repairApplicationMapper.updateRepairApplication(repairApplication);
    }

    /**
     * 批量删除报修申请
     *
     * @param ids 需要删除的报修申请主键
     * @return 结果
     */
    @Override
    public int deleteRepairApplicationByIds(String[] ids) {
        return repairApplicationMapper.deleteRepairApplicationByIds(ids);
    }

    /**
     * 删除报修申请信息
     *
     * @param id 报修申请主键
     * @return 结果
     */
    @Override
    public int deleteRepairApplicationById(String id) {
        return repairApplicationMapper.deleteRepairApplicationById(id);
    }
}

