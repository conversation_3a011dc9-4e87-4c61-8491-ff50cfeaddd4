package com.ibms.service.realty.web.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 办公文档资料管理对象 t_office_document
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@Data
@TableName("t_office_document")
@ApiModel(value = "办公文档资料管理对象", description = "办公文档资料管理对象")
public class OfficeDocument {
    private static final long serialVersionUID = 6224464524071412037L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 文档编号
     */
    @Excel(name = "文档编号")
    @ApiModelProperty(value = "文档编号")
    private String documentNumber;

    /**
     * 文档名称
     */
    @Excel(name = "文档名称")
    @ApiModelProperty(value = "文档名称")
    private String documentName;

    /**
     * 文档类型ID，关联t_office_category的id，且type=0
     */
    @Excel(name = "文档类型ID，关联t_office_category的id，且type=0")
    @ApiModelProperty(value = "文档类型ID，关联t_office_category的id，且type=0")
    private Integer documentCategoryId;
    private Integer documentTypeId;

    /**
     * 归档时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "归档时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "归档时间")
    private Date archiveTime;

    /**
     * 起效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "起效日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "起效日期")
    private Date effectiveDate;

    /**
     * 失效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "失效日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "失效日期")
    private Date expiryDate;

    /**
     * 版本
     */
    @Excel(name = "版本")
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建人ID
     */
    @Excel(name = "创建人ID")
    @ApiModelProperty(value = "创建人ID")
    private Long creatorId;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String creator;

    /**
     * 审核人ID
     */
    @Excel(name = "审核人ID")
    @ApiModelProperty(value = "审核人ID")
    private Long reviewerId;

    /**
     * 审核人
     */
    @Excel(name = "审核人")
    @ApiModelProperty(value = "审核人")
    private String reviewer;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private Date reviewerTime;

    /**
     * 附件url，多个逗号隔开
     */
    @Excel(name = "附件url，多个逗号隔开")
    @ApiModelProperty(value = "附件url，多个逗号隔开")
    private String accessoryUrl;

    /**
     * 状态，字典：realty_office_document_status；0:未审核，1:已审核
     */
    @Excel(name = "状态，字典：realty_office_document_status；0:未审核，1:已审核")
    @ApiModelProperty(value = "状态，字典：realty_office_document_status；0:未审核，1:已审核")
    private String status;

    /**
     * 文档类型名称
     */
    @Excel(name = "文档类型名称")
    @ApiModelProperty(value = "文档类型名称")
    @TableField(exist = false)
    private String documentTypeName;

    /**
     * 归档开始时间 param
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "归档开始时间")
    @TableField(exist = false)
    private Date archiveTimeStart;

    /**
     * 归档结束时间 param
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "归档结束时间")
    @TableField(exist = false)
    private Date archiveTimeEnd;

}
