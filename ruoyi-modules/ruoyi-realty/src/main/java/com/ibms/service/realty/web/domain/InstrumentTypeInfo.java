package com.ibms.service.realty.web.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 仪器类型信息对象 t_instrument_type_info
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
@Data
@TableName("t_instrument_type_info")
@ApiModel(value = "仪器类型信息对象", description = "仪器类型信息对象")
public class InstrumentTypeInfo {
    private static final long serialVersionUID = 3002961084529660748L;

    /**
     * 主键自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键自增")
    private Integer id;

    /**
     * 名称
     */
    @Excel(name = "名称")
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 计量单位
     */
    @Excel(name = "计量单位")
    @ApiModelProperty(value = "计量单位")
    private String unit;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;
}

