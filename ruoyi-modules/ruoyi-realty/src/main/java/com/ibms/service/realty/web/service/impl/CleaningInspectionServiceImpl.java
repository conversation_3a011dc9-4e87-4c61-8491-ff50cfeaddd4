package com.ibms.service.realty.web.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ibms.service.realty.web.domain.CleaningInspection;
import com.ibms.service.realty.web.mapper.CleaningInspectionMapper;
import com.ibms.service.realty.web.service.CleaningInspectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 保洁检查Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-04
 */
@Service
public class CleaningInspectionServiceImpl extends MPJBaseServiceImpl<CleaningInspectionMapper, CleaningInspection> implements CleaningInspectionService {
    @Autowired
    private CleaningInspectionMapper cleaningInspectionMapper;

    /**
     * 查询保洁检查
     *
     * @param id 保洁检查主键
     * @return 保洁检查
     */
    @Override
    public CleaningInspection selectCleaningInspectionById(Integer id) {
        return cleaningInspectionMapper.selectCleaningInspectionById(id);
    }

    /**
     * 查询保洁检查列表
     *
     * @param cleaningInspection 保洁检查
     * @return 保洁检查
     */
    @Override
    public List<CleaningInspection> selectCleaningInspectionList(CleaningInspection cleaningInspection) {
        return cleaningInspectionMapper.selectCleaningInspectionList(cleaningInspection);
    }

    /**
     * 新增保洁检查
     *
     * @param cleaningInspection 保洁检查
     * @return 结果
     */
    @Override
    public int insertCleaningInspection(CleaningInspection cleaningInspection) {
        return cleaningInspectionMapper.insertCleaningInspection(cleaningInspection);
    }

    /**
     * 修改保洁检查
     *
     * @param cleaningInspection 保洁检查
     * @return 结果
     */
    @Override
    public int updateCleaningInspection(CleaningInspection cleaningInspection) {
        return cleaningInspectionMapper.updateCleaningInspection(cleaningInspection);
    }

    /**
     * 批量删除保洁检查
     *
     * @param ids 需要删除的保洁检查主键
     * @return 结果
     */
    @Override
    public int deleteCleaningInspectionByIds(Integer[] ids) {
        return cleaningInspectionMapper.deleteCleaningInspectionByIds(ids);
    }

    /**
     * 删除保洁检查信息
     *
     * @param id 保洁检查主键
     * @return 结果
     */
    @Override
    public int deleteCleaningInspectionById(Integer id) {
        return cleaningInspectionMapper.deleteCleaningInspectionById(id);
    }
}

