package com.ibms.service.realty.web.controller.cleaningProblemRecord;


import com.ibms.service.realty.web.domain.CleaningProblemRecord;
import com.ibms.service.realty.web.service.CleaningProblemRecordService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 保洁巡查问题记录Controller
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@RestController
@RequestMapping("/cleanRecord")
@Api(tags = "保洁巡查问题记录")
public class CleaningProblemRecordController extends BaseController {
    @Autowired
    private CleaningProblemRecordService cleaningProblemRecordService;

    /**
     * 查询保洁巡查问题记录列表
     */
    @GetMapping("/list")
    @ApiOperation("查询保洁巡查问题记录列表")
    public TableDataInfo list(CleaningProblemRecord cleaningProblemRecord) {
        startPage();
        List<CleaningProblemRecord> list = cleaningProblemRecordService.selectCleaningProblemRecordList(cleaningProblemRecord);
        return getDataTable(list);
    }

    /**
     * 导出保洁巡查问题记录列表
     */
    @Log(title = "保洁巡查问题记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出保洁巡查问题记录列表")
    public void export(HttpServletResponse response, CleaningProblemRecord cleaningProblemRecord) {
        List<CleaningProblemRecord> list = cleaningProblemRecordService.selectCleaningProblemRecordList(cleaningProblemRecord);
        ExcelUtil<CleaningProblemRecord> util = new ExcelUtil<CleaningProblemRecord>(CleaningProblemRecord.class);
        util.exportExcel(response, list, "保洁巡查问题记录数据");
    }

    /**
     * 获取保洁巡查问题记录详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取保洁巡查问题记录详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(cleaningProblemRecordService.selectCleaningProblemRecordById(id));
    }

    /**
     * 新增保洁巡查问题记录
     */
    @Log(title = "保洁巡查问题记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增保洁巡查问题记录")
    public AjaxResult add(@RequestBody CleaningProblemRecord cleaningProblemRecord) {
        return toAjax(cleaningProblemRecordService.insertCleaningProblemRecord(cleaningProblemRecord));
    }

    /**
     * 修改保洁巡查问题记录
     */
    @Log(title = "保洁巡查问题记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改保洁巡查问题记录")
    public AjaxResult edit(@RequestBody CleaningProblemRecord cleaningProblemRecord) {
        return toAjax(cleaningProblemRecordService.updateCleaningProblemRecord(cleaningProblemRecord));
    }

    /**
     * 删除保洁巡查问题记录
     */
    @Log(title = "保洁巡查问题记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除保洁巡查问题记录")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(cleaningProblemRecordService.deleteCleaningProblemRecordByIds(ids));
    }
}

