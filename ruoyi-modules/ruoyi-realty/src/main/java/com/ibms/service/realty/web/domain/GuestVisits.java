package com.ibms.service.realty.web.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 来人来访信息对象 t_guest_visits
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@TableName("t_guest_visits")
@Data
@ApiModel(value = "来人来访信息对象", description = "来人来访信息对象")
public class GuestVisits {
    private static final long serialVersionUID = -4722061071824319245L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 管理区id
     */
    @Excel(name = "管理区id")
    @ApiModelProperty(value = "管理区id")
    private Integer managementAreaId;

    /**
     * 来访人姓名
     */
    @Excel(name = "来访人姓名")
    @ApiModelProperty(value = "来访人姓名")
    private String guestName;

    /**
     * 证件号码
     */
    @Excel(name = "证件号码")
    @ApiModelProperty(value = "证件号码")
    private String idCardNum;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactNum;

    /**
     * 被访人
     */
    @Excel(name = "被访人")
    @ApiModelProperty(value = "被访人")
    private String visitedName;

    /**
     * 来访时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "来访时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "来访时间")
    private Date visitTime;

    /**
     * 离开时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "离开时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "离开时间")
    private Date leaveTime;

    /**
     * 事由
     */
    @Excel(name = "事由")
    @ApiModelProperty(value = "事由")
    private String reason;

}

