package com.ibms.service.realty.web.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 房间仪对象 t_room_instrument
 *
 * <AUTHOR>
 * @date 2023-04-14
 */
@Data
@TableName("t_room_instrument")
@ApiModel(value = "房间仪对象", description = "房间仪对象")
public class RoomInstrument {
    private static final long serialVersionUID = -7928846368263366013L;

    /**
     * 主键自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String number;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 房间编号，对应物业资源信息表
     */
    @Excel(name = "房间编号，对应物业资源信息表")
    @ApiModelProperty(value = "房间编号，对应物业资源信息表")
    private Integer roomCode;

    /**
     * 仪表类型id，对应t_instrument_type_info表
     */
    @Excel(name = "仪表类型id，对应t_instrument_type_info表")
    @ApiModelProperty(value = "仪表类型id，对应t_instrument_type_info表")
    private Integer instrumentTypeId;

    /**
     * 仪表类型名字
     */
    @Excel(name = "仪表类型名字")
    @ApiModelProperty(value = "仪表类型名字")
    @TableField(exist = false)
    private String instrumentTypeName;

    /**
     * 房间名字
     */
    @ApiModelProperty(value = "房间名字")
    @TableField(exist = false)
    private String roomName;

    /**
     * 楼层名字
     */
    @ApiModelProperty(value = "楼层名字")
    @TableField(exist = false)
    private String floorName;

    /**
     * 楼栋名字
     */
    @ApiModelProperty(value = "楼栋名字")
    @TableField(exist = false)
    private String buildingName;

    /**
     * 房间id param
     */
    @ApiModelProperty(value = "房间id param")
    @TableField(exist = false)
    private Integer roomId;

    /**
     * 楼层id param
     */
    @ApiModelProperty(value = "楼层id param")
    @TableField(exist = false)
    private Integer floorId;

    /**
     * 楼栋id param
     */
    @ApiModelProperty(value = "楼栋id param")
    @TableField(exist = false)
    private Integer buildingId;

}
