package com.ibms.service.realty.web.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ibms.service.realty.web.domain.DecorationApplicationSettings;
import com.ibms.service.realty.web.mapper.DecorationApplicationSettingsMapper;
import com.ibms.service.realty.web.service.DecorationApplicationSettingsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 装修申请设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@Service
public class DecorationApplicationSettingsServiceImpl extends MPJBaseServiceImpl<DecorationApplicationSettingsMapper, DecorationApplicationSettings> implements DecorationApplicationSettingsService {
    @Autowired
    private DecorationApplicationSettingsMapper decorationApplicationSettingsMapper;

    /**
     * 查询装修申请设置
     *
     * @param id 装修申请设置主键
     * @return 装修申请设置
     */
    @Override
    public DecorationApplicationSettings selectDecorationApplicationSettingsById(Integer id) {
        return decorationApplicationSettingsMapper.selectDecorationApplicationSettingsById(id);
    }

    /**
     * 查询装修申请设置列表
     *
     * @param decorationApplicationSettings 装修申请设置
     * @return 装修申请设置
     */
    @Override
    public List<DecorationApplicationSettings> selectDecorationApplicationSettingsList(DecorationApplicationSettings decorationApplicationSettings) {
        return decorationApplicationSettingsMapper.selectDecorationApplicationSettingsList(decorationApplicationSettings);
    }

    /**
     * 新增装修申请设置
     *
     * @param decorationApplicationSettings 装修申请设置
     * @return 结果
     */
    @Override
    public int insertDecorationApplicationSettings(DecorationApplicationSettings decorationApplicationSettings) {
        return decorationApplicationSettingsMapper.insertDecorationApplicationSettings(decorationApplicationSettings);
    }

    /**
     * 修改装修申请设置
     *
     * @param decorationApplicationSettings 装修申请设置
     * @return 结果
     */
    @Override
    public int updateDecorationApplicationSettings(DecorationApplicationSettings decorationApplicationSettings) {
        return decorationApplicationSettingsMapper.updateDecorationApplicationSettings(decorationApplicationSettings);
    }

    /**
     * 批量删除装修申请设置
     *
     * @param ids 需要删除的装修申请设置主键
     * @return 结果
     */
    @Override
    public int deleteDecorationApplicationSettingsByIds(Integer[] ids) {
        return decorationApplicationSettingsMapper.deleteDecorationApplicationSettingsByIds(ids);
    }

    /**
     * 删除装修申请设置信息
     *
     * @param id 装修申请设置主键
     * @return 结果
     */
    @Override
    public int deleteDecorationApplicationSettingsById(Integer id) {
        return decorationApplicationSettingsMapper.deleteDecorationApplicationSettingsById(id);
    }
}
