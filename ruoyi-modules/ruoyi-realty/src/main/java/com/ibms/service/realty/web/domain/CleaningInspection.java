package com.ibms.service.realty.web.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 保洁检查对象 t_cleaning_inspection
 *
 * <AUTHOR>
 * @date 2023-07-04
 */
@Data
@TableName("t_cleaning_inspection")
@ApiModel(value = "CleaningInspection", description = "保洁检查对象")
public class CleaningInspection {
    private static final long serialVersionUID = -194215805019093020L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private Integer id;

    /**
     * 保洁区域id
     */
    @ApiModelProperty(value = "保洁区域id")
    private Integer cleaningRegionId;

    @Excel(name = "保洁区域")
    @ApiModelProperty(value = "保洁区域")
    @TableField(exist = false)
    private String cleaningRegionName;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    private Long responsiblePersonId;

    /**
     * 负责人名字
     */
    @Excel(name = "负责人")
    @ApiModelProperty(value = "负责人")
    private String responsiblePersonName;

    /**
     * 检查人id
     */
    @ApiModelProperty(value = "检查人id")
    private Long inspectorId;

    /**
     * 检查人名字
     */
    @Excel(name = "检查人")
    @ApiModelProperty(value = "检查人名字")
    private String inspectorName;

    /**
     * 检查日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检查时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "检查时间")
    private Date inspectionDate;

    /**
     * 检查情况
     */
    @Excel(name = "检查情况")
    @ApiModelProperty(value = "检查情况")
    private String inspectionStatus;

    /**
     * 检查结果,0:合格，1：不合格，2：整改
     */
    @Excel(name = "检查结果", readConverterExp = "0=合格,1=不合格,2=整改")
    @ApiModelProperty(value = "检查结果")
    private String inspectionResult;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 检查日期开始param
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "检查日期开始param")
    @TableField(exist = false)
    private Date inspectionDateStart;

    /**
     * 检查日期结束param
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "检查日期结束param")
    @TableField(exist = false)
    private Date inspectionDateEnd;
}
