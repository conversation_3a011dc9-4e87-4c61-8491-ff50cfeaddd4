package com.ibms.service.realty.web.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.ibms.service.realty.web.controller.patrolSchedule.param.ScheduleSignParam;
import com.ibms.service.realty.web.domain.PatrolSchedule;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 巡更任务Mapper接口
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@Mapper
public interface PatrolScheduleMapper extends MPJBaseMapper<PatrolSchedule> {
    /**
     * 查询巡更任务
     *
     * @param taskId 巡更任务主键
     * @return 巡更任务
     */
    public List<PatrolSchedule> selectPatrolScheduleByTaskId(Integer taskId);

    /**
     * 查询巡更任务列表
     *
     * @param patrolSchedule 巡更任务
     * @return 巡更任务集合
     */
    public List<PatrolSchedule> selectPatrolScheduleList(PatrolSchedule patrolSchedule);

    /**
     * 新增巡更任务
     *
     * @param patrolSchedule 巡更任务
     * @return 结果
     */
    public int insertPatrolSchedule(PatrolSchedule patrolSchedule);

    /**
     * 修改巡更任务
     *
     * @param patrolSchedule 巡更任务
     * @return 结果
     */
    public int updatePatrolSchedule(PatrolSchedule patrolSchedule);

    /**
     * 删除巡更任务
     *
     * @param taskId 巡更任务主键
     * @return 结果
     */
    public int deletePatrolScheduleByTaskId(Integer taskId);

    /**
     * 批量删除巡更任务
     *
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePatrolScheduleByTaskIds(Integer[] taskIds);

    int signImgAdd(ScheduleSignParam scheduleSignParam);
}
