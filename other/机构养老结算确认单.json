[
  {
    "type": "bedFee",
    "typeName": "床位费(单人间)",
    "feeStandard": 3000,
    "changeDate": "2024-09-01",
    "paidAmount": 6666,
    "prepaidPeriod": {
      "discount": 0.5
    },
    "paymentAmount": 2000,
    "remarks": "",
    "estimatedExpiryDate": "2024-10-01"
  },
  {
    "type": "careFee",
    "typeName": "护理费(全护1级)",
    "feeStandard": 2500,
    "changeDate": "2024-09-01",
    "paidAmount": 6666,
    "prepaidPeriod": {
      "type": 1, // 类型，1：补费，2：退费
      "full": 0,
      "partial": 0
    },
    "paymentAmount": 0,
    "remarks": "",
    "estimatedExpiryDate": "2024-09-01"
  },
  {
    "type": "mealFee",
    "typeName": "餐费(标准)",
    "feeStandard": 800,
    "changeDate": "2024-09-01",
    "paidAmount": 6666,
    "prepaidPeriod": {
      "type": 1, // 类型，1：补费，2：退费
      "full": 0,
      "partial": 0
    },
    "paymentAmount": 0,
    "remarks": "",
    "estimatedExpiryDate": "2024-10-11"
  },
  {
    "type": "acFee",
    "typeName": "空调费",
    "feeStandard": 500,
    "paidAmount": 6666,
    "prepaidPeriod": {
      "type": 1, // 类型，1：补费，2：退费
      "full": 0,
      "partial": 0
    },
    "paymentAmount": 0,
    "dateRange": "2025/6/1~2025/9/15",
    "remarks": "",
    "estimatedExpiryDate": "2024-08-01"
  },
  {
    "type": "addedService",
    "typeName": "增值服务(3)",
    "billIds": "1,3,5", // 本次缴费的增值服务账单ID，多个逗号隔开
    "paymentAmount": 666,
    "remarks": ""
  },
  {
    "type": "medicalSecurityFee",
    "typeName": "医疗保险金",
    "feeStandard": 0,  // 展示确认单此处累加金额
    "paymentAmount": 0,
    "remarks": ""
  },
  {
    "type": "accountDeduction",
    "typeName": "消费账户抵扣",
    "feeStandard": 0, // 展示账户余额
    "paymentAmount": 0,
    "remarks": ""
  }
]
