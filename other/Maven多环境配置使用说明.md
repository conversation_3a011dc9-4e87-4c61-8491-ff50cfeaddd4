# Maven 多环境配置使用说明

## 概述

本项目已配置 Maven 多环境支持，可以在构建时动态选择不同的环境配置，避免手动修改 `bootstrap.yml` 文件。

## 环境配置

项目支持以下四个环境：

### 1. zkx-local（自定义环境）
- **Profile ID**: `zkx-local`
- **Spring Active**: `zkx-local`
- **Nacos地址**: `117.159.27.170:23404`
- **Sentinel控制台**: `127.0.0.1:8718`

### 2. local（默认环境）
- **Profile ID**: `local`
- **Spring Active**: `local`
- **Nacos地址**: `127.0.0.1:8848`
- **Sentinel控制台**: `127.0.0.1:8718`

### 3. dev（开发环境）
- **Profile ID**: `dev`
- **Spring Active**: `dev`
- **Nacos地址**: `117.159.27.170:23404`
- **Sentinel控制台**: `127.0.0.1:8718`

### 4. test（测试环境）
- **Profile ID**: `test`
- **Spring Active**: `test`
- **Nacos地址**: `117.159.27.170:23404`
- **Sentinel控制台**: `127.0.0.1:8718`

## 使用方法

### 1. Maven 命令行构建

#### 使用默认环境（zkx-local）
```bash
mvn clean package
```

#### 指定特定环境
```bash
# 使用本地环境
mvn clean package -P local

# 使用开发环境
mvn clean package -P dev

# 使用测试环境
mvn clean package -P test

# 使用zkx-local环境
mvn clean package -P zkx-local
```

### 2. IDEA 中使用

1. 打开 IDEA 的 Maven 面板
2. 在 Profiles 部分选择需要的环境（local、dev、test、zkx-local）
3. 执行 Maven 构建命令

### 3. 构建单个模块

```bash
# 构建网关模块（使用dev环境）
mvn clean package -P dev -pl ruoyi-gateway

# 构建custom模块（使用test环境）
mvn clean package -P test -pl ruoyi-modules/ruoyi-custom

# 构建system模块（使用local环境）
mvn clean package -P local -pl ruoyi-modules/ruoyi-system
```

## 配置原理

### 1. 根 pom.xml 配置
- 定义了四个 `<profile>` 配置
- 每个 profile 包含对应的属性值
- 配置了资源过滤插件

### 2. bootstrap.yml 配置
- 使用 Maven 占位符 `@profiles.active@` 替代硬编码的环境名
- 使用 `@nacos.server.addr@` 替代硬编码的 Nacos 地址
- 使用 `@sentinel.dashboard.addr@` 替代硬编码的 Sentinel 地址

### 3. 子模块 pom.xml 配置
- 添加了资源过滤配置
- 配置了 maven-resources-plugin 插件

- **作者**: zkx
- **日期**: 2025年06月08日