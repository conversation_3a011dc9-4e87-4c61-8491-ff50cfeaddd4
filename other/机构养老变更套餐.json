
[
  //床位费
  {
    "type": "bedFee", // 类型
    "typeName": "床位费(单人间)", // 类型Str
    "oldBedId": 1, // 变更前房间id
    "originalFeeStandard": 3000, // 变更前费用标准
    "afterFeeStandard": 4000, // 变更后费用标准
    "paidAmount": 6666, // 已缴金额
    "changedItem": {
      "roomId": 1, // 变更后房间id
      "roomVersion": "V.1", // 房间版本
      "bedId": 1, // 变更后床位id
      "bedName": "楼-层-房-1号床", // 变更后床位名称(全称)
      "discount": "0.5"
    },
    "changedAmount": 3000, // 调整金额
    "startDate": "", // 最近变更开始日期
    "endDate": "", // 变更结束日期
    "remark": ""
  },
  // 护理费
  {
    "type": "careFee", // 类型
    "typeName": "护理费(全护1级)", // 类型Str
    "originalFeeStandard": 2500, // 变更前费用标准
    "afterFeeStandard": 3000, // 变更后费用标准
    "paidAmount": 6666, // 已缴金额
    "changedItem": {
      "careLevel": "1", // 护理级别
      "careLevelName": "全护1级", // 护理级别名称
      "careComboId": 1, // 护理套餐id
      "careComboName": "全护1级套餐", // 护理套餐名称
      "careComboFeeVersion": 1, // 护理套餐费用版本
    },
    "changedAmount": 3000, // 调整金额
    "startDate": "", // 最近变更开始日期
    "endDate": "", // 变更结束日期
    "remark": ""
  },
  // 餐费
  {
    "type": "mealFee", // 类型
    "typeName": "餐费（标准）", // 类型Str
    "originalFeeStandard": 800,  // 变更前费用标准
    "afterFeeStandard": 1000, // 变更后费用标准
    "paidAmount": 6666, // 已缴金额
    "changedItem": {
      "mealComboId": 1, // 餐食套餐id
      "mealComboName": "标准套餐", // 餐食套餐名称
      "mealComboFeeId": 1, // 餐食套餐费用id
    },
    "changedAmount": 3000, // 调整金额
    "startDate": "", // 最近变更开始日期
    "endDate": "", // 变更结束日期
    "remark": ""
  },
  // 消费账户
  {
    "type": "accountDeduction",
    "typeName": "消费账户",
    "originalFeeStandard": 0, // 展示账户余额
    "changedAmount": 0, // 调整金额, 正减，负加
    "remarks": ""
  }
]
